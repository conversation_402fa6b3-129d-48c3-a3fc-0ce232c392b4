import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/data/model/user_info.dart';
import 'package:mposxs/app/res/font/app_fonts.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/theme/app_common_style.dart';
import 'package:mposxs/app/ui/widget/touchable_widget.dart';

class HomeWidget extends StatelessWidget {
  final String? appVersion;
  final List<MenuHome>? listMenuHomeShow;
  final Function? onPressHomeItemButton;

  const HomeWidget({Key? key, this.appVersion, this.listMenuHomeShow, this.onPressHomeItemButton}) : super(key: key);

  Widget _buildButtonMain(MenuHome homeButtonItem) {
    return TouchableWidget(
      decoration: bodyDecoration(),
      child: Column(
        children: <Widget>[
          Expanded(
            flex: 2,
            child: Image.asset(homeButtonItem.iconLink!),
          ),
          Expanded(
            flex: 1,
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: Padding(
                padding: const EdgeInsets.only(top: 10),
                child: Text(
                  Get.locale!.languageCode == 'vi' ? homeButtonItem.title! : homeButtonItem.titleEn!,
                  maxLines: 1,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: 15,
                    fontFamily: AppFonts.robotoMedium,
                    color: AppColors.blackText,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
      onPressed: () {
        if (onPressHomeItemButton != null) {
          onPressHomeItemButton!(homeButtonItem);
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        children: [
          Container(
              child: GridView.count(
                  shrinkWrap: true,
                  physics: new NeverScrollableScrollPhysics(),
                  padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                  crossAxisCount: 2,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                  childAspectRatio: 17 / 11,
                  children: (listMenuHomeShow ?? []).map((item) => _buildButtonMain(item)).toList())),
          Spacer(),
          Center(
            child: Text(
              'Version: ${appVersion ?? ''} Powered by mPOS - Ngân Lượng',
              style: TextStyle(
                color: AppColors.greyText,
                fontSize: 12,
              ),
            ),
          ),
          SizedBox(height: 15),
        ],
      ),
    );
  }
}
