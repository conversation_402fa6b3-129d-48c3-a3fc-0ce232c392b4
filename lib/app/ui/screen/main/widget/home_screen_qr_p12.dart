import 'package:card_swiper/card_swiper.dart';
import 'package:cashiermodule/constants/style.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/controller/app_controller.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/theme/app_dimens.dart';
import 'package:qr_flutter/qr_flutter.dart';

import '../../../../util/constants.dart';
import '../../../widget/card_info_device_widget.dart';
import '../../../widget/common_button.dart';

class HomeScreenQrP12 extends StatefulWidget {
  final String? qrCodeVietQr;
  final String? qrCodeAppleGgPay;
  final String? nameMerchant;
  final String? bankNumber;
  final String? mUser;
  final String? ipAddress;
  final String? statusEmqx;
  final bool? isShowQrCodeAppleGgPay;
  final List? listQr;
  final Function? onPressFetchQr;
  final Function? onSelectQr;

  const HomeScreenQrP12(
      {Key? key,
      this.qrCodeVietQr,
      this.qrCodeAppleGgPay,
      this.nameMerchant,
      this.bankNumber,
      this.onPressFetchQr,
      this.mUser,
      this.statusEmqx,
      this.isShowQrCodeAppleGgPay,
      this.listQr,
      this.onSelectQr,
      this.ipAddress})
      : super(key: key);

  @override
  _HomeScreenQrP12 createState() => _HomeScreenQrP12();
}

class _HomeScreenQrP12 extends State<HomeScreenQrP12> {
  // SwiperController controller = SwiperController();
  int currentIndex = 0;

  void changeQr() {
    setState(() {
      //change current Qr
      if (widget.listQr!.length > 1 && currentIndex == 0) {
        currentIndex = 1;
      } else {
        currentIndex = 0;
      }

      // currentIndex = index;
      if (widget.onSelectQr != null) {
        widget.onSelectQr!(widget.listQr?[currentIndex]);
      }
    });
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
          child: Container(
            margin: EdgeInsets.only(right: 10),
            width: MediaQuery.of(context).size.width,
            height: 280,
            child: Center(
              child: Container(
                child: Stack(
                  children: [
                      ((widget.listQr!.contains(StaticQRType.AGPayQR) && (widget.isShowQrCodeAppleGgPay == true) && Get.find<MyAppController>().paymentMethod!.vietQrActive) ? InkWell(
                      onTap: () {
                        // controller.next(animation: true);
                        changeQr();
                      },
                      child: Align(
                        alignment: Alignment.centerRight,
                        child: Container(
                          margin: EdgeInsets.only(right: 30),
                          alignment: Alignment.centerLeft,
                          decoration: BoxDecoration(
                              color: AppColors.grayBackground.withOpacity(0.5),
                              borderRadius: BorderRadius.circular(10),
                              boxShadow: [
                                BoxShadow(
                                  color: AppColors.black.withOpacity(0.2),
                                  spreadRadius: 1,
                                  blurRadius: 2,
                                ),
                              ]),
                          width: 100,
                          height: 200,
                          child: Image.asset((widget.listQr?[currentIndex] == StaticQRType.VietQR) ? AppImages.ic_appleqr_sw : AppImages.ic_vietqr_sw),
                        ),
                      ),
                    ) : SizedBox.shrink()),
                    Center(
                      child: Container(
                        width: 300,
                        height: 300,
                        // color: Colors.amber,
                        child: _buildMainQrWidget(widget.listQr![currentIndex])

                        // child: Swiper(
                        //   controller: controller,
                        //   itemWidth: 290.0,
                        //   itemHeight: 280.0,
                        //   layout: SwiperLayout.CUSTOM,
                        //   customLayoutOption:
                        //       CustomLayoutOption(startIndex: -1, stateCount: 3)
                        //         ..addTranslate([
                        //           Offset(-370.0, -60.0),
                        //           Offset(0.0, 0.0),
                        //           Offset(370.0, -40.0)
                        //         ]),
                        //   itemBuilder: (BuildContext context, int index) {
                        //     return _buildMainQrWidget(widget.listQr![index]);
                        //   },
                        //   autoplay: false,
                        //   indicatorLayout: PageIndicatorLayout.NONE,
                        //   itemCount: (widget.isShowQrCodeAppleGgPay == true && Get.find<MyAppController>().paymentMethod!.vietQrActive) ? widget.listQr!.length : 1,
                        //   scrollDirection: Axis.horizontal,
                        //   loop: (widget.isShowQrCodeAppleGgPay == true && Get.find<MyAppController>().paymentMethod!.vietQrActive) ? true : false,
                        //   // control: const SwiperControl(),
                        //   viewportFraction: 0.8,
                        //   onIndexChanged: (index) {
                        //     changeQr(index);
                        //   },
                        // ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
        (widget.listQr?[currentIndex] == StaticQRType.VietQR) ? _bodyVietQrWidget() : _bodyGgApplePayQrWidget()
      ],
    );
  }

  _buildMainQrWidget(StaticQRType qrData) {
    String qrCode = '';
    if (qrData == StaticQRType.VietQR) {
      qrCode = widget.qrCodeVietQr ?? '';
    }else {
      qrCode = widget.qrCodeAppleGgPay ?? '';
    }
    return (qrCode == '')
        ? Container(
            child: LayoutBuilder(builder: (context, constraints) {
              final containerSize = constraints.biggest.shortestSide;
              return Container(
                padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                height: containerSize,
                width: containerSize,
                decoration: BoxDecoration(
                  color: AppColors.lightGrayRipple,
                  borderRadius: BorderRadius.all(Radius.circular(10)),
                ),
                child: Center(
                  child: (qrData == StaticQRType.VietQR)
                      ? CommonButton(
                          minWidth: 110,
                          borderCircular: 25,
                          color: AppColors.white1,
                          onPressed: () {
                            widget.onPressFetchQr!();
                          },
                          child: Text(
                            AppStrings.getString(
                                AppStrings.tv_create_qr_again)!,
                            style: TextStyle(color: AppColors.primary),
                          ),
                        )
                      : Container(
                          child: Text('Mã Qr không khả dụng'),
                        ),
                ),
              );
            }),
          )
        : Container(
            width: 280,
            height: 280,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
              decoration: BoxDecoration(
                  color: AppColors.white,
                  borderRadius: BorderRadius.circular(10),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.black.withOpacity(0.2),
                      spreadRadius: 1,
                      blurRadius: 2,
                    ),
                  ]),
              child: QrImageView(
                padding: EdgeInsets.all(5),
                backgroundColor: Colors.transparent,
                data: qrCode ?? '',
                version: QrVersions.auto,
                embeddedImage: (qrData == StaticQRType.VietQR) ? AssetImage(AppImages.ic_vietqr_mini) : null,
                embeddedImageStyle: QrEmbeddedImageStyle(size: Size(30, 30)),
              ),
            ),
          );
  }

  _bodyVietQrWidget() {
    return Container(
      // padding: EdgeInsets.only(bottom: 5, top: 10),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            widget.nameMerchant ?? '',
            style: style_S14_W400_WhiteColor,
          ),
          SizedBox(
            height: 2,
          ),
          Text(
            widget.bankNumber ?? '',
            style: style_S20_W600_WhiteColor,
          ),
          SizedBox(
            height: 2,
          ),
          Text(
            AppStrings.getString(AppStrings.tv_detail_bank_number)!,
            style: TextStyle(
                fontSize: AppDimens.textSizeXSmall,
                fontFamily: kFontFamilyBeVietnamPro,
                color: AppColors.white1),
          ),
          CardInfoDevice(mUser: widget.mUser, ipAddress: widget.ipAddress, status: widget.statusEmqx,)
        ],
      ),
    );
  }

  _bodyGgApplePayQrWidget() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 30),
          child: Text(
            '${AppStrings.getString(AppStrings.msg_intro_static_qr_ag)}',
            style: style_S14_W600_WhiteColor,textAlign: TextAlign.center,
          ),
        ),
        SizedBox(
          height: 10,
        ),
        CardInfoDevice(mUser: widget.mUser, ipAddress: widget.ipAddress, status: widget.statusEmqx,)
      ],
    );
  }
}
