import 'package:flutter/material.dart';
import 'package:flutter_dash/flutter_dash.dart';
import 'package:intl/intl.dart';
import 'package:mposxs/app/controller/app_controller.dart';
import 'package:mposxs/app/data/model/summary_response.dart';
import 'package:mposxs/app/res/font/app_fonts.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/widget/common_button.dart';
import 'package:mposxs/app/util/app_utils.dart';

class SummaryWidget extends StatefulWidget {
  final SummaryResponse? summaryData;
  final DateTime? startDate;
  final DateTime? endDate;
  final Function? onPressPrint;

  const SummaryWidget({Key? key, this.summaryData, this.startDate, this.endDate, this.onPressPrint}) : super(key: key);

  @override
  _SummaryWidgetState createState() => _SummaryWidgetState();
}

class _SummaryWidgetState extends State<SummaryWidget> {
  _onPressClose() {
    Navigator.of(context).pop();
  }

  _onPressPrint() {
    if (widget.onPressPrint != null) {
      widget.onPressPrint!();
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).padding.bottom + MediaQuery.of(context).viewInsets.bottom,
            top: 16,
          ),
          decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(6),
                topRight: Radius.circular(6),
              )),
          child: Column(
            children: [
              Text(
                AppStrings.getString(AppStrings.titleSummary)!,
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 18, fontFamily: AppFonts.robotoMedium, color: AppColors.blackText),
              ),
              Container(
                padding: EdgeInsets.symmetric(vertical: 15, horizontal: 15),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Row(
                      children: [
                        Expanded(
                          flex: 1,
                          child: Column(
                            children: [
                              Text(
                                AppStrings.getString(AppStrings.timeStart)!,
                                style: TextStyle(
                                  fontSize: 13,
                                  color: AppColors.blackText,
                                ),
                              ),
                              SizedBox(
                                height: 4,
                              ),
                              Text(
                                widget.startDate != null
                                    ? DateFormat('HH:mm - dd/MM/yyyy', 'vi_VN').format(widget.startDate!)
                                    : '',
                                style: TextStyle(
                                  fontSize: 15,
                                  color: AppColors.blackText,
                                  fontFamily: AppFonts.robotoBold,
                                ),
                              )
                            ],
                          ),
                        ),
                        Image.asset(
                          AppImages.ic_arrow_blue,
                          width: 15,
                          height: 15,
                          fit: BoxFit.contain,
                        ),
                        Expanded(
                          flex: 1,
                          child: Column(
                            children: [
                              Text(
                                AppStrings.getString(AppStrings.timeEnd)!,
                                style: TextStyle(
                                  fontSize: 13,
                                  color: AppColors.blackText,
                                ),
                              ),
                              SizedBox(
                                height: 4,
                              ),
                              Text(
                                widget.endDate != null
                                    ? DateFormat('HH:mm - dd/MM/yyyy', 'vi_VN').format(widget.endDate!)
                                    : '',
                                style: TextStyle(
                                  fontSize: 15,
                                  color: AppColors.blackText,
                                  fontFamily: AppFonts.robotoBold,
                                ),
                              )
                            ],
                          ),
                        ),
                      ],
                    ),
                    Divider(
                      color: AppColors.gray3,
                      thickness: 1,
                    ),
                    Row(
                      children: [
                        Expanded(
                          flex: 3,
                          child: Text(
                            AppStrings.getString(AppStrings.summaryType)!.toUpperCase(),
                            style: TextStyle(
                              fontSize: 14,
                              fontFamily: AppFonts.robotoMedium,
                              color: AppColors.greyText,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            AppStrings.getString(AppStrings.summaryTotalTrans)!.toUpperCase(),
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 14,
                              fontFamily: AppFonts.robotoMedium,
                              color: AppColors.greyText,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 3,
                          child: Text(
                            AppStrings.getString(AppStrings.summaryTotalAmount)!.toUpperCase(),
                            textAlign: TextAlign.right,
                            style: TextStyle(
                              fontSize: 14,
                              fontFamily: AppFonts.robotoMedium,
                              color: AppColors.greyText,
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          flex: 3,
                          child: Text(
                            AppStrings.getString(AppStrings.summaryTransCard)!.toUpperCase(),
                            style: TextStyle(
                              fontSize: 15,
                              fontFamily: AppFonts.robotoRegular,
                              color: AppColors.blackText,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            (widget.summaryData?.resultSummaryWorkDay?.cARD?.totalTransaction ?? 0).toString(),
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 16,
                              fontFamily: AppFonts.robotoRegular,
                              color: AppColors.blackText,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 3,
                          child: Text(
                            '${AppUtils.formatCurrency(widget.summaryData?.resultSummaryWorkDay?.cARD?.totalAmount ?? 0)}đ',
                            textAlign: TextAlign.right,
                            style: TextStyle(
                              fontSize: 16,
                              fontFamily: AppFonts.robotoRegular,
                              color: AppColors.blackText,
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 4),
                    Row(
                      children: [
                        Expanded(
                          flex: 3,
                          child: Text(
                            AppStrings.getString(AppStrings.summaryTransQR)!.toUpperCase(),
                            style: TextStyle(
                              fontSize: 15,
                              fontFamily: AppFonts.robotoRegular,
                              color: AppColors.blackText,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            (widget.summaryData?.resultSummaryWorkDay?.qR?.totalTransaction ?? 0).toString(),
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 16,
                              fontFamily: AppFonts.robotoRegular,
                              color: AppColors.blackText,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 3,
                          child: Text(
                            '${AppUtils.formatCurrency(widget.summaryData?.resultSummaryWorkDay?.qR?.totalAmount ?? 0)}đ',
                            textAlign: TextAlign.right,
                            style: TextStyle(
                              fontSize: 16,
                              fontFamily: AppFonts.robotoRegular,
                              color: AppColors.blackText,
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 4),
                    Row(
                      children: [
                        Expanded(
                          flex: 3,
                          child: Text(
                            AppStrings.getString(AppStrings.summaryTransCb)!.toUpperCase(),
                            style: TextStyle(
                              fontSize: 15,
                              fontFamily: AppFonts.robotoRegular,
                              color: AppColors.blackText,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            (widget.summaryData?.resultSummaryWorkDay?.rEFUND?.totalTransaction ?? 0).toString(),
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 16,
                              fontFamily: AppFonts.robotoRegular,
                              color: AppColors.blackText,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 3,
                          child: Text(
                            '${AppUtils.formatCurrency(widget.summaryData?.resultSummaryWorkDay?.rEFUND?.totalAmount ?? 0)}đ',
                            textAlign: TextAlign.right,
                            style: TextStyle(
                              fontSize: 16,
                              fontFamily: AppFonts.robotoRegular,
                              color: AppColors.blackText,
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 16,
                    ),
                    Dash(
                      length: MediaQuery.of(context).size.width - 36,
                      direction: Axis.horizontal,
                      dashLength: 3.5,
                      dashGap: 3.5,
                      dashColor: AppColors.dash,
                      dashThickness: 1,
                    ),
                    SizedBox(
                      height: 16,
                    ),
                    Row(
                      children: [
                        Expanded(
                          flex: 1,
                          child: Text(
                            AppStrings.getString(AppStrings.summaryTotal)!.toUpperCase(),
                            style: TextStyle(
                              fontSize: 16,
                              fontFamily: AppFonts.robotoRegular,
                              color: AppColors.blackText,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child: Text(
                            (widget.summaryData?.resultSummaryWorkDay?.tOTAL?.totalTransaction ?? 0).toString(),
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 16,
                              fontFamily: AppFonts.robotoBold,
                              color: AppColors.blackText,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child: Text(
                            '${AppUtils.formatCurrency(widget.summaryData?.resultSummaryWorkDay?.tOTAL?.totalAmount ?? 0)}đ',
                            textAlign: TextAlign.right,
                            style: TextStyle(
                              fontSize: 18,
                              fontFamily: AppFonts.robotoBold,
                              color: AppColors.primary,
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 10,
                    ),
                    Text(
                      AppStrings.getString(AppStrings.summaryNote)!,
                      textAlign: TextAlign.left,
                      style: TextStyle(fontSize: 13, fontFamily: AppFonts.robotoItalic, color: AppColors.yellow),
                    ),
                    SizedBox(
                      height: 20,
                    ),
                    Row(
                      children: [
                        Expanded(
                          flex: 1,
                          child: CommonButton(
                            onPressed: _onPressClose,
                            color: AppColors.gray3,
                            textColor: AppColors.primary,
                            textSize: 18,
                            title: AppStrings.getString(AppStrings.close),
                            minWidth: MediaQuery.of(context).size.width - 30,
                            elevation: 0,
                            height: 50,
                            fontFamily: AppFonts.robotoMedium,
                          ),
                        ),
                        MyAppController.isSupportPrinter() ? SizedBox(width: 16) : SizedBox(),
                        MyAppController.isSupportPrinter()
                            ? Expanded(
                                flex: 1,
                                child: CommonButton(
                                  onPressed: _onPressPrint,
                                  color: AppColors.primary,
                                  textColor: AppColors.white,
                                  textSize: 18,
                                  title: AppStrings.getString(AppStrings.buttonPrint),
                                  minWidth: MediaQuery.of(context).size.width - 30,
                                  elevation: 0,
                                  height: 50,
                                  fontFamily: AppFonts.robotoMedium,
                                ),
                              )
                            : SizedBox(),
                      ],
                    )
                  ],
                ),
              ),
            ],
          )),
    );
  }
}
