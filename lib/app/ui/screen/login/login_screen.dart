import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/controller/app_controller.dart';
import 'package:mposxs/app/controller/login_controller.dart';
import 'package:mposxs/app/res/font/app_fonts.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/screen/login/footscreen.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/theme/app_dimens.dart';
import 'package:mposxs/app/ui/widget/common_button.dart';
import 'package:mposxs/app/ui/widget/common_screen.dart';
import 'package:mposxs/app/ui/widget/common_text_field.dart';
import 'package:mposxs/app/ui/widget/touchable_widget.dart';

class LoginScreen extends GetView<LoginController> {
  final MyAppController _appController = Get.find<MyAppController>();

  @override
  Widget build(BuildContext context) {
    controller.context = context;
    if (MyAppController.isKozenP12orN4()) {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
    }

    return CommonScreen(
      mainBackgroundColor: AppColors.white,
      child: Container(
        padding: EdgeInsets.only(
          top: MediaQuery.of(context).padding.top,
          bottom: MediaQuery.of(context).padding.bottom,
          left: AppDimens.spaceLarge24,
          right: AppDimens.spaceLarge24,
        ),
        child: Column(children: <Widget>[
          // body: login form
          Expanded(
            flex: 1,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Obx(() => controller.keyboardVisible.value && MyAppController.isKozenP12orN4()
            ? SizedBox.shrink()
                      : SizedBox(height: AppDimens.spaceLarge32,)),
                  // SizedBox(height: AppDimens.spaceLarge32,),
                  Container(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        //=> text Dang nhap
                        Text(
                          '${AppStrings.getString(AppStrings.login)}',
                          style: TextStyle(
                            color: AppColors.blackText,
                            fontSize: 28,
                            fontWeight: FontWeight.w500,
                            fontFamily: AppFonts.robotoMedium,
                          ),
                        ),
                        //=> language
                        GestureDetector(
                          onTap: () => controller.onPressChangeLanguageApp(),
                          child: Image.asset(
                              Get.locale!.languageCode == AppStrings.language_code_vi ? AppImages.ic_language_vi : AppImages.ic_language_en,
                              width: 60),
                        ),
                      ],
                    ),
                  ),
                  // serial number
                  Container(
                    margin: EdgeInsets.only(top: 10),
                    child: Text(
                      (controller.isLoginDevice.value == true)
                          ? '${AppStrings.getString(AppStrings.labelDeviceId)}: ${_appController.serialNumber ?? ''}'
                          : '${AppStrings.getString(AppStrings.deviceId)}: ${_appController.serialNumber ?? ''}',
                      style: TextStyle(
                        color: AppColors.blackBlueText,
                        fontSize: 14,
                      ),
                    ),
                  ),
                  SizedBox(
                    height: AppDimens.spaceSmall,
                  ),

                  buildFormEnter(),
                  // Obx(() => controller.isLoginDevice.value ? buildViewLoginDevice() : buildFormEnter()),
                ],
              ),
            ),
          ),
          // => footer: call support
          Obx(() => controller.keyboardVisible.value ? SizedBox.shrink() : FootScreenLogin()),
        ]),
        // ),
      ),
    );
  }

  buildFormEnter() {
    return Obx(
      () => Column(
        children: [
          //==> input account
          CommonTextField(
            fontSize: 22,
            fontFamily: AppFonts.robotoMedium,
            controller: controller.accController,
            keyboardType: TextInputType.emailAddress,
            hintText: AppStrings.getString(AppStrings.account) ?? '',
            hintTextFontSize: 18,
            maxLength: 40,
            textInputAction: MyAppController.isKozenP12orN4() ? TextInputAction.done : TextInputAction.next,
            suffix: TouchableWidget(
              width: 30,
              height: 20,
              padding: EdgeInsets.all(0),
              child: Image.asset(
                AppImages.icClose,
                color: AppColors.lightGreyText,
                width: 15,
                height: 15,
              ),
              onPressed:(){
                controller.accController.text = '';
              },
            ),
            onSubmit: (user) {
              if (MyAppController.isKozenP12orN4()) {
                controller.onPressLogin();
              }
            },
          ),

            //==> input pass
            controller.isLoginDevice.value ? SizedBox.shrink(): CommonTextField(
              fontSize: 22,
              fontFamily: AppFonts.robotoMedium,
              controller: controller.passController,
              hintText: AppStrings.getString(AppStrings.password) ?? '',
              hintTextFontSize: 18,
              maxLength: 20,
              obscureText: !controller.isShowPassword.value,
              suffix: TouchableWidget(
                width: 30,
                height: 20,
                padding: EdgeInsets.all(0),
                child: Image.asset(
                  AppImages.icEye,
                  width: 19,
                  height: 12,
                ),
                onPressed: controller.onPressShowHidePassword,
              ),
            ),
            // => forgot pass
          // todo fake test
            controller.isLoginDevice.value ? SizedBox.shrink():
            Center(
              child: CommonButton(
                onPressed: controller.onPressForgotPass,
                padding: EdgeInsets.only(left: 2, right: 2, top: 1),
                color: AppColors.transparent,
                textColor: AppColors.primary,
                textSize: 15,
                title: '${AppStrings.getString(AppStrings.titleForgetPassword)}',
              ),
            ),

          //==> button login
          Container(
            margin: EdgeInsets.only(top: AppDimens.spaceMedium),
            child: CommonButton(
              onPressed: controller.onPressLogin,
              title: AppStrings.getString(AppStrings.login) ?? '',
            ),
          ),
        ],
      ),
    );
  }

  buildViewLoginDevice() {
    return Container(
      alignment: Alignment.center,
      padding: EdgeInsets.all(AppDimens.spaceLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(
            height: AppDimens.spaceMedium,
          ),
          Text('Checking data...')
        ],
      ),
    );
  }
}
