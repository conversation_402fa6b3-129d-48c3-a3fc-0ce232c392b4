import 'package:credit_card_type_detector/credit_card_type_detector.dart';
import 'package:flutter/widgets.dart';
import 'package:mposxs/app/controller/app_controller.dart';
import 'package:mposxs/app/data/model/mp_data_login_model.dart';
import 'package:mposxs/app/res/font/app_fonts.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/theme/app_common_style.dart';
import 'package:mposxs/app/ui/widget/base_bottom_sheet.dart';
import 'package:mposxs/app/ui/widget/common_image_network.dart';
import 'package:mposxs/app/ui/widget/common_text_field.dart';
import 'package:mposxs/app/ui/widget/touchable_widget.dart';
import 'package:mposxs/app/util/app_utils.dart';
import 'package:mposxs/app/util/app_validation.dart';

class BottomSheetCheckBank extends StatefulWidget {
  final MPInstallmentInfo selectedBank;
  final Function onPressSkipCheckBin;
  final Function onPressContinueCheckBin;

  const BottomSheetCheckBank(this.selectedBank, this.onPressSkipCheckBin, this.onPressContinueCheckBin, {Key? key})
      : super(key: key);

  @override
  _BottomSheetCheckBankState createState() => _BottomSheetCheckBankState();
}

class _BottomSheetCheckBankState extends State<BottomSheetCheckBank> {
  String _binInput = '';
  CreditCardType? _typeCardCheck;
  TextEditingController _textBinController = TextEditingController();

  _onPressSkipCheckBin() {
    _onPressCloseCheckBin();
    widget.onPressSkipCheckBin();
  }

  _onPressContinueCheckBin() {
    String binCheck = _binInput.replaceAll(' ', '');
    print('press continue: bincheck=$binCheck, typeCardCheck=$_typeCardCheck');
    widget.onPressContinueCheckBin(binCheck, _typeCardCheck);
    _onPressCloseCheckBin();
  }

  _onPressCloseCheckBin() {
    Navigator.pop(context);
    _textBinController.text = '';
    setState(() {
      _binInput = '';
      _typeCardCheck = null;
    });
  }

  _onChangedBinInput(String text) {
    String formatedText = AppUtils.format4CharNumber(text.replaceAll(' ', ''));
    _textBinController.value =
        TextEditingValue(text: formatedText, selection: TextSelection.collapsed(offset: formatedText.length));
    setState(() {
      _binInput = formatedText;
    });
    CreditCardType typeCardCheck = detectCCType(text);
    if (typeCardCheck != _typeCardCheck) {
      print('text= $text - typeCardCheck= $_typeCardCheck - new typeCardCheck$typeCardCheck');
      setState(() {
        _typeCardCheck = typeCardCheck;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return BaseBottomSheet(
      onPressClose: _onPressCloseCheckBin,
      title: AppStrings.getString(AppStrings.installmentListBankCheckTitle),
      child: Stack(
        children: <Widget>[
          Column(
            children: <Widget>[
              Container(
                padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    MyAppController.isKozenP12orN4()?_buildWidgetP12():_buildWidgetNormal(),
                    Padding(
                      padding: const EdgeInsets.only(top: 15),
                      child: Text(
                        AppStrings.getString(AppStrings.installmentListBankCheckCard6Char)!
                            .replaceFirst('%s', (widget.selectedBank.binNumberDigit ?? 6).toString()),
                        style: TextStyle(fontSize: 14, color: AppColors.blueText),
                      ),
                    ),
                    CommonTextField(
                      fontSize: 16,
                      fontFamily: AppFonts.robotoMedium,
                      controller: _textBinController,
                      keyboardType: TextInputType.number,
                      hintTextFontSize: 16,
                      onChanged: _onChangedBinInput,
                      prefixIcon: Container(
                        transform: Matrix4.translationValues(0.0, -2.0, 0.0),
                        margin: EdgeInsets.only(right: 10),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: <Widget>[
                            Container(
                              width: 42,
                              height: 26,
                              decoration: BoxDecoration(
                                color: AppUtils.getCardTypeImage(_typeCardCheck) == null
                                    ? AppColors.transparent
                                    : AppColors.mainBackground,
                                borderRadius: BorderRadius.circular(6),
                              ),
                              child: Image.asset(
                                AppUtils.getCardTypeImage(_typeCardCheck) ?? AppImages.icCardPlaceholder,
                                width: 42,
                                height: 26,
                              ),
                            ),
                          ],
                        ),
                      ),
                      maxLength: (widget.selectedBank.binNumberDigit ?? 6) + 1,
                      autoFocus: true,
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(left: 10, right: 10, bottom: 15),
                child: Row(
                  children: <Widget>[
                    (!['ACB', 'VPBank', 'BKAV', 'VIETCOMBANK'].contains(widget.selectedBank.bankName)
                        ? Expanded(
                            flex: 1,
                            child: TouchableWidget(
                              height: 50,
                              onPressed: _onPressSkipCheckBin,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(6),
                                border: Border.all(width: 1, color: AppColors.tabUnSelected),
                              ),
                              margin: EdgeInsets.only(left: 5, right: 5),
                              child: Text(
                                AppStrings.getString(AppStrings.skip)!,
                                style: buttonTextStyle(color: AppColors.tabUnSelected),
                              ),
                            ),
                          )
                        : SizedBox.shrink()),
                    Expanded(
                      flex: 2,
                      child: TouchableWidget(
                        height: 50,
                        onPressed: isNullEmpty(_binInput.replaceAll(' ', '')) ||
                                _binInput.replaceAll(' ', '').length < (widget.selectedBank.binNumberDigit ?? 6)
                            ? null
                            : _onPressContinueCheckBin,
                        decoration: BoxDecoration(
                          color: isNullEmpty(_binInput.replaceAll(' ', '')) ||
                                  _binInput.replaceAll(' ', '').length < (widget.selectedBank.binNumberDigit ?? 6)
                              ? AppColors.tabUnSelected
                              : AppColors.blue,
                          borderRadius: BorderRadius.circular(6),
                        ),
                        margin: EdgeInsets.only(left: 5, right: 5),
                        child: Text(
                          AppStrings.getString(AppStrings.continueText)!,
                          style: buttonTextStyle(),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  _buildWidgetNormal() {
    return Column(children: [
      Center(
        child: Container(
          width: 60,
          height: 60,
          padding: const EdgeInsets.all(7),
          decoration: BoxDecoration(
            color: AppColors.grayBackground,
            borderRadius: BorderRadius.all(Radius.circular(30)),
          ),
          child: CommonImageNetwork(
            url: widget.selectedBank.logo,
          ),
        ),
      ),
      Center(
        child: Padding(
          padding: const EdgeInsets.only(top: 10, left: 10, right: 10),
          child: Text(
            widget.selectedBank.policy ?? '',
            textAlign: TextAlign.center,
            style:
            TextStyle(fontSize: 15, fontFamily: AppFonts.robotoMedium, color: AppColors.darkGrayText),
          ),
        ),
      )
    ],);
  }

  _buildWidgetP12() {
    return Row(children: [
      Center(
        child: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: AppColors.grayBackground,
            borderRadius: BorderRadius.all(Radius.circular(30)),
          ),
          child: CommonImageNetwork(
            url: widget.selectedBank.logo,
          ),
        ),
      ),
      Center(
        child: Padding(
          padding: const EdgeInsets.only(top: 10, left: 10, right: 10),
          child: Text(
            widget.selectedBank.policy ?? '',
            textAlign: TextAlign.center,
            style:
            TextStyle(fontSize: 15, fontFamily: AppFonts.robotoMedium, color: AppColors.darkGrayText),
          ),
        ),
      )
    ],);
  }
}
