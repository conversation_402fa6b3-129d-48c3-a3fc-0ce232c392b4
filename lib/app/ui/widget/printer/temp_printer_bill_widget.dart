import 'dart:convert';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/res/font/app_fonts.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/theme/app_dimens.dart';
import 'package:mposxs/app/util/app_utils.dart';
import 'package:mposxs/app/util/app_validation.dart';
import 'package:screenshot/screenshot.dart';

class TempPrinterBillWidget extends StatelessWidget {
  final String? printTID;
  final String? printMID;
  final String? printRef;
  final String? printInvoice;
  final String? printBatch;
  final String? printApprove;
  final String? printPan;
  final String? printHolder;
  final String? printType;
  final int? printAmount;
  final String? printDate;
  final String? printTime;
  final String? printSign;
  final String? printMCName;
  final String? printMCAddress;
  final String? printTxid;
  final String? printDes;
  final bool? printIsVoid;
  final bool? isTransSkipSignature;
  final ScreenshotController? screenshotController;

  TempPrinterBillWidget({
    Key? key,
    this.printTID,
    this.printMID,
    this.printRef,
    this.printInvoice,
    this.printBatch,
    this.printApprove,
    this.printPan,
    this.printHolder,
    this.printType,
    this.printAmount,
    this.printDate,
    this.printTime,
    this.printSign,
    this.printMCName,
    this.printMCAddress,
    this.printTxid,
    this.printDes,
    this.printIsVoid,
    this.isTransSkipSignature = false,
    this.screenshotController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SingleChildScrollView(
        scrollDirection: Axis.vertical,
        child: Screenshot(
          controller: screenshotController!,
          child: Container(
            width: AppDimens.billWidth,
            color: AppColors.white,
            child: Column(
              children: [
                //==> LOGO
                Image.asset(AppImages.ic_logo_mpos_print, height: 75, fit: BoxFit.fitHeight,),
                //==> MC NAME + ADDRESS
                Text(
                  ((printMCName ?? '') + '\n' + (printMCAddress ?? '')).toUpperCase(),
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.black,
                    fontFamily: AppFonts.robotoRegular,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 10),
                Text(
                  printIsVoid!?'VOIDED - HUY':'SALE - THANH TOAN',
                  style: TextStyle(
                    fontSize: 28,
                    color: Colors.black,
                    fontFamily: AppFonts.robotoMedium,
                  ),
                  textAlign: TextAlign.center,
                ),
                Text(
                  '------------------------------------------------------',
                  style: TextStyle(
                    fontSize: 24,
                    color: Colors.black,
                    fontFamily: AppFonts.robotoRegular,
                  ),
                  textAlign: TextAlign.center,
                ),
                Row(
                  children: [
                    Text(
                      'Date/Ngay:',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.black,
                        fontFamily: AppFonts.robotoRegular,
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Text(
                        printDate ?? '',
                        style: TextStyle(
                          fontSize: 20,
                          color: Colors.black,
                          fontFamily: AppFonts.robotoRegular,
                        ),
                        textAlign: TextAlign.right,
                      ),
                    )
                  ],
                ),
                SizedBox(height: 5),
                Row(
                  children: [
                    Text(
                      'Time/Gio:',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.black,
                        fontFamily: AppFonts.robotoRegular,
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Text(
                        printTime ?? '',
                        style: TextStyle(
                          fontSize: 20,
                          color: Colors.black,
                          fontFamily: AppFonts.robotoRegular,
                        ),
                        textAlign: TextAlign.right,
                      ),
                    )
                  ],
                ),
                SizedBox(height: 5),
                Row(
                  children: [
                    Text(
                      'TID:',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.black,
                        fontFamily: AppFonts.robotoRegular,
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Text(
                        printTID ?? '',
                        style: TextStyle(
                          fontSize: 20,
                          color: Colors.black,
                          fontFamily: AppFonts.robotoRegular,
                        ),
                        textAlign: TextAlign.right,
                      ),
                    )
                  ],
                ),
                SizedBox(height: 5),
                Row(
                  children: [
                    Text(
                      'MID:',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.black,
                        fontFamily: AppFonts.robotoRegular,
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Text(
                        printMID ?? '',
                        style: TextStyle(
                          fontSize: 20,
                          color: Colors.black,
                          fontFamily: AppFonts.robotoRegular,
                        ),
                        textAlign: TextAlign.right,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 5),
                Row(
                  children: [
                    Text(
                      'Transaction ID:',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.black,
                        fontFamily: AppFonts.robotoRegular,
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Text(
                        printTxid ?? '',
                        style: TextStyle(
                          fontSize: 20,
                          color: Colors.black,
                          fontFamily: AppFonts.robotoRegular,
                        ),
                        textAlign: TextAlign.right,
                      ),
                    ),
                  ],
                ),
                !isNullEmpty(printBatch)
                    ? Padding(
                        padding: const EdgeInsets.only(top: 5),
                        child: Row(
                          children: [
                            Text(
                              'Batch No/ So lo:',
                              style: TextStyle(
                                fontSize: 18,
                                color: Colors.black,
                                fontFamily: AppFonts.robotoRegular,
                              ),
                            ),
                            Expanded(
                              flex: 1,
                              child: Text(
                                printBatch!,
                                style: TextStyle(
                                  fontSize: 20,
                                  color: Colors.black,
                                  fontFamily: AppFonts.robotoRegular,
                                ),
                                textAlign: TextAlign.right,
                              ),
                            ),
                          ],
                        ),
                      )
                    : SizedBox.shrink(),
                !isNullEmpty(printInvoice)
                    ? Padding(
                        padding: const EdgeInsets.only(top: 5),
                        child: Row(
                          children: [
                            Text(
                              'Invoice No:',
                              style: TextStyle(
                                fontSize: 18,
                                color: Colors.black,
                                fontFamily: AppFonts.robotoRegular,
                              ),
                            ),
                            Expanded(
                              flex: 1,
                              child: Text(
                                printInvoice!,
                                style: TextStyle(
                                  fontSize: 20,
                                  color: Colors.black,
                                  fontFamily: AppFonts.robotoRegular,
                                ),
                                textAlign: TextAlign.right,
                              ),
                            ),
                          ],
                        ),
                      )
                    : SizedBox.shrink(),
                !isNullEmpty(printRef)
                    ? Padding(
                        padding: const EdgeInsets.only(top: 5),
                        child: Row(
                          children: [
                            Text(
                              'Ref No:',
                              style: TextStyle(
                                fontSize: 18,
                                color: Colors.black,
                                fontFamily: AppFonts.robotoRegular,
                              ),
                            ),
                            Expanded(
                              flex: 1,
                              child: Text(
                                printRef!,
                                style: TextStyle(
                                  fontSize: 20,
                                  color: Colors.black,
                                  fontFamily: AppFonts.robotoRegular,
                                ),
                                textAlign: TextAlign.right,
                              ),
                            ),
                          ],
                        ),
                      )
                    : SizedBox.shrink(),
                //==> APPROVAL CODE
                !isNullEmpty(printApprove)
                    ? Padding(
                        padding: const EdgeInsets.only(top: 5),
                        child: Row(
                          children: [
                            Text(
                              'Appr Code/MCC:',
                              style: TextStyle(
                                fontSize: 18,
                                color: Colors.black,
                                fontFamily: AppFonts.robotoRegular,
                              ),
                            ),
                            Expanded(
                              flex: 1,
                              child: Text(
                                printApprove!,
                                style: TextStyle(
                                  fontSize: 20,
                                  color: Colors.black,
                                  fontFamily: AppFonts.robotoRegular,
                                ),
                                textAlign: TextAlign.right,
                              ),
                            ),
                          ],
                        ),
                      )
                    : SizedBox.shrink(),
                SizedBox(height: 5),
                //==> PAN
                Row(
                  children: [
                    Text(
                      'Card/So the:',
                      style: TextStyle(
                        fontSize: 20,
                        color: Colors.black,
                        fontFamily: AppFonts.robotoRegular,
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Text(
                        printPan ?? '',
                        style: TextStyle(
                          fontSize: 20,
                          color: Colors.black,
                          fontFamily: AppFonts.robotoRegular,
                        ),
                        textAlign: TextAlign.right,
                      ),
                    )
                  ],
                ),
                SizedBox(height: 5),
                //==> CARD HOLDER
                Row(
                  children: [
                    Text(
                      'Name/Ten:',
                      style: TextStyle(
                        fontSize: 20,
                        color: Colors.black,
                        fontFamily: AppFonts.robotoRegular,
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Text(
                        printHolder ?? '',
                        style: TextStyle(
                          fontSize: 20,
                          color: Colors.black,
                          fontFamily: AppFonts.robotoRegular,
                        ),
                        textAlign: TextAlign.right,
                      ),
                    )
                  ],
                ),
                SizedBox(height: 5),
                //==> CARD TYPE
                Row(
                  children: [
                    Text(
                      'Card type/Loai:',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.black,
                        fontFamily: AppFonts.robotoRegular,
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Text(
                        printType ?? '',
                        style: TextStyle(
                          fontSize: 20,
                          color: Colors.black,
                          fontFamily: AppFonts.robotoRegular,
                        ),
                        textAlign: TextAlign.right,
                      ),
                    )
                  ],
                ),
                SizedBox(height: 5),
                //==> EXPIRE DATE
                // Row(
                //   children: [
                //     Text(
                //       'EXP DATE/NGAY H.HAN:',
                //       style: TextStyle(
                //         fontSize: 18,
                //         color: Colors.black,
                //         fontFamily: AppFonts.robotoRegular,
                //       ),
                //     ),
                //     Expanded(
                //       flex: 1,
                //       child: Text(
                //         'XX/XX',
                //         style: TextStyle(
                //           fontSize: 20,
                //           color: Colors.black,
                //           fontFamily: AppFonts.robotoRegular,
                //         ),
                //         textAlign: TextAlign.right,
                //       ),
                //     )
                //   ],
                // ),
                //==> DESCRIPTION
                !isNullEmpty(printDes)
                    ? Column(
                        children: [
                          Row(
                            children: [
                              Text(
                                'DES/GHICHU:',
                                style: TextStyle(
                                  fontSize: 18,
                                  color: Colors.black,
                                  fontFamily: AppFonts.robotoRegular,
                                ),
                              ),
                              Expanded(
                                flex: 1,
                                child: Text(
                                  printDes ?? '',
                                  style: TextStyle(
                                    fontSize: 20,
                                    color: Colors.black,
                                    fontFamily: AppFonts.robotoRegular,
                                  ),
                                  textAlign: TextAlign.right,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 5),
                        ],
                      )
                    : SizedBox.shrink(),
                Text(
                  '------------------------------------------------------',
                  style: TextStyle(
                    fontSize: 24,
                    color: Colors.black,
                    fontFamily: AppFonts.robotoRegular,
                  ),
                  textAlign: TextAlign.center,
                ),
                // SizedBox(height: 5),
                //==> AMOUNT
                Row(
                  children: [
                    Text(
                      'TOTAL/TONG:',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.black,
                        fontFamily: AppFonts.robotoRegular,
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Text(
                        '${AppUtils.formatCurrency(printAmount ?? 0)} VND',
                        style: TextStyle(
                          fontSize: 24,
                          color: Colors.black,
                          fontFamily: AppFonts.robotoMedium,
                        ),
                        textAlign: TextAlign.right,
                      ),
                    ),
                  ],
                ),
                // SizedBox(height: 10),
                //==>  AREA SIGNATURE (SKIP OR SIGNATURE)
                isTransSkipSignature!
                    ? Text(
                        'No signature required /\nKhong yeu cau khach hang ky'.capitalize!,
                        style: TextStyle(
                          fontSize: 18,
                          color: Colors.black,
                          fontFamily: AppFonts.robotoRegular,
                        ),
                        textAlign: TextAlign.center,
                      )
                    : Stack(
                        children: [
                          //==> signature
                          Container(
                            width: 384,
                            child: isNullEmpty(printSign) ? SizedBox.shrink() :
                              Image.memory(base64Decode(printSign!.replaceAll("\n", "")), width: 100, height: 100, fit: BoxFit.scaleDown),
                          ),
                          Align(
                            alignment: Alignment.topLeft,
                            child: Text('SIGN/CHU KY', style: TextStyle(fontSize: 18, color: Colors.black, fontFamily: AppFonts.robotoRegular),),
                          ),
                          //==> text VOIDED
                          printIsVoid == true
                              ? Align(
                                  alignment: Alignment.centerRight,
                                  child: Transform.rotate(
                                    angle: -pi / 10,
                                    child: Container(
                                      margin: const EdgeInsets.only(top: 12, right: 12),
                                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                      decoration: BoxDecoration(
                                        border: Border.all(
                                          color: Colors.black,
                                          width: 2,
                                        ),
                                        borderRadius: BorderRadius.circular(6.0),
                                      ),
                                      child: Text(
                                        'ĐÃ HUỶ',
                                        style: TextStyle(
                                          fontFamily: AppFonts.robotoMedium,
                                          fontSize: 24,
                                          color: Colors.black,
                                        ),
                                      ),
                                    ),
                                  ),
                                )
                              : SizedBox.shrink(),
                        ],
                      ),
                SizedBox(height: isTransSkipSignature!?0:10),
                isTransSkipSignature!
                    ? SizedBox.shrink()
                    : Text(
                        printHolder ?? '',
                        style: TextStyle(
                          fontSize: 20,
                          color: Colors.black,
                          fontFamily: AppFonts.robotoRegular,
                        ),
                        textAlign: TextAlign.center,
                      ),
                SizedBox(height: 15),
                Text(
                  'NO REFUND/KHONG HOAN TIEN\n*** CUSTOMER COPY/ LIEN DANH CHO KHACH HANG***',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black,
                    fontFamily: AppFonts.robotoRegular,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
