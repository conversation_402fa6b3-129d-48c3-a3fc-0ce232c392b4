import 'package:flutter/material.dart';

enum TrxType {
  NonPayment,
  NormalPayment,
  ServicePayment,
  MotoPayment
}

enum PayType {
  PayTypeNon,
  PayTypeCash,
  PayTypeQR,
  PayTypeCard,
}

enum PrintState {
  PrintStateNon,
  PrintStatePrinted,
  PrintStatePrinting,
  PrintStateFail,
}

class Configuration {
  static const String logger_Storage = 'LOG_STORAGE';
  static const String LOGGER_TYPE_ACTION = 'ACTION';
  static const String LOGGER_TYPE_REQUEST = 'REQUEST';
  static const String LOGGER_TYPE_RESPONSE = 'RESPONSE';

  static const mainBackground = Color.fromRGBO(242, 242, 242, 1);
  static const hintMainColor = Color.fromRGBO(128, 136, 144, 1);

  static Color blackMainColour = Color.fromRGBO(64, 64, 64, 1);
  static Color orangerMainColour = Color.fromRGBO(255, 164, 18, 1.0);
  static Color orangerLightMainColour = Color.fromRGBO(255, 164, 18, 0.3);
  static Color blueMainColour = Color.fromRGBO(53, 152, 219, 1.0);
  static Color greenMainColour = Color.fromRGBO(111, 166, 55, 1);
  static Color greenLightMainColour = Color.fromRGBO(241, 246, 235, 1);

  static Color mainColour = Color.fromRGBO(207, 58, 63, 1.0);

  static const int rejectOrderCounting = 5;
  static const double paddingMain = 15.0;
  static const double mainCornerRadius = 15.0;
  static const double mainButtunCornerRadius = 5.0;

  static const double fontTitlePolicy = 12;
  static const double fontNormal = 14;
  static const double fontButton = 16;
  static const double fontTitle = 20;

  static const double heightMainButton = 55;
  static const double heightSimpleButton = 40;

  static int DO_SERVICE_SUCCESS = 1000;
}

class Dimens{
  static const double iconCloseWidth = 20;
}
