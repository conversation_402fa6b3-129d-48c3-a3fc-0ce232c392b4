import 'dart:convert';

import 'package:flutter/services.dart';
typedef FunctionValue = void Function(MethodCall call);

// ignore: camel_case_types
class NativeBridge{
  static final String channelName = 'Lister_Native_Channel';
  static final String actionGetDeviceInfor = 'ActionGetDeviceInfor';

  static final MethodChannel methodChannel = MethodChannel(channelName);

  FunctionValue? callbackFunc;

  Future<String> callNativeMethod(String actionName, Map data) async {
    try {
      String result = await methodChannel.invokeMethod(actionName, data);
      return result;
    } on PlatformException catch (e) {
      print('Can not call func ${e.message}');
      return "";
    }
  }


  Future<String> nativeGetDeviceInfor() async {
    try {
      final String result = await methodChannel.invokeMethod('ActionGetDeviceInfor');
      return result;
    } on PlatformException catch (e) {
      print('Can not call func ${e.message}');
      return '';
    }
  }

  Future<void> setDisplayNavbar(bool value) async {
    print('set display navbar: ${value}');
    try {
      Map params = {
        'isDisplayNavbar': value,
      };
      await methodChannel.invokeMethod('ActionDisplayNavbar', params);
    } on PlatformException catch (e) {
      print('${e.toString()}');
    }
  }

  Future<void> setStatusBar(bool value) async {
    print('set disable status bar: ${value}');
    try {
      Map params = {
        'isShowStatusBar': value,
      };
      await methodChannel.invokeMethod('ActionShowStatusBar', params);
    } on PlatformException catch (e) {
      print('${e.toString()}');
    }
  }

}