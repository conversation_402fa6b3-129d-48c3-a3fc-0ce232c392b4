import 'dart:convert';
import 'dart:developer' as dev;
import 'dart:io';

import 'package:intl/intl.dart';
import 'package:mposxs/app/data/model/error_response.dart';
import 'package:mposxs/app/util/api_constant.dart';
import 'package:mposxs/build_constants.dart';
import 'package:package_info/package_info.dart';

import '../../util/app_validation.dart';
import '../model/base_response.dart';
import 'local_storage.dart';


class LoggerMp {
  // String? domainPushLog = BuildConstants.serverAPI + ApiConstant.urlApi;
  String? domainPushLog = "https://mpos.vn/mpos-api/api";

  static final _shareInstance = LoggerMp._instance();

  LoggerMp._instance();

  factory LoggerMp() => _shareInstance;
  static LoggerMp getInstance(){
    return _shareInstance;
  }

  static const String LOGGER_TYPE_ACTION    = 'ACT';
  static const String LOGGER_TYPE_REQUEST   = 'REQ';
  static const String LOGGER_TYPE_RESPONSE  = 'RES';
  static const String LOGGER_TYPE_ERROR     = 'ERR';


  StringBuffer logData = StringBuffer();
  String _userLogin = '';
  String? _deviceSerialNo = '';

  String dateTemp = '';

  String formatTime = 'HH:mm:ss dd/MM/yyyy';
  String formatDate = 'dd/MM/yyyy';


  void setUserAndDeviceInfo({String userLogin = '', String? deviceNo = ''}){
    _userLogin = userLogin;
    if(!isNullEmpty(deviceNo)) {
      _deviceSerialNo = deviceNo;
    }
  }

  void writeLogErr(String log, BaseResponse response) {
    writeLog('$log |code=${response.code ?? ''}, msg=${response.message}');
  }

  void writeLogErrFunc(String log, Object e) {
    writeLog('$log |msg=${e.toString()}');
  }
  void writeLog(String log, {String actionName = LOGGER_TYPE_ACTION}) async{
    // if (kDebugMode) {
    //   print(inputValue);
    //   return;
    // }
    DateTime now = DateTime.now();
    String timeLog = DateFormat(formatTime).format(now);
    String writeValue;

    try {
      if(logData.isEmpty && (!isNullEmpty(_userLogin) || !isNullEmpty(_deviceSerialNo))) {
        logData.writeln(await _buildFirstLineLog());
      }

      if(isNullEmpty(dateTemp)) {
        dev.log(' time case 1');
        dateTemp = DateFormat(formatDate).format(now);
        logData.writeln('**$dateTemp');
      } else if(timeLog.contains(dateTemp)){
        dev.log(' time case 2');
        timeLog = timeLog.substring(0, 8);
      } else {
        dev.log(' time case 3');
        dateTemp = DateFormat(formatDate).format(now);
        logData.writeln('**$dateTemp');
        timeLog = timeLog.substring(0, 8);
      }


      writeValue = '$timeLog | $actionName | $log';
    } catch (e) {
      writeValue =
          '$timeLog | $actionName | $log \nError WriteLog: ${e.toString()}';
    }
    if (!isNullEmpty(writeValue)) {
      dev.log('write Log: $writeValue');
      logData.writeln(writeValue);
      // listLog.add(writeValue);
    }
  }

  // Future<void> saveLogToStorage() async {
  //   if(logData.isNotEmpty) {
  //     await LocalStorage().saveDataToLast(LocalStorage.KEY_LOGGER, logData.toString());
  //     logData.clear();
  //   }
  // }

  Future<void> saveLogToStorage() async {
    if (logData.isNotEmpty) {
      String logSend = logData.toString();
      await LocalStorage().saveDataToLast(LocalStorage.KEY_LOGGER, logSend);
      if (logData.length > logSend.length) {
        String newLog = logData.toString().substring(logSend.length);
        logData.clear();
        logData.write(newLog);
      } else {
        logData.clear();
      }
    }
  }


  Future<String> getLogFromStorage() async {
    return await LocalStorage().getData(LocalStorage.KEY_LOGGER, '');
  }

  clearLogFromStorage(){
    LocalStorage().clear(LocalStorage.KEY_LOGGER);
  }

  void processPushLog() async {
    // if (kDebugMode){
    //   print("PUSH LOG TEST");
    //   return;
    // }
    await saveLogToStorage();
    String logDataSaved = await getLogFromStorage();
    if (logDataSaved.isNotEmpty) {
      String firstLineLog = await _buildFirstLineLog();
      if(!logDataSaved.contains(firstLineLog)) {
        logDataSaved = '$firstLineLog\n$logDataSaved' ;
      }
      // dev.log('push log to host: $logDataSaved');

      var jsonResponse = await _pushLogToServer(logDataSaved);
      try {
        Map<String, dynamic>? response = jsonDecode(jsonResponse);
        ErrorResponse errorResponse = ErrorResponse.parserData(response);
        // dev.log('push log to host: result=${errorResponse.code}');

        if (errorResponse.code == 1000) {
          clearLogFromStorage();
        }
      }catch (e) {
        dev.log('push log to host: err=$e');
      }
    }
    else{
      dev.log('push log to host: no log');

    }
  }

  Future<String> _pushLogToServer(String detailLog) async {
    _getUserLogin();
    Map<String, dynamic> dataObject = {};
    dataObject['serviceName'] = 'SAVE_LOG_ACTION';
    dataObject['description'] = detailLog;
    dataObject['os'] = Platform.operatingSystem;
    dataObject['muid'] = _userLogin;
    // print('Platform.operatingSystem: ${Platform.operatingSystem}');
    String jsonObject = jsonEncode(dataObject);
    String strResponse = '';

    try {
      HttpClient client = HttpClient();
      client.badCertificateCallback = ((X509Certificate cert, String host, int port) => true);
      dev.log('domainPushLog=$domainPushLog');
      Uri uri = Uri.parse(domainPushLog!);
      HttpClientRequest request = await client.postUrl(uri);
      request.headers.set('content-type', 'application/json');
      request.add(utf8.encode(jsonObject));
      HttpClientResponse response = await request.close();
      if (response.statusCode == 200){
        strResponse = await response.transform(utf8.decoder).join();
      }
    }catch (e) {
      dev.log('_pushLogToServer err =$domainPushLog - ${e.toString()}');
    }
    return strResponse;
  }

  void clearLog() async {
    logData.clear();
    // listLog.clear();
  }

  Future<String> _buildFirstLineLog() async {
    String infoApp = await getInfoVersion();
    _getUserLogin();
    return 'userId: $_userLogin | SN: $_deviceSerialNo | app: MposLite $infoApp';
  }

  _getUserLogin() async {
    if(isNullEmpty(_userLogin)) {
      _userLogin = await LocalStorage().getUserName();
    }
  }

  Future<String> getInfoVersion() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    return '| vc: ${packageInfo.buildNumber} | pn: ${packageInfo.packageName}';
  }
}



