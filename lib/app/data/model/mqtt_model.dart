

class MqttModel {
  final String? emqxUsername;
  final String? emqxPass;
  final String? emqxUrl;
  String host = '';
  int port = 0;

  MqttModel({
    this.emqxUsername,
    this.emqxPass,
    this.emqxUrl,
  });

  parseUriToHostPort() {
    if (emqxUrl != null) {
      var arrUri = emqxUrl!.split(':');
      if (arrUri.length > 1) {
        port = int.parse(arrUri[arrUri.length - 1]);
        host = emqxUrl!.substring(0, emqxUrl!.lastIndexOf(":"));
      }
    }
  }


  MqttModel.fromJson(Map<String, dynamic> json) :
        emqxUsername = json['emqxUsername'] as String?,
        emqxPass = json['emqxPass'] as String?,
        emqxUrl = json['emqxUrl'] as String?,
        host = json['host'] as String,
        port = json['port']
  ;

  Map<String, dynamic> toJson() => {
    'emqxUsername': emqxUsername,
    'emqxPass': emqxPass,
    'emqxUrl': emqxUrl,
    'host': host,
    'port': port,
  };
}