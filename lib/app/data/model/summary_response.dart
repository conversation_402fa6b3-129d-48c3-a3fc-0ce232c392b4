import 'package:mposxs/app/data/model/base_response.dart';

class SummaryResponse implements BaseResponseData {
  int? pageIndex;
  int? pageSize;
  Error? error;
  TxidMap? txidMap;
  String? merchantName;
  int? checkFeeChange;
  int? checkFeeTrans;
  int? checkFeeInstallment;
  ResultSummaryWorkDay? resultSummaryWorkDay;
  String? base64PdfReceipt;
  String? base64ImageReceipt;
  String? logoPrintBank;
  String? logoPrintMpos;

  SummaryResponse({
    this.pageIndex,
    this.pageSize,
    this.error,
    this.txidMap,
    this.merchantName,
    this.checkFeeChange,
    this.checkFeeTrans,
    this.checkFeeInstallment,
    this.resultSummaryWorkDay,
    this.base64PdfReceipt,
    this.base64ImageReceipt,
    this.logoPrintBank,
    this.logoPrintMpos,
  });

  SummaryResponse.fromJson(Map<String, dynamic> json) {
    pageIndex = json['pageIndex'];
    pageSize = json['pageSize'];
    error = json['error'] != null ? new Error.fromJson(json['error']) : null;
    txidMap = json['txidMap'] != null ? new TxidMap.fromJson(json['txidMap']) : null;
    merchantName = json['merchantName'];
    checkFeeChange = json['checkFeeChange'];
    checkFeeTrans = json['checkFeeTrans'];
    checkFeeInstallment = json['checkFeeInstallment'];
    resultSummaryWorkDay =
    json['resultSummaryWorkDay'] != null ? new ResultSummaryWorkDay.fromJson(json['resultSummaryWorkDay']) : null;
    base64PdfReceipt = json['base64PdfReceipt'];
    base64ImageReceipt = json['base64ImageReceipt'];
    logoPrintBank = json['logoPrintBank'];
    logoPrintMpos = json['logoPrintMpos'];
  }

  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['pageIndex'] = this.pageIndex;
    data['pageSize'] = this.pageSize;
    if (this.error != null) {
      data['error'] = this.error!.toJson();
    }
    if (this.txidMap != null) {
      data['txidMap'] = this.txidMap!.toJson();
    }
    data['merchantName'] = this.merchantName;
    data['checkFeeChange'] = this.checkFeeChange;
    data['checkFeeTrans'] = this.checkFeeTrans;
    data['checkFeeInstallment'] = this.checkFeeInstallment;
    if (this.resultSummaryWorkDay != null) {
      data['resultSummaryWorkDay'] = this.resultSummaryWorkDay!.toJson();
    }
    data['base64PdfReceipt'] = this.base64PdfReceipt;
    data['base64ImageReceipt'] = this.base64ImageReceipt;
    data['logoPrintBank'] = this.logoPrintBank;
    data['logoPrintMpos'] = this.logoPrintMpos;
    return data;
  }
}

class Error {
  int? code;
  String? message;
  String? messageEn;

  Error({this.code, this.message, this.messageEn});

  Error.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    message = json['message'];
    messageEn = json['messageEn'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['code'] = this.code;
    data['message'] = this.message;
    data['messageEn'] = this.messageEn;
    return data;
  }
}

class TxidMap {
  String? temp;

  TxidMap({this.temp});

  TxidMap.fromJson(Map<String, dynamic> json) {
    temp = json['temp'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['temp'] = this.temp;
    return data;
  }
}

class ResultSummaryWorkDay {
  CARD? cARD;
  CARD? tOTAL;
  CARD? qR;
  CARD? rEFUND;

  ResultSummaryWorkDay({this.cARD, this.tOTAL, this.qR, this.rEFUND});

  ResultSummaryWorkDay.fromJson(Map<String, dynamic> json) {
    cARD = json['CARD'] != null ? new CARD.fromJson(json['CARD']) : null;
    tOTAL = json['TOTAL'] != null ? new CARD.fromJson(json['TOTAL']) : null;
    qR = json['QR'] != null ? new CARD.fromJson(json['QR']) : null;
    rEFUND = json['REFUND'] != null ? new CARD.fromJson(json['REFUND']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.cARD != null) {
      data['CARD'] = this.cARD!.toJson();
    }
    if (this.tOTAL != null) {
      data['TOTAL'] = this.tOTAL!.toJson();
    }
    if (this.qR != null) {
      data['QR'] = this.qR!.toJson();
    }
    if (this.rEFUND != null) {
      data['REFUND'] = this.rEFUND!.toJson();
    }
    return data;
  }
}

class CARD {
  int? totalTransaction;
  int? totalAmount;

  CARD({this.totalTransaction, this.totalAmount});

  CARD.fromJson(Map<String, dynamic> json) {
    totalTransaction = json['totalTransaction'];
    totalAmount = json['totalAmount'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['totalTransaction'] = this.totalTransaction;
    data['totalAmount'] = this.totalAmount;
    return data;
  }
}
