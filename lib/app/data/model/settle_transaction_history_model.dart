class SettleTransactionHistoryModel {
  bool? canSettle;
  bool? canVoid;
  List<PaymentItems>? paymentItems;

  SettleTransactionHistoryModel(
      {this.canSettle, this.canVoid, this.paymentItems});

  SettleTransactionHistoryModel.fromJson(Map<String, dynamic> json) {
    canSettle = json['canSettle'];
    canVoid = json['canVoid'];
    if (json['paymentItems'] != null) {
      paymentItems = [];
      json['paymentItems'].forEach((v) {
        paymentItems!.add(new PaymentItems.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['canSettle'] = this.canSettle;
    data['canVoid'] = this.canVoid;
    if (this.paymentItems != null) {
      data['paymentItems'] = this.paymentItems!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class PaymentItems {
  String? accquirer;
  String? mAmount;
  String? mApprovalCode;
  String? mCurency;
  String? mDate;
  String? mId;
  String? mInvoiceNo;
  String? mName;
  String? mNumber;
  int? mStatus;
  String? mThumb;
  String? mTime;
  int? mType;
  bool? mVoided;
  String? transactionRequestID;
  String? trxType;
  String? udid;

  PaymentItems(
      {this.accquirer,
        this.mAmount,
        this.mApprovalCode,
        this.mCurency,
        this.mDate,
        this.mId,
        this.mInvoiceNo,
        this.mName,
        this.mNumber,
        this.mStatus,
        this.mThumb,
        this.mTime,
        this.mType,
        this.mVoided,
        this.transactionRequestID,
        this.trxType,
        this.udid});

  PaymentItems.fromJson(Map<String, dynamic> json) {
    accquirer = json['accquirer'];
    mAmount = json['mAmount'];
    mApprovalCode = json['mApprovalCode'];
    mCurency = json['mCurency'];
    mDate = json['mDate'];
    mId = json['mId'];
    mInvoiceNo = json['mInvoiceNo'];
    mName = json['mName'];
    mNumber = json['mNumber'];
    mStatus = json['mStatus'];
    mThumb = json['mThumb'];
    mTime = json['mTime'];
    mType = json['mType'];
    mVoided = json['mVoided'];
    transactionRequestID = json['transactionRequestID'];
    trxType = json['trxType'];
    udid = json['udid'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['accquirer'] = this.accquirer;
    data['mAmount'] = this.mAmount;
    data['mApprovalCode'] = this.mApprovalCode;
    data['mCurency'] = this.mCurency;
    data['mDate'] = this.mDate;
    data['mId'] = this.mId;
    data['mInvoiceNo'] = this.mInvoiceNo;
    data['mName'] = this.mName;
    data['mNumber'] = this.mNumber;
    data['mStatus'] = this.mStatus;
    data['mThumb'] = this.mThumb;
    data['mTime'] = this.mTime;
    data['mType'] = this.mType;
    data['mVoided'] = this.mVoided;
    data['transactionRequestID'] = this.transactionRequestID;
    data['trxType'] = this.trxType;
    data['udid'] = this.udid;
    return data;
  }
}
