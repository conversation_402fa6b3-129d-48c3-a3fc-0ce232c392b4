import 'dart:async';
import 'dart:convert';
import 'dart:developer' as dev;
import 'dart:typed_data';

import 'package:cashiermodule/Class/SpeakerHandler.dart';
import 'package:cashiermodule/Class/TextToSpeak.dart';
import 'package:cashiermodule/model_instance/mqtt_client.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mposxs/app/data/model/base_response.dart';
import 'package:mposxs/app/data/model/data_session.dart';
import 'package:mposxs/app/data/model/mp_data_login_model.dart';
import 'package:mposxs/app/data/model/mqtt_model.dart';
import 'package:mposxs/app/data/model/type_payment.dart';
import 'package:mposxs/app/data/provider/api_client.dart';
import 'package:mposxs/app/data/provider/local_storage.dart';
import 'package:mposxs/app/data/provider/logger.dart';
import 'package:mposxs/app/data/provider/native_bridge.dart';
import 'package:mposxs/app/ui/screen/settings/bottomsheet_setting_language.dart';
import 'package:mposxs/app/ui/widget/printer/temp_printer_bill_widget.dart';
import 'package:mposxs/app/util/api_constant.dart';
import 'package:mposxs/app/util/app_utils.dart';
import 'package:mposxs/app/util/app_validation.dart';
import 'package:mposxs/app/util/mpos_constant.dart';
// import 'package:mqtt_client/mqtt_client.dart';
import 'package:screenshot/screenshot.dart';

import '../data/model/payment_method.dart';
import '../util/constants.dart';
import 'mqtt_controller.dart';
import 'package:cashiermodule/model_instance/app_configuration.dart' as cashierModule;

class MyAppController extends SuperController {
  RxBool loading = false.obs;
  RxString messageLoading = ''.obs;
  String loginAccount = '';
  String loginPassword = '';
  String? serialNumber = '';
  String deviceIdentifier = '';
  String? tokenCountNoti = ''; // token macq, use for get count notication
  // String? appVersion = '';
  String? buildNumber = '';
  static int? readerType = -1;
  static String deviceModel = '';
  MPDataLoginModel? userInfo;
  // UserInfo? userInfo;
  PaymentInfoSession? paymentInfoSession;
  InstallmentInfoSession? installmentInfoSession;
  Timer? _timer;
  bool enableQuickDraw = false;
  bool isShowAdvancedSettings = false; // enable setting: lock status Bar, navBar, auto dismiss other dialog
  MqttModel? mqttModel;

  RxList<TypePayment> listTypePayment = RxList.empty();

  LocalStorage localStorage = LocalStorage();

  List<String> listOrderPaid = [];

  PaymentMethod? paymentMethod;
  RxBool isShowVietQr = true.obs;
  RxList<StaticQRType> listQr = [StaticQRType.VietQR].obs;
  // late var emqxConnect;

  void initListQr() {
    listQr.clear();
    if (isShowVietQr.value) {
      if (paymentMethod!.vietQrActive) {
        listQr.add(StaticQRType.VietQR);
      }
      listQr.add(StaticQRType.AGPayQR);
    } else {
      listQr.add(StaticQRType.AGPayQR);
      if (paymentMethod!.vietQrActive) {
        listQr.add(StaticQRType.VietQR);
      }
    }
    listQr.refresh();
  }
  void isShowDefaultVietQr() async {
    isShowVietQr.value = await LocalStorage().getData(LocalStorage.isShowDefaultVietQr, true);
    initListQr();
  }
  void saveShowDefaultVietQr(bool value) async {
    isShowVietQr.value = value;
    initListQr();
    await LocalStorage().saveData(LocalStorage.isShowDefaultVietQr, value);
  }

  static bool isSupportPrinter(){
    AppUtils.log('isSupportPrinter: $readerType -deviceModel=$deviceModel');
    // sp01
    if (readerType == MposConstant.READER_SP01) {
      return true;
    }
    // smartpos Pro
    else if (readerType == MposConstant.READER_SP02 && (deviceModel == 'P8' || deviceModel == 'Smart_Prime' || deviceModel == 'P8 Neo')){
      return true;
    }
    // N31
    else if (readerType == MposConstant.READER_SP02 && (deviceModel == 'N31' || deviceModel == 'P12')) {
      return true;
    }
    else if (isKozenDevices()){
      return true;
    }
    return false;
  }

  static bool isKozenDevices(){
    return isKozenP8() || isKozenMposPro() || isKozenP12orN4() || isKozenP10();
  }


  static bool isKozenP8(){
    if (readerType == MposConstant.READER_SP02 && (deviceModel == 'P8' || deviceModel == 'Smart_Prime')){
      return true;
    }
    return false;
  }
  static bool isKozenP10(){
    if (readerType == MposConstant.READER_SP02 && (deviceModel == 'P10' || deviceModel == 'SP06' || deviceModel == 'N33')){
      return true;
    }
    return false;
  }

  static bool isKozenMposPro(){
    if (readerType == MposConstant.READER_SP02 && (deviceModel == 'N4' || deviceModel == 'mPOS PRO')){
      return true;
    }
    return false;
  }

  static bool isKozenP12orN4(){
    if (readerType == MposConstant.READER_SP02 && (deviceModel == 'P12' || deviceModel == 'N31' || isKozenMposPro())){
      return true;
    }
    return false;
  }

  void saveCachePayments(List<TypePayment> listTypePayment){
    this.listTypePayment.value = listTypePayment;
    String typePayments = jsonEncode(listTypePayment.map((e) => e.toJson()).toList());
    AppUtils.log('saveCachePayments typePayments: $typePayments');
    localStorage.saveData(LocalStorage.KEY_PAYMENT_HOME, typePayments);
  }

  // _checkCacheLogError() async {
  //   LoggerMp().processPushLog();
  // }

  showLoading({bool autoHide = true, String? message}) {
    loading.value = true;
    messageLoading.value = message ?? '';
    if(autoHide) {
      _timer = Timer.periodic(Duration(seconds: 40), (timer) {
        if (loading.value == true) {
          hideLoading();
        }
      });
    }
  }

  hideLoading() {
    loading.value = false;
    if (_timer != null) {
      _timer!.cancel();
    }
  }

  @override
  void onDetached() {}

  @override
  void onInactive() {}

  @override
  void onPaused() {
    LoggerMp().saveLogToStorage();
  }

  @override
  void onResumed() {
    // _checkCacheLogError();
  }

  void fetchToken() async {
    if (deviceIdentifier.isEmpty) {
      // LoggerMp().writeLog('Token');
      appendLogReq('Token');
      try {
        final fcmToken = await FirebaseMessaging.instance.getToken();
        deviceIdentifier = fcmToken ?? '';
      } catch (e) {
        appendLogRes('fetch err: ${e.toString()}');
      }
    }
  }

  changeLanguageApp(BuildContext? context) async {
    String currentLanguageApp = await localStorage.getLanguageApp();
    if (currentLanguageApp.isNotEmpty) {
      List<ObjSettingSelect> listLanguage = [
        ObjSettingSelect('Tiếng Việt', 'vi'),
        ObjSettingSelect('English', 'en'),
      ];

      showModalBottomSheet(
        context: context!,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(6)),
        ),
        builder: (context) => SettingLanguage(
          onSelectItem: (settingSelected){
            AppUtils.log('select item ${settingSelected.value}');
            handlerSelectLanguage(settingSelected.value);
          },
          listSetting: listLanguage,
          valueSelected: ObjSettingSelect('', currentLanguageApp),
        ),
        isScrollControlled: true,
        isDismissible: false,
      );
    }
  }

  handlerSelectLanguage(String? languageSelected){
    Get.back();
    Get.updateLocale(languageSelected == 'vi' ?Locale('vi', 'VN'):Locale('en', 'US') );
    localStorage.setLanguageApp(languageSelected);
    NativeBridge.getInstance().nativeSetLanguage(languageSelected);
  }

  printTransactionReceipt(BuildContext context, String transId, bool isCancel,
      ScreenshotController screenshotController, Rx<Widget> tempPrinterWidget) async {
    showLoading();
    NativeResponseModel nativeResponseModelTID = await NativeBridge.getInstance().nativeGetTID();
    NativeResponseModel nativeResponseModelMID = await NativeBridge.getInstance().nativeGetMID();
    var dataPrint;
    try {
      final storage = FlutterSecureStorage();
      String value = await storage.read(key: transId) ?? '';
      if (!isNullEmpty(value)) {
        dataPrint = json.decode(value);
      }
    } on Exception {}
    hideLoading();
    // print offline from cache
    if (dataPrint != null) {
      _printReceiptLocal(dataPrint, tempPrinterWidget, nativeResponseModelTID, nativeResponseModelMID, transId, isCancel,
          screenshotController);
    } else {
      var result = await _printReceiptFromMpos(context, tempPrinterWidget, nativeResponseModelTID, nativeResponseModelMID,
          transId, '', isCancel, screenshotController);
      if(!result) {
        _printReceiptFromMacqOrBank(context, transId, '');
      }
    }
  }

  _printReceiptLocal(dataPrint, Rx<Widget> tempPrinterWidget, NativeResponseModel<dynamic> nativeResponseModelTID,
      NativeResponseModel<dynamic> nativeResponseModelMID, String transId, bool isCancel, ScreenshotController screenshotController) {
    int transactionDate = int.tryParse(dataPrint['transactionDate']) ?? 0;
    DateTime dateTime;
    if (transactionDate == 0) {
      dateTime = DateTime.now();
    } else {
      dateTime = DateTime.fromMillisecondsSinceEpoch((transactionDate), isUtc: true).add(Duration(hours: 7));
    }
    showLoading();
    tempPrinterWidget.value = Container(
      child: TempPrinterBillWidget(
        printTID: nativeResponseModelTID.data,
        printMID: nativeResponseModelMID.data,
        printRef: dataPrint['refNo'],
        printInvoice: dataPrint['invoiceNo'],
        printBatch: dataPrint['batchNo'],
        printApprove: dataPrint['approvalCode'],
        printPan: dataPrint['pan'],
        printHolder: dataPrint['cardHolderName'],
        printType: dataPrint['issuerCode'],
        printAmount: dataPrint['amount'],
        printDate: DateFormat('dd/MM/yyyy').format(dateTime),
        printTime: DateFormat('HH:mm:ss').format(dateTime),
        printSign: dataPrint['targetBase64Receipt'],
        printMCName: userInfo?.businessName ?? "",
        printMCAddress: userInfo?.businessAddress ?? "",
        printTxid: transId,
        printDes: dataPrint['description'],
        printIsVoid: isCancel,
        isTransSkipSignature: dataPrint['flagNoSignature'],
        screenshotController: screenshotController,
      ),
    );

    _captureAndPrintBill(screenshotController, tempPrinterWidget);
  }

  Future<bool> _printReceiptFromMpos(BuildContext? context, Rx<Widget> tempPrinterWidget,
      NativeResponseModel<dynamic> nativeResponseModelTID,
      NativeResponseModel<dynamic> nativeResponseModelMID,
      String transId, String transRqId, bool isCancel, ScreenshotController screenshotController) async {
    showLoading();
    BaseResponse response = await ApiClient.instance.request(
        url: ApiConstant.urlReceipt,
        data: json.encode({
          'serviceName': 'RECEIPT_GET_DETAILS',
          'txid': transId,
        }));
    hideLoading();
    if (response.result!) {
      int? transactionDate = response.data['transactionDate'];
      DateTime dateTime;// = DateTime.fromMillisecondsSinceEpoch((transactionDate ?? 0), isUtc: true).add(Duration(hours: 7));
      if (transactionDate == null) {
        dateTime = DateTime.now();
      } else {
        dateTime = DateTime.fromMillisecondsSinceEpoch((transactionDate), isUtc: true).add(Duration(hours: 7));
      }
      showLoading();
      tempPrinterWidget.value = Container(
        child: TempPrinterBillWidget(
          printTID: nativeResponseModelTID.data,
          printMID: nativeResponseModelMID.data,
          printRef: response.data['refNo'],
          printInvoice: response.data['invoiceNo'],
          printBatch: response.data['batchNo'],
          printApprove: response.data['approvalCode'],
          printPan: response.data['pan'],
          printHolder: response.data['cardHolderName'],
          printType: response.data['issuerCode'],
          printAmount: response.data['amount'],
          printDate: DateFormat('dd/MM/yyyy').format(dateTime),
          printTime: DateFormat('HH:mm:ss').format(dateTime),
          printSign: response.data['targetBase64Receipt'] ?? response.data['base64PdfReceipt'],
          printMCName: userInfo?.businessName ?? "",
          printMCAddress: userInfo?.businessAddress ?? "",
          printTxid: transId,
          printDes: response.data['description'],
          printIsVoid: isCancel,
          isTransSkipSignature: response.data['flagNoSignature'],
          screenshotController: screenshotController,
        ),
      );
      _captureAndPrintBill(screenshotController, tempPrinterWidget);
      return true;
    } else {
      return false;
    }
  }
  _printReceiptFromMacqOrBank(BuildContext context, String transId, String transRqId) async {
    showLoading();
    NativeResponseModel nativeResponseModel =
    await NativeBridge.getInstance().nativeGetTransactionReceipt(transId, transRqId);
    hideLoading();
    if (nativeResponseModel.isSuccess) {
      showLoading();
      NativeResponseModel nativeResponseModelPrint =
      await NativeBridge.getInstance().nativePrintBase64(nativeResponseModel.data);
      hideLoading();
      if (!nativeResponseModelPrint.isSuccess) {
        AppUtils.showDialogErrorNative(context, nativeResponseModelPrint.error);
      }
    } else {
      AppUtils.showDialogErrorNative(context, nativeResponseModel.error);
    }
  }

  _captureAndPrintBill(ScreenshotController screenshotController, Rx<Widget> tempPrinterWidget) {
    screenshotController.capture(
      delay: Duration(seconds: 2),
      pixelRatio: 1.0,
    ).then((Uint8List? captureImage) async {
      String imageB64 = base64Encode(captureImage!);
      await NativeBridge.getInstance().nativePrintBase64(imageB64);
      tempPrinterWidget.value = Container();
      hideLoading();
    }, onError: (e) {
      tempPrinterWidget.value = Container();
      hideLoading();
    });
  }

  appendLog(String log) {
    LoggerMp().writeLog(log);
  }
  appendLogReq(String log) {
    LoggerMp().writeLog(log, actionName: LoggerMp.LOGGER_TYPE_REQUEST);
  }
  appendLogRes(String log) {
    LoggerMp().writeLog(log, actionName: LoggerMp.LOGGER_TYPE_RESPONSE);
  }
  appendLogError(String log) {
    LoggerMp().writeLog(log, actionName: LoggerMp.LOGGER_TYPE_ERROR);
  }

  initBgServiceSound() async {
    dev.log('_initBgServiceSound');
    await initializeService();

    final service = FlutterBackgroundService();
    var isRunning = await service.isRunning();
    dev.log('service is running=$isRunning');
    if (isRunning) {
      stopBackgroundService(service: service);
      Duration(seconds: 3).delay(() {
        initBgServiceSound();
      },);
    } else {
      await service.startService();
    }
    dev.log('end _initBackgroundService');
  }

  Future<void> initializeService() async {
    dev.log('initializeService ===>');
    final service = FlutterBackgroundService();

    /// OPTIONAL, using custom notification channel id
    const AndroidNotificationChannel channel = AndroidNotificationChannel(
      notificationIdChanel, // id
      notificationTitle, // title
      description:
      'This channel is used for important notifications.', // description
      importance: Importance.low, // importance must be at low or higher level
    );
    //
    final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
    FlutterLocalNotificationsPlugin();

    // if (Platform.isIOS || Platform.isAndroid) {
    //   await flutterLocalNotificationsPlugin.initialize(
    //     const InitializationSettings(
    //       iOS: DarwinInitializationSettings(),
    //       android: AndroidInitializationSettings('ic_bg_service_small'),
    //     ),
    //   );
    // }

    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
        AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(channel);
    dev.log('initializeService ===> 111');

    await service.configure(
      androidConfiguration: AndroidConfiguration(
        // this will be executed when app is in foreground or background in separated isolate
        onStart: onStartServices,
        // auto start service
        autoStart: false,
        isForegroundMode: true,

        notificationChannelId: notificationIdChanel,
        initialNotificationTitle: notificationTitle,
        initialNotificationContent: 'Initializing',
        foregroundServiceNotificationId: 888,
        foregroundServiceTypes: [AndroidForegroundType.mediaPlayback],
      ),
      iosConfiguration: IosConfiguration(
        // auto start service
        autoStart: false,

        // this will be executed when app is in foreground in separated isolate
        onForeground: onStartServices,

        // you have to enable background fetch capability on xcode project
        onBackground: null,
        // onBackground: onIosBackground,
      ),
    );
    dev.log('initializeService ===>end of config');
  }


  stopBackgroundService({var service}) async {
    dev.log('_stopBackgroundService--->${service!=null?'hasService':'no service'}');

    bool hasInvoke = false;
    if(service==null){
      service = FlutterBackgroundService();
      var isRunning = await service.isRunning();
      dev.log('service is running=$isRunning');
      if (isRunning) {
        hasInvoke = true;
      }
    } else{
      hasInvoke = true;
    }
    if (hasInvoke) {
      isServiceRunning = false;
      service.invoke("stopService");
      dev.log('stop service---->');
    }
  }

  bool isMqttConnected() {
    if (mqttModel != null && emqxConnect?.getEmqxCurrState().equals(EmqxClient.emqxAlive)) {
      return true;
    }
    return false;
  }

  Future<void> playSoundPromotion(String jsonResultTrans) async {
    print("Playing sound promotion with data: $jsonResultTrans");
    SpeakerHandler.speakWithPromotion(jsonResultTrans);
  }
  Future<void> playSound(int amount) async {
    SpeakerHandler.speakWithAmount(amount);
  }

/*
  _initMqtt() async {
    print('init Mqtt: ${_appController.mqttModel?.toJson().toString()}');
    if(_appController.mqttModel!=null) {
      emqxConnect = EmqxClient();
      AppConfiguration().setEmqxEnable(true);
      var topicTemp = _appController.serialNumber;
      // todo fake test
      topicTemp = 'SP052308013006';
      print('topicTemp: $topicTemp');
      var client = await emqxConnect.connect(
          server: 'mqtt1.nextpay.vn',
          // server: _appController.mqttModel!.host,
          // todo fake test
          clientIdentifier: 'N31DEV01',
          // clientIdentifier: topicTemp,
          port: _appController.mqttModel!.port,
          username: _appController.mqttModel!.emqxUsername,
          password:  _appController.mqttModel!.emqxPass,
          sslEnable: true,
          useQueueMsg: true,
          maxMsgInQueue: 5
      );
      // todo fake test
      // topicTemp = 'mpos-soundbox';
      emqxConnect.setTopic(topicTemp);
      print('client.connectionStatus!.state=${client.connectionStatus!.state}');
      if (client.connectionStatus!.state == MqttConnectionState.connected) {
        emqxConnect.subscribeTopic();
        // emqxConnect.changeFeedListener = (element) async {
        //   print('mqtt received=$element');
        //   _handlerMqttMessage(element);
        // };
        emqxConnect.changeFeedConnection = (connectionElement) {
          print('mqtt feedConnection=$connectionElement');


        };
        emqxConnect.changeFeedQueueListener = (element) async {
          dev.log('-1-received msg: ${element.toString()}');
          _handlerMqttMessage(element);
        };
      }
    }
  }

  _handlerMqttMessage(var map){
    dev.log('------');
    // Map map = jsonDecode(msg);
    String amount = map['amount']??map['money']??'';
    dev.log('amount=$amount');
    if(amount!='') {
      TtsControl().speak(AppStrings.getString(AppStrings.msg_noti_payment_success)!.replaceFirst('%s', amount));
    }
  }*/
}
