import 'dart:convert';
import 'dart:developer' as dev;

import 'package:cashiermodule/BaseService/macq_request.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/controller/app_controller.dart';
import 'package:mposxs/app/data/model/mp_data_login_model.dart';
import 'package:mposxs/app/data/model/mqtt_model.dart';
import 'package:mposxs/app/data/model/payment_method.dart';
import 'package:mposxs/app/data/provider/local_storage.dart';
import 'package:mposxs/app/data/provider/logger.dart';
import 'package:mposxs/app/data/provider/native_bridge.dart';
import 'package:mposxs/app/data/provider/session_data.dart';
import 'package:mposxs/app/res/font/app_fonts.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/route/app_pages.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/theme/app_dimens.dart';
import 'package:mposxs/app/ui/widget/base_dialog.dart';
import 'package:mposxs/app/ui/widget/dialog_count_down_alert.dart';
import 'package:mposxs/app/util/app_utils.dart';
import 'package:mposxs/app/util/app_validation.dart';
import 'package:mposxs/app/util/mpos_constant.dart';
import 'package:shared_preferences/shared_preferences.dart';


class LoginController extends GetxController {
  MyAppController _appController = Get.find<MyAppController>();
  TextEditingController accController = new TextEditingController();
  TextEditingController passController = new TextEditingController();
  RxBool isShowPassword = false.obs;
  RxBool keyboardVisible = false.obs;
  RxBool isLoginDevice = false.obs;
  BuildContext? context;
  LocalStorage secureStorage = LocalStorage();

  int numFetchMcConfigFail = 0;
  int lastTimeFetchMcConfigFail = 0;

  @override
  void onInit() {
    var keyboardVisibilityController = KeyboardVisibilityController();
    keyboardVisibilityController.onChange.listen((bool visible) {
      keyboardVisible.value = visible;
    });
    super.onInit();
  }

  @override
  void onReady() async {
    super.onReady();
    NativeBridge.getInstance().nativeSetLanguage(Get.locale.toString() == 'vi_VN' ? 'vi' : 'en');
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    // if (Platform.isIOS) {
    //   IosDeviceInfo iosDeviceInfo = await deviceInfo.iosInfo;
    //   SessionData.device = iosDeviceInfo.utsname.machine;
    //   SessionData.osVersion = iosDeviceInfo.systemName;
    // } else {
      AndroidDeviceInfo androidDeviceInfo = await deviceInfo.androidInfo;
      SessionData.device = androidDeviceInfo.model;
      SessionData.osVersion = androidDeviceInfo.version.release;
      MyAppController.deviceModel = androidDeviceInfo.model;
    // }

    if (MyAppController.isKozenP12orN4()) {
      isLoginDevice.value = true;
    }

    NativeBridge.getInstance().setDisableStatusBar(false);
    Future.delayed(Duration(milliseconds: 300));
    NativeBridge.getInstance().setDisplayNavbar(true);

    NativeBridge.getInstance().initActionOpenApp();

    _checkAutoLogin();
  }

  onPressShowHidePassword() {
    isShowPassword.value = !isShowPassword.value;
  }

  onPressLogin() async {
    AppUtils.hideKeyboard(context);
    if (_checkWaitCountDownFetchMcConfig()) {
      return;
    }

    // todo fake test
    // accController.text = "tap2"; // use with SN: SP022092900323 // anhnt P12
    // accController.text = "tmacq2"; // use with SN:  // thuannx P12
    // accController.text = "pax1";
    // passController.text = "123456";

    String accountName = accController.text;
    String accountPass = passController.text;
    String? errorMsg = checkValidAccountName(accountName);

    if (errorMsg == null && !isLoginDevice.value) {
      errorMsg = checkValidPassword(accountPass);
    }

    if (errorMsg != null) {
      AppUtils.showDialogError(context!, errorMsg);
      return;
    }

    LoggerMp().setUserAndDeviceInfo(userLogin: accountName, deviceNo: _appController.serialNumber);
    // _appController.appendLog('try login: $accountName - ${_appController.serialNumber}');
    _appController.appendLogReq('login: $accountName - ${_appController.serialNumber}');

    if (isLoginDevice.value) {
      _loginMacqByDevice(accountName, '');
    } else {
      _loginMacqByDevice(accountName, accountPass);
      // _doFetchMcConfigMpos(accountName);
    }
  }

  _checkAutoLogin() async {
    // AppUtils.log('${this.runtimeType}: checkAutoLogin');
    String accountName = await secureStorage.getUserName();
    AppUtils.log('${this.runtimeType}: checkAutoLogin -> accountName:' + accountName);
    if (isLoginDevice.value) {
      if (accountName.length > 0) {
        accController.text = accountName;
        onPressLogin();
      }
    } else {
      String accountPass = await secureStorage.getPassword();
      AppUtils.log('accountPass:' + accountPass);
      if (accountName.length > 0 && accountPass.length > 0) {
        accController.text = accountName;
        passController.text = accountPass;
        onPressLogin();
      }
    }
  }

  _checkCanUseCacheMcConfig() async {
    String mcConfigCache = await secureStorage.getGatewayMcConfig();
    // dev.log('_checkCanUseCacheMcConfig: ${isNullEmpty(mcConfigCache)?'no cache':'has cache'}');
    if(!isNullEmpty(mcConfigCache)) {
      MPDataLoginModel userInfoCache = MPDataLoginModel.fromJson(jsonDecode(mcConfigCache));
      dev.log('has cache user=${userInfoCache.muid} pass=${userInfoCache.pass}');
      dev.log('input user=${accController.text} pass=${passController.text}');
      if(userInfoCache.muid == accController.text
          && userInfoCache.pass == passController.text
          ) {
        if (AppUtils.compareTimeSameDate(userInfoCache.lastTimeCache!, DateTime.now().millisecondsSinceEpoch)) {
          _appController.userInfo = userInfoCache;
          _appController.paymentMethod = PaymentMethod.initPaymentMethod(_appController.userInfo!);
          return mcConfigCache;
        }
        secureStorage.setGatewayMcConfig('');
      }
    }
    return '';
  }

  _checkWaitCountDownFetchMcConfig(){
    if (numFetchMcConfigFail >= 1) {
      int secondWait;
      if (numFetchMcConfigFail > 10) {
        secondWait = 180;
      }
      else {
        secondWait = AppUtils.getNextFibonacciByPosition(numFetchMcConfigFail) * 5;
      }

      secondWait = ((DateTime.now().millisecondsSinceEpoch - (lastTimeFetchMcConfigFail + secondWait * 1000))/1000).truncate();
      if (secondWait < 0) {
        // appendLog("need wait before call API: " + secondWait);
        int timeWaitShow = secondWait.abs();
        String second = AppStrings.getPluralSecond(timeWaitShow)!.tr.replaceFirst('%s', timeWaitShow.toString());
        dev.log('--second=$second  timeWaitShow=$timeWaitShow -> plural=${AppStrings.getPluralSecond(timeWaitShow)}');

        String msgShow = AppStrings.getString(AppStrings.error_sdk_count_down_call_api)!.tr.replaceFirst('%s', second);

        _showAlertCountDownStop(msgShow, timeWaitShow);
        _appController.appendLog('denial req mc_config: so fast');
        return true;
      }
    }
    return false;
  }

  _showAlertCountDownStop(String msgShow, int timeCountDown){
    showDialog(
        context: context!,
        builder: (BuildContext context) {
          return DialogCountDownAlert(
            title: AppStrings.getString(AppStrings.titleNotice),
            description: msgShow,
            timeSecondCountDown: timeCountDown,
          );
        });
  }

  _increaseNumConfigFail() {
    numFetchMcConfigFail++;
    lastTimeFetchMcConfigFail = DateTime.now().millisecondsSinceEpoch;
  }


  onPressChangeLanguageApp() async {
    _appController.appendLog('onPressChangeLanguageApp');
    _appController.changeLanguageApp(context);
  }

  onPressForgotPass() {
    // todo fake test
    // onPressSoundTingbox();
    showDialog(
        context: context!,
        builder: (context) => BaseDialog(
              pathIcTop: AppImages.ic_forgot_pass_big,
              title: AppStrings.getString(AppStrings.titleForgetPassword),
              widgetDesc: RichText(
                textAlign: TextAlign.center,
                text: TextSpan(

                    style: TextStyle(
                      fontSize: AppDimens.textSizeSmall,
                      color: AppColors.black,
                    ),
                    children: <TextSpan>[
                      TextSpan(text: 'Để lấy lại mật khẩu vui lòng liên hệ tới Hotline '),
                      // TextSpan(text: AppStrings.labelHello.tr),
                      TextSpan(
                        text: MposConstant.SUPPORT_PHONE,
                        style: TextStyle(fontFamily: AppFonts.robotoRegular, color: AppColors.primary),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () async {
                            // AppUtils.openCallPhoneSupport();
                          },
                      ),
                      TextSpan(text: ' hoặc email: '),
                      TextSpan(
                        text: MposConstant.SUPPORT_EMAIL,
                        style: TextStyle(fontFamily: AppFonts.robotoRegular, color: AppColors.primary),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () {
                            // AppUtils.launchMailHotroMpos();
                          },
                      ),
                      TextSpan(text: ' để được hỗ trợ.'),
                    ]),
              ),
              // widgetDesc: Text('Để lấy lại mật khẩu vui lòng liên hệ tới Hotline 1900-63-64-88 hoặc email: <EMAIL> để được hỗ trợ.'),
              // onPress1stButton: (){
              //   AppUtils.log('click function');
              // },
            ));
  }

  // onPressSoundTingbox() async {
  //   dev.log('---onPressSoundTingbox--');
  //   String currLangSound = await _appController.localStorage.getLanguageSoundTingbox();
  //   if (currLangSound.isNotEmpty) {
  //     List<ObjSettingSelect> listLanguage = [
  //       ObjSettingSelect('Giọng 1', NPTingTingSpeakerHandler.soundNorth),
  //       ObjSettingSelect('Giọng 2', NPTingTingSpeakerHandler.soundSouth),
  //     ];
  //
  //     showModalBottomSheet(
  //       context: context!,
  //       shape: RoundedRectangleBorder(
  //         borderRadius: BorderRadius.vertical(top: Radius.circular(6)),
  //       ),
  //       builder: (context)
  //       => SettingSoundTingbox(
  //         titleScreen: 'Giọng đọc',
  //         onSelectItem: (settingSelected){
  //           AppUtils.log('select item ${settingSelected.value}');
  //           NPTingTingSpeakerHandler.playDemoVoice(settingSelected.value);
  //           // handlerSelectLanguage(settingSelected.value);
  //           currLangSound = settingSelected.value;
  //         },
  //         onPressConfirm: () async {
  //           await _appController.localStorage.setLanguageSoundTingbox(currLangSound);
  //           NPTingTingSpeakerHandler.setPathToSound(currLangSound);
  //           Get.back();
  //         },
  //         listSetting: listLanguage,
  //         valueSelected: ObjSettingSelect('', currLangSound),
  //       ),
  //       isScrollControlled: true,
  //       isDismissible: false,
  //     );
  //   }
  // }

  _loginMacqByDevice(String accountName, String pass) async {
    _appController.showLoading(autoHide: false);
    _appController.fetchToken();

    _appController.loginAccount = accountName;
    _appController.loginPassword = pass;
    NativeResponseModel nativeResponseModel = await NativeBridge.getInstance()
        .callNativeLogin(accountName, pass, _appController.serialNumber!,
            MyAppController.readerType, _appController.deviceIdentifier);
    _appController.hideLoading();

    if (nativeResponseModel.isSuccess) {
      _appController.appendLogReq('deviceId: ${_appController.deviceIdentifier}');
      var mapLogin = jsonDecode(nativeResponseModel.data);
      _appController.tokenCountNoti = mapLogin["token"] ?? '';
      String merchantId = mapLogin["merchantId"] ?? '';

      if (merchantId.isEmpty && (mapLogin['message'] != null)) {
        _appController.appendLogError('fail get-all-mcconfig');
        AppUtils.showDialogError(context!, mapLogin['message'] ?? '');
        return;
      }

      var responseMcConfig = await MacqRequest.requestMacqGetAllConfg(accountName, merchantId, deviceIdentifier: _appController.deviceIdentifier, serialNumber: _appController.serialNumber);
      if (responseMcConfig.isSuccess) {
        if (responseMcConfig.data != null) {
          _appController.userInfo = MPDataLoginModel.fromJson(json.decode(responseMcConfig.data));
          _appController.paymentMethod = PaymentMethod.initPaymentMethod(_appController.userInfo!);
        }
        secureStorage.setPaymentErrorUnsign(null);
        secureStorage.setUserName(accountName);
        secureStorage.setPassword(pass);

        _appController.userInfo!.lastTimeCache = DateTime.now().millisecondsSinceEpoch;
        _appController.userInfo!.muid = accountName;

        // secureStorage.setGatewayMcConfig(jsonEncode(_appController.userInfo!.toJson()));
        secureStorage.setGatewayMcConfig(responseMcConfig.data);
        secureStorage.setSerialNumber(_appController.serialNumber);

        _appController.enableQuickDraw = _checkQuickDraw();
        // _appController.loginAccount = accountName;
        SessionData.account = accountName;
        initAdvancedSettings();
        LoggerMp().processPushLog();
        Get.offAndToNamed(AppRoute.main_screen);
      } else {
        _appController.appendLogError('fail get-all-mcconfig');
        AppUtils.showDialogError(context!, responseMcConfig.error!.message);
      }
    } else {
      _appController.appendLogError('fail login-sdk-bydevice');
      AppUtils.showDialogErrorNative(context!, nativeResponseModel.error!);
    }
  }
  bool _checkQuickDraw() {
    if ((_appController.userInfo!.quickWithdrawInfo == null) ||
        (_appController.userInfo!.quickWithdrawInfo!.amountMin == null) ||
        (_appController.userInfo!.quickWithdrawInfo!.amountMax == null) ||
        (_appController.userInfo!.quickWithdrawInfo!.jsonQuickWithdrawList ==
            null)) {
      return false;
    }
    return true;
  }

  Future<void> initAdvancedSettings() async {
    if (MyAppController.isKozenDevices() &&
        (_appController.userInfo?.enableRethinkdb == true || _appController.userInfo!.config?.connectType == 4)) {
      _appController.isShowAdvancedSettings = true;
    }
    _appController.appendLog('Advanced Settings: ${_appController.isShowAdvancedSettings} '
        '| mores: ${_appController.userInfo?.mores?.toJson().toString()}');
    String cacheMqttModel = '';
    // todo fake test
    // _appController.userInfo?.mores?.emqxUsername = 'rltb01';
    // _appController.userInfo?.mores?.emqxPass = 'rltb01123456';
    // _appController.userInfo?.mores?.emqxUrl = 'mqtt-app.nextpay.vn:8883';
    // _appController.userInfo?.mores?.emqxUsername = 'n31_gDQNyBuji8A';
    // _appController.userInfo?.mores?.emqxPass = 'hhDqz0TfoTfd8d7';
    // _appController.userInfo?.mores?.emqxUrl = 'mqtt.nextpay.vn:8883';

    // setting for mqtt server
    if (_appController.userInfo?.mores != null &&
        _appController.userInfo?.mores?.emqxUsername != null &&
        _appController.userInfo?.mores?.emqxUsername != '' &&
        _appController.userInfo?.mores?.emqxPass != null &&
        _appController.userInfo?.mores?.emqxPass != '' &&
        _appController.userInfo?.mores?.emqxUrl != null &&
        _appController.userInfo?.mores?.emqxUrl != ''
    ) {
      MqttModel mqttModel = MqttModel(
          emqxUrl: _appController.userInfo?.mores?.emqxUrl,
          emqxUsername: _appController.userInfo?.mores?.emqxUsername,
          emqxPass: _appController.userInfo?.mores?.emqxPass);
      mqttModel.parseUriToHostPort();
      cacheMqttModel = jsonEncode(mqttModel);//mqttModel.toJson().toString();
      dev.log('cacheMqttModel=$cacheMqttModel');
      _appController.mqttModel = mqttModel;
    } else {
      _appController.mqttModel = null;
    }
    SharedPreferences preferences = await SharedPreferences.getInstance();
    preferences.setString("mqttModel", cacheMqttModel);
  }
}
