import 'dart:async';
import 'dart:convert';
import 'dart:developer' as dev;
import 'dart:math';

import 'package:cashiermodule/Utilities/Logger.dart';
import 'package:cashiermodule/constants/style.dart';
import 'package:cashiermodule/widget_custom/touchable_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mposxs/app/controller/app_controller.dart';
import 'package:mposxs/app/controller/main_controller.dart';
import 'package:mposxs/app/controller/payment_finish_controller.dart';
import 'package:mposxs/app/data/model/base_response.dart';
import 'package:mposxs/app/data/model/qr_data_config.dart';
import 'package:mposxs/app/data/model/type_qr_payment.dart';
import 'package:mposxs/app/data/provider/api_client.dart';
import 'package:mposxs/app/data/provider/local_storage.dart';
import 'package:mposxs/app/data/provider/logger.dart';
import 'package:mposxs/app/data/provider/native_bridge.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/route/app_pages.dart';
import 'package:mposxs/app/ui/screen/qr/item_row_list_qr.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/theme/app_dimens.dart';
import 'package:mposxs/app/ui/widget/base_bottom_sheet.dart';
import 'package:mposxs/app/ui/widget/qr_wallet_widget.dart';
import 'package:mposxs/app/util/TTSControl.dart';
import 'package:mposxs/app/util/api_constant.dart';
import 'package:mposxs/app/util/app_utils.dart';
import 'package:mposxs/app/util/constants.dart';
import 'package:mposxs/app/util/mpos_constant.dart';
import 'package:uuid/uuid.dart';

import '../util/app_validation.dart';
import 'payment_init_controller.dart';

class QrCodeP12Controller extends GetxController
    with GetSingleTickerProviderStateMixin {
  late BuildContext context;
  String? currentAmount;

  final MyAppController _appController = Get.find<MyAppController>();

  RxString qrCodeData = ''.obs;

  List<QrChildren> listQr = [];
  RxList<TypeQrPayment> listQrSupport = RxList.empty();

  late AnimationController animationController;

  Timer? timerLifeQr;
  Timer? timerAutoCheckStatusQr;
  bool shouldStopDelayedAutoCheckStatus = false;

  RxBool isResetQr = false.obs;
  RxString liveTimeQr = ''.obs;
  RxString logoQr = ''.obs; // = null is default logo vietQr
  String tileBoxQr = ''; // = null is default logo vietQr
  String orderID = ''; // = null is default logo vietQr
  int autoCloseFinishScreen = 15;   //default timer close finish screen

  late QrDataConfig _qrDataConfig;

  late String udid;
  RxString qrType = "".obs;
  String qrChirdName = "";

  RxInt countCheckStatus = 0.obs;
  RxBool isEnableCheckStatus = true.obs;
  bool isPaymentSuccess = false;
  bool isTimerLifeQrCancelled = false;

  int timeCheckQr = 2;
  // late FlutterTts? flutterTts;

  RxBool isShowGroupVAQr = false.obs;
  RxBool isShowGroupQrbank = false.obs;
  RxBool isShowGroupQrQT = false.obs;
  RxBool isShowGroupWallet = false.obs;

  RxBool isPaymentSocket = false.obs;

  Rx<TypePay> typePay = TypePay.QR_PAY.obs;

  @override
  void onInit() async {
    super.onInit();
    animationController =
        AnimationController(vsync: this, duration: Duration(seconds: 2))
          ..repeat();
    if (Get.arguments != null) {
      typePay.value = Get.arguments['typePay'] ?? TypePay.QR_PAY;
      currentAmount = Get.arguments['amount'];
      qrChirdName = Get.arguments['qrChirdName'] ?? '';
      qrType.value = Get.arguments['qrType'] ?? '';
      logoQr.value = Get.arguments['logoQr'] ?? '';
      orderID = Get.arguments['orderID'] ?? '';
      isPaymentSocket.value = Get.arguments['isPaymentSocket'] ?? false;
      autoCloseFinishScreen = Get.arguments['autoCloseFinishScreen'] ?? 15;
    }

    TtsControl().initTts();

    // if (MyAppController.isKozenMposPro()) {
    //   isEnableCheckStatus.value = false;
    // }
  }

  @override
  void onReady() async {
    super.onReady();
    if (typePay.value == TypePay.QR_PAY) {
      initAndFetchQr(qrType.value, qrChirdName, logoQr.value, '');
    }else {
      fetchLinkCardParam();
    }

    // initQrSupported(MposConstant.name_group_qr_VAQR);

    // initAndFetchQr(qrType.value, qrChirdName, logoQr.value, '');
    // fetchQrParam(qrType.value,
    //     qrChirdName); // todo init default viet qr
  }

  void handlerCancelQRTcp() {
    /*
           isPaymentSuccess - Thanh toán thành công chưa - > true -> không cho cancel, false -> cho cancel and close
          */
    if (!isPaymentSuccess) {
      // gửi kết quả cancel về
      // NativeBridge.getInstance().nativeCallBackCancelOrderSocket(
      //     'CANCELED', orderID, currentAmount, qrType.value, udid);
      closeInstanceAndBack(); // gửi kết qủa giao dịch về
    }
  }

  @override
  void onClose() async {
    animationController.dispose();
    // flutterTts?.stop();
  }

  //InitUI
  _initListQr(TypeQr typeQr) {
    listQr.clear();
    switch (typeQr) {
      case TypeQr.QR_VIETQR:
        QrChildren qrDataConfig = AppUtils.getQRChidWithMethodPayment(jsonEncode(_qrDataConfig), MposConstant.name_group_qr_VAQR);
        double minAmount = MposConstant.MIN_AMOUNT_SCAN;
        if ((qrDataConfig.amountMin != null) && qrDataConfig.amountMin!.isNotEmpty) {
          minAmount = double.parse(qrDataConfig.amountMin!);
        }
        if (validateAmount(context, currentAmount!, minAmount)) {
          fetchQrParam(
              MposConstant.name_group_qr_VAQR, MposConstant.qr_name_VietQr);
          initQrSupported(MposConstant.name_group_qr_VAQR);
          logoQr.value = ''; // logo isEmpty = default VietQR
        }
        break;
      case TypeQr.QR_BANK:
        _addQrChildrenByGroupName(MposConstant.name_group_qr_QR_BANK);
        tileBoxQr = AppStrings.getString(AppStrings.tv_name_group_qr_bank)!;
        showListQr();
        break;
      case TypeQr.QR_MVISA:
        _addQrChildrenByGroupName(MposConstant.name_group_qr_QR_QT);
        tileBoxQr = AppStrings.getString(AppStrings.tv_name_group_qr_mvisamaster) ?? '';
        showListQr();
        break;
      case TypeQr.QR_WALLET:
        _addQrChildrenByGroupName(MposConstant.name_group_qr_QR_VI);
        tileBoxQr = AppStrings.getString(AppStrings.tv_name_group_qr_wallet) ?? '';
        showListQr();
        break;
    }
  }

  void _addQrChildrenByGroupName(String groupName) {
    _qrDataConfig.data!.forEach((element) {
      if (element.groupName == groupName) {
        listQr.addAll(element.qrChildren!);
      }
    });
  }

  void showListQr() {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(6)),
      ),
      builder: (context) {
        return BaseBottomSheet(
          isCloseHeader: true,
          hideCloseButton: true,
          child: QrWalletWidget(
            onPressItem: (qrType, qrName, logo, amountMin) => initAndFetchQr(qrType, qrName, logo, amountMin),
            listQr: listQr,
            tileBoxQr: tileBoxQr,
          )

          // child: Column(
          //   children: [
          //     //header
          //     _buildHeaderWalletQr(),
          //     Container(
          //       padding: EdgeInsets.all(10),
          //       height: 250,
          //       child: _buildListWalletQr(),
          //     )
          //   ],
          // ),
        );
      },
      isScrollControlled: true,
      isDismissible: false,
    );
  }

  showDialogSuccessScanQr(dynamic data) async {
    if (data['amount'] != null) {
      LoggerMp().writeLog('showDialogSuccessScanQr ${data['amount']} - ${typePay.value}');
      TtsControl().speak(AppStrings.getString(AppStrings.msg_noti_payment_success)!
          .replaceFirst('%s', data['amount'].toString()));

      cancelTimerCheckStatusQr();
      LoggerMp().processPushLog();
      Get.toNamed(AppRoute.payment_finish_screen,
          arguments: PaymentFinishArguments((typePay.value == TypePay.CREATE_LINK) ? MposConstant.CREATE_LINK_PAYMENT : MposConstant.QR_PAYMENT, data, '', autoCloseFinishScreen: autoCloseFinishScreen));
    }
  }

  handlerQrSuccessMposPro(String amount) async {
    Map data = {};
    data['amount'] = amount;
    LoggerMp().processPushLog();
    Get.toNamed(AppRoute.payment_finish_screen,
        arguments: PaymentFinishArguments(MposConstant.QR_PAYMENT, data, '', autoCloseFinishScreen: autoCloseFinishScreen));
  }

  // fetch/checkStatus Qr

  autoCheckStatusQr() {
    // if (MyAppController.isKozenMposPro()) {
    //   return;
    // }

    timerAutoCheckStatusQr = Timer.periodic(Duration(seconds: timeCheckQr), (timer) async {
      dev.log('Action executed every $timeCheckQr count == $countCheckStatus');
      if (countCheckStatus >= 5) {
        cancelTimerCheckStatusQr(); // Hủy bỏ timer nếu điều kiện được đáp ứng
      } else {
        if (isEnableCheckStatus.value) {
          checkStatus();
        }
      }
    });
  }

  cancelTimerCheckStatusQr() {
    if (timerAutoCheckStatusQr != null) {
      timerAutoCheckStatusQr?.cancel();
      timerAutoCheckStatusQr = null;
    }
  }
  cancelTimerLifeQr() {
    isTimerLifeQrCancelled = true;
    if (timerLifeQr != null) {
      timerLifeQr?.cancel();
      timerLifeQr = null;
    }
  }

  checkStatus() async {
    countCheckStatus++;
    isEnableCheckStatus.value = false;
    checkStatusQRPay();
    // if (typePay.value == TypePay.QR_PAY) {
    //   checkStatusQRPay();
    // }
  }

  fetchQrParam(String? qrType, String qrChirdName) async {
    dev.log("fetchQrParam  $qrChirdName typeQr= $qrType");
    Logger().writeLog(LoggerMp.LOGGER_TYPE_ACTION, "fetchQrParam  $qrChirdName typeQr= $qrType");

    this.qrCodeData.value = '';
    this.qrType.value = qrType!;
    this.qrChirdName = qrChirdName;
    isResetQr.value = false;

    countCheckStatus.value = 0;
    cancelTimerLifeQr();
    cancelTimerCheckStatusQr();
    liveTimeQr.value = '';
    // currentQR.value.typeQr = typeQrPayment;

    udid = 'QR-$orderID|${Uuid().v4()}_${getTransCode()}';
    Map params = {
      'serviceName': 'PREPARE_TRANSACTION',
      'udid': udid,
      'deviceIdentifier': '',
      'muid': _appController.loginAccount,
      'merchantId': _appController.userInfo?.merchantId ?? 0,
      'paymentMethod': 'QR',
      'customerEmail': '',
      'customerMobile': '',
      'description': '',
      'qrType': qrType,
      // 'amount': currentAmount + (checkedFee.value ? fee : 0),
      'amount': currentAmount,
      'flatFee': 0,
      'amountBeforePayment': currentAmount,
      // 'buyerBearFee': checkedFee.value,
      'shortNameChild': qrChirdName,
    };

    LoggerMp().writeLog(json.encode(params));
    // _appController.showLoading();
    fetchQR(json.encode(params));
  }

  fetchQR(String params) async {
    // _appController.showLoading();
    BaseResponse response = await ApiClient.instance
        .request(url: ApiConstant.urlApi, data: params);
    _appController.hideLoading();

    if (response.result!) {

      if (typePay.value == TypePay.QR_PAY) {
        qrCodeData.value = response.data['qrCode'];
        int? expireTime = response.data['expireTime'];
        if (expireTime != null) {
          _startCountdown(expireTime);
        }
      }else if (typePay.value == TypePay.CREATE_LINK) {
        qrCodeData.value = response.data['linkCheckout'];
        LoggerMp().writeLog("response = ${response.data}");
      }
      await Future.delayed(Duration(seconds: 5));
      if (!shouldStopDelayedAutoCheckStatus && (typePay.value == TypePay.QR_PAY)) {
          autoCheckStatusQr();
      }
    } else {
      dev.log("PREPARE_TRANSACTION err");
      isResetQr.value = true;
      AppUtils.showDialogError(
          context, '${response.code ?? ''}: ${response.message ?? ''}');
    }
  }

  onPressResetQr() {
    dev.log("onPressResetQr");
    if (typePay.value == TypePay.QR_PAY) {
      fetchQrParam(qrType.value, qrChirdName);
    }else if (typePay.value == TypePay.CREATE_LINK) {
      fetchLinkCardParam();
    }
  }

  onPressClose() {
    LoggerMp().writeLog("onPressClose");
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(6)),
      ),
      builder: (context) {
        return BaseBottomSheet(
          isCloseHeader: true,
          hideCloseButton: true,
          child: Container(
            padding: EdgeInsets.symmetric(vertical: 20, horizontal: 30),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Image.asset(AppImages.ic_scan_qr),
                SizedBox(
                  height: 10,
                ),
                Text(
                  AppStrings.getString(AppStrings.tv_close_qr) ?? '',
                  style: style_S20_W600_BlackColor,
                ),
                SizedBox(
                  height: 15,
                ),
                Text(
                  AppStrings.getString(AppStrings.msg_warning_close_qr_screen) ?? '',
                  style: style_S16_W400_BlackColor,
                  textAlign: TextAlign.center,
                ),
                SizedBox(
                  height: 15,
                ),
                Row(
                  children: [
                    Expanded(
                      flex: 1,
                      child: TouchableWidget(
                        height: AppDimens.heightButton,
                        padding: EdgeInsets.all(0),
                        decoration: BoxDecoration(
                            color: AppColors.lightGreyText,
                            borderRadius:
                                BorderRadius.circular(AppDimens.radiusSmall)),
                        child: Text(
                          AppStrings.getString(AppStrings.skip)!,
                          style: TextStyle(
                            fontSize: AppDimens.textSizeLarge20,
                            fontWeight: FontWeight.w500,
                            fontFamily: kFontFamilyBeVietnamPro,
                            color: AppColors.white,
                          ),
                        ),
                        // margin: EdgeInsets.only(left: 5, right: 5),
                        onPressed: () {
                          Get.back();
                        },
                      ),
                    ),
                    SizedBox(
                      width: 10,
                    ),
                    Expanded(
                      flex: 1,
                      child: TouchableWidget(
                        width: 50,
                        height: AppDimens.heightButton,
                        padding: EdgeInsets.all(0),
                        decoration: BoxDecoration(
                            color: AppColors.bgButton,
                            borderRadius:
                                BorderRadius.circular(AppDimens.radiusSmall)),
                        child: Text(
                          AppStrings.getString(AppStrings.close) ?? '',
                          style: TextStyle(
                            fontSize: AppDimens.textSizeLarge20,
                            fontWeight: FontWeight.w500,
                            fontFamily: kFontFamilyBeVietnamPro,
                            color: AppColors.white,
                          ),
                        ),
                        // margin: EdgeInsets.only(left: 5, right: 5),
                        onPressed: () {
                          closeInstanceAndBack();
                        },
                      ),
                    )
                  ],
                )
              ],
            ),
          ),
        );
      },
      isScrollControlled: true,
      isDismissible: false,
    );
  }

  onPressShowMoreQr() {
    LoggerMp().writeLog("onPressShowMoreQr");
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(6)),
      ),
      builder: (context) {
        return BaseBottomSheet(
          onPressClose: () {
            Get.back();
          },
          title: AppStrings.getString(AppStrings.tv_other_type_qr),
          titleAlign: TextAlign.start,
          child: Container(
            padding: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
            margin: EdgeInsets.only(bottom: 10),
            child: GridView.count(
              crossAxisCount: 2,
              padding: EdgeInsets.only(top: AppDimens.spaceXSmall, left: AppDimens.spaceXSmall, right: AppDimens.spaceXSmall),
              mainAxisSpacing: AppDimens.spaceXSmall,
              crossAxisSpacing: AppDimens.spaceXSmall,
              childAspectRatio: 3.2,
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              children: (listQrSupport.map((e) => _buildItemQr(e)).toList()),
            ),
          ),
        );
      },
      isScrollControlled: true,
      isDismissible: false,
    );
  }
  
  onPressSelectQr(TypeQr typeQr) {
    Get.back();
    dev.log("onPressSelectQr $typeQr");
    LoggerMp().writeLog("onPressSelectQr $typeQr");
    _initListQr(typeQr);
  }

  onPressCheckStatusTrans() async {
    dev.log("onPressCheckStatusTrans");
    LoggerMp().writeLog("onPressCheckStatusTrans");
    if (isEnableCheckStatus.value) {
      countCheckStatus.value = 0;
      if (typePay.value == TypePay.QR_PAY) {
        autoCheckStatusQr();
      }else if (typePay.value == TypePay.CREATE_LINK) {
        checkStatusLinkCard();
      }
      // checkStatus();
    }
  }

  closeInstanceAndBack() async {
    LoggerMp().writeLog("closeInstanceAndBack");
    // Get.back();
    cancelTimerLifeQr();
    cancelTimerCheckStatusQr();
    shouldStopDelayedAutoCheckStatus = true;
    Get.until(ModalRoute.withName(AppRoute.main_screen));
    MainController _mc = Get.find<MainController>();
    // _mc.currentScreen.value = MainController.MAIN_SCREEN_P12_QR;
    if (MyAppController.isKozenP12orN4()) {
      _mc.initScreenP12();
    }
    _mc.currentAmount.value = '0';

    PaymentInitController initController = Get.find<PaymentInitController>();
    initController.valueInput.value = '0';

    if ((_appController.userInfo?.config?.connectType == 4) && (isPaymentSocket.value == true)) {
      LoggerMp().writeLog("callback data socket");
      NativeBridge.getInstance().nativeCallBackResultPaymentSocket('CANCELED', orderID, currentAmount, qrType.value, udid ?? '', errCode: MposConstant.ERROR_CANCEL_ORDER);
    }
    Get.delete<QrCodeP12Controller>();
  }

  void _startCountdown(int? expireTime) async {
    await Future.delayed(Duration(seconds: 6));
    dev.log("expireTime= $expireTime");
    DateTime targetDateTime = DateTime.fromMillisecondsSinceEpoch(expireTime!);
    const oneSec = Duration(seconds: 1);
    if (!isTimerLifeQrCancelled) {
      timerLifeQr = Timer.periodic(oneSec, (timer) {
        final now = DateTime.now();
        if (targetDateTime.isAfter(now)) {
          isResetQr.value = true;
          timer.cancel();
          showNotifyCreateNewTrans();
          return;
        }

        handlerCountDown(targetDateTime.difference(now));
      });
    }
  }

  void handlerCountDown(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);
    liveTimeQr.value =
        '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  initAndFetchQr(String? typeQr, String? qrCode, String? logo, String? amountMin) {
    double minAmount = MposConstant.MIN_AMOUNT_SCAN;
    if ((amountMin != null) && amountMin.isNotEmpty) {
      minAmount = double.parse(amountMin);
    }
    if (validateAmount(context, currentAmount!, minAmount)) {
      logoQr.value = logo ?? '';
      initQrSupported(typeQr ?? '');
      dev.log("initAndFetchQr $qrCode");
      fetchQrParam(typeQr, qrCode ?? '');
    }
  }

  showNotifyCreateNewTrans() {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(6)),
      ),
      builder: (context) {
        return BaseBottomSheet(
          isCloseHeader: true,
          hideCloseButton: true,
          child: Container(
            padding: EdgeInsets.only(top: 30, bottom: 10, left: 20, right: 20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Image.asset(AppImages.ic_recent),
                SizedBox(
                  height: 15,
                ),
                Text(AppStrings.getString(AppStrings.msg_order_expired) ?? '',
                    style: style_S16_W600_BlackColor,
                    textAlign: TextAlign.center),
                SizedBox(
                  height: 20,
                ),
                Row(
                  children: [
                    Expanded(
                      flex: 1,
                      child: TouchableWidget(
                        width: 50,
                        height: AppDimens.heightButton,
                        padding: EdgeInsets.all(10),
                        decoration: BoxDecoration(
                            color: AppColors.bgButton,
                            borderRadius:
                                BorderRadius.circular(AppDimens.radiusSmall)),
                        child: Text(
                          AppStrings.getString(AppStrings.tv_create_new_order) ?? '',
                          style: TextStyle(
                            fontSize: AppDimens.textSizeLarge,
                            fontWeight: FontWeight.w500,
                            fontFamily: kFontFamilyBeVietnamPro,
                            color: AppColors.white,
                          ),
                        ),
                        // margin: EdgeInsets.only(left: 5, right: 5),
                        onPressed: () {
                          closeInstanceAndBack();
                        },
                      ),
                    ),
                  ],
                )
              ],
            ),
          ),
        );
      },
      isScrollControlled: true,
      isDismissible: false,
    );
  }

  Widget _buildItemQr(TypeQrPayment e) {
    return ItemRowListQr(
      nameQr: e.nameQR,
      detail: e.desception,
      typeQr: e.typeQr,
      color: e.color,
      onPressed: () => onPressSelectQr(e.typeQr!),
    );
  }

  void showDialogNotPaidQR() {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(6)),
      ),
      builder: (context) {
        return BaseBottomSheet(
          isCloseHeader: true,
          hideCloseButton: true,
          child: Container(
            padding:
            EdgeInsets.only(top: 30, bottom: 10, left: 20, right: 20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Image.asset(AppImages.ic_recent),
                SizedBox(
                  height: 15,
                ),
                Text(
                  AppStrings.getString(AppStrings.msgCheckTransactionQrNotPay) ?? '',
                  style: style_S16_W600_BlackColor,
                  textAlign: TextAlign.center,
                ),
                SizedBox(
                  height: 20,
                ),
                Row(
                  children: [
                    Expanded(
                      flex: 1,
                      child: TouchableWidget(
                        width: 50,
                        height: AppDimens.heightButton,
                        padding: EdgeInsets.all(10),
                        decoration: BoxDecoration(
                            color: AppColors.bgButton,
                            borderRadius: BorderRadius.circular(
                                AppDimens.radiusSmall)),
                        child: Text(
                          AppStrings.getString(AppStrings.close) ?? '',
                          style: TextStyle(
                            fontSize: AppDimens.textSizeLarge,
                            fontWeight: FontWeight.w500,
                            fontFamily: kFontFamilyBeVietnamPro,
                            color: AppColors.white,
                          ),
                        ),
                        // margin: EdgeInsets.only(left: 5, right: 5),
                        onPressed: () {
                          Get.back();
                        },
                      ),
                    ),
                  ],
                )
              ],
            ),
          ),
        );
      },
      isScrollControlled: true,
      isDismissible: false,
    );
  }

  void initQrSupported(String qrType) async {
    listQrSupport.clear();
    String configQr = await _appController.localStorage.getConfigQrP12();
    final Map responseMap = json.decode(configQr);
    _qrDataConfig = QrDataConfig.fromJson(responseMap as Map<String, dynamic>);

    if (_qrDataConfig.data == null) {
      return;
    }

    for (final element in _qrDataConfig.data!) {
      if (element.groupName == qrType) {
        break;
      }
      switch (element.groupName) {
        case MposConstant.name_group_qr_VAQR:
          isShowGroupVAQr.value = true;
          listQrSupport.add(TypeQrPayment(nameQR: AppStrings.getString(AppStrings.tv_title_vietQR), desception: AppStrings.getString(AppStrings.tv_detail_qr_vietqr), typeQr: TypeQr.QR_VIETQR, color: AppColors.success));
          break;
        case MposConstant.name_group_qr_QR_BANK:
          isShowGroupQrbank.value = true;
          listQrSupport.add(TypeQrPayment(nameQR: AppStrings.getString(AppStrings.tv_name_group_qr_bank), desception: AppStrings.getString(AppStrings.tv_detail_qr_vnpay), typeQr: TypeQr.QR_BANK, color: AppColors.primary));
          break;
        case MposConstant.name_group_qr_QR_QT:
          isShowGroupQrQT.value = true;
          listQrSupport.add(TypeQrPayment(nameQR: AppStrings.getString(AppStrings.tv_name_group_qr_mvisamaster), desception: AppStrings.getString(AppStrings.tv_detail_qr_mvisa_master), typeQr: TypeQr.QR_MVISA, color: AppColors.tabUnSelected));
          break;
        case MposConstant.name_group_qr_QR_VI:
          isShowGroupWallet.value = true;
          listQrSupport.add(TypeQrPayment(nameQR: AppStrings.getString(AppStrings.tv_name_group_qr_wallet), desception: AppStrings.getString(AppStrings.tv_detail_qr_wallet), typeQr: TypeQr.QR_WALLET, color: AppColors.orange));
          break;
      }
    }
  }

  void fetchLinkCardParam() async {
    this.qrCodeData.value = '';
    isResetQr.value = false;

    countCheckStatus.value = 0;
    cancelTimerLifeQr();
    cancelTimerCheckStatusQr();
    liveTimeQr.value = '';

    udid = '|${Uuid().v4()}';
    Map params = {
      'serviceName': 'PREPARE_TRANSACTION',
      'udid': udid,
      'deviceIdentifier': '',
      'muid': _appController.loginAccount,
      'merchantId': _appController.userInfo?.merchantId ?? 0,
      'paymentMethod': 'LINKCARD',
      'transLinkType': 'CREATE_LINK',
      'customerEmail': '',
      'customerMobile': '',
      'description': '',
      'amount': currentAmount,
      'flatFee': 0,
      'amountBeforePayment': currentAmount,
      'serialNumber': _appController.serialNumber,
    };
    fetchQR(json.encode(params));
  }

  void checkStatusQRPay() async {
    Map params = {
      'serviceName': 'TRANSACTION_PUSH_CHECK_STATUS',
      'udid': udid,
      'type': qrType.value,
    };
    LoggerMp().writeLog('params = ${params.toString()}');

    // if (countCheckStatus > 5) {
    //   _appController.showLoading();
    // }
    BaseResponse response = await ApiClient.instance.request(
        url: ApiConstant.urlCheckStatusMVISA, data: json.encode(params));
    // _appController.hideLoading();

    isEnableCheckStatus.value = true;

    if (response.result!) {
      String? transactionStatus = response.data['transactionStatus'];
      print('transactionStatus= $transactionStatus');
      if (transactionStatus == 'APPROVED') {
        isPaymentSuccess = true;
        // show Dialog success
        isEnableCheckStatus.value = false;

        //save order paid
        _appController.listOrderPaid.add(response.data['orderCode'] ?? '');
        LocalStorage().setListOrderPaid(_appController.listOrderPaid);
        LocalStorage().setLastTimeCacheQrPaid(
            DateFormat('dd/MM/yy').format(DateTime.now()));

        //callback result payment tcp/ip
        if (_appController.userInfo?.config?.connectType == 4) {
          // if (await NativeBridge.getInstance().nativeIsPermitSocket()) {
          //todo callback data socket
          LoggerMp().writeLog('callback data socket APPROVED');
          NativeBridge.getInstance().nativeCallBackResultPaymentSocket('APPROVED', orderID, currentAmount, qrType.value, udid, transId: response.data['txid'] ?? '');
        }

        showDialogSuccessScanQr(response.data);
      }
      // else {
      //   if (countCheckStatus >= 4) {
      //     showDialogNotPaidQR();
      //   }
      // }
    } else {
      if (countCheckStatus >= 4) {
        AppUtils.showDialogError(
            context, '${response.code ?? ''}: ${response.message ?? ''}');
      }
    }
  }

  void checkStatusLinkCard() async {
    Map<String, dynamic> params = {
      "serviceName": 'CHECK_PAYMENT_LINK_STATUS',
      "udid": udid,
    };
    dev.log("checkStatus Link Card: ${json.encode(params)}");
    LoggerMp().writeLog("checkStatus Link Card: ${json.encode(params)}");

    BaseResponse response = await ApiClient.instance.request(
        url: ApiConstant.urlApi, data: json.encode(params));
    // _appController.hideLoading();
    if (!response.result!) {
      try {
        int? code = response.data['error']['code'];
        LoggerMp().writeLog('checkStatusLinkCard: $code');
        if (code == 0) {
          showDialogSuccessScanQr(response.data);
        } else {
          showDialogNotPaidQR();
        }
        // if (code == 80) {
        //   // return PaymentStatus.pending;
        // }
        // if (code == 81 || code == 99) {
        //   // return PaymentStatus.failed;
        // }
      } catch (e) {
        LoggerMp().writeLog('checkStatusLinkCard: ${e.toString()}');
      }
    } else {
      AppUtils.showDialogError(
          context, '${response.code ?? ''}: ${response.message ?? ''}');
    }
  }

  String getTransCode() {
    final Random random = Random();
    final StringBuffer sb = StringBuffer();
    for (int i = 0; i < 6; ++i) {
      sb.write(random.nextInt(9));
    }
    return sb.toString();
  }
}
