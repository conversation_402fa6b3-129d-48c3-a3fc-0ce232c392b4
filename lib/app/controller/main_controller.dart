import 'dart:async';
import 'dart:convert';
import 'dart:developer' as dev;
import 'dart:io';

import 'package:cashiermodule/Class/AESEncrypter.dart';
import 'package:cashiermodule/Class/CardInfo.dart';
import 'package:cashiermodule/Class/brightness_control.dart';
import 'package:cashiermodule/Model/MotoPaymentModel.dart';
import 'package:cashiermodule/Model/qr_static_response.dart';
import 'package:cashiermodule/Pages/notification/extension/notification_extension.dart';
import 'package:cashiermodule/Pages/push_payment_page/class/setting_model.dart';
import 'package:cashiermodule/Pages/push_payment_page/pass_code_page/passs_code_page.dart';
import 'package:cashiermodule/Utilities/LocalizationCustom.dart';
import 'package:cashiermodule/Utilities/MethodFuc.dart';
import 'package:cashiermodule/Utilities/SystemPreference.dart';
import 'package:cashiermodule/Utilities/TextUtils.dart';
import 'package:cashiermodule/Utilities/app_utils.dart' as mp;
import 'package:cashiermodule/Utilities/configuration.dart' as configModule;
import 'package:cashiermodule/constants/style.dart';
import 'package:cashiermodule/controller/app_controller.dart';
import 'package:cashiermodule/extension/extension_string_utils.dart';
import 'package:cashiermodule/model_instance/app_configuration.dart';
import 'package:cashiermodule/widget_custom/common_button.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mpos_module_base/mpos_module_base.dart';
import 'package:mpos_module_base/mpos_module_base_widget.dart';
import 'package:mposxs/app/controller/QrCodeP12Controller.dart';
import 'package:mposxs/app/controller/app_controller.dart';
// import 'package:mposxs/app/controller/mqtt_controller.dart';
import 'package:mposxs/app/controller/payment_finish_controller.dart';
import 'package:mposxs/app/controller/payment_init_controller.dart';
import 'package:mposxs/app/controller/webview_info_controller.dart';
import 'package:mposxs/app/data/model/app_version.dart';
import 'package:mposxs/app/data/model/base_response.dart';
import 'package:mposxs/app/data/model/mqtt_model.dart';
import 'package:mposxs/app/data/model/qr_data_config.dart';
import 'package:mposxs/app/data/model/settle_transaction_detail_model.dart';
import 'package:mposxs/app/data/model/summary_response.dart';
import 'package:mposxs/app/data/model/user_info.dart';
import 'package:mposxs/app/data/provider/api_client.dart';
import 'package:mposxs/app/data/provider/local_storage.dart';
import 'package:mposxs/app/data/provider/logger.dart';
import 'package:mposxs/app/data/provider/native_bridge.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/route/app_pages.dart';
import 'package:mposxs/app/ui/screen/main/widget/bottom_sheet_confirm_password.dart';
import 'package:mposxs/app/ui/screen/main/widget/summary_widget.dart';
import 'package:mposxs/app/ui/screen/main/widget/time_range_widget.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/theme/app_dimens.dart';
import 'package:mposxs/app/ui/widget/base_bottom_sheet.dart';
import 'package:mposxs/app/util/TTSControl.dart';
import 'package:mposxs/app/util/api_constant.dart';
import 'package:mposxs/app/util/app_utils.dart';
import 'package:mposxs/app/util/app_validation.dart';
import 'package:mposxs/app/util/mpos_constant.dart';
import 'package:mposxs/build_constants.dart' as bc;
import 'package:network_info_plus/network_info_plus.dart';
import 'package:package_info/package_info.dart';
import 'package:screenshot/screenshot.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

import '../data/model/data_notification_new.dart';
import '../util/constants.dart';
import 'mqtt_controller.dart';


class MainController extends GetxController with NotificationExtension{
  // DEVICE IS NOT P12
  static final int MAIN_SCREEN_NORMAL_PAY = 0;
  static final int MAIN_SCREEN_NORMAL_HISTORY = 1;

  // DEVICE IS P12
  static final int MAIN_SCREEN_P12_QR = 2;
  static final int MAIN_SCREEN_P12_ENTER_AMOUNT = 3;

  late BuildContext context;
  // RxString appVersion = ''.obs;
  final MyAppController _appController = Get.find<MyAppController>();

  RxBool isShowGuideP12 = false.obs;
  RxInt currentScreen = 0.obs;
  RxString base64ImageReceipt = ''.obs;
  final ScreenshotController screenshotController = ScreenshotController();

  //p12
  RxString currentAmount = '0'.obs;
  RxString qrCode = ''.obs;
  RxString qrCodeAppleGoogle = ''.obs;
  RxBool isShowQrCodeAppleGgPay = false.obs;
  RxString nameMerchant = ''.obs;
  RxString bankNumber = ''.obs;
  RxString ipAddress = ''.obs;
  Timer? autoDissmisDlgSuccesTimer;

  List<DataNotification> listDataNoti = [];

  // List<String> listOrderPaid = [];
  bool isShowResult = true;
  // FlutterTts? flutterTts = FlutterTts();

  var appCfPushPayment = AppConfiguration();

  NativeBridge _nativeBridge = NativeBridge.getInstance();

  int lastTimePressBackMs = 0; // ms = millisecond
  late bool btsPassWordHideCloseButton;
  bool? btsPassWordHorizontalComponent;

  RxBool isShowWaitingTcpScreen = false.obs;
  String pwWaitScreen = '';
  RxString statusEmqx = ''.obs;

  @override
  void onInit() {
    dev.log('------> onInit');

    if (MyAppController.isKozenP12orN4()) {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: [SystemUiOverlay.bottom]);
      initScreenP12();
    } else {
      currentScreen.value = MAIN_SCREEN_NORMAL_PAY;
    }

    // if (MyAppController.isSupportPrinter()) {
    //   NativeBridge.getInstance().nativeInitPrinter();
    // }
    super.onInit();
  }

  @override
  void onReady() async {
    super.onReady();
    dev.log('------> onReady');

    initConfigCashierModule();
    _processErrorUnSignAndCallingApp().then((hasReSignature) => {
          if (Platform.isAndroid && !hasReSignature) {_processCheckVersion()}
        });

    checkEnableNavbarAndStatusbar();
    // if (!MyAppController.isKozenP12()) {
    //   _checkCachePrint();
    // }
    initGuildAndTitle();
    if (MyAppController.isKozenP12orN4()) {
      fetchListQr();
    }
    initFireBaseMessasing();
    await initSound();
    dev.log('------> onReady ----5');
    if(_appController.mqttModel!=null) {
      _appController.initBgServiceSound();
      FlutterBackgroundService().on('callbackEmqx').listen(initCallbackHandleMqttMsg);
      FlutterBackgroundService().on('statusEmqx').listen(initCallbackStatusEmqx);
    } else{
      _appController.stopBackgroundService();
    }
    // _initMqtt();
    dev.log('------> onReady ----6');
    initNotification();
    initListenerFromNative();
    // autoGotoPushPayment();

    if (await LocalStorage().getData(LocalStorage.isHoldScreenQr, false)) {
      WakelockPlus.enable();
    }
  }

  @override
  void onClose() async {
    print('onClose activity');
    LoggerMp().saveLogToStorage();
    setBrightnessControl(1);
    // flutterTts?.stop();
    // if(emqxConnect!=null) {
    //   emqxConnect.unSubscribeTopic();
    // }
    super.onClose();
  }

  /*     ------ Init ------    */
  initListenerTcp() async {
    if (_appController.userInfo?.config?.connectType == 4) {
      isShowWaitingTcpScreen.value = await LocalStorage().getData(LocalStorage.isShowWaitingTcpScreen, false);
      if (isShowWaitingTcpScreen.value) {
        BrightnessControl().initScreens([AppRoute.main_screen]);
        setBrightnessControl(0);
      }
      pwWaitScreen = await LocalStorage().getData(LocalStorage.pwWaitingTcpScreen, '');
      // if (await _nativeBridge.nativeIsPermitSocket()) {
      LoggerMp().writeLog('start init socket');
      NativeBridge.getInstance().nativeInitSocket();
      final info = NetworkInfo();
      ipAddress.value = (await info.getWifiIP()) ?? '';
      Timer.periodic(Duration(seconds: 10), (timer) async {
        ipAddress.value = (await info.getWifiIP()) ?? '';
      });
    }
  }

  void checkEnableNavbarAndStatusbar() async {
    if (_appController.isShowAdvancedSettings) {
      NativeBridge.getInstance().setDisableStatusBar(await LocalStorage().getData(LocalStorage.disableStatusBar, false));
      NativeBridge.getInstance().setDisplayNavbar(await LocalStorage().getData(LocalStorage.displayNavbar, true));
    }
  }

  initSound() async {
    TtsControl().initTts();
  }

  initScreenP12() {
    if (qrCode.isNotEmpty || qrCodeAppleGoogle.isNotEmpty) {
      currentScreen.value = MAIN_SCREEN_P12_QR;
    }else {
      currentScreen.value = MAIN_SCREEN_P12_ENTER_AMOUNT;
    }
  }

  Future<bool> _processErrorUnSignAndCallingApp() async {
    Map? paymentUnSignMap = await LocalStorage().getPaymentErrorUnsign();
    printInfo(info: 'unsign: ${paymentUnSignMap.toString()}');

    if (paymentUnSignMap != null) {
      if (_appController.userInfo!.isMacqFlow == 1) {
        // AppUtils.showDialogAlert(context,
        //     description: AppStrings.getString(AppStrings.warningMaUnSign), onPress1stButton: () {
        //   Get.back();
        //   showBottomSheetConfirmPass(hideCloseButton: true);
        // });
        _gotoScreenHistory();
      } else {
        Get.toNamed(AppRoute.continue_payment_screen,
            arguments: paymentUnSignMap);
      }
      return true;
    }
    return false;
  }

  gotoP12ScreenPaymentCard() {
    currentScreen.value = MAIN_SCREEN_P12_ENTER_AMOUNT;
  }

  gotoP12ScreenScanQr() {
    currentScreen.value = MAIN_SCREEN_P12_QR;
  }

  changeP12Screen() {
    if (currentScreen.value == MAIN_SCREEN_P12_ENTER_AMOUNT) {
      gotoP12ScreenScanQr();
    } else if (currentScreen.value == MAIN_SCREEN_P12_QR) {
      gotoP12ScreenPaymentCard();
    }
  }

  // _processMacqCompareOnlinePass(String password, bool retry) async {
  //   _appController.showLoading(message: 'Đang xác thực');
  //   LoggerMp().writeLog('VerifySN retry: $retry', actionName: LoggerMp.LOGGER_TYPE_REQUEST);
  //   NativeResponseModel nativeResponseModel = await NativeBridge.getInstance().nativeVerifySerialNumber(_appController.serialNumber, password);
  //   _appController.hideLoading();
  //   if (nativeResponseModel.isSuccess) {
  //     if (currentScreen.value != MAIN_SCREEN_NORMAL_HISTORY) {
  //       _gotoScreenHistory();
  //     }
  //   } else {
  //     LoggerMp().writeLog('VerifySerialNumber err');
  //     if (!retry) {
  //       if ((nativeResponseModel.error != null) && (nativeResponseModel.error!.code == '412')) {
  //         LoggerMp().writeLog('nativeInitAuthenMA');
  //         await NativeBridge.getInstance().nativeInitAuthenMA(_appController.loginAccount, _appController.serialNumber, password);
  //         _processMacqCompareOnlinePass(password, true);
  //         return;
  //       }
  //     }
  //
  //     AppUtils.showDialogErrorNative(context, nativeResponseModel.error,
  //         title: AppStrings.getString(AppStrings.errorCallApiResponseTitle), onPressButton: () {
  //       Get.back();
  //       showBottomSheetConfirmPass(
  //           hideCloseButton: btsPassWordHideCloseButton,
  //           horizontalComponent: btsPassWordHorizontalComponent);
  //     });
  //   }
  // }

  /*_processMacqCompareOfflinePass(String password) async {
    // SecureStorage secureStorage = SecureStorage();
    String accountPass = await LocalStorage().getPassword();
    if(accountPass.isEmpty) {
      accountPass = _appController.loginPassword;
    }
    dev.log('compare pass: enterPass=$password correctPass=$accountPass');
    if (password == accountPass) {
      _gotoScreenHistory();
    } else {
      AppUtils.showDialogError(
          context, AppStrings.getString(AppStrings.errorPassNotMatch),
          onPressButton: () {
        Get.back();
        showBottomSheetConfirmPass(
            hideCloseButton: btsPassWordHideCloseButton,
            horizontalComponent: btsPassWordHorizontalComponent);
      });
    }
  }*/

  // _processLoginBankLevel2(String password) async {
  //   _appController.showLoading();
  //   NativeResponseModel nativeResponseModel = await NativeBridge.getInstance()
  //       .nativeLoginLevel2(_appController.loginAccount, password);
  //   _appController.hideLoading();
  //   if (nativeResponseModel.isSuccess) {
  //     if (currentScreen.value != MAIN_SCREEN_NORMAL_HISTORY) {
  //       _gotoScreenHistory();
  //     }
  //   } else {
  //     AppUtils.showDialogErrorNative(context, nativeResponseModel?.error!,
  //         title: AppStrings.getString(AppStrings.errorCallApiResponseTitle), onPressButton: () {
  //       Get.back();
  //       // showBottomSheetConfirmPass();
  //       showBottomSheetConfirmPass(
  //           hideCloseButton: btsPassWordHideCloseButton,
  //           horizontalComponent: btsPassWordHorizontalComponent);
  //     });
  //   }
  // }

  _processCheckVersion() async {
    dev.log('check version--->');

    BaseResponse response = await ApiClient.instance.request<AppVersion>(
        url: ApiConstant.urlCheckAppVersion,
        method: ApiClient.GET,
        fromJsonModel: (data) => AppVersion.fromJson(data));
    dev.log('version result:${response.result} ');
    if (response.result!) {
      AppVersion? appVersion = response.data;
      if (MyAppController.readerType == MposConstant.READER_SP01) {
        _handlerCheckVersion(appVersion!.sP01!);
      }
      else if (MyAppController.isKozenP12orN4()) {
        _handlerCheckVersion(appVersion!.n31!);
      }
      else if (MyAppController.readerType == MposConstant.READER_SP02) {
        _handlerCheckVersion(appVersion!.sP02!);
      }
    }
  }

  _handlerCheckVersion(MposVersionUpdate mposVersion) async {
    dev.log('versionCode->${mposVersion.vc}');
    if (mposVersion.vc! < 0) {
      return;
    }
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    // DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    // AndroidDeviceInfo androidDeviceInfo = await deviceInfo.androidInfo;
    dev.log(
        'versionSdkInt=${packageInfo.version} buildNumber=${packageInfo.buildNumber}  mposVersion.type=${mposVersion.type}');
    int currVerInstall = -1;
    try {
      currVerInstall = int.parse(packageInfo.buildNumber);
    } catch (e) {
      print(e);
    }
    if (currVerInstall > 0 && currVerInstall < mposVersion.vc!) {
      bool isForceUpdate = false;
      int counterNotify = -1;
//    type:
//       0: -> force update
//       1: -> notify
//       2: -> notify sau bao nhiêu lần sẽ force (max lần notify ở key ntf)
      if (mposVersion.type == 0) {
        isForceUpdate = true;
      } else if (mposVersion.type == 2) {
        counterNotify = await _appController.localStorage.getData(LocalStorage.KEY_NOTIFY_UPDATE_APP, 3);
        if (counterNotify > mposVersion.ntf!) {
          isForceUpdate = true;
        }
      }

      if (counterNotify >= 0) {
        _appController.localStorage
            .saveData(LocalStorage.KEY_NOTIFY_UPDATE_APP, counterNotify + 1);
      }
      dev.log('isForceUpdate=$isForceUpdate');
      _showDialogUpdateApplication(mposVersion, isForceUpdate);
    }
  }

  _showDialogUpdateApplication(
      MposVersionUpdate mposVersion, bool isForceUpdate) {
    String? msg = Get.locale.toString() == AppStrings.localeCodeEn
        ? mposVersion.contentEn
        : mposVersion.contentVi;
    AppUtils.showDialogAlert(context,
        title: 'mPoS.vn',
        description: msg,
        text1stButton: AppStrings.getString(AppStrings.titleUpdate),
        text2ndButton: AppStrings.getString(AppStrings.cancel), onPress1stButton: () {
      AppUtils.openAppByPackageName(mposVersion.link!);
    }, onPress2ndButton: () {
      Get.back();
    }, isTwoButton: !isForceUpdate);
  }

  /*void showBottomSheetConfirmPass(
      {bool hideCloseButton = false, bool? horizontalComponent = false}) {
    btsPassWordHideCloseButton = hideCloseButton;
    btsPassWordHorizontalComponent = horizontalComponent;
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(6)),
      ),
      builder: (context) => BottomSheetConfirmPassword(
        onPressConfirm: _onPressConFirmPassword,
        hideCloseButton: hideCloseButton,
        isHorizonBtnTextField: horizontalComponent,
      ),
      isScrollControlled: true,
      enableDrag: !hideCloseButton,
      isDismissible: false,
    );
  }*/

  _gotoScreenHistory() {
    // initConfigCashierModule();
    Get.toNamed(AppRoute.HISTORY_PAGE);

    // currentScreen.value = MAIN_SCREEN_NORMAL_HISTORY;
    // HistoryController historyController = Get.find<HistoryController>();
    // historyController.getSettleTransactionHistory(false);
  }

  fetchListQr() async {
    _appController.appendLogReq('Static QR - ${_appController.loginAccount}');
    Map params = {
      'serviceName': 'GET_QR_STATIC_LIST',
      'merchantId': _appController.userInfo?.merchantId,
      'muid': _appController.loginAccount
    };
    BaseResponse response = await ApiClient.instance
        .request(url: ApiConstant.urlApi, data: json.encode(params));
    if (response.result!) {
      _appController.localStorage.setUserCacheQR(_appController.loginAccount);
      var qrViStaticInfoData = ((response.data?['qrStaticList'] ?? []) as List)
          .firstWhere((e) => e['qrType'] == 'VAQR', orElse: () => {});
      var qrAGStaticInfoData = ((response.data?['qrStaticList'] ?? []) as List)
          .firstWhere((e) => e['qrType'] == 'LINKCARD', orElse: () => {});

      if (qrViStaticInfoData['qrType'] != null && (qrViStaticInfoData['qrCode'] != null)) {
        // qrCode.value = qrViStaticInfoData['qrCode'] ?? '';
        showStaticQrP12(jsonEncode(qrViStaticInfoData));
      }else {
        fetchStaticQrMacq();
      }

      if (qrAGStaticInfoData['qrType'] != null && (qrAGStaticInfoData['qrContent'] != null)) {
        _appController.localStorage.setStaticAppleGoogleQrP12(qrAGStaticInfoData['qrContent']);
        qrCodeAppleGoogle.value = qrAGStaticInfoData['qrContent'] ?? '';
        isShowQrCodeAppleGgPay.value = true;
      } else {
        String staticQr = await _appController.localStorage.getStaticAppleGoogleQrP12();
        String userCacheQR = await _appController.localStorage.getUserCacheQR();
        LoggerMp().writeLog("use qr cache: $userCacheQR <---> ${_appController.loginAccount}");
        if (staticQr.isNotEmpty && (userCacheQR == _appController.loginAccount)) {
          qrCodeAppleGoogle.value = staticQr;
          isShowQrCodeAppleGgPay.value = true;
        }
      }
      initScreenP12();

      if (qrViStaticInfoData['showDefault'] != null) {
        _appController.saveShowDefaultVietQr(true);
      }else if (qrAGStaticInfoData['showDefault'] != null) {
        _appController.saveShowDefaultVietQr(false);
      }else {
        _appController.saveShowDefaultVietQr(true);
      }
    } else {
      fetchStaticQrMacq();
    }
  }

  fetchStaticQrMacq() async {
    // LoggerMp().writeLog("fetchStaticQr: ${_appController.loginAccount}");
    _appController.appendLogReq('Static QR - fetchStaticQrMacq');
    NativeResponseModel nativeResponseModel = await NativeBridge.getInstance()
        .nativeFetchStaticQr(_appController.loginAccount);

    if (nativeResponseModel.isSuccess) {
      // final Map responseMap = json.decode(nativeResponseModel.data);
      // dev.log('fetchStaticQr success: data=${nativeResponseModel.data}');
      // LoggerMp().writeLog("use qr from server: ");
      LoggerMp().writeLog("use qr new");
      String dataQr = nativeResponseModel.data;
      if (dataQr.isNotEmpty) {
        _appController.localStorage.setStaticQrP12(dataQr);
        // _appController.localStorage.setUserCacheQR(_appController.loginAccount);
        showStaticQrP12(dataQr);
      }
    }else {
      String staticQr = await _appController.localStorage.getStaticQrP12();
      String userCacheQR = await _appController.localStorage.getUserCacheQR();
      LoggerMp().writeLog("use qr cache: $userCacheQR <---> ${_appController.loginAccount}");
      if (staticQr.isNotEmpty && (userCacheQR == _appController.loginAccount)) {
        showStaticQrP12(staticQr);
      }
    }
  }

  showStaticQrP12(String data) {
    if (!_appController.paymentMethod!.vietQrActive) {
      return;
    }
    Map<String, dynamic> jsonData = jsonDecode(data);
    QrStaticResponse dataQrStaticMa = QrStaticResponse.fromJson(jsonData);

    qrCode.value = dataQrStaticMa.qrCode;
    bankNumber.value = buildFormatBankNumber(dataQrStaticMa.vaBankNumber);
    nameMerchant.value = dataQrStaticMa.qrName;
  }

  void autoGotoPushPayment() async {
    bool result = false;
    _appController.hideLoading();
    PPSettingModel ppSettingModel = await PPSettingModel().loadPPSetting();
    if (ppSettingModel.supportScreenStartup){
      result = true;
    }
    if (result) {
      initAndGotoPushPayment();
    }
  }

  void initAndGotoPushPayment() async {
    _appController.appendLog('auto go push payment');
    // initConfigCashierModule();
    Get.toNamed(AppRoute.push_payment);
  }

  initConfigCashierModule() async {
    _appController.appendLog('init module');
    // String configQr = await _appController.localStorage.getConfigQrP12();
    // appCfPushPayment.appType = AppType.PACKAGE;
    appCfPushPayment.appType = AppType.MPOS_LITE;
    appCfPushPayment.setMobileUser(_appController.loginAccount, password: _appController.loginPassword.isNotEmpty ? _appController.loginPassword : '123456');
    try {
      String mpDataLoginModel = await _appController.localStorage.getGatewayMcConfig();
      // appCfPushPayment.setMerchantConfigGatewayMPOS(jsonEncode(_appController.userInfo));

      appCfPushPayment.setMerchantConfigGatewayMPOS(mpDataLoginModel);
    }catch (e) {
      LoggerMp().writeLog('setMerchantConfigGatewayMPOS err ${e.toString()}');
    }
    appCfPushPayment.setIsKozenP12(MyAppController.isKozenP12orN4());

    await mp.AppUtils.initDeviceInfo().timeout(Duration(milliseconds: 10000));

    // NativeResponseModel nativeResponseModelTID = await NativeBridge.getInstance().nativeGetTID();
    // NativeResponseModel nativeResponseModelMID = await NativeBridge.getInstance().nativeGetMID();
    NativeResponseModel nativeResponseModel = await NativeBridge.getInstance().nativeGetTidAndMid().timeout(Duration(seconds: 2));
    Map data = jsonDecode(nativeResponseModel.data);
    appCfPushPayment.setTidMid(data['tid'] ?? '', data['mid'] ?? '');
    LocalizationCustom().setLanguage((Get.locale.toString() == 'vi_VN') ? 'vi' : 'en');
  }

  String createUdidByOrderId(String orderId) {
    return 'SOCKET[${_appController.loginAccount},$orderId]-${Uuid().v4()}';
  }

  _callNativeMethodScanCard(String totalAmount, String udid, int autoCloseFinishScreen) async {
    Get.find<PaymentInitController>().permitScanCard = false;
    Map params = {
      'amount': totalAmount,
      'phone': '',
      'email': '',
      'description': '',
      'paymentID': udid
    };
    NativeResponseModel nativeResponseModel = await NativeBridge.getInstance().nativePaymentScanCard(params);
    Get.find<PaymentInitController>().permitScanCard = true;
    // _appController.hideLoading();
    if (nativeResponseModel.isSuccess) {
      final Map responseMap = json.decode(nativeResponseModel.data);
      responseMap['emailSendReceipt'] = '';
      Map? result = responseMap['result'];
      if (result != null && result['status'] != 'CANCEL') {
        await Get.toNamed(AppRoute.payment_finish_screen,
            arguments: PaymentFinishArguments(MposConstant.CARD_PAYMENT, responseMap, '', autoCloseFinishScreen: autoCloseFinishScreen));
      }
    } else {
      await Get.toNamed(AppRoute.payment_finish_screen,
          arguments: PaymentFinishArguments(
              MposConstant.CARD_PAYMENT,
              null,
              AppUtils.getErrorByCode(context, nativeResponseModel.error?.code,
                  defaultMessage: nativeResponseModel.error?.message),
              autoCloseFinishScreen: autoCloseFinishScreen));
    }
  }

  void _callNativeMethodScanCardDeposit(String depositCode, String amount, String orderID, String phone, int autoCloseFinishScreen) async {
    LoggerMp().writeLog('depositID: ${depositCode.toUpperCase()}');
    var udid = 'SOCKET[${_appController.loginAccount},$orderID, $depositCode]-${Uuid().v4()}';
    Map paymentInfo = Map();
    paymentInfo['amount'] = amount.replaceAll(".", "");
    paymentInfo['udid'] = udid;
    paymentInfo['depositID'] = depositCode.toUpperCase();
    paymentInfo['customerPhone'] = phone;
    paymentInfo['trxType'] = configModule.TrxType.DepositPayment.index;
    String stringJson = jsonEncode(paymentInfo);
    print("JSON POST: " + stringJson);

    NativeResponseModel nativeResponseModel = await NativeBridge.getInstance().nativePaymentScanCardDeposit(stringJson);
    Get.find<PaymentInitController>().permitScanCard = true;
    // _appController.hideLoading();
    if (nativeResponseModel.isSuccess) {
      final Map responseMap = json.decode(nativeResponseModel.data);
      responseMap['emailSendReceipt'] = '';
      Map? result = responseMap['result'];
      if (result != null && result['status'] != 'CANCEL') {
        await Get.toNamed(AppRoute.payment_finish_screen,
            arguments: PaymentFinishArguments(MposConstant.CARD_PAYMENT, responseMap, '', autoCloseFinishScreen: autoCloseFinishScreen));
      }
    } else {
      await Get.toNamed(AppRoute.payment_finish_screen,
          arguments: PaymentFinishArguments(
              MposConstant.CARD_PAYMENT,
              null,
              AppUtils.getErrorByCode(context, nativeResponseModel.error?.code,
                  defaultMessage: nativeResponseModel.error?.message),
              autoCloseFinishScreen: autoCloseFinishScreen));
    }
  }

  /// ------ OnClick ------
  onPressAccountProtect() async {
    Get.back();
    Get.toNamed(AppRoute.change_pass_screen);
  }

  onPressSummary() async {
    Get.back();
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(6)),
      ),
      builder: (context) {
        return TimeRangeWidget(
          maxRange: Duration(days: 7),
          errorRange: AppStrings.getString(AppStrings.errorTimeRangeInvalid7),
          onPressConfirm: (startTime, endTime) {
            _doTransactionSummary(startTime, endTime);
          },
        );
      },
      isScrollControlled: true,
      isDismissible: false,
    );
  }

/*  onPressChangeLanguageApp() async {
    Get.back();
    String currentLanguageApp = await LocalStorage().getLanguageApp();
    if (currentLanguageApp.length > 0) {
      String changeToLang =
      currentLanguageApp == 'vi' ? 'English' : 'Tiếng Việt';
      AppUtils.showDialogAlert(
        context,
        description: 'Đổi ngôn ngữ / Change language',
        isTwoButton: true,
        text1stButton: AppStrings.getString(AppStrings.cancel),
        text2ndButton: changeToLang,
        onPress2ndButton: () {
          Get.back();
          Get.updateLocale(currentLanguageApp == 'vi'
              ? Locale('en', 'US')
              : Locale('vi', 'VN'));
          LocalStorage()
              .setLanguageApp(currentLanguageApp == 'vi' ? 'en' : 'vi');
          NativeBridge.getInstance()
              .nativeSetLanguage(currentLanguageApp == 'vi' ? 'en' : 'vi');
        },
      );
    }
  }*/

  onPressShowDeceptionQr() {
    LoggerMp().writeLog('onPressShowDeceptionQr');
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(6)),
      ),
      builder: (context) {
        return BaseBottomSheet(
          hideCloseButton: false,
          onPressClose: () => Get.back(),
          title: _appController.isShowVietQr.value ? AppStrings.getString(AppStrings.tvScanQr) : AppStrings.getString(AppStrings.tv_title_apple_google_qr),
          titleAlign: TextAlign.start,
          child: Container(
            height: 250,
            padding: EdgeInsets.symmetric(vertical: 15, horizontal: 15),
            child: Column(
              children: [
                Expanded(
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 20),
                    child: Center(
                      child: Text(
                        '${(_appController.deviceIdentifier.isEmpty)
                            ? AppStrings.getString(AppStrings.msg_warning_get_token_firebase)
                            : _appController.isShowVietQr.value ? AppStrings.getString(AppStrings.msg_deception_static_qr) : AppStrings.getString(AppStrings.msg_deception_static_qr_gg_apple)}',
                        style: style_S16_W400_BlackColor,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ),
                Container(
                  width: Get.width,
                  child: CommonButton(
                    borderCircular: 20,
                    minWidth: Get.width - AppDimens.spaceLarge32,
                    onPressed: () => {Get.back()},
                    title: AppStrings.getString(AppStrings.tv_know_guide),
                    color: AppColors.redButton,
                    textColor: AppColors.white,
                    fontFamily: kFontFamilyBeVietnamPro,
                    textSize: AppDimens.textSizeLarge,
                    elevation: 0,
                    height: 60,
                  ),
                )
              ],
            ),
          ),
        );
      },
      isScrollControlled: true,
      isDismissible: false,
    );
  }

  onPressQuickPayment(bool isHomeQrP12) {
    if (MyAppController.isKozenP12orN4()) {
      // if (currentScreen.value == MAIN_SCREEN_P12_QR) {
      //   currentScreen.value = MAIN_SCREEN_P12_ENTER_AMOUNT;
      // }
      currentScreen.value = MAIN_SCREEN_P12_ENTER_AMOUNT;
      if (isHomeQrP12) {
        initScreenP12();
        // currentScreen.value = MAIN_SCREEN_P12_QR;
      }
      _processErrorUnSignAndCallingApp();
    } else if (currentScreen.value != MAIN_SCREEN_NORMAL_PAY) {
      currentScreen.value = MAIN_SCREEN_NORMAL_PAY;
      // must resign before make new trans
      _processErrorUnSignAndCallingApp();
    }
  }

  onPressInstallment() {
    Get.back();
    if (_appController.userInfo?.installmentInfo == null ||
        _appController.userInfo!.installmentInfo!.length == 0) {
      Get.toNamed(AppRoute.webview_info_screen,
          arguments:
          WebViewInfoArguments(MposConstant.URL_LANDING_INSTALLMENT, null));
    } else {
      Get.toNamed(AppRoute.installment_list_bank_screen);
    }
  }

  onPressHomeItemButton(MenuHome homeButtonItem) async {
    switch (homeButtonItem.serviceCode) {
      case MposConstant.cardPayment:
        Get.toNamed(AppRoute.payment_init_screen);
        break;
      case MposConstant.installmentPayment:
        onPressInstallment();
        break;
      case MposConstant.history_tag:
      // if (widget.onPressHistory != null) widget.onPressHistory();
        break;
      default:
        break;
    }
  }

  onPressKeyBack() {
    int timeBetween2Press =
        DateTime.now().millisecondsSinceEpoch - lastTimePressBackMs;

    if (timeBetween2Press > 1500) {
      Fluttertoast.showToast(
          msg: AppStrings.getString(AppStrings.confirmExitApp)!,
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.CENTER,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.black38,
          textColor: Colors.white,
          fontSize: AppDimens.textSizeMedium);
      lastTimePressBackMs = DateTime.now().millisecondsSinceEpoch;
      return false;
    }
    return true;
  }

  onPressSettings() {
    Get.back();
    Get.toNamed(AppRoute.settings_screen);
  }

  onPressLoyalCard() {
    Get.back();
    Get.toNamed(AppRoute.SME_CARD_PAGE);
  }

  onPressCloseGuideP12() {
    isShowGuideP12.value = false;
    _appController.localStorage
        .saveData(LocalStorage.KEY_NEED_GUIDE_P12, false);
  }

  // onPressLogout() {
  //   Get.back();
  //   AppUtils.showDialogAlert(
  //     context,
  //     description: AppStrings.messageConfirmLogout.tr,
  //     isTwoButton: true,
  //     text1stButton: AppStrings.cancel.tr,
  //     text2ndButton: AppStrings.ok.tr,
  //     onPress2ndButton: () {
  //       Get.back();
  //       doLogout();
  //     },
  //   );
  // }

  doLogout() async {
    Map params = {
      "serviceName": "GATEWAY_MERCHANT_LOGOUT",
      "merchantId": _appController.userInfo?.merchantId,
      "readerSerial": _appController.serialNumber,
      "deviceIdentifier": '',
      "os": Platform.isAndroid ? 'Android' : 'iOS',
      "muid": _appController.loginAccount,
    };

    _appController.showLoading(message: 'Đang đăng xuất');
    BaseResponse response =
    await ApiClient.instance.request(data: json.encode(params));
    _appController.hideLoading();
    if (response.result!) {
      NativeBridge.getInstance().nativeClearDataAuto();
      NativeBridge.getInstance().closeAndResetSocket();
      var depositCacheData = await SystemPreference.getData('listDepositCode', '');
      LocalStorage().clearAll();
      _appController.loginAccount = '';
      // _appController.serialNumber = '';
      // AppController.readerType = -1;
      _appController.listTypePayment.clear();
      _appController.userInfo = null;
      _appController.deviceIdentifier = '';
      _appController.isShowAdvancedSettings = false;
      _appController.localStorage
          .saveData(LocalStorage.KEY_NEED_SAY_HELLO, true);
      AppConfiguration().clearAll();
      SystemPreference.saveData('listDepositCode', depositCacheData);
      Get.offAllNamed(AppRoute.login_screen);
      _appController.stopBackgroundService();
    } else {
      AppUtils.showDialogError(
          context, '${response.code ?? ''}: ${response.message ?? ''}');
    }
  }

  _doTransactionSummary(DateTime startTime, DateTime endTime) async {
    // if (startTime == null || endTime == null) return;
    Map params = {
      'serviceName': 'SUMMARY_TRANSACTION_WORKDAY',
      'merchantId': _appController.userInfo?.merchantId ?? 0,
      'muid': _appController.loginAccount,
      'receiptWidth': 380,
      'summaryTransWorkDayFrom':
          DateFormat('HH:mm dd/MM/yyyy', 'vi_VN').format(startTime),
      'summaryTransWorkDayTo':
          DateFormat('HH:mm dd/MM/yyyy', 'vi_VN').format(endTime),
    };
    if (MyAppController.readerType == MposConstant.READER_SP01 ||
        MyAppController.readerType == MposConstant.READER_SP02) {
      params["notReceipt"] = false;
    }
    _appController.showLoading(message: 'Đang tổng kết');
    BaseResponse response = await ApiClient.instance.request<SummaryResponse>(
        url: ApiConstant.urlApi,
        data: json.encode(params),
        fromJsonModel: (data) => SummaryResponse.fromJson(data));
    _appController.hideLoading();
    if (response.result!) {
      SummaryResponse? summaryResponse = response.data as SummaryResponse?;
      showModalBottomSheet(
        context: context,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(6)),
        ),
        builder: (context) {
          return SummaryWidget(
            startDate: startTime,
            endDate: endTime,
            summaryData: summaryResponse,
            onPressPrint: () async {
              // Get.back();
              if (!isNullEmpty(summaryResponse?.base64ImageReceipt)) {
                await NativeBridge.getInstance()
                    .nativePrintBase64(summaryResponse!.base64ImageReceipt);
                // await NativeBridge.getInstance().nativePrintPush(3);

                // base64ImageReceipt.value = summaryResponse.base64ImageReceipt;
                // _appController.showLoading();
                // screenshotController
                //     .capture(
                //   delay: Duration(seconds: 2),
                //   pixelRatio: 1.0,
                // )
                //     .then((Uint8List captureImage) async {
                //   String imageB64 = base64Encode(captureImage);
                //   await NativeBridge.getInstance().nativePrintBase64(imageB64);
                //   await NativeBridge.getInstance().nativePrintPush(4);
                //   base64ImageReceipt.value = '';
                //   _appController.hideLoading();
                // }, onError: (e) {
                //   base64ImageReceipt.value = '';
                //   _appController.hideLoading();
                // });
              } else {
                AppUtils.showDialogError(context,
                    AppStrings.getString(AppStrings.errorSummaryPrint));
              }
            },
          );
        },
        isScrollControlled: true,
        isDismissible: false,
      );
    } else {
      AppUtils.showDialogError(
          context, '${response.code ?? ''}: ${response.message ?? ''}');
    }
  }

  // todo fake test
  /*_fakePushMsgToQueue(){
    dev.log('--------starting fake push message');
    List<Map<String, dynamic>> arr = [];
    try {
      for (int i = 0; i <15; i++) {
            Map<String, dynamic> obj ={
              "amount":"${(i*1000)}d",
            };// new Map<String, dynamic>();
            // arr.add(obj);
            emqxConnect.fakeReceiveMsgWhenUseQueue(obj);
          }
    } catch (e) {
      dev.log(e.toString());
    }
    dev.log('end fake push msg');
  }*/

  /*onPressGotoNotificationScreen() async {
    // try {
    //   if(emqxConnect!=null) {
    //         emqxConnect.unSubscribeTopic();
    //   }
    // } catch (e) {
    //   print(e);
    // }
    // _initMqtt();
    // _fakePushMsgToQueue();
    // testSpeak();
    testSpeakTingbox();
  }*/
  onPressGotoNotificationScreen() async {
    // await initConfigCashierModule();
    var result = await Get.toNamed(
        AppRoute.NOTIFICATION_LIST,
        arguments: {
          'tokenInput': _appController.tokenCountNoti,
          'isProdEnv': bc.BuildConstants.currentEnvironment == bc.Environment.PROD,
        });
    if(result != null){
      _countNotification();
    }
  }

  onPressNewPayment() {
    LoggerMp().writeLog('onPressNewPayment');
    Get.back();
    onPressQuickPayment(false);
    // if (currentScreen.value != 0) {
    //   currentScreen.value = 0;
    // }
  }

  onPressPushPayment() async {
    LoggerMp().writeLog('onPressPushPayment');
    Get.back();
    if ((_appController.userInfo == null) ||
        (_appController.userInfo?.enableRethinkdb == false)) {
      LoggerMp().writeLog('onPressPushPayment ${AppStrings.getString(AppStrings.msg_warning_not_support_pushpayment)}');
      AppUtils.showDialogError(
          context, '${AppStrings.getString(AppStrings.msg_warning_not_support_pushpayment)}');
      return;
    }

    initAndGotoPushPayment();
  }

  onPressOnlineSupport() async {
    Get.back();
    Get.toNamed(AppRoute.webview_info_screen,
        arguments: WebViewInfoArguments(
            '${ApiConstant.urlSupportCenter}', AppStrings.getString(AppStrings.menuContact)));
  }

  onPressManual() async {
    Get.back();
    Get.toNamed(AppRoute.webview_info_screen,
        arguments: WebViewInfoArguments(
            '${ApiConstant.urlManual}', AppStrings.getString(AppStrings.menuGuide)));
  }

  onPressHistory() async {
    _appController.appendLog('History');
    // LoggerMp().writeLog('onPressHistory');
    Get.back();

    // todo fake test
    // runTestMacq();

    if(currentScreen.value == MAIN_SCREEN_NORMAL_HISTORY) {
      return;
    }
    _gotoScreenHistory();
    // if (_appController.userInfo!.isMacqFlow == 1) {
    //   if (_appController.userInfo!.permitVoid == 1) {
    //     showBottomSheetConfirmPass(horizontalComponent: true);
    //   } else {
    //     _gotoScreenHistory();
    //   }
    // } else {
    //   _appController.showLoading();
    //   NativeResponseModel nativeResponseModel = await NativeBridge.getInstance()
    //       .nativeCheckLoginLevel2(
    //       _appController.loginAccount, _appController.loginPassword);
    //   _appController.hideLoading();
    //   if (nativeResponseModel.isSuccess == true) {
    //     if (currentScreen.value != MAIN_SCREEN_NORMAL_HISTORY) {
    //       _gotoScreenHistory();
    //     }
    //   } else {
    //     showBottomSheetConfirmPass();
    //   }
    // }
  }

  runTestMacq() async {
    dev.log('---- run test mqtt5 ----');
    // TestMqtt5Server mqtt5server = new TestMqtt5Server();
    // mqtt5server.runTestConnect();
  }

  // _onPressConFirmPassword(String password) {
  //   if (_appController.userInfo!.isMacqFlow == 1) {
  //     if (MyAppController.isKozenP12orN4()) {
  //       _processMacqCompareOnlinePass(password, false);
  //     } else {
  //       _processMacqCompareOfflinePass(password);
  //     }
  //   } else {
  //     _processLoginBankLevel2(password);
  //   }
  // }

  ///      ------ Notification ------    */
  void initNotification() {
    _countNotification();
    _subscribe();
  }

  _subscribe() async {
    try {
      FirebaseMessaging messaging = FirebaseMessaging.instance;
      String? tokenFirebase = await messaging.getToken();
      subscribe(
        tokenFirebase: tokenFirebase,
        // isProdEnv: bc.BuildConstants.currentEnvironment == bc.Environment.PROD,
        tokenInput: _appController.tokenCountNoti,
      );
    } catch (e) {
     LoggerMp().writeLogErrFunc('LOGCRASH', e);
    }
  }

  _countNotification(){
    countUnReadNotification(
        userLogin: _appController.loginAccount,
        isProdEnv: bc.BuildConstants.currentEnvironment == bc.Environment.PROD,
        tokenInput: _appController.tokenCountNoti
    );
  }

  void initFireBaseMessasing() async {
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      // LoggerMp().writeLog('new message');
      String currentRoute = Get.currentRoute;
      if (currentRoute == AppRoute.listOrderPage || currentRoute == AppRoute.push_payment){
        return;
      }
      if (currentRoute == AppRoute.qr_code_p12_screen) {
        LoggerMp().writeLog('Current screen is dynamic QR');
        return;
      }
      _appController.appendLogRes('new Noti');
      if (message.data.isNotEmpty) {
        String jsonData = jsonEncode(message.data);
        // LoggerMp().writeLog("notification data=$jsonData");
        _appController.appendLog("notification data=$jsonData");
        try {
          DataNotification dataNotification = DataNotification.fromJson(json.decode(jsonData));
          _handlerDataNotification(dataNotification);
        } catch (e) {
          LoggerMp().writeLog("notification err ${e.toString()}",
              actionName: LoggerMp.LOGGER_TYPE_ERROR);
          LoggerMp().processPushLog();
        }
      }
    });
  }

  void _handlerDataNotification(DataNotification dataNotification) {
    if(MyAppController.isKozenMposPro()) {
      return;
    }
    if ((dataNotification.payload!['type'] ?? '').toString().toUpperCase() ==
        'UPDATE_QR_STATIC') {
      // update new static qr
      fetchListQr();
      return;
    }

    String typePayment = dataNotification.sub_category ?? (dataNotification.payload!['sub_category'] ?? '');
    // LoggerMp().writeLog("typePayment: $typePayment");
    _appController.appendLog("typePayment: $typePayment");

    switch (typePayment) {
      case MposConstant.TYPE_NOTI_QR_STATIC:
        _handlerNotiQr(dataNotification);
        break;
      case MposConstant.TYPE_NOTI_QR_DYNAMIC:
        break;
      default:
        break;
    }
  }

  void _handlerNotiQr(DataNotification dataNotification) {
    var orderCode = "";
    try{
      orderCode = dataNotification.payload?['orderCode'];
    }catch (exception){
      LoggerMp().writeLog("Exception handle QR Static: ${exception.toString()}");
    }
    if (orderCode.isEmpty){
      return;
    }
    // Case noti đến nhưng gd đã thanh toán
    if (_appController.listOrderPaid.contains(orderCode)) {
      LoggerMp().writeLog("notification duplicate $orderCode");
      return;
    }
    // continue handler notification
    if (listDataNoti.isNotEmpty && isContainsNotiWaitShow(orderCode)) {
      LoggerMp().writeLog("list wait show have order");
      return;
    }
    // LoggerMp().writeLog("prepare notification for showing");
    // LoggerMp().writeLog("prepare showing noti");
    _appController.appendLog("prepare showing noti orderCode: $orderCode");
    listDataNoti.add(dataNotification);
    checkOrderSuccessAndShowResult();
  }

  bool isContainsNotiWaitShow(String orderCode) {
    bool result = false;
    for (DataNotification notification in listDataNoti) {
      // if ((notification.payload?['orderCode'] != null) && (notification.orderCode == orderCode)) {
      if ((notification.payload?['orderCode'] ?? '') == orderCode) {
        result = true;
        break;
      }
    }

    return result;
    // listDataNoti.asMap().containsValue(dataNotification.orderCode)
  }

  void checkOrderSuccessAndShowResult() {
    if (!isShowResult) {
      dev.log("!isShowResult");
      return;
    }

    if (listDataNoti.isEmpty) {
      isShowResult = true;
      dev.log("!listDataNoti.isEmpty");
      return;
    }
    _processSoundAndWidgetQrResult(listDataNoti[0]);
  }

  void _processSoundAndWidgetQrResult(DataNotification dataNotification) async {
    // LoggerMp().writeLog("_processSoundAndWidgetQrResult");
    // LoggerMp().writeLog('noti ok, sound and show result');
    _appController.appendLog('noti ok, sound and show result');
    //save order paid
    if (dataNotification.payload!['orderCode'] != null) {
      _appController.listOrderPaid.add('${dataNotification.payload?['orderCode']}');
    }
    LocalStorage().setListOrderPaid(_appController.listOrderPaid);
    LocalStorage().setLastTimeCacheQrPaid(DateFormat('dd/MM/yy').format(DateTime.now()));

    // LoggerMp().writeLog("amount sound is: ${dataNotification.payload?['amount'] ?? 'null'}");
    String amountString = dataNotification.payload?['amount'].toString() ?? '';
    if (amountString.isNotEmpty) {
      // LoggerMp().writeLog(AppStrings.getString(AppStrings.msg_noti_payment_success)!.tr.replaceFirst('%s', amountString));
      _appController.appendLog(AppStrings.getString(AppStrings.msg_noti_payment_success)!.tr.replaceFirst('%s', amountString));
      // await flutterTts?.speak(AppStrings.msg_noti_payment_success.replaceFirst('%s', amountString).tr);

      // showWidgetSuccessScanQr(dataNotification);

      // todo english sound
      // flutterTts?.speak(AppStrings.getString(AppStrings.msg_noti_payment_success)?.replaceFirst('%s', amountString) ?? '');
      if(!_appController.isMqttConnected()) {
        showWidgetSuccessScanQr(dataNotification);
        TtsControl().speak(AppStrings.getString(AppStrings.msg_noti_payment_success)?.replaceFirst('%s', amountString) ?? '');
      }
    } else {
      dismissDlgAndContinueShowNoti(dataNotification);
    }
    LoggerMp().processPushLog();
  }

  //Handler message mqtt
  void initCallbackStatusEmqx(var status) {
    print('status emqx: ${status['status']}');

    if (status['status'] != statusEmqx.value) {
      statusEmqx.value = status['status'];
    }
  }

  void initCallbackHandleMqttMsg(var map){
    dev.log('callback mqtt message: ${jsonEncode(map)}');
    if (map == null) {
      return;
    }
    String broadcastType = map['broadcast_type']??'';
    if (broadcastType == '3' || broadcastType == '4') {
      _updateStaticQrInMainScreen(map);
    }

    if (broadcastType == '3' || broadcastType == '1') {
      if (map['money'] == '0') {
        LoggerMp().writeLog('${map['money']}');
        return;
      }
      // show toast success message
      DataNotification dataNotification = DataNotification();
      dataNotification.payload = Map();
      dataNotification.payload?['amount'] = map['money'];

      // if Qr tĩnh -> hiển thị thông báo success luôn
      // if Qr động -> check current screen qr động và type noti là qr động
      showWidgetSuccessScanQr(dataNotification);

      String currentRoute = Get.currentRoute;
      if (currentRoute == AppRoute.qr_code_p12_screen) {
        LoggerMp().writeLog('Current screen is dynamic QR');
        Get.find<QrCodeP12Controller>().handlerQrSuccessMposPro(map['money']);
        return;
      }
    }
  }

  void _updateStaticQrInMainScreen(var map) {
    //{"qr_type":"VIETQR","account_number":"M828S92115475746183","device_id":"SP0524B0200008",
    // "biz_type":"1","description":"","homeqrcode":"00020101021138630010A000000727013300069704070119M828S921154757461830208QRIBFTTA53037045802VN6204080063049637",
    // "broadcast_type":"4","tts":"100","qr_id":"VA_STATIC50488454","account_name":"HOAITHANH1",
    // "mobile_user":"VIETQR|sp5huyentt","request_id":"YyAZy1739266620027"}
    if (map['homeqrcode'] != null && map['homeqrcode'] !='') {
      qrCode.value = map['homeqrcode'];
    }
    if (map['account_name']!=null && map['account_name'] !='' ) {
      nameMerchant.value = map['account_name'];
    }
  }

  /*void testSpeakTingbox() async {
    NPTingTingSpeakerHandler.receivedCurrencyAmountSound(35000);
  }
  void testSpeak() async {
    // flutterTts?.speak(AppStrings.getString(AppStrings.msg_noti_payment_success)?.replaceFirst('%s', "10000") ?? '');
    TtsControl().speak(AppStrings.getString(AppStrings.msg_noti_payment_success)?.replaceFirst('%s', "10000") ?? '');
  }*/

  void dismissDlgAndContinueShowNoti(DataNotification dataNotification) {
    isShowResult = true;
    listDataNoti.remove(dataNotification);
    if (autoDissmisDlgSuccesTimer != null) {
      autoDissmisDlgSuccesTimer?.cancel();
      autoDissmisDlgSuccesTimer = null;
    }
    // Get.back();
    checkOrderSuccessAndShowResult();
  }

  showWidgetSuccessScanQr(DataNotification dataNotification) {
    String amount = AppUtils.formatCurrency(int.parse('${dataNotification.payload?['amount'] ?? '0'}')) + ' VND';
    // String time = DateFormat('HH:mm').format(DateTime.fromMillisecondsSinceEpoch(int.tryParse('${dataNotification.payload?['timePayment'] ?? '0'}')!));
    // String time = DateFormat('HH:mm').format(dataNotification.payload?['timePayment'] ? DateTime.now() : DateTime.fromMillisecondsSinceEpoch(int.tryParse('${dataNotification.payload?['timePayment']}')!));

    int? timeMillis = int.tryParse('${dataNotification.payload?['timePayment']}');
    DateTime timePayment = timeMillis != null
        ? DateTime.fromMillisecondsSinceEpoch(timeMillis)
        : DateTime.now();
    String time = DateFormat('HH:mm').format(timePayment);

    String row1 = dataNotification.payload?['cardholderName'] ?? '/';
    String row2 = dataNotification.payload?['pan'] ?? '';
    String icon = dataNotification.payload?['icon'] ?? '';

    LoggerMp().writeLog('showDialogSuccess $amount - $icon - $time - $row1 - $row2');

    try {
      MPUtil.showTopNotification(Get.find<MainController>().context,
          logoLeading: icon.isNotEmpty ? Padding(
            padding: EdgeInsets.all(4.0),
            child: MPImageWidget.network(
              path: icon,
            ),
          ) : Image.asset(AppImages.ic_vietqr_mini),
          title: amount,
          time: time,
          row1: row1,
          row2: row2,
          onPress: () {},
          textButtonClose: AppStrings.getString(AppStrings.close),
          fontFamily: kFontFamilyBeVietnamPro, onDismiss: () {
            dismissDlgAndContinueShowNoti(dataNotification);
          }, showButtonClose: true, displayDuration: Duration(seconds: 20));
    }catch (e) {
      LoggerMp().writeLog('DialogSuccessScanQr err: ${e.toString()}');
    }

    LoggerMp().writeLog('DialogSuccessScanQr end');
  }

  ///      ------ Socket Tcp/IP ------    */

  void handlerAddOrder(dynamic data, String currentRoute) async {
    if (data != null) {
      var mapArgument = jsonDecode(data);
      int totalAmount = mapArgument["totalAmount"] ?? 0;
      String typePayment = mapArgument["typePayment"] ?? "";
      String orderId = mapArgument["orderId"] ?? "";
      int autoDismissDlgTimer = mapArgument["autoDismissDlgTimer"] ?? 15;  //default timer close finish screen

      LoggerMp().writeLog('SOCKET - addOrder ${mapArgument.toString()}');
      // LoggerMp().writeLog('SOCKET - handlerAddOrder: totalAmount = $totalAmount; typePayment = $typePayment; orderID = $orderId; autoDismisDlg = $autoDismissDlgTimer');
      if (currentRoute == AppRoute.main_screen) {
        if (typePayment == MposConstant.TYPE_CARD) {
          if (!Get.find<PaymentInitController>().permitScanCard) {
            LoggerMp().writeLog('SOCKET - permitScanCard can not enable');
            NativeBridge.getInstance().nativeCallBackResultPaymentSocket('TRANS_ERROR', orderId, totalAmount.toString(), "", "", errCode: MposConstant.ERROR_NOT_SUPPORT);
            return;
          }
          _callNativeMethodScanCard(totalAmount.toString(), createUdidByOrderId(orderId), autoDismissDlgTimer);
        }else if (typePayment == MposConstant.TYPE_QRCODE) {
          String paymentMethod = mapArgument["paymentMethod"] ?? "";
          LoggerMp().writeLog('SOCKET - paymentMethod = $paymentMethod');
          handlerOrderWithPaymentMethod(totalAmount, orderId, paymentMethod, autoDismissDlgTimer);
        }
      }else {
        NativeBridge.getInstance().nativeCallBackResultPaymentSocket('TRANS_ERROR', orderId, totalAmount.toString(), "", "", errCode: MposConstant.ERROR_NOT_EXITS_SCREEN);
      }
    }
  }

  void handlerAddOrderDeposit(dynamic data, String currentRoute) async {
    if (data != null) {
      var mapArgument = jsonDecode(data);
      int totalAmount = mapArgument["totalAmount"] ?? 0;
      String depositId = mapArgument["depositID"] ?? "";
      String orderId = mapArgument["orderId"] ?? "";
      String phone = mapArgument["phone"] ?? "";
      int autoDismissDlgTimer = mapArgument["autoDismissDlgTimer"] ?? 15;  //default timer close finish screen

      LoggerMp().writeLog('SOCKET - ${mapArgument.toString()}');
      if (currentRoute == AppRoute.main_screen) {
        if (!Get.find<PaymentInitController>().permitScanCard && !_appController.paymentMethod!.depositPayment) {
          LoggerMp().writeLog('SOCKET - permitScanCard can not enable');
          NativeBridge.getInstance().nativeCallBackResultPaymentSocket('TRANS_ERROR', orderId, totalAmount.toString(), "", "", errCode: MposConstant.ERROR_NOT_SUPPORT);
          return;
        }
        _callNativeMethodScanCardDeposit(depositId, totalAmount.toString(), orderId, phone, autoDismissDlgTimer);
      }else {
        NativeBridge.getInstance().nativeCallBackResultPaymentSocket('TRANS_ERROR', orderId, totalAmount.toString(), "", "", errCode: MposConstant.ERROR_NOT_EXITS_SCREEN);
      }
    }
  }

  void handlerSetFinalAmountDeposit(dynamic data, String currentRoute) async {
    if (data != null) {
      var mapArgument = jsonDecode(data);
      int totalAmount = mapArgument["totalAmount"] ?? -1;
      String depositId = mapArgument["depositID"] ?? "";

      LoggerMp().writeLog('SOCKET - ${mapArgument.toString()}');
      if (currentRoute == AppRoute.main_screen) {
        // Hiển thị dialog loading.
        _appController.showLoading(message: 'Đang xử lý ...');
        if (totalAmount == -1 || depositId.isEmpty) {
          LoggerMp().writeLog('SOCKET - amount or depositId null');
          NativeBridge.getInstance().nativeCallBackResultSetFinalDepositSocket('${MposConstant.ERROR_DATA_INVALID}', '', depositId, totalAmount.toString());
          return;
        }

        Map params = {
          'merchantId': _appController.userInfo?.merchantId,
          'depositRefCode': depositId,
          'totalAmount': totalAmount,
        };
        LoggerMp().writeLog('param finish: ${params.toString()}');
        NativeResponseModel result = await NativeBridge.getInstance().callNativeRequest('/api/corewf/update-amount-deposit', jsonEncode(params));
        _appController.hideLoading();
        if (result.isSuccess) {
          LoggerMp().writeLog('result: ${result.data}');
          Map responseMap = json.decode(result.data);
          NativeBridge.getInstance().nativeCallBackResultSetFinalDepositSocket('00', '', depositId, totalAmount.toString(), lstTrxOrder: '${jsonEncode(responseMap['data'])}');
        }else {
          LoggerMp().writeLog('result: ${result.error?.message}');
          NativeBridge.getInstance().nativeCallBackResultSetFinalDepositSocket('${result.error?.code}', '${result.error?.message}', depositId, totalAmount.toString());
        }
      }else {
        NativeBridge.getInstance().nativeCallBackResultSetFinalDepositSocket('${MposConstant.ERROR_NOT_EXITS_SCREEN}', '', depositId, totalAmount.toString());
      }
    }
  }

  void handlerSetFinishDeposit(dynamic data, String currentRoute) async {
    if (data != null) {
      var mapArgument = jsonDecode(data);
      int totalAmount = mapArgument["totalAmount"] ?? -1;
      int finalAmount = mapArgument["finalAmount"] ?? -1;
      String depositId = mapArgument["depositID"] ?? "";
      String wfId = mapArgument["wfId"] ?? "";

      LoggerMp().writeLog('SOCKET - ${mapArgument.toString()}');
      if (currentRoute == AppRoute.main_screen) {
        _appController.showLoading(message: 'Đang xử lý ...');
        if (totalAmount == -1 || depositId.isEmpty || finalAmount == -1) {
          LoggerMp().writeLog('SOCKET - amount or depositId null');
          NativeBridge.getInstance().nativeCallBackResultFinishDepositSocket('${MposConstant.ERROR_DATA_INVALID}', '', depositId, totalAmount.toString());
          return;
        }

        if (_appController.userInfo?.allowDeposit == '1') {
          // type 1
          Map params = {
            'wfId': wfId,
            'depositRefCode': depositId,
            'amount': finalAmount.toString(),
            'amountDeposit': totalAmount.toString(),
          };
          LoggerMp().writeLog('param finish: ${params.toString()}');
          NativeResponseModel result = await NativeBridge.getInstance().callNativeRequest('/api/corewf/settle-deposit', jsonEncode(params));
          _appController.hideLoading();
          if (result.isSuccess) {
            LoggerMp().writeLog('result: ${result.data}');
            // Map responseMap = json.decode(result.data);
            NativeBridge.getInstance().nativeCallBackResultFinishDepositSocket('00', '', depositId, totalAmount.toString(), data: '[${result.data}]');
          }else {
            LoggerMp().writeLog('result: ${result.error?.message}');
            NativeBridge.getInstance().nativeCallBackResultFinishDepositSocket('${result.error?.code}', '${result.error?.message}', depositId, totalAmount.toString());
          }
        }else {
          // type 2
          Map params = {
            'merchantId': AppConfiguration().rethinkConfig.merchantId.toString(),
            'depositRefCode': depositId,
            'totalAmount': totalAmount,
          };
          LoggerMp().writeLog('param finish: ${params.toString()}');
          NativeResponseModel result = await NativeBridge.getInstance().callNativeRequest('/api/corewf/finish-deposit', jsonEncode(params));
          _appController.hideLoading();
          if (result.isSuccess) {
            LoggerMp().writeLog('result: ${result.data}');
            Map responseMap = json.decode(result.data);
            NativeBridge.getInstance().nativeCallBackResultFinishDepositSocket('00', '', depositId, totalAmount.toString(), data: '${jsonEncode(responseMap['data'])}');
          }else {
            LoggerMp().writeLog('result: ${result.error?.message}');
            NativeBridge.getInstance().nativeCallBackResultFinishDepositSocket('${result.error?.code}', '${result.error?.message}', depositId, totalAmount.toString());
          }
        }

      }else {
        NativeBridge.getInstance().nativeCallBackResultFinishDepositSocket('${MposConstant.ERROR_NOT_EXITS_SCREEN}', '', depositId, totalAmount.toString());
      }
    }
  }

  void handlerVoidTrans(dynamic data, String currentRoute) async {
    if (data != null) {
      var mapArgument = jsonDecode(data);
      String transCode = mapArgument["transCode"] ?? "";
      int confirmVoid = mapArgument["confirmVoid"] ?? 0;
      String orderId = mapArgument["orderId"] ?? "";

      if (currentRoute == AppRoute.main_screen) {
        if (!(_appController.userInfo?.config?.permitVoidSocket == 1)) {
        // if (!(await NativeBridge.getInstance().nativeIsPermitVoidSocket())) {
          NativeBridge.getInstance().nativeCallBackResultVoidSocket(
              buildDataCallBackVoid(mapArgument, MposConstant.ERROR_NOT_SUPPORT.toString()));
          return;
        }
        if (confirmVoid == 0) {
          _doVoidTransTCP(transCode, mapArgument);
        }else {
          AppUtils.showDialogAlert(
              context, description: AppStrings.getString(AppStrings.tv_confirm_void_trans)!.replaceFirst('%s', orderId),
              text1stButton: AppStrings.getString(AppStrings.title_no),
              text2ndButton: AppStrings.getString(AppStrings.buttonConfirm),
              onPress1stButton: () {
                Get.back();
                NativeBridge.getInstance().nativeCallBackResultVoidSocket(
                    buildDataCallBackVoid(mapArgument, "-1"));
              },
              onPress2ndButton: () {
                Get.back();
                _doVoidTransTCP(transCode, mapArgument);
              }, isTwoButton: true);
        }
      }else {
        NativeBridge.getInstance().nativeCallBackResultVoidSocket(
            buildDataCallBackVoid(mapArgument, MposConstant.ERROR_NOT_EXITS_SCREEN.toString()));
      }
    }
  }

  void handlerAddMoto(dynamic data, String currentRoute) async {
    if (data != null) {
      var mapArgument = jsonDecode(data);
      int totalAmount = mapArgument["totalAmount"] ?? 0;
      String orderId = mapArgument["orderId"] ?? "";
      String phone = mapArgument["phone"] ?? "";
      String cardExp = mapArgument["cardExp"] ?? "";
      String cardHolder = mapArgument["cardHolder"] ?? "";
      String cvv = mapArgument["cvv"] ?? "";
      String email = mapArgument["email"] ?? "";
      String pan = mapArgument["pan"] ?? "";
      // int autoDismissDlgTimer = mapArgument["autoDismissDlgTimer"] ?? 15; //default timer close finish screen

      var cardInfo = CardInfo();
      cardInfo.cardNumber = pan;
      cardInfo.cardHolderName = cardHolder;
      cardInfo.cardExp = cardExp;
      cardInfo.cardCVV = cvv;
      var maskPan = cardInfo.cardNumber.maskString(
          8, cardInfo.cardNumber!.length - 4);
      var udid = "MOTO|" + TextUtils.generatePaymentIdentifer();
      LoggerMp().writeLog('SOCKET - ${mapArgument.toString()}');
      if (currentRoute == AppRoute.main_screen) {
        if (!_appController.paymentMethod!.motoActive) {
          LoggerMp().writeLog('SOCKET - permitScanCard can not enable');
          NativeBridge.getInstance().nativeCallBackResultPaymentSocket('TRANS_ERROR', orderId, totalAmount.toString(), "", "", errCode: MposConstant.ERROR_NOT_SUPPORT);
          return;
        }

        var errMess = MotoPaymentModel.validateCardMoto(cardInfo);
        if (errMess == 'CARD_NUMBER_NOT_SUPPORT_MOTO_PAYMENT') {
          // callback(false, LocalizationCustom.localization("CARD_NUMBER_NOT_SUPPORT_MOTO_PAYMENT"));
          return;
        }

        var splitExpired = cardInfo.cardExp!.split("/");
        var mapJsonCard = Map();
        mapJsonCard["pan"] = cardInfo.cardNumber!.replaceAll(" ", "");
        mapJsonCard["cardholder"] = cardInfo.cardHolderName!.toUpperCase();
        mapJsonCard["expired"] = splitExpired[1] + splitExpired[0];
        mapJsonCard["cvv"] = cardInfo.cardCVV;
        String jsonStringData = jsonEncode(mapJsonCard);
        var dataEncryted = AESEncrypter.encryptAES_ECB(jsonStringData, udid);
        LoggerMp().writeLog("MOTO|User:[${_appController
            .loginAccount}] Amount:[$totalAmount] maskPan:[$maskPan]");
        if (dataEncryted != null) {
          FocusScope.of(context).requestFocus(FocusNode());
          var stringJson = await MethodFuc.macq_newPayment(
              udid,
              '',
              _appController.loginAccount,
              totalAmount,
              configModule.TrxType.MotoPayment.index,
              AppConfiguration().deviceMpos.deviceType,
              AppConfiguration().tId,
              AppConfiguration().mId,
              email,
              phone,
              dataEncryted,
              readerNumber: AppConfiguration().deviceMpos.deviceSerial);
          var result = await NativeBridge.getInstance().callNativeMotoPayRequest(stringJson);
          try {
            var dataResponse = jsonDecode(result.data ?? '');
            if (dataResponse["saleResCode"] == "00") {
              NativeBridge.getInstance().nativeCallBackResultPaymentSocket('APPROVED', orderId, totalAmount.toString(), "", udid);
            } else {
              String message = dataResponse["message"] ?? '';
              if (message.isEmpty) {
                message = 'TIMEOUT';
              }
              NativeBridge.getInstance().nativeCallBackResultPaymentSocket('TRANS_ERROR', orderId, totalAmount.toString(), "", "", errCode: MposConstant.ERROR_CANCEL_ORDER);
            }
          } catch (e) {
            NativeBridge.getInstance().nativeCallBackResultPaymentSocket('TRANS_ERROR', orderId, totalAmount.toString(), "", "", errCode: MposConstant.ERROR_DEFAULT);
          }
        } else {
          NativeBridge.getInstance().nativeCallBackResultPaymentSocket('TRANS_ERROR', orderId, totalAmount.toString(), "", "", errCode: MposConstant.ERROR_NOT_EXITS_SCREEN);
        }
      }
    }
  }

  void _doVoidTransTCP(String transCode, Map mapArgument) async {
    _appController.showLoading(message: 'Đang xử lý ...');
    Map params = {
      "transactionID": transCode,
    };
    NativeResponseModel nativeResponseModel = await NativeBridge
        .getInstance().nativeGetTransactionDetail(params);
    _appController.hideLoading();
    if (nativeResponseModel.isSuccess) {
      final String detailSettleTransactionString = nativeResponseModel
          .data;
      Map responseMap = json.decode(detailSettleTransactionString);
      TransactionDetail detailSettle = TransactionDetail.fromJson(
          responseMap as Map<String, dynamic>);

      Map params = {
        "transactionID": detailSettle.wfId,
      };
      _appController.showLoading();
      NativeResponseModel nativeResponseModelVoid = await NativeBridge
          .getInstance().nativeVoidTransaction(params);
      _appController.hideLoading();
      if (nativeResponseModelVoid.isSuccess) {
        LoggerMp().writeLog('void trans success');
        NativeBridge.getInstance().nativeCallBackResultVoidSocket(
            buildDataCallBackVoid(mapArgument, "00"));
      } else {
        NativeBridge.getInstance().nativeCallBackResultVoidSocket(
            buildDataCallBackVoid(mapArgument, "-1"));
        AppUtils.showDialogErrorNative(
            context, nativeResponseModelVoid.error!);
        LoggerMp().writeLog('void trans fail');
      }
    } else {
      NativeBridge.getInstance().nativeCallBackResultVoidSocket(
          buildDataCallBackVoid(mapArgument, "-1"));
      AppUtils.showDialogErrorNative(
          context, nativeResponseModel.error!);
    }
  }

  Map buildDataCallBackVoid(Map map, String code) {
    map['responseCode'] = code;
    print("uffoooo ${map.values}");
    return map;
  }

  void handlerOrderWithPaymentMethod(int totalAmount, String orderId, String paymentMethod, int autoDismissDlgTimer) async {
    String configQr = await _appController.localStorage.getConfigQrP12();
    if(configQr.isEmpty) {
      Get.find<PaymentInitController>().fetchConfigQrP12(
          Get.find<MainController>().context, true,
          amount: totalAmount.toString(),
          orderID: orderId,
          autoDismissDlgTimer: autoDismissDlgTimer,
          paymentMethod: paymentMethod,
          isPaymentSocket: true);
    }else if(paymentMethod.isNotEmpty) {
      QrChildren qrChildren = AppUtils.getQRChidWithMethodPayment(configQr, paymentMethod);
      if (qrChildren.qrType != null) {
        Get.find<PaymentInitController>().gotoP12QrScreen(Get.find<MainController>().context, qrChildren.qrType!, qrChildren.shortNameChild!, qrChildren.logoChild ?? '', qrChildren.amountMin ?? '', amount: totalAmount.toString(), orderID: orderId, autoCloseFinishScreen: autoDismissDlgTimer, isPaymentSocket: true);
      }else {
        NativeBridge.getInstance().nativeCallBackResultPaymentSocket('TRANS_ERROR', orderId, totalAmount.toString(), paymentMethod, "", errCode: MposConstant.ERROR_NOT_SUPPORT);
      }
    }else {
      QrChildren qrChildren = AppUtils.getQRChidWithMethodPayment(configQr, paymentMethod);
      Get.find<PaymentInitController>().gotoP12QrScreen(Get.find<MainController>().context, qrChildren.qrType ?? MposConstant.name_group_qr_VAQR, qrChildren.shortNameChild ?? MposConstant.qr_name_VietQr ,'', qrChildren.amountMin ?? '', amount: totalAmount.toString(), orderID: orderId, autoCloseFinishScreen: autoDismissDlgTimer, isPaymentSocket: true);
    }
  }

  void onPressCloseWaitTransScreen() {
    setBrightnessControl(0);
    showModalBottomSheet(
        isScrollControlled: true,
        isDismissible: false,
        shape: RoundedRectangleBorder(
          borderRadius:
          BorderRadius.vertical(top: Radius.circular(6)),
        ),
        context: context,
        builder: (context) {
          return BaseBottomSheet(
            isCloseHeader: false,
            hideCloseButton: false,
            title: AppStrings.getString(
                AppStrings.titleConfirmPassword),
            child: Container(
              padding: EdgeInsets.all(10),
              child: Column(
                children: [
                  PassCodePage(
                    autoCloseKeyboard: true,
                    confirmLogoutEnable: true,
                    onPressConfirm: (value) {
                      if (value.isNotEmpty && value == pwWaitScreen) {
                        isShowWaitingTcpScreen.value = false;
                        LocalStorage().saveData(LocalStorage.isShowWaitingTcpScreen, false);
                        setBrightnessControl(1);
                      } else {
                        var snackBar = SnackBar(content: Text('${AppStrings.getString(AppStrings.errorPassNotMatch)}', style: style_S16_W400_WhiteColor,));
                        ScaffoldMessenger.of(context).showSnackBar(snackBar);
                      }
                    },
                    onCallbackAutoClose: (value) {
                      Get.back();
                    },
                  )
                ],
              ),
            ),
          );
        });
  }

  /*
  * 0: default - auto brightness - defaultBrightness
  * 1: disable auto brightness - holdBrightnessControl() -> sáng màn hình trở lại: tắt setting
  * 2:
  * */

  void setBrightnessControl(int type, {int? timeout}) async {
      switch (type) {
        case 0:
          if (isShowWaitingTcpScreen.value) {
            BrightnessControl().monitorBrightnessControl(timeout: timeout);
          }
          break;
        case 1:
          BrightnessControl().holdBrightnessControl();
          break;
      }
  }

  void onSelectQr(StaticQRType value) {
    if (value == StaticQRType.VietQR) {
      _appController.isShowVietQr.value = true;
    }else {
      _appController.isShowVietQr.value = false;
    }
  }


  void initGuildAndTitle() async {
    bool needShowGuideP12 = await _appController.localStorage
        .getData(LocalStorage.KEY_NEED_GUIDE_P12, true);
    if (needShowGuideP12 && _appController.paymentMethod!.vietQrActive) {
      isShowGuideP12.value = true;
    }
    _appController.isShowDefaultVietQr();
  }

  void initKeyBoardN04() {
    NativeBridge.getInstance().nativeInitN04KeyBoard();

  }

  void initListenerFromNative() {
    initListenerTcp();
    initKeyBoardN04();

    _nativeBridge.instanceListenerNative((call) async {
      String currentRoute = Get.currentRoute;
      print("currentRoute mainController = $currentRoute");
      print("currentRoute mainController = ${call.method} --- ${call.arguments}");
      setBrightnessControl(0);
      switch (call.method) {
        case MposConstant.action_tcp_add_order: {
          handlerAddOrder(call.arguments, currentRoute);
          break;
        }
        case MposConstant.action_tcp_add_order_deposit: {
          handlerAddOrderDeposit(call.arguments, currentRoute);
          break;
        }
        case MposConstant.action_tcp_set_final_deposit: {
          handlerSetFinalAmountDeposit(call.arguments, currentRoute);
          break;
        }
        case MposConstant.action_tcp_finish_deposit: {
          handlerSetFinishDeposit(call.arguments, currentRoute);
          break;
        }
        case MposConstant.action_tcp_void: {
          handlerVoidTrans(call.arguments, currentRoute);
          break;
        }
        case MposConstant.action_tcp_add_moto: {
          handlerAddMoto(call.arguments, currentRoute);
          break;
        }
        case MposConstant.action_tcp_cancel_order: {  // Socket Cancel QR Code
          if (currentRoute == AppRoute.qr_code_p12_screen) {
            QrCodeP12Controller _codeP12Controller = Get.find<QrCodeP12Controller>();
            _codeP12Controller.handlerCancelQRTcp();
          }
          break;
        }
        case MposConstant.action_tcp_state_payment: {
          if ((call.arguments == 'END') || (call.arguments == 'AP_ACTION_ERR')) {
            Get.find<PaymentInitController>().permitScanCard = true;
          }
          break;
        }
      }
    });
  }
}
