import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../utils/app_colors.dart';
import '../utils/styles.dart';

class LoadingWidget extends StatelessWidget {
  final Color? backgroundColor;
  final String? message;

  const LoadingWidget({Key? key, this.backgroundColor, this.message}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Container(
      // constraints: BoxConstraints.expand(),
      color: backgroundColor ?? (message?.isEmpty == true ? AppColors.black.withOpacity(0.6) : AppColors.transparent),
      child: Center(
          child: AlertDialog(
            elevation: 0,
            contentPadding: const EdgeInsets.fromLTRB(30.0, 0.0, 30.0, 0.0),
            backgroundColor: Colors.transparent,
            content: Container(
              padding: const EdgeInsets.fromLTRB(00.0, 30.0, 00.0, 10.0),
              decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.all(Radius.circular(15))),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CupertinoActivityIndicator(
                    radius: 15,
                  ),
                  Container(
                      margin: EdgeInsets.only(top: 10),
                      child: Padding(
                        padding: const EdgeInsets.all(10.0),
                        child: (message != null && message!.isNotEmpty)
                            ? Text(
                                message!,
                                textAlign: TextAlign.center,
                                style: style_S14_W600_BlackColor,
                              )
                            : null,
                      )),
                ],
              ),
            ),
          ),
        )
    );
  }
}
