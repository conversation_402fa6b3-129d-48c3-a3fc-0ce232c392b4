package com.mpos.models;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

/**
 * Create by anhnguyen on 6/10/20
 */
public class CashierPay {

    @SerializedName("amount")
    @Expose
    private Integer amount;
    @SerializedName("casherId")
    @Expose
    private String casherId;
    @SerializedName("orderId")
    @Expose
    private String orderId;
    @SerializedName("rethinkId")
    @Expose
    private String rethinkId;
    @SerializedName("udid")
    @Expose
    private String udid;
    @SerializedName("description")
    @Expose
    private String description;

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public String getCasherId() {
        return casherId;
    }

    public void setCasherId(String casherId) {
        this.casherId = casherId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderCode) {
        this.orderId = orderCode;
    }

    public String getRethinkId() {
        return rethinkId;
    }

    public void setRethinkId(String rethinkId) {
        this.rethinkId = rethinkId;
    }

    public String getUdid() {
        return udid;
    }

    public void setUdid(String udid) {
        this.udid = udid;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
