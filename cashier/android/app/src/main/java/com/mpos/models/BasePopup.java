package com.mpos.models;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

/**
 * Created by anhnguyen on 1/10/18.
 */

public class BasePopup {


    @SerializedName("error")
    BaseObjJson errObj;


    @SerializedName("title")
    @Expose
    private String title;

    @SerializedName("category")
    @Expose
    private String category;

    @SerializedName("body")
    @Expose
    private String body;

    @SerializedName("bodyUrl")
    @Expose
    private String urlContentVi;

    @SerializedName("bodyUrlEn")
    @Expose
    private String urlContentEn;

    public String getTitle() {
        return title;
    }

    public void setTitle(String content) {
        this.title = content;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getEndTime() {
        return body;
    }

    public void setEndTime(String endTime) {
        this.body = endTime;
    }

    public String getUrlContentVi() {
        return urlContentVi;
    }

    public void setUrlContentVi(String urlContentVi) {
        this.urlContentVi = urlContentVi;
    }

    public String getUrlContentEn() {
        return urlContentEn;
    }

    public void setUrlContentEn(String urlContentEn) {
        this.urlContentEn = urlContentEn;
    }
    public BaseObjJson getErrObj() {
        return errObj;
    }

    public void setErrObj(BaseObjJson errObj) {
        this.errObj = errObj;
    }

}
