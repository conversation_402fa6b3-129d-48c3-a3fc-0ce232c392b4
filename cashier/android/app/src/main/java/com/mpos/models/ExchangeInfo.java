package com.mpos.models;

/**
 * Created by anhnguyen on 5/8/18.
 */

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class ExchangeInfo {

    @SerializedName("issuerCode")
    @Expose
    private String issuerCode;

    @SerializedName("issuerName")
    @Expose
    private String issuerName;

    @SerializedName("fee")
    @Expose
    private Float fee;

    public String getIssuerCode() {
        return issuerCode;
    }

    public void setIssuerCode(String issuerCode) {
        this.issuerCode = issuerCode;
    }

    public String getIssuerName() {
        return issuerName;
    }

    public void setIssuerName(String issuerName) {
        this.issuerName = issuerName;
    }

    public Float getFee() {
        return fee;
    }

    public void setFee(Float fee) {
        this.fee = fee;
    }

}
