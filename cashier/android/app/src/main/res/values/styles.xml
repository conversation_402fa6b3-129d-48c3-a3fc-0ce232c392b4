<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Theme applied to the Android Window while the process is starting when the OS's Dark Mode setting is off -->
    <style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <!-- Show a splash screen on the activity. Automatically removed when
             <PERSON>lutter draws its first frame -->
        <item name="android:windowBackground">@drawable/launch_background</item>
    </style>
    <!-- Theme applied to the Android Window as soon as the process has started.
         This theme determines the color of the Android Window while your
         Flutter UI initializes, as well as behind your Flutter UI while its
         running.

         This Theme is only used starting with V2 of Flutter's Android embedding. -->
    <style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style>
    <color name="gray">#848484</color>
    <!--    printer layout-->
    <style name="layoutPrintValue">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">right</item>
        <item name="android:textSize">@dimen/pts_18</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:layout_marginStart">@dimen/printer_margin_column</item>
        <item name="android:layout_marginTop">@dimen/printer_margin_line</item>
    </style>
    <style name="layoutPrintValueQR">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">right</item>
        <item name="android:textSize">@dimen/pts_20</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:layout_marginStart">@dimen/printer_margin_column</item>
        <item name="android:layout_marginTop">@dimen/printer_margin_line</item>
    </style>

    <style name="layoutPrintTitle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@android:color/black</item>
        <item name="android:layout_marginTop">@dimen/printer_margin_line</item>
        <item name="android:textSize">@dimen/pts_18</item>
    </style>

    <style name="layoutPrintTitleQR">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@android:color/black</item>
        <item name="android:layout_marginTop">@dimen/printer_margin_line</item>
        <item name="android:textSize">@dimen/pts_20</item>
    </style>

</resources>
