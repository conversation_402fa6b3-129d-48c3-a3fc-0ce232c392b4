//
//  TinkGooInstance.h
//  paymentGateWay
//
//  Created by <PERSON><PERSON> on 8/31/20.
//  Copyright © 2020 Ho<PERSON> Son. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface TinkGooInstance : NSObject

+ (id) shareInstance;
- (NSString*) generateNewKey;
- (NSString*) getCurrentTinkKey;

- (NSString*) ecipherData:(NSDictionary*)dict;
- (NSData*) decryptData:(NSData*)dataEncipher;
- (NSDictionary*) decryptDataGoogleTink_WithResponseData:(id)dataResponse;
@end

NS_ASSUME_NONNULL_END
