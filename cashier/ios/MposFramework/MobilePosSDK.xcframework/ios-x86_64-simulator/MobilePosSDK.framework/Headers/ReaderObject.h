//
//  ReaderObject.h
//  mpos-framework
//
//  Created by sondh on 10/5/18.
//  Copyright © 2018 sondh. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "MobilePos_Config.h"
#import "Constant.h"

NS_ASSUME_NONNULL_BEGIN

@interface ReaderObject : NSObject

@property (nonatomic, strong, nullable) NSString* readerSerialNumber;
@property (nonatomic, assign) READER_TYPE           readerType;
@property (nonatomic, assign) READER_STATE          readerState;

+ (NSString*) getReaderStateDetail:(READER_STATE)readerState;

@end

NS_ASSUME_NONNULL_END
