//
//  mPosTransactions.h
//  mpos-framework
//
//  Created by sondh on 3/19/19.
//  Copyright © 2019 sondh. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "mPosAuthenticate.h"
#import "MobilePos_Config.h"
NS_ASSUME_NONNULL_BEGIN

@interface mPosTransactions : NSObject
//Get list Transaction UnSettle
//* HistoryCompleteBlock complete
//* list custom object <HistoryListObject>
//* codeResponse:
+ (void) getListTransactionWithIndex:(NSInteger)index Complete:(HistoryCompleteBlock)complete;

//Get Transaction Detail from <HistoryListObject>
//* HistoryDetailCompleteBlock: object HistoryDetail and codeResponse
+ (void) getTransationDetail:(nonnull NSString*)transactionId Complete:(HistoryDetailCompleteBlock)complete;

//Get Transaction Status
+ (void) getTransactionStatus:(nullable NSString*)transactionId PaymentIdentifier:(nonnull NSString*)paymentIdentifier Complete:(DataCompleteBlock)complete;

//Void Transaction with Transaction Identifier param.
//*
+ (void) voidTransaction:(nonnull NSString*)transactionId Complete:(DataCompleteBlock)complete;

//Settle all transaction from <HistoryListObject>
//*
+ (void) settleAllTransaction_Complete:(DataCompleteBlock)complete;

//Send receipt to email customer with transaction identifier
//*
+ (void) sendReceiptWithTransaction:(nullable NSString*)transactionId TranReq:(nullable NSString*)transReq Email:(nonnull NSString*)email Complete:(DataCompleteBlock)complete;

+ (void) getListPenddingSignature_Complete:(DataCompleteBlock)complete;

+ (void) getDecline_ListTransaction_WithItems:(NSInteger)items Page:(NSInteger)pages Complete:(DataCompleteBlock)complete;
@end

NS_ASSUME_NONNULL_END
