//
//  Base64Encode.h
//  mPoS.vn_Beta
//
//  Created by <PERSON><PERSON> on 3/15/16.
//  Copyright © 2016 Hoang Son. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface Base64EncodeSetting : NSObject
+ (void) initialize;

+ (NSString*) encode:(const uint8_t*) input length:(NSInteger) length;

+ (NSString*) encode:(NSData*) rawBytes;

+ (NSData*) decode:(const char*) string length:(NSInteger) inputLength;

+ (NSData*) decode:(NSString*) string;
@end
