//
//  LibPayment.h
//  mPoS.vn_Beta
//
//  Created by <PERSON><PERSON> on 10/7/15.
//  Copyright © 2015 <PERSON><PERSON>. All rights reserved.
//  VersionCode: 1.0.180704.1453

#import <Foundation/Foundation.h>
#import <ExternalAccessory/ExternalAccessory.h>
#import "MobilePos_Config.h"
#import "Constant.h"
//1.
//Require Framework
/*
 Foundation
 UIKit
 AudioToolbox
 AVFoundation
 MediaPlayer
 CoreBluetooth
 Security
 ExternalAccessory
 */

//2.
//Infor PLIST
/*
 add new
 +   Supported external accessory protocols
 with value
 +   com.datecs.pinpad
 */

//3.
/*
add Other link Flag: -ObjC
*/

//$(PROJECT_DIR)/include


typedef NS_ENUM (int, ERROR_CODE)
{
    NO_AID = 1111110,
    DISCONNECT_READER_PROCESSING = 1111111,
    POST_SIGN_FAIL = 1111112,
    PIN_ENTRY_ERROR = 1111113,
    mVISA_MERCHANT_NOT_FOUND = 1111114,
};

typedef NS_ENUM (int, GRO<PERSON>_KEY_PR02)
{
    GROUP_KEY_STB   = 0,
    G<PERSON><PERSON>_KEY_BIDV  = 1,
    GRO<PERSON>_KEY_VTB   = 2,
    GROUP_KEY_VCB   = 5,
    GROUP_KEY_ACQ   = 9,
};

typedef NS_ENUM (int, PR01_KEY_ENCRYPT_INDEX)
{
    PVC_PIN_KEY     = 9,
    PVC_DATA_KEY    = 10,
    STB_DATA_KEY    = 11,
    STB_PIN_KEY     = 12,
    MCC_DATA_KEY    = 13,
    MCC_PIN_KEY     = 14,
    VTB_DATA_KEY    = 15,
    VTB_PIN_KEY     = 16,
    CYBS_DATA_KEY   = 17,
};

typedef NS_ENUM(int, ENCRYPTION_MODE)
{
    CBC_3DES =1,
    DUKPT,
};

typedef NS_ENUM(int, RESPONE_STATUS)
{
    RESPONE_STATUS_NON,
    RESPONE_STATUS_APPROVED,
    RESPONE_STATUS_CANCEL,
    RESPONE_STATUS_PIN_ENTRY,
    RESPONE_STATUS_TEMINATED,
    RESPONE_STATUS_DECLINED,
    RESPONE_STATUS_CAPK_FAIL,
    RESPONE_STATUS_READ_FAIL,
    RESPONE_STATUS_NOT_ICC,
    RESPONE_STATUS_SELECT_APP_FAIL,
    RESPONE_STATUS_CARD_BLOCKED_OR_NO_EMV_APPS,
    RESPONE_STATUS_DEVICE_ERROR,
    RESPONE_STATUS_CARD_NOT_SUPPORTED,
    RESPONE_STATUS_INVALID_ICC_DATA,
    RESPONE_STATUS_NFC_TERMINATED,
    RESPONE_STATUS_UPDATE_WORKEY_FAIL = 15,
    RESPONE_STATUS_POS_BUSY,
};

typedef NS_ENUM(NSInteger, CARD_TYPE_PROCESS) {
    CARD_TYPE_NONE,
    CARD_TYPE_ICC,
    CARD_TYPE_MAG,
    CARD_TYPE_NFC,
};

extern NSString * _Nullable const CURRENTCY_CODE_VND;
extern NSString * _Nullable const CURRENTCY_CODE_USD;

#pragma mark - Reader Information Delegate

@protocol ReaderDelegate <NSObject>
/*
    Require dispatch_get_main_queue for update UI.
*/
//Get state or error when begin read infomation reader.
- (void) readerBeginReadInfor:(nullable NSString *)info;
//Read Information Card: Get ReaderNumber and Batter percent. If batter percent <20% require change battery life
- (void) readerResponse_ReaderNumber:(nullable NSString*)readerNum BatteryPercent:(nullable NSString*)battery withReaderType:(READER_TYPE)type_ withState:(READER_STATE)state_ AndListConnect:(nullable NSArray*)arrConnected;
@optional
//Get all accessory bluetooth connected (only Bluepad)
- (void) onGetListAccessoryConnected:(nullable NSArray*)arrReaderConnected;
//Get state update CAPublic Key
- (void) on_updateCAKeyStatus:(BOOL)status;
- (void) on_qpos_responeinjectionkey:(nullable NSString*)status;

- (void) onResponseUpgradeFW:(nullable NSString*)status;
- (void) onResponseUpgradeFW_Progress_Percent:(NSInteger)percent;
- (void) onResponseReaderPr02Infor:(nullable NSDictionary*)dictInfor;
@end


#pragma mark - Read Card Delegate

@protocol ReadCardDelegate <NSObject>

@optional
- (void) readerReadCardResponseMaskPAN:(nullable NSString*)maskPan CardHolderName:(nullable NSString*)cardHolderName_;
- (void) readerReadCardNFC:(nullable NSString*)udid;

- (void) readerIsCardPresent;
- (void) readerBeginProcess_withCardType:(CARD_TYPE_PROCESS)cardType;
- (void) readerShowPinInput;

//MCC - LoyaltyCard: Get clear Track1, Track2 (With Loyalty card or MCC/OCC Card)
- (void) readerReadCardResponseMCCCard:(nullable NSString*)track1 Track2:(nullable NSString*)track2;

@required
//Magnetic Card: Get TrackData encrypt, KSN and Readertype for Command Sale service (Payment Magtripe)
- (void) readerReadCardResponseMagtripe:(nullable NSString*) trackData
                                 AndKSN:(nullable NSString*)strKSN
                             ReaderType:(READER_TYPE)readerType;
//SmartCard: Get ClearTag(emvTag) and EncipherTag need Command Sale service (Payment EMV)
- (void) readerReadCardResponseEmvWithClearTag:(nullable NSString*)clearTag
                                   EncipherTag:(nullable NSString*)encipherTag
                                    ReaderType:(READER_TYPE)readerType
                                isCheckPANOnly:(BOOL)isCheckPANOnly
                                       WithNFC:(BOOL)isNFC;
//SmartCard: Confirm EMV when request Command sale successfully.
- (void) readerReadCardConfirmEmvWithClearTag:(nullable NSString*)clearTag EncipherTag:(nullable NSString*)encipherTag ReaderType:(READER_TYPE)readerType;
//Gen PIN block with OnlinePIN (run with ATM card)
- (void) readerGenPinBlockWithMagtripe:(nullable NSString*)trackData
                                AndKSN:(nullable NSString*)strKSN
                              PinBlock:(nullable NSString*)pinBlock
                              WithFail:(BOOL) failPin
                            ReaderType:(READER_TYPE)readerType;

//With EMV Online PIN
- (void) readerGenPinBlockWithEMV:(nullable NSString*)pinBlock;

//Fail Read
- (void) readerReadCardResponseEmvWithStatus:(RESPONE_STATUS)status;

//LoyatyCard - Get clear PIN
- (void) readerGenPinBlockWithMagtripeWithMCCCardPinBlock:(nullable NSString*)pinBlock WithFail:(BOOL) failPin;

@end

@interface LibPayment : NSObject
//Using step by step (current system)
//1. shared Instance object
+ (nullable id) shareDevice;
//Set all Delegate: ReaderDelegate/ ReadCardDelegate/ RequestDelegate
- (void) setDelegateReaderInfor:(nullable id)_delegate;
- (void) setDelegateReaderInforForPaymentController:(nullable id)_delegate;
- (void) setDelegateReaderProcessCard:(nullable id)_delegate;
//Default: Language = VI and CurrentCode = VND
- (void) setLanguage:(nullable NSString*)languageInput;
- (void) setLanguage:(nullable NSString*)languageInput CurrentCode:(nullable NSString*)currentCodeInput;

//- (void) setLoadConfigType:(LOAD_CONFIG_TYPE)configType;

- (void) setGroupKeyIndexWithIndexKey:(NSInteger)indexKey;
- (void) setEncryptionDeviceWithIndex:(BANK_TYPE)bankType;
- (void) setReaderNumForAutoConnect:(nullable NSString*)readerNum ReaderType:(READER_TYPE)type;
- (void) clearCacheAllReader;

- (void) resetMasterKeyWithKeyValue:(nullable NSString*)keyValue KCV:(nullable NSString*)kcv GroupKeyIndex:(NSInteger)keyIndex;
- (void) injectionKeyWith_ReaderType:(READER_TYPE)readerType Ipek:(nullable NSString*)ipek PinMasterKey:(nullable NSString*)pinMasterKey KSN_Value:(nullable NSString*)ksnValue KCVMasterKey:(nullable NSString*)kcv KeyTypeInjection:(INJECT_KEY_TYPE)keyTypeInject;
- (void) upgradeFW_withData:(nullable NSData*)data;
- (void) updateEMVConfig:(nullable NSData*)emvConfig Capk:(nullable NSData*)capk;

//2. Read Reader information with type = 1/ Audio 2/ Bluepad
- (void) ReadInforCardReaderWith_ReaderType:(READER_TYPE)readerType_ AndBlockReader:(READER_TYPE)blockReaderType_ WithIsPayment:(BOOL)isPaying;

#pragma mark -

//3. Preparing to begin Transaction
//3.2 Set EncryptMode (default = 1) and InitTag data (require) from response Login level 1 in BANK API
- (void) setEncryptMode:(ENCRYPTION_MODE)encryptMode InitTag:(nullable NSDictionary*)initTagsValuesList;
//3.3.1 Using with PR01 (Datec)
- (void) updateCAPublicKeyWithValue:(nullable NSArray*)arrValue;
//3.3.2 Using with Qpos (DSRRead)
- (void) updateCAPublicKeyWithValue:(nullable NSArray*)arrValue AndEMVApp:(nullable NSArray*)arrEMVApp;

//4 Begin reader Card data
//4.1
//type: Reader type: Audio/BluePad reader
//isLoyatyCard: if not using BANK Card isLoyatyCard = NO
//hasEvent: Event discount amount payment. If hasEvent = YES please using ReadCardContinueWithHasEventWithAmount to set amount discount
- (void) SetWorkingKeyWithPinOnlineEnable:(nullable NSString*)workingKey;
//- (void) ReadCardWithType:(READER_TYPE)type WithAmount:(double)amount WithLoyatyCard:(BOOL)isLoyatyCard HasEvent:(BOOL)hasEvent;
- (void) PayWithReaderType:(READER_TYPE)type WithAmount:(double)amount;

- (void) reReadSmartCard_IssuerbankWithErrorCode:(int)code;
//4.1.1
//set amount paynet again if hasEvent = YES
- (void) ReadCardContinueWithHasEventWithAmount:(double)amount;

//4.2
//4.2.1 Using if payment Magtripe Success response data
- (void) BuildPinBlock_With_eZPK:(nullable NSString*)eZPK AndPAN:(nullable NSString*)pan KCVValue:(nullable NSString*)kcv WithEMVOnlinePIN:(BOOL)isOnlinePIN GroupKeyIndex:(NSInteger)index;

//4.2.2 Using if payment EMV Success response data
//Process confirm EMV response
- (void) RunEMVConfirmProcessOnlineWithEmvTagResponse:(nullable NSString*)emvTag HostResponeCode:(nonnull NSString*)hostResponseCode;

//4.3 Support method
//4.3.1
//Show ENTER PIN to Build PIN Block. Clear PIN/Encipher PIN if LoyaltyCard enable (isLoyalty = YES)
- (void) showEnterPinWithAmount:(double) amount WithLoyatyCard:(BOOL)isLoyaty;
//4.3.2
//Show Status BluePad screen if finish Transaction (YES/NO)
- (void) showPaymentSuccess:(BOOL) success;
//Finish and Turn off all Reader
- (void) FinishProcessCard;
- (BOOL) cancelPay;
- (void) getKCVWithKeyIndex:(NSInteger)keyIndex CompleteBlock:(void(^_Nullable)(id _Nullable response))complete;

- (void) libPayment_writeWorkKey:(nullable NSString*)workKey Index:(NSInteger)kIndex;
//- (void) getEMVAppConfigWithList:(NSString*)arrayList ReaderType:(READER_TYPE)type Complete:(void(^)(id response))complete;
@end
