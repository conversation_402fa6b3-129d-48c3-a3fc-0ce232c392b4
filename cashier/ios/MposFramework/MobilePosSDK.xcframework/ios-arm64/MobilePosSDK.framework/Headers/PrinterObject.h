//
//  PrinterObject.h
//  mPoS3.0
//
//  Created by <PERSON><PERSON> on 7/17/17.
//  Copyright © 2017 <PERSON><PERSON>. All rights reserved.
//

#import <Foundation/Foundation.h>

/*
 Copy to your class
 
 #import <MposSDK/PrinterObject.h>
 
 PrinterObject *print = [PrinterObject sharedManager];
 [print scanPrinter:^(BOOL isConnect) {
     if (isConnect) {
         [print printTextValue:@"In thử text máy in nhiệt" Align:ALIGN_RIGHT];
         [print printTextWithTitle:@"Đây là Title" Value:@"Đây là Value"];
         [print printTextValue:@"Đây là style Bold" Align:ALIGN_LEFT TextStyle:TEXT_STYLE_BOLD];
         [print printTextValue:@"Đây là style Double Ưidth" Align:ALIGN_LEFT TextStyle:TEXT_STYLE_DOUBLE_WIDTH];
         [print printTextValue:@"Đây là style Double Height" Align:ALIGN_CENTER TextStyle:TEXT_STYLE_DOUBLE_HEIGHT];
         [print printTextValue:@"Đây là style Under Line" Align:ALIGN_RIGHT TextStyle:TEXT_STYLE_UNDERLINE];
     }
 }];
 */

typedef NS_ENUM (int, ALIGN_TYPE)
{
    ALIGN_LEFT,
    ALIGN_CENTER,
    ALIGN_RIGHT,
};

typedef NS_ENUM (int, TEXT_STYLE_TYPE)
{
    TEXT_STYLE_BOLD,
    TEXT_STYLE_DOUBLE_HEIGHT,
    TEXT_STYLE_DOUBLE_WIDTH,
    TEXT_STYLE_UNDERLINE,
};

typedef void (^PrinterState)(BOOL isConnect);

/*
Print buffer
BUF_SIZE            8*1024
GET_NUM             47
 */

@interface PrinterObject : NSObject

+ (nonnull id)sharedManager;

- (void) scanPrinter:(nullable PrinterState)complete;

- (void) printTextWithTitle:(nullable NSString*)title Value:(nullable NSString*)value;
- (void) printTextValue:(nullable NSString*)value Align:(ALIGN_TYPE)type;
- (void) printTextValue:(nullable NSString*)value Align:(ALIGN_TYPE)type TextStyle:(TEXT_STYLE_TYPE)type;
- (void) printImageData:(nullable NSData*)imageData Align:(ALIGN_TYPE)type;

/*
 printBitMap
 mode 0: 8 points single density
 mode 1: 8 points dual density
 mode 32: 24 points single density
 mode 33: 24 points dual density
 */
- (void) printBitMapMode:(int)mode bitmap:(NSData*_Nullable)bitMap Align:(ALIGN_TYPE)type;
@end
