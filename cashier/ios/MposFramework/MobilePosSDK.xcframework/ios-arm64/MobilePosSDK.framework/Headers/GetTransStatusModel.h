//
//  GetTransStatusModel.h
//  MobilePosSDK
//
//  Created by <PERSON> on 3/25/20.
//  Copyright © 2020 sondh. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface GetTransStatusModel : NSObject

@property (nonatomic, assign) double amountAuthorized;
@property (nonatomic, strong) NSString *approvalCode;
@property (nonatomic, strong) NSString *cardHolderName;
@property (nonatomic, strong) NSString *invoiceNumber;
@property (nonatomic, strong) NSString *maskedPAN;
@property (nonatomic, assign) double transactionDate;
@property (nonatomic, assign) NSInteger transactionStatus;
@property (nonatomic, strong) NSString *transactionID;
@property (nonatomic, strong) NSString *udid;
@property (nonatomic, assign) NSInteger trxType;
@property (nonatomic, strong) NSString *applicationLabel;
@property (nonatomic, strong) NSString *transactionRequestID;

+ (id) ParserData:(id) data;

@end

NS_ASSUME_NONNULL_END
