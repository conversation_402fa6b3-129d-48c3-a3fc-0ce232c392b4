//
//  ReaderManager.h
//  mpos-framework
//
//  Created by sondh on 2/11/19.
//  Copyright © 2019 sondh. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "MobilePos_Config.h"
#import "ReaderObject.h"
#import "Constant.h"
#import "MPOSServiceProperty.h"

#define READER_LOCAL_STORAGE @"CURRENT_READER_CONNECT"

#define EMV_CONFIG_STATUS_DONE @"EMV_CONFIG_DONE"
#define EMV_CONFIG_STATUS_STEP @"EMV_CONFIG_STEP"
#define EMV_CONFIG_STATUS_FAIL @"EMV_CONFIG_FAIL"

#define MAX_PER_TRANS_COUNT 10

NS_ASSUME_NONNULL_BEGIN

typedef void (^__nullable ReaderCompleteBlock)(ReaderObject * _Nullable  readerObject,NSString * _Nullable  messageNote);
typedef void (^__nullable UpdateDeviceResultCompleteBlock)(NSString* result, NSString *updateType);

@interface ReaderManager : NSObject

@property (nonatomic, assign, readonly) BOOL needUpdateConfig;
@property (nonatomic, assign, readonly) BOOL needUpdateFW;
@property (nonatomic, assign, readonly) NFC_STATUS nfcState;

+ (id) shareManager;

- (void) searchMposDeviceWithReaderType:(READER_TYPE)type
                             SearchType:(SEARCH_READER_TYPE)searchType
                          CompleteBlock:(ReaderCompleteBlock)complete;

- (void) clearReaderObject;
- (void) clearReaderInjected;

- (ReaderObject* _Nullable ) getReaderActive;

- (NSInteger) getNumberSaleRemaining;
- (NSInteger) setNumberSaleRemaining;

- (void) ParserDataRequireUpdate:(NSDictionary*)data;

- (void) setUrlDowload_Config:(NSString*)strEMVConfig
                         CAPK:(NSString*)strCapk
                RequireUpdate:(BOOL)isRequireUpdate;

- (void) setDebugReaderName:(nullable NSString*)readerName;
- (void) setReaderAutoConnect:(nullable NSString*)readerName;
- (void) setUpdateConfigRequire:(BOOL)isRequire;
- (void) setInjectionKeyValue:(id)value Index:(NSInteger)index
             KeyTypeInjection:(NSInteger)keyTypeInject
                CompleteBlock:(ReaderResultCompleteBlock)complete;
- (void) setFlagNFCState:(NFC_STATUS)nfcStatus;

- (NSInteger) setIndexKeyToDeviceWithACQName:(NSString*)acqName;

- (void) resetMasterKey:(id)value KCV:(id)kcv Index:(NSInteger)index;

- (BOOL) checkReaderInjected:(NSString*)readerNumber;
- (void) checkReaderInjected:(NSString*)readerInjection Complete:(void(^)(BOOL isFinish))complete;

- (BOOL) getNeedUpdateConfig;

- (void) showDialogConfirmUpgrade:(UPGRADE_TYPE)upgradeType ShowUI:(BOOL)isShowUI Complete:(UpdateDeviceResultCompleteBlock)complete;
//BIDV USED
- (void) showUpgradePartnerApp_ReaderNumber:(NSString*)readerNumber EMV:(nullable NSString*)emvConfig CAPK:(nullable NSString*)capk CompleteBlock:(UpdateDeviceResultCompleteBlock)complete;
//BIDV END

- (void) readCardInfor_ReaderConnected:(ReaderObject*)readerConnected Complete:(ReadCardInfoCompleteBlock)completeBlock;
- (void) setCallbackReadCardInfo_Interface:(NSString*)cardInterface CardDataEncrypted:(NSString*)cardDataEncrypted Ksn:(NSString*)ksn Result:(NSDictionary*)result;
- (void) resetDeviceStatus;
@end

NS_ASSUME_NONNULL_END
