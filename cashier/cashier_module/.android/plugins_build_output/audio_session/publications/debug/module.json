{"formatVersion": "1.1", "component": {"group": "com.ryanheise.audio_session", "module": "audio_session_debug", "version": "1.0", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "7.5"}}, "variants": [{"name": "debugVariantDebugApiPublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "io.flutter", "module": "flutter_embedding_debug", "version": {"requires": "1.0.0-1a65d409c7a1438a34d21b60bf30a6fd5db59314"}}], "files": [{"name": "audio_session_debug-1.0.aar", "url": "audio_session_debug-1.0.aar", "size": 22299, "sha512": "d110270949f156deb5940b72c0182a82f6de00caaa9569a259c3c9e34d9a91718bed5094c98428823e2ab404b474fb8f4a96012c7f4c54de2013a9287b0160fb", "sha256": "d54080a412ca31186ae2b03deacee8ef65cb2e81a6e611c358e93dae750d4781", "sha1": "8623d2c734e74d296424a726325577fd323e5213", "md5": "8625ba9cca29251c610d1f34e4701497"}]}, {"name": "debugVariantDebugRuntimePublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "io.flutter", "module": "flutter_embedding_debug", "version": {"requires": "1.0.0-1a65d409c7a1438a34d21b60bf30a6fd5db59314"}}, {"group": "androidx.media2", "module": "media2-session", "version": {"requires": "1.2.1"}}], "files": [{"name": "audio_session_debug-1.0.aar", "url": "audio_session_debug-1.0.aar", "size": 22299, "sha512": "d110270949f156deb5940b72c0182a82f6de00caaa9569a259c3c9e34d9a91718bed5094c98428823e2ab404b474fb8f4a96012c7f4c54de2013a9287b0160fb", "sha256": "d54080a412ca31186ae2b03deacee8ef65cb2e81a6e611c358e93dae750d4781", "sha1": "8623d2c734e74d296424a726325577fd323e5213", "md5": "8625ba9cca29251c610d1f34e4701497"}]}]}