{"formatVersion": "1.1", "component": {"group": "com.example.imagegallerysaver", "module": "image_gallery_saver_debug", "version": "1.0", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "7.5"}}, "variants": [{"name": "debugVariantDebugApiPublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "io.flutter", "module": "flutter_embedding_debug", "version": {"requires": "1.0.0-1a65d409c7a1438a34d21b60bf30a6fd5db59314"}}], "files": [{"name": "image_gallery_saver_debug-1.0.aar", "url": "image_gallery_saver_debug-1.0.aar", "size": 10763, "sha512": "21443676afed6f3d338fca443724ede55e96bf05212729cf343f86568e03741400165f82ce85bf94e5eb6940366b448c599fa2f8f89c4455bad997c4f13575b7", "sha256": "881a7dd6ac575e4c2e46376509eedc1549cb57b88098e797727ff04d59f679be", "sha1": "b87c401887e38307ac935f9250d88193de6f55ca", "md5": "67d2f8b9f85c9c554c65d0c224247c6f"}]}, {"name": "debugVariantDebugRuntimePublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "io.flutter", "module": "flutter_embedding_debug", "version": {"requires": "1.0.0-1a65d409c7a1438a34d21b60bf30a6fd5db59314"}}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7", "version": {"requires": "1.7.10"}}], "files": [{"name": "image_gallery_saver_debug-1.0.aar", "url": "image_gallery_saver_debug-1.0.aar", "size": 10763, "sha512": "21443676afed6f3d338fca443724ede55e96bf05212729cf343f86568e03741400165f82ce85bf94e5eb6940366b448c599fa2f8f89c4455bad997c4f13575b7", "sha256": "881a7dd6ac575e4c2e46376509eedc1549cb57b88098e797727ff04d59f679be", "sha1": "b87c401887e38307ac935f9250d88193de6f55ca", "md5": "67d2f8b9f85c9c554c65d0c224247c6f"}]}]}