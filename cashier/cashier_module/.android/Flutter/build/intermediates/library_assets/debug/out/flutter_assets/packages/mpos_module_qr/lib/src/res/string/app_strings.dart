import 'package:get/get.dart';

import 'en_strings.dart';
import 'vi_strings.dart';

class AppStrings extends Translations {
  static const String localeCodeVi = 'vi_VN';
  static const String localeCodeEn = 'en_US';

  @override
  Map<String, Map<String, String>> get keys => {
        localeCodeVi: viStrings,
        localeCodeEn: enStrings,
      };

  static String getString(String key) {
    Map<String, String> selectedLanguage = Get.locale.toString() == localeCodeEn ? enStrings : viStrings;
    String text = key;
    if (selectedLanguage.containsKey(key) && selectedLanguage[key] != null) {
      text = selectedLanguage[key] ?? key;
    }
    return text;
  }
}

class StringConstants {
  static String qrListSourceTitle = 'qrListSourceTitle';
  static String titleQRPaymentEnterInfo = 'titleQRPaymentEnterInfo';
  static String buttonChooseQRPaymentEnterInfo = 'buttonChooseQRPaymentEnterInfo';
  static String title1ChooseQRPaymentEnterInfo = 'title1ChooseQRPaymentEnterInfo';
  static String description2 = 'description2';
  static String description2PlaceHolder = 'description2PlaceHolder';
  static String phone2 = 'phone2';
  static String phone2PlaceHolder = 'phone2PlaceHolder';
  static String feeForCustomer = 'feeForCustomer';
  static String qrAmountEmpty = 'qrAmountEmpty';
  static String qrAmountMinError = 'qrAmountMinError';
  static String qrAmountMinUsdError = 'qrAmountMinUsdError';
  static String currencyVND = 'currencyVND';
  static String currencyUSD = 'currencyUSD';
  static String currencyVNDShort = 'currencyVNDShort';
  static String customerInfo = 'customerInfo';
  static String optional = 'optional';
  static String next = 'next';
  static String addQRFee = 'addQRFee';
  static String totalAmount = 'totalAmount';
  static String emptyPhone = 'emptyPhone';
  static String emptyPhoneInstallment = 'emptyPhoneInstallment';
  static String emptyDescription = 'emptyDescription';
  static String emptyIdentity = 'emptyIdentity';
  static String emptyPhoneAndEmail = 'emptyPhoneAndEmail';
  static String emptyEmail = 'emptyEmail';
  static String invalidPhone = 'invalidPhone';
  static String invalidEmail = 'invalidEmail';
  static String invalidIdentity = 'invalidIdentity';
  static String emptyTopupItem = 'emptyTopupItem';
  static String emptyTopupPhone = 'emptyTopupPhone';
  static String invalidTopupPhone = 'invalidTopupPhone';
  static String invalidReceivePhone = 'invalidReceivePhone';
  static String descriptionTooShort = 'descriptionTooShort';
  static String descriptionEmpty = 'descriptionEmpty';
  static String errorQrCodeEmpty = 'errorQrCodeEmpty';
}
