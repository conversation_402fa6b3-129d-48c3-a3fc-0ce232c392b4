{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "audio_session", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audio_session-0.1.21/", "native_build": true, "dependencies": []}, {"name": "bugsnag_flutter", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/bugsnag_flutter-2.5.0/", "native_build": true, "dependencies": []}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-4.0.2/", "native_build": true, "dependencies": []}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.0.2/", "native_build": true, "dependencies": []}, {"name": "firebase_analytics", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-9.3.8/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-1.24.0/", "native_build": true, "dependencies": []}, {"name": "firebase_messaging", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-12.0.3/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "flutter_background_service_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_background_service_ios-5.0.3/", "native_build": true, "dependencies": []}, {"name": "flutter_keyboard_visibility", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility-5.4.1/", "native_build": true, "dependencies": []}, {"name": "flutter_local_notifications", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/", "native_build": true, "dependencies": []}, {"name": "flutter_ringtone_player", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_ringtone_player-3.2.0/", "native_build": true, "dependencies": []}, {"name": "flutter_secure_storage", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-8.0.0/", "native_build": true, "dependencies": []}, {"name": "flutter_tts", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_tts-4.0.2/", "native_build": true, "dependencies": []}, {"name": "gallery_saver", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gallery_saver-2.3.2/", "native_build": true, "dependencies": []}, {"name": "image_gallery_saver", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/", "native_build": true, "dependencies": []}, {"name": "just_audio", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/just_audio-0.9.37/", "native_build": true, "dependencies": ["audio_session"]}, {"name": "mpos_module_base", "path": "/Users/<USER>/.pub-cache/git/mpos_module_base-6b82ea8f08f4be32f1353c8de251521024bd9f05/", "native_build": true, "dependencies": ["connectivity_plus", "share_plus", "gallery_saver"]}, {"name": "nextpay_module_webapp", "path": "/Users/<USER>/.pub-cache/git/nextpay_module_webapp-df2cc0dd22cfc97842e3d9e44c7f7383a12e6358/", "native_build": true, "dependencies": ["mpos_module_base"]}, {"name": "package_info", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info-2.0.2/", "native_build": true, "dependencies": []}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-4.0.2/", "native_build": true, "dependencies": []}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.3.1/", "native_build": true, "dependencies": []}, {"name": "qr_code_scanner", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/", "native_build": true, "dependencies": []}, {"name": "screen_brightness_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/screen_brightness_ios-0.1.0/", "native_build": true, "dependencies": []}, {"name": "share_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.0.2/", "native_build": true, "dependencies": []}, {"name": "shared_preferences_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.3.4/", "native_build": true, "dependencies": []}, {"name": "sqflite", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.2.8+4/", "native_build": true, "dependencies": []}, {"name": "url_launcher_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.2.0/", "native_build": true, "dependencies": []}, {"name": "wakelock_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.1.4/", "native_build": true, "dependencies": ["package_info_plus"]}, {"name": "webview_flutter_wkwebview", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.9.4/", "native_build": true, "dependencies": []}], "android": [{"name": "audio_session", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audio_session-0.1.21/", "native_build": true, "dependencies": []}, {"name": "bugsnag_flutter", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/bugsnag_flutter-2.5.0/", "native_build": true, "dependencies": []}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-4.0.2/", "native_build": true, "dependencies": []}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.0.2/", "native_build": true, "dependencies": []}, {"name": "firebase_analytics", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-9.3.8/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-1.24.0/", "native_build": true, "dependencies": []}, {"name": "firebase_messaging", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-12.0.3/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "flutter_background_service_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_background_service_android-6.3.0/", "native_build": true, "dependencies": []}, {"name": "flutter_keyboard_visibility", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility-5.4.1/", "native_build": true, "dependencies": []}, {"name": "flutter_local_notifications", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/", "native_build": true, "dependencies": []}, {"name": "flutter_ringtone_player", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_ringtone_player-3.2.0/", "native_build": true, "dependencies": []}, {"name": "flutter_secure_storage", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-8.0.0/", "native_build": true, "dependencies": []}, {"name": "flutter_tts", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_tts-4.0.2/", "native_build": true, "dependencies": []}, {"name": "gallery_saver", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gallery_saver-2.3.2/", "native_build": true, "dependencies": []}, {"name": "image_gallery_saver", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/", "native_build": true, "dependencies": []}, {"name": "just_audio", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/just_audio-0.9.37/", "native_build": true, "dependencies": ["audio_session"]}, {"name": "mpos_module_base", "path": "/Users/<USER>/.pub-cache/git/mpos_module_base-6b82ea8f08f4be32f1353c8de251521024bd9f05/", "native_build": true, "dependencies": ["connectivity_plus", "share_plus", "gallery_saver"]}, {"name": "nextpay_module_webapp", "path": "/Users/<USER>/.pub-cache/git/nextpay_module_webapp-df2cc0dd22cfc97842e3d9e44c7f7383a12e6358/", "native_build": true, "dependencies": ["mpos_module_base"]}, {"name": "package_info", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info-2.0.2/", "native_build": true, "dependencies": []}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-4.0.2/", "native_build": true, "dependencies": []}, {"name": "path_provider_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.1/", "native_build": true, "dependencies": []}, {"name": "qr_code_scanner", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/", "native_build": true, "dependencies": []}, {"name": "screen_brightness_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/screen_brightness_android-0.1.0+2/", "native_build": true, "dependencies": []}, {"name": "share_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.0.2/", "native_build": true, "dependencies": []}, {"name": "shared_preferences_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.2.1/", "native_build": true, "dependencies": []}, {"name": "sqflite", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.2.8+4/", "native_build": true, "dependencies": []}, {"name": "url_launcher_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.2.0/", "native_build": true, "dependencies": []}, {"name": "wakelock_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.1.4/", "native_build": true, "dependencies": ["package_info_plus"]}, {"name": "webview_flutter_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_android-3.12.1/", "native_build": true, "dependencies": []}], "macos": [{"name": "audio_session", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audio_session-0.1.21/", "native_build": true, "dependencies": []}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-4.0.2/", "native_build": true, "dependencies": []}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.0.2/", "native_build": true, "dependencies": []}, {"name": "firebase_analytics", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-9.3.8/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-1.24.0/", "native_build": true, "dependencies": []}, {"name": "firebase_messaging", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-12.0.3/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "flutter_keyboard_visibility_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_macos-1.0.0/", "native_build": false, "dependencies": []}, {"name": "flutter_local_notifications", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/", "native_build": true, "dependencies": []}, {"name": "flutter_secure_storage_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/", "native_build": true, "dependencies": []}, {"name": "flutter_tts", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_tts-4.0.2/", "native_build": true, "dependencies": []}, {"name": "just_audio", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/just_audio-0.9.37/", "native_build": true, "dependencies": ["audio_session"]}, {"name": "package_info", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info-2.0.2/", "native_build": true, "dependencies": []}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-4.0.2/", "native_build": true, "dependencies": []}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.3.1/", "native_build": true, "dependencies": []}, {"name": "screen_brightness_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/screen_brightness_macos-0.1.0+1/", "native_build": true, "dependencies": []}, {"name": "share_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.0.2/", "native_build": true, "dependencies": []}, {"name": "shared_preferences_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.3.4/", "native_build": true, "dependencies": []}, {"name": "sqflite", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.2.8+4/", "native_build": true, "dependencies": []}, {"name": "url_launcher_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.1.0/", "native_build": true, "dependencies": []}, {"name": "wakelock_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.1.4/", "native_build": true, "dependencies": ["package_info_plus"]}], "linux": [{"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-4.0.2/", "native_build": false, "dependencies": []}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.0.2/", "native_build": false, "dependencies": []}, {"name": "flutter_keyboard_visibility_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_linux-1.0.0/", "native_build": false, "dependencies": []}, {"name": "flutter_local_notifications_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/", "native_build": false, "dependencies": []}, {"name": "flutter_secure_storage_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.2/", "native_build": true, "dependencies": []}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-4.0.2/", "native_build": false, "dependencies": []}, {"name": "path_provider_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/", "native_build": false, "dependencies": []}, {"name": "share_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.0.2/", "native_build": false, "dependencies": ["url_launcher_linux"]}, {"name": "shared_preferences_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.3.2/", "native_build": false, "dependencies": ["path_provider_linux"]}, {"name": "url_launcher_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.1.0/", "native_build": true, "dependencies": []}, {"name": "wakelock_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.1.4/", "native_build": false, "dependencies": ["package_info_plus"]}], "windows": [{"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-4.0.2/", "native_build": true, "dependencies": []}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.0.2/", "native_build": false, "dependencies": []}, {"name": "flutter_keyboard_visibility_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_windows-1.0.0/", "native_build": false, "dependencies": []}, {"name": "flutter_secure_storage_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-2.1.1/", "native_build": true, "dependencies": []}, {"name": "flutter_tts", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_tts-4.0.2/", "native_build": true, "dependencies": []}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-4.0.2/", "native_build": false, "dependencies": []}, {"name": "path_provider_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.2.1/", "native_build": false, "dependencies": []}, {"name": "screen_brightness_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/screen_brightness_windows-0.1.3/", "native_build": true, "dependencies": []}, {"name": "share_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.0.2/", "native_build": true, "dependencies": ["url_launcher_windows"]}, {"name": "shared_preferences_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.3.2/", "native_build": false, "dependencies": ["path_provider_windows"]}, {"name": "url_launcher_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.0/", "native_build": true, "dependencies": []}, {"name": "wakelock_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.1.4/", "native_build": false, "dependencies": ["package_info_plus"]}], "web": [{"name": "audio_session", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audio_session-0.1.21/", "dependencies": []}, {"name": "cloud_firestore_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_web-2.8.10/", "dependencies": ["firebase_core_web"]}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-4.0.2/", "dependencies": []}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.0.2/", "dependencies": []}, {"name": "firebase_analytics_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_web-0.4.2+7/", "dependencies": ["firebase_core_web"]}, {"name": "firebase_core_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-1.7.3/", "dependencies": []}, {"name": "firebase_messaging_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.2.0/", "dependencies": ["firebase_core_web"]}, {"name": "flutter_keyboard_visibility_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_web-2.0.0/", "dependencies": []}, {"name": "flutter_secure_storage_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/", "dependencies": []}, {"name": "flutter_tts", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_tts-4.0.2/", "dependencies": []}, {"name": "just_audio_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/just_audio_web-0.4.9/", "dependencies": []}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-4.0.2/", "dependencies": []}, {"name": "share_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.0.2/", "dependencies": ["url_launcher_web"]}, {"name": "shared_preferences_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.2.1/", "dependencies": []}, {"name": "url_launcher_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.0.19/", "dependencies": []}, {"name": "wakelock_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.1.4/", "dependencies": ["package_info_plus"]}]}, "dependencyGraph": [{"name": "audio_session", "dependencies": []}, {"name": "bugsnag_flutter", "dependencies": []}, {"name": "cloud_firestore_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "connectivity_plus", "dependencies": []}, {"name": "device_info_plus", "dependencies": []}, {"name": "firebase_analytics", "dependencies": ["firebase_analytics_web", "firebase_core"]}, {"name": "firebase_analytics_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_core", "dependencies": ["firebase_core_web"]}, {"name": "firebase_core_web", "dependencies": []}, {"name": "firebase_messaging", "dependencies": ["firebase_core", "firebase_messaging_web"]}, {"name": "firebase_messaging_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "flutter_background_service", "dependencies": ["flutter_background_service_android", "flutter_background_service_ios"]}, {"name": "flutter_background_service_android", "dependencies": []}, {"name": "flutter_background_service_ios", "dependencies": []}, {"name": "flutter_keyboard_visibility", "dependencies": ["flutter_keyboard_visibility_linux", "flutter_keyboard_visibility_macos", "flutter_keyboard_visibility_web", "flutter_keyboard_visibility_windows"]}, {"name": "flutter_keyboard_visibility_linux", "dependencies": []}, {"name": "flutter_keyboard_visibility_macos", "dependencies": []}, {"name": "flutter_keyboard_visibility_web", "dependencies": []}, {"name": "flutter_keyboard_visibility_windows", "dependencies": []}, {"name": "flutter_local_notifications", "dependencies": ["flutter_local_notifications_linux"]}, {"name": "flutter_local_notifications_linux", "dependencies": []}, {"name": "flutter_ringtone_player", "dependencies": ["path_provider"]}, {"name": "flutter_secure_storage", "dependencies": ["flutter_secure_storage_linux", "flutter_secure_storage_macos", "flutter_secure_storage_web", "flutter_secure_storage_windows"]}, {"name": "flutter_secure_storage_linux", "dependencies": []}, {"name": "flutter_secure_storage_macos", "dependencies": []}, {"name": "flutter_secure_storage_web", "dependencies": []}, {"name": "flutter_secure_storage_windows", "dependencies": []}, {"name": "flutter_tts", "dependencies": []}, {"name": "gallery_saver", "dependencies": ["path_provider"]}, {"name": "image_gallery_saver", "dependencies": []}, {"name": "just_audio", "dependencies": ["just_audio_web", "audio_session", "path_provider"]}, {"name": "just_audio_web", "dependencies": []}, {"name": "mpos_module_base", "dependencies": ["connectivity_plus", "share_plus", "gallery_saver", "shared_preferences", "path_provider", "url_launcher"]}, {"name": "nextpay_module_webapp", "dependencies": ["webview_flutter", "mpos_module_base"]}, {"name": "package_info", "dependencies": []}, {"name": "package_info_plus", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "qr_code_scanner", "dependencies": []}, {"name": "screen_brightness", "dependencies": ["screen_brightness_android", "screen_brightness_ios", "screen_brightness_macos", "screen_brightness_windows"]}, {"name": "screen_brightness_android", "dependencies": []}, {"name": "screen_brightness_ios", "dependencies": []}, {"name": "screen_brightness_macos", "dependencies": []}, {"name": "screen_brightness_windows", "dependencies": []}, {"name": "share_plus", "dependencies": ["url_launcher_web", "url_launcher_windows", "url_launcher_linux"]}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "sqflite", "dependencies": []}, {"name": "url_launcher", "dependencies": ["url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_web", "url_launcher_windows"]}, {"name": "url_launcher_android", "dependencies": []}, {"name": "url_launcher_ios", "dependencies": []}, {"name": "url_launcher_linux", "dependencies": []}, {"name": "url_launcher_macos", "dependencies": []}, {"name": "url_launcher_web", "dependencies": []}, {"name": "url_launcher_windows", "dependencies": []}, {"name": "wakelock_plus", "dependencies": ["package_info_plus"]}, {"name": "webview_flutter", "dependencies": ["webview_flutter_android", "webview_flutter_wkwebview"]}, {"name": "webview_flutter_android", "dependencies": []}, {"name": "webview_flutter_wkwebview", "dependencies": []}], "date_created": "2025-06-03 15:32:12.745398", "version": "3.7.11"}