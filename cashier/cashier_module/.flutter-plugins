# This is a generated file; do not edit or check into version control.
audio_session=/Users/<USER>/.pub-cache/hosted/pub.dev/audio_session-0.1.21/
bugsnag_flutter=/Users/<USER>/.pub-cache/hosted/pub.dev/bugsnag_flutter-2.5.0/
cloud_firestore_web=/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_web-2.8.10/
connectivity_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-4.0.2/
device_info_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.0.2/
firebase_analytics=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-9.3.8/
firebase_analytics_web=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_web-0.4.2+7/
firebase_core=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-1.24.0/
firebase_core_web=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-1.7.3/
firebase_messaging=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-12.0.3/
firebase_messaging_web=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.2.0/
flutter_background_service=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_background_service-5.1.0/
flutter_background_service_android=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_background_service_android-6.3.0/
flutter_background_service_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_background_service_ios-5.0.3/
flutter_keyboard_visibility=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility-5.4.1/
flutter_keyboard_visibility_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_linux-1.0.0/
flutter_keyboard_visibility_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_macos-1.0.0/
flutter_keyboard_visibility_web=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_web-2.0.0/
flutter_keyboard_visibility_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_windows-1.0.0/
flutter_local_notifications=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/
flutter_local_notifications_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/
flutter_ringtone_player=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_ringtone_player-3.2.0/
flutter_secure_storage=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-8.0.0/
flutter_secure_storage_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.2/
flutter_secure_storage_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/
flutter_secure_storage_web=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/
flutter_secure_storage_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-2.1.1/
flutter_tts=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_tts-4.0.2/
gallery_saver=/Users/<USER>/.pub-cache/hosted/pub.dev/gallery_saver-2.3.2/
image_gallery_saver=/Users/<USER>/.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/
just_audio=/Users/<USER>/.pub-cache/hosted/pub.dev/just_audio-0.9.37/
just_audio_web=/Users/<USER>/.pub-cache/hosted/pub.dev/just_audio_web-0.4.9/
mpos_module_base=/Users/<USER>/.pub-cache/git/mpos_module_base-6b82ea8f08f4be32f1353c8de251521024bd9f05/
nextpay_module_webapp=/Users/<USER>/.pub-cache/git/nextpay_module_webapp-df2cc0dd22cfc97842e3d9e44c7f7383a12e6358/
package_info=/Users/<USER>/.pub-cache/hosted/pub.dev/package_info-2.0.2/
package_info_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-4.0.2/
path_provider=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.1/
path_provider_android=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.1/
path_provider_foundation=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.3.1/
path_provider_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/
path_provider_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.2.1/
qr_code_scanner=/Users/<USER>/.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/
screen_brightness=/Users/<USER>/.pub-cache/hosted/pub.dev/screen_brightness-0.2.2+1/
screen_brightness_android=/Users/<USER>/.pub-cache/hosted/pub.dev/screen_brightness_android-0.1.0+2/
screen_brightness_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/screen_brightness_ios-0.1.0/
screen_brightness_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/screen_brightness_macos-0.1.0+1/
screen_brightness_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/screen_brightness_windows-0.1.3/
share_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.0.2/
shared_preferences=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.2.2/
shared_preferences_android=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.2.1/
shared_preferences_foundation=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.3.4/
shared_preferences_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.3.2/
shared_preferences_web=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.2.1/
shared_preferences_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.3.2/
sqflite=/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.2.8+4/
url_launcher=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.1.11/
url_launcher_android=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.2.0/
url_launcher_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.2.0/
url_launcher_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.1.0/
url_launcher_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.1.0/
url_launcher_web=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.0.19/
url_launcher_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.0/
wakelock_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.1.4/
webview_flutter=/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter-4.4.2/
webview_flutter_android=/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_android-3.12.1/
webview_flutter_wkwebview=/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.9.4/
