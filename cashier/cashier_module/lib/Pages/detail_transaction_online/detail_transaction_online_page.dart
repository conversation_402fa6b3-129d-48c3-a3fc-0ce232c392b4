import 'package:cashiermodule/Utilities/LocalizationCustom.dart';
import 'package:cashiermodule/constants/style.dart';
import 'package:cashiermodule/widget_custom/app_bar.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mpos_module_base/mpos_module_base_widget.dart';

import '../../Utilities/TextUtils.dart';
import '../../Utilities/configuration.dart';
import '../../app/base_view.dart';
import '../../model_instance/app_configuration.dart';
import '../../widget_custom/row_items_detail_history.dart';
import 'detail_transaction_online_controller.dart';

class DetailTransactionOnlinePage
    extends BaseView<DetailTransactionOnlineController> {
  @override
  Widget builder(BuildContext context) {
    controller.buildContext = context;
    return Scaffold(
      backgroundColor: Configuration.whiteColor,
      appBar: MPAppBar(
        isButtomLine: true,
        titleChird: Text(
          LocalizationCustom.localization("Chi tiết giao dịch"),
          style: style_S16_W400_WhiteColor,
        ),
        leftTitle: IconButton(
          icon: Icon(
            Icons.arrow_back_ios_new,
            color: Configuration.whiteColor,
            size: AppConfiguration().isKozenP12 ? 30 : 20,
          ),
          onPressed: () {
            controller.back();
          },
        ),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(10.0),
          child: Obx(
            () => ListView(
              children: [
                RowItemDetailHistory(
                    title: LocalizationCustom.localization("Tổng tiền"),
                    rightWidget: Text(
                      TextUtils.formatAmountDynamicObj(
                              controller.detailHistoryQr.value.amount) +
                          "đ",
                      style: TextStyle(
                          fontSize: Configuration.fontTitle,
                          fontWeight: FontWeight.w600,
                          fontFamily: kFontFamilyBeVietnamPro,
                          color: Configuration.blue2MainColor),
                    )),
                Divider(),
                RowItemDetailHistory(
                    title: LocalizationCustom.localization("Card holder"),
                    value: controller.detailHistoryQr.value.cardholderName),
                RowItemDetailHistory(
                    title: LocalizationCustom.localization("Card type"),
                    value: controller.detailHistoryQr.value.issuerCode),
                RowItemDetailHistory(
                    title: LocalizationCustom.localization("Mask pan"),
                    value: controller.detailHistoryQr.value.pan),
                Divider(),
                RowItemDetailHistory(
                    title: LocalizationCustom.localization("Mã giao dịch"),
                    value: controller.detailHistoryQr.value.txid),
                RowItemDetailHistory(
                    title: LocalizationCustom.localization("Created time"),
                    value: TextUtils.parserTimeFormat(
                        controller.detailHistoryQr.value.createdDate ?? 0)),
                RowItemDetailHistory(
                    title: LocalizationCustom.localization("Thanh toán bằng"),
                    value: controller.detailHistoryQr.value.issuerCode),
                Divider(),
                RowItemDetailHistory(
                    title: LocalizationCustom.localization("Loại QR"),
                    value: LocalizationCustom.localization(
                        controller.detailHistoryQr.value.qrStaticType ?? "")),
                RowItemDetailHistory(
                    title: LocalizationCustom.localization("Mô tả ĐVCNT"),
                    value: controller.detailHistoryQr.value.description),
                RowItemDetailHistory(
                    title: LocalizationCustom.localization("Mô tả ngân hàng"),
                    value: controller.detailHistoryQr.value.remark),
                SizedBox(
                  height: 20,
                ),
                SizedBox(
                  height: 30,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    controller.supportPrintReceipt.value == true
                        ? Flexible(
                            child: MPButton(
                              title:
                                  LocalizationCustom.localization('In hóa đơn'),
                              titleStyle: TextStyle(
                                  color: Configuration.greenMainColour,
                                  fontFamily: kFontFamilyBeVietnamPro,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w400),
                              backgroundColor:
                                  Configuration.greenLightMainColour,
                              onPressed: () {
                                // controller.onPressSendReceipt();
                                controller.onPressPrintQrReceipt();
                              },
                            ),
                          )
                        : SizedBox()
                  ],
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
