name: cashiermodule
description: cashier module.

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
#
# This version is used _only_ for the Runner app, which is used if you just do
# a `flutter run` or a `flutter make-host-app-editable`. It has no impact
# on any other native host app that you embed your Flutter project into.
version: 1.0.0+1

environment:
  sdk: '>=2.12.0 <3.0.0'

dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.5

  #rethinkDB
  rethink_db_ns: ^0.0.4
  http: ^0.13.6
  provider: ^6.0.5
  package_info: ^2.0.2
  uuid: ^3.0.7
  shared_preferences: ^2.1.1
  flutter_secure_storage: 8.0.0
  marquee: ^2.2.2
  intl: ^0.17.0
  flutter_datetime_picker: ^1.5.1
  flutter_dash: ^1.0.0
  encrypt: ^5.0.1
#  camera: ^0.10.5+1
#  device_info: ^2.0.3
  device_info_plus: 9.0.2
  qr_flutter: 4.1.0
  connectivity_plus: ^4.0.1
  get: ^4.6.5
  gallery_saver: ^2.3.2
  share_plus: 7.0.2
  screenshot: 1.3.0
  path_provider: ^2.0.15
  package_info_plus: 4.0.2
  flutter_screenutil: 5.7.0
  flutter_keyboard_visibility: ^5.4.1
  carousel_slider: ^4.2.1
  auto_size_text: ^3.0.0
  webview_flutter: ^4.2.2
  bugsnag_flutter: ^2.5.0
  dotted_border: ^2.0.0+3
  internet_connection_checker: ^1.0.0+1
  pull_to_refresh: ^2.0.0
  sticky_headers: ^0.3.0+2
  flutter_ringtone_player: ^3.2.0
  firebase_core: ^1.24.0
  firebase_messaging: ^12.0.3
  firebase_core_platform_interface: 4.5.1
  firebase_analytics: ^9.3.8
  qr_code_scanner: ^1.0.1
  flutter_html: ^3.0.0-beta.2
#  mqtt_client: ^9.0.0
  mqtt5_client: ^3.4.0
  shimmer: ^3.0.0
  screen_brightness: ^0.2.2
  flutter_typeahead: ^4.8.0
  url_launcher: ^6.1.6
#  flutter_appavailability: ^0.0.21

  mpos_module_static_qr:
    git:
      url: https://gitlab.saobang.vn/nextpay1/mobile-library/mpos_modules/mpos_module_static_qr
      ref: main
#    path: ../../../../mpos_modules/mpos_module_static_qr

  mpos_module_base:
    git:
      url: https://gitlab.saobang.vn/nextpay1/mobile-library/mpos_module_base
      ref: main
#    path: ../../../../mpos_modules/mpos_module_base

  mpos_module_qr:
    git:
      url: https://gitlab.saobang.vn/nextpay1/mobile-library/mpos_module_qr
      ref: main_new_ui
#    path: /Users/<USER>/Projects/flutter_module_qr

  mpos_module_installment:
    git:
      url: https://gitlab.saobang.vn/nextpay1/mobile-library/mpos_modules/mpos_module_installment
      ref: main
#    path: ../../../../mpos_modules/mpos_module_installment

  mpos_module_link:
    git:
      url: https://gitlab.saobang.vn/nextpay1/mobile-library/mpos_modules/mpos_module_link
      ref: main
#    path: ../../../../mpos_modules/mpos_module_link

  nextpay_module_webapp:
    git:
      url: https://gitlab.saobang.vn/nextpay1/mobile-library/nextpay_modules/nextpay_module_webapp
      ref: main
#    path: ../../../../mpos_modules/nextpay_module_webapp

#dependency_overrides:
#  mpos_module_base:
#    path: ../../../../mpos_modules/mpos_module_base

dev_dependencies:
  flutter_test:
    sdk: flutter

  build_runner: ^2.3.3

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add Flutter specific assets to your application, add an assets section, 
  # like this:

  assets:
    - lib/resource/images/
    - lib/resource/images/svg/
    - lib/resource/images/lottie/
  #    - resource/Images/
  #    - cashier_module/resource/Images/
  #    - cashier/cashier_module/resource/Images/
  #    - images/a_dot_ham.jpeg
  fonts:
    - family: BeVietnamPro
      fonts:
        - asset: lib/resource/font/BeVietnamPro-Thin.ttf
          weight: 100
        - asset: lib/resource/font/BeVietnamPro-ExtraLight.ttf
          weight: 200
        - asset: lib/resource/font/BeVietnamPro-Light.ttf
          weight: 300
        - asset: lib/resource/font/BeVietnamPro-Regular.ttf
          weight: 400
        - asset: lib/resource/font/BeVietnamPro-Medium.ttf
          weight: 500
        - asset: lib/resource/font/BeVietnamPro-SemiBold.ttf
          weight: 600
        - asset: lib/resource/font/BeVietnamPro-Bold.ttf
          weight: 700
        - asset: lib/resource/font/BeVietnamPro-ExtraBold.ttf
          weight: 800
        - asset: lib/resource/font/BeVietnamPro-Black.ttf
          weight: 900
        - asset: lib/resource/font/BeVietnamPro-ThinItalic.ttf
          weight: 100
          style: italic
        - asset: lib/resource/font/BeVietnamPro-ExtraLightItalic.ttf
          weight: 200
          style: italic
        - asset: lib/resource/font/BeVietnamPro-LightItalic.ttf
          weight: 300
          style: italic
        - asset: lib/resource/font/BeVietnamPro-Italic.ttf
          weight: 400
          style: italic
        - asset: lib/resource/font/BeVietnamPro-MediumItalic.ttf
          weight: 500
          style: italic
        - asset: lib/resource/font/BeVietnamPro-SemiBoldItalic.ttf
          weight: 600
          style: italic
        - asset: lib/resource/font/BeVietnamPro-BoldItalic.ttf
          weight: 700
          style: italic
        - asset: lib/resource/font/BeVietnamPro-ExtraBoldItalic.ttf
          weight: 800
          style: italic
        - asset: lib/resource/font/BeVietnamPro-BlackItalic.ttf
          weight: 900
          style: italic

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add Flutter specific custom fonts to your application, add a fonts
  # section here, in this "flutter" section. Each entry in this list should
  # have a "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages


  # This section identifies your Flutter project as a module meant for
  # embedding in a native host app.  These identifiers should _not_ ordinarily
  # be changed after generation - they are used to ensure that the tooling can
  # maintain consistency when adding or modifying assets and plugins.
  # They also do not have any bearing on your native host application's
  # identifiers, which may be completely independent or the same as these.
  module:
    androidX: true
    androidPackage: com.pps.cashiermodule
    iosBundleIdentifier: com.todo.cashier
