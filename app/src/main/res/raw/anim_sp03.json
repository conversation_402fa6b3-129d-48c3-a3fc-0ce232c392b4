{"v": "4.8.0", "meta": {"g": "LottieFiles AE 1.0.0", "a": "", "k": "", "d": "", "tc": ""}, "fr": 29.9700012207031, "ip": 0, "op": 300.00001221925, "w": 375, "h": 400, "nm": "smartPOS", "ddd": 1, "assets": [{"id": "image_0", "w": 50, "h": 50, "u": "", "p": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAEfUlEQVRogeWaQUgcVxjHf9+YdULAXWOtpZHo0kSJEM2CweYiTKv0kByS3UBpRcjSS8FL8dIoQvEiG3uxXnI2ILnF0UMChYYueAkhwqqBhKShxpBDg9CsoaHr4r4e3LXr6rozs7Pr2v5us/vem/+fN2++b973BBfwmxO11WwYpFIBJRggAcCXp3kcVAwkJiLRDaqjK8HBt8VqkGI6t5rjV1AqrOBykSLmEJl6Frw+W8QY9mkxI2FRjCqk2emN9xajXiph9HlweMp+XxucNiOGpphy20AugnqZEsK/BYej1vtYwG9O1HpUYhT4zqE2p0wmRR+1soYKGkmbiALn3FDmgMWk6EYhM/saaTF/DKBSUfK/gcpFHNGM58HvY/ka5DVSQSYy7GtmTyNnzIh/U0mMyjGRIZ4U3b/XY6bl/uA3J2o3lcxSeSYAfB6ViPrNidrcP3YZSb+dDmphW+FcWuMOdjxap82IIUp+LZukIlCiPsuOMztmRFNMlVuQU3K1bhtpMSPhUkdsN1FIc4sZCWeut42IYvQgBOXi9ejcvBBipKOnYNtszRpsZbGVMBtej850dx+9H7fQ5vuoYHuFNLea41cgMyNKhUuq0CI3L1zljK+Bd8kEY0u/WOuU1i7pXOrP0smzxnjnJYJNZ3mXTNA/f5sn8TeW+yZFP65Vs2GUTp41Rjp6CDadBWBs6b4tEwDVbBiaUsoogTbLhJrauXbqPABDC/eYWV22P0gqFdBABVzWZplQUzs3Oi8CcOvFI2cmACUYWnqjoOy0+Rq2X7Hm6mPGlu4XMZoENA4gOWzzNTDd3UeNR+fh2iuuL9wtdkjfrqSx1GwFvKvUeHSext8w8OCOK+PaMjLS0cN0dx9tvgZHN8sEvMZjXl6/X6d//jbryYSjsXKxZaTxmI+u+pNMd/cRamq3fbPsgDfw4I5rJsCmkaGFuzxce0WNR+dG50VL+VCG8c5LdNWfdBTwrGDLyHpaxK0XjwC4duo80919eD36vv2KDXhW0IC43U5jS/cZWrjHu2SCrvqTzH3+Td5140rAK0xc29pQts/M6jL987d5/X6dxmPePdeNWwGvMCpWVf9lrx8Rw0n3tcRfmKvLBOoa+aSmjt4TLXirjzL/x++0+Rr4qesyetURzNXH/BD72WXx/yKKW1UffP1FLfCV00ESqU1mVpfxVh8lUHeCQN0JPv2wmW9bL2wHvIEHMy7K3o1o2qSraXyoqZ2Rjh5q0ov/afyNq7EiH0nRjwtA68yN2WJrHBm2cqhe1pN/M7Rwt+QmBOaehYauHNm6kimUcsXIk/QslA2RKcja12qdiaxUwne7HQT18llo2A9ZAVFJZeyi2CFb846dxsM0K9mzATkpSkoIl1uQU3K17jCS3kudLKMep0zm1hd3JY1J0UeBxTIJcsJiWuMO9iz0pIPkCpVXI7Fe6AFYCQ6+RTQDB5lxCYkjWt6i6H+/GJrhsJSnC34hrgQH3yZFNziYt9mkFRPwfzvCkcuhP1STy6E/5pSLk4NnooiiaTG3Dp79A5c+3wlhdwXcAAAAAElFTkSuQmCC", "e": 1}, {"id": "image_1", "w": 318, "h": 187, "u": "", "p": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAT4AAAC7CAYAAAD8MvLvAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAgAElEQVR4nO3dXXAa6bkn8L/EmBkwDhiBSho5wCjRDI6zEZJyMmfPkVa4ale62T1u3ZwLuzZiquyqPTcjUrny1LiMa7biq43Rudna9VQZb63nIhcrvDkXK+/HQSvlItmSjFJxTI4SBYg9ciSkgTECG5vxXjRvu4EGGtQ00vTzq3KVxUf3S3887/fbXajku29B8Ssfurq4qvcIIeToiKO7K4TQSKTyja6yv/7tqh9dXQEAZnXSRQghbbeE7i4fQiNx9sLrwPfDtRCAWfXTRAghbZdBd5cXoZEoAHQDAH64GgAFPULI15cZX72KwHffAgDd/H+6/J1OFSGEtJkZX33lB4BuFL/ygdr0CCGa0FUKfNR7SwjRDjN8973dnU4FIYSojQIfIURzKPARQjSHAh8hRHMo8BFCNIcCHyFEcyjwEUI0hwIfIURzKPARQjTnjXZu3GLUweMwCH+nc0VEk/l27pKIuGx6uGx6eE+fEF6LPHyKaDKPdK7Y0jbF51Tudiqvg0qRWLbhNrxuU9335WyDEEbxwGcx6uCb6IFv3IphiYs9ky8ivJpBaHm35sXqdZvwj5eHau5jPZlHJJZFJPYU4dWM5Gde3R6Rneau2fvC/yOXhzBZ5yZLpAqIlvYfWt5teOOz7V0LP0FgYavh9sXYd5rFjZnhn+qV3M9Vrg8AsBTL8ulpMmD4Jnpw4/wAAOD2yh58NxMNv+NxGOqeT+D1OQ0ubiOeKlS93+j7crZBCKNo4OPGzAhdcsJs0AmvJVIF4SKcdJtgNugwO27F7LgVd9cy8N1MNF36GHYYMOwwYG7KjkSqAG5+U7WSpNOmh9Omx7lRM26cH8D8vR0EFrZaLkEpyWLUITw3WBbw1pN5RJN5xFMFuGx6eErHbrKUuTR7DvxTduH/3JgZlju6pn77UkWgZWll59Q3YYX/ziOElvckv78uUcqs3MbclP1QnRdy+CgW+HwTVty66ATAl+qCizsILe9W5bwehwH+6V7MjltxbtSMyOUheK9v1LxAxaUx9n2XXQ9u1ILZcSucNj0iHw3B+5MNyeBX+X25pEpb4qqjr7TvuSk7vG4TuPlNWaUM7/WN8r9FpdtW0wrwQS9yeUgoZd9dy8B/55Fkmlw2PQIz/bLPAcONmeG06QHw59hs4Ev3wcVt2ems/P0s7dyYGQGuH06bHrcuOhHfKUiWRv13Hkm+zrbhG+/BpNsknBc5v4tojyKdG+Kgt57Mw/NxDIGFLcmbLprMw3czgZm/30QmX8Sww4CIjGqM+PvhVb6UMnIlJtyAwQunlPgpdcVT/M0YWNiC68cP8KPPHgPgSxrRf++GxahrsIX2qAx6H3yaqBuI46kCfDcT+OBTvpo67DAgdMnZcD/+qV4AfFBlJTJxCbBV6VwRoeU9eK7EsF7KvJo9n2wb3usbZeclcnmoY+eFHF4HDnyuUg4N8EHPe31DVsknvJqB9yd87j/sMCAw09/0vqPJPPx3HgHgqzuuUmlELcHF7bLgG54bVHX/jH+6tyzo1aomVgot7wnB79yoGdxY7WUZXTa9UKUMLm4LpTynTQ/fhPUgyRekc0XhfA47DC2fz+DidllQb+XaIl9vBw58LGfO5ItNVyuiyTxGrsRw9voGIg+ftrR/8U0udaO0O7dnJViAD76Neh+V5rLphQ6L2yt7soMeE1reE85BfKd2hsWCR6JU6o2nCri7xncs+cZ7Wkx9NXE19iAZWWh5D9fCTwAAc1N21TNFcrgdKPC5So38ABBYeNJSW0pU6KFtfThCok4Js94wCqWEVzNCo71/urft+xPjxizC/1lpqVnsHNTqIGLtZwAQCL9u92Slvkm3SZXj3Kzg4jYyef6aFB8nQg4U+LynX5duQsu7B05Mq5yHIDcPrfC/n2UEauFK+7u7lmlbI75vogdmg04YisREYlkh01Eq4CtZQk/nXqdX7ZI4OdwO1Kvrsr0JgB+i0KmeM3H7klSJxeM01v2+UoOqo4nX23DZ9KqNI2NVuHYO4GUdGOHV6uAavLeDG+cHMDvOD0M56HXAqtSZfFGR38TOA3VwELEDBT6Wi3ZqNobXbRLaGG+v7EnedGywbS1LsazkEItmiY+BmoGPlXajiVxbtu91m4R9SA2mDi3vIjDTB7NBB/90b0sDrgE+MPmnezFXCrLBxZ3WEy0SefgUV7k+2YPGiTYoMo6v3aW9yilP3tMn4HWbhIt5XdS7S5TFqrBLpQ6NSqw6OTtuhW/cKivweRyGsjY3r9sEj9MgDHy/vbLXcgCt5LLzQbteOzDRngMFvkgsq1pPZtg/WDYjhFmKZcHNb9YMvmevb6gyj1N8DNScLrWezGPYYYD39AnFf6e482rSbWo4DZANbWnUsxxPFcCNmqumNLKB70oFPQDwOIzCPglhDtS5wYKNx9neHr10rgguuCn8nckXcS38BCNXYodmZD5rS8zki6reZKyKzbWhU6WVDgs5Q1vSuSJ8NxNCj2siVcDZ6xuw/LtfKxr0gNfHhRbHIGIHKvGFV9O4cX6gNHWpcU5/EJFYFtfCT3CV49uT4qnnh+piFncANOOgQTsSe4rZ0oIQXrdJsVIfv9gE33E0f28H4dV03c97nEbcOD8gDG1pdG7Y4PNbF51wlqYBKl1iFbdPNko/0ZYDlfjiqYIwfi144VRLPWfcmBmBmX5Zo+sDC1tl+zssY8f8073CDdbssJ6DBu/Q8p7QftXqtD12/MUzN7gxs9C0wFZxqfcvuLjd9NCW0PIebq/wmeVVrk/RJhOL8fU0RrZqCyHMgWdusE4Fs0HX9LxIj8OAhQ8HcZXrkz2ynpvfFKaIhS45Oz5MweMwIDDDz5y4u5bpyA3m/+z1NC85c27FQpecuMr14SrXh/T+69JngOMzolq95VKC9/ie2Nlxq+zz6b/zSJifG/YPKjLDggU91oYoZ+ksoi0HDnzRZL6lSeHcmBmRj/jFCRKpguxeWXF737DDoMriBLX4p3tx/xM3zAYd1kVT19QWXs0IJafZcausc8AWNpgd56uz18JPhKAtriI2s/JKaHlXaLfzTcibxiZu71NivrPXbSr7XR98mjhUTSLkcFBkOEtwcRseh0Foa4r/9Az8dx5JDnj1lCaNs97CTL5Yt1dWiri9b3bcikjsaVvbFwE+3RajDpbjOnjdJ8CNvl6iqVHPshpY0J0dt2LSbUL8p2cklwZjQ0n80/aaw0dY0FqqM41Ninhoi3/aLrujQtzexzKzWhmh1IB07+kT/LS60fJls3w3E023uRJtUG49vpsJRGJPEbxwCmaDDrcuOnHrYvnCk67SIp7MejIvex27SoGFLWEsX/DCKUQTecmbVM7KvVLr4LHqXz2ZfBGBhSdNlYraqfIciH9DIlWomtonlX6XTS+UloL3mv9dgYUtzI5bm+7wCi3vwes+gdlxK+am7DVX1240IJ1Nq1NiFgn5+lJ0BebQ8h7CqxkEZvrhm+AvfqkR8+vJPIL3tg9cSuPmNxH/6RmhiuT68YMDba8Rtpo0P6m/9rL3ncTOQeXy/5UZTngtg+DidnWJ3GkQpiC28vviqQLm7+3A4zCUxtDxbYSVKy9L8d95xJeqjTr4xnvK9l/v+2zaYTSZO5TnhBw+XfjhWgTAZDs2zqqHLrse6VwR6f3igR50Q1rDzgM97IkQAN1dZ9v6lDXhJou1cy+kEQp2hJSj5+oSQjSHAh8hRHMo8BFCNIcCHyFEcyjwEUI0hwIfIURzKPARQjSHAh8hRHMo8BFCNIcCHyFEcyjwEUI0hwIfIURzKPARQjSHAh8hRHMo8BFCNKet6/EBgMf8OfzfWoHL8IWi2935p9/A/u53hb/3d/ll0o/3NP8Q7Hoiu4MIxP6VotskhHRW2wNf5K//E8zHnim+3Z/93xgm/+r1g2cebH8OADhjU/bxjpO2TcRzJxFKfl/R7RKiJRajTnjQ/GFYgb2tgc9r24T52DN8+fQZ/sf/egh0tXNvwIPf/RkP/unPMj7ZBfvbJxt+6sV+HlNn3XAZlS2tEqI13tMmLHw4iESqAG5+s+Orgre9xAcAz569wJkRF878xcGemSr2s//8v6teU2MfhJDmRR5msZ7M88/e/mgI3p9sdDT4qRL4AGA7sQPkFPyhr6pfyqYywI6CpTOJfRBCmpfOFeG5EkPokpN/FvZHQ/B8HGvp0bJKUC3wwWQE+u1lL1mGBnDMoK/xhWov8gWs/cP/A14W4fmb9wG8EN4b+It3cf+Xf8DP/+dv8b7nm+i1n1Aq5YQQhfhuJmAx6nBu1Izw3CA8VzrzJDLVAl+voRtnTh4rfzEl/4HVhRdFRFY20PsdF0aG7NB/+bTsfct+FmdHBvD4+9/Cyn//Fd7/7tsYeNuiRNIJIS3wuk3wOI0ILe+WdWj4biYQ/cSNYYcB/unesgfaq+XIjON78PBzmN/9Jt4/ZaoKeoIXLzHwLIvxv/kBfhn9E/b3n6ubSEKIwDfRgxvnBxC5PASLUSe8ns4V4buZAAAEZvo6krYjEfj295/jj0+eYuTbNuDFy4af732WxXuT38NvHm6pkDpCiJTg4rbQoRGeK+90jMSyWIplYTbo4Juwqp62IxH44oldvHPGAX1efueIq+ctPN6uUTIkhLRdNJmH9/oGMvkiJt2mqgAXWtkFAHCj6jdJHYnAt72TxcBAcwfn+LM8TLZvUHWXEBV5HAYEZvrhcRgAVFRruf6yz4ZXMwCAc6NmdROJIxL4AAD55ru9jxW/akNCCCG1uOx6XOX6EPnodbteeDWDRKoAp00vBESAD4rrpbF84tfVoN5wloPK7gOG5oaofLHzJfZzNuznOjNWiBCtEQ9U9k30CD224bUM5qbs8J4+UTZwOZ4qYNhhKOv8UINqgW9zO4ftl611NmS7uvF4K93U2Lz9/efAsTfwYOsAc3e72zzHjpCvmXSuiOC9bdy66AQ3ahYCHxvOUhngosl8R6q6qgW+QVdPy9PJ9l8Ci5+t4MzpIvTH5OUMv3m4hff+8t3qsYNN+NnvqVeYkFZFYsouGKIk9aq6uXzL08mOA3jvX3wX99f/iPe/72r4+XQmh8d7OfzrvzQCL180/DwhRBkWow7BC6cAAKHlXeF1l42foRVPlXc2et0m9RIncmQ6N4ZsbyGx9aWsXtrf/HYL73zPBT0FPUJU5XEYYDbosBTLCvNwLUYduDG+Oht5WF4KfB0Q1W2HPzKdG2wM3/6rLhxv8Nnt9DO89+w5AGODT8rj7dlUZDuEfN1FYll0zd4ve80/3VsVDAE+6DlteiRSBdUD35Ep8TH6Y41j9cles7IrwRBCWuKbsOIqx09LCyxsVbzXA6AzbYFHJvA9+O3nsDvtsLzZOMln3v82Nh+nVUgVIaQWj8OAWxedAIBr4SdlAc5i1ME3zs/kELcFquVIBL7CiyJ+l/gCP/jrIVmf7335DCe/9TYe/PbzNqeMEFKLx8kPSp6/t1NV2vNP98Jp02Mplu1IiU+9hUh3skCNQFR4WUT6i9pV0xcvixh4720cfyr/AI18px+L/+1X/H4JIaoLLe8htLxX9brHYahZ/VWLaoHPONgP+1+d5v94WQSe5sreH6jz3Qe/+D1OOW0A5D+k5PizPI6ZvwHXPx/C8W+0Nh0m8l8jLX2PECLNZdMj8hFfc5u/t9OxsX6qBT7T06fo/dOjlr774Nk+jhVeAIbmauam7q9w/FkOvd0t9hh9RWvPE6IUT+l5G2aDDuvJPPx3WosHSjgSbXzQdfNzdZv04nkB+hPqTn4mhEgTBz3v9Y2OpuVIjOPr/aYNj7d2m5qrW3hRRPrpc1i6XzVTQyaEtIHHYUA0kUc0mUdgYavjz9Y9EoFvwGHF8q/jOPNC/lzdjY0/w+l5R9aKzYSQ9ooeglKe2JGo6lqel4anPJQ3PCWdyeF3iS/w3rftjT9MCNGcIxH4AOAHZ/qx81LXcGxeOpPD//nFHzDyb34Ay/NnKqWOEHKUqFbVzeYK2N45yDMwnuLM8Dfxy6WH2N77Pd4dtJU9PnJ//zniiV387k9pDAwP4vjODrZfUuMeIaSaegOYsy+RO8iioACwlcVJG9/B8Ytf/RF/y40Ib8UTu9jczuFk/0nkdr/EAyVmwdBCpIR8Lam3EGmvEWe+09/4gzJJLRKqxj4IIUefKoFPd/wtbK4m2j59bDO5p+g+3jC9pdi2CCGHR1sDXyQ1iMyLt9BzvAvv/8t/puy2f75a9VrvKSvecdeb/Nac48d0wKuXCD85o9g2CSGd1/YSn+/+3yL43Z/DaWxt2fmaJGaTmbqB3jfkTzNbz/Qj/aL+zI7ol28j+IfxZlNHCDnEuvDDtQiAyU4nhBBCVNHddfbIjOMjhBClUOAjhGgOBT5CiOZQ4COEaA4FPkKI5lDgI4RoDgU+QojmHImFSEltLpseLpse6VwR0WT7HqLucRhgMfKLwHbqATFiXrdJ+H80mW9qRV/2W+T+DotRB+9pEzwOI6LJHCIPs03tz+s2tf38yOFxGMCN8SsapXNFRB4+rZkm8fmup9Gxd9n04MYsZduq9WS1evtU+vgpGvi4MTP8U72IyniQiMumR2CmH163CU6bHgCQSBUQWtlDcHG75sH0uk3wT/fC4zA09b1KFqMO8Z+egdmgw9nrG6rfzJHLQ5gU3bxiS7Fsw9VqAzP98I1bhWNQ63tetwn/eLnx84hr7dM3YUXwwimYDeUXJHtWqtpLiEulJ5Mvwn/nkeSjDCv5p3tx4zw/rbFr9r6s/bGHYje7P4tRh/DcICbdJlnntF3E6ai0FMuCm9+sOo/BC6dqXp9ite4di1GH0CUnzo2aq96rFfjq7VPp46dY4AvM9AvPymyEGzNj4cNBAPxFtBTLwmXTw2nT4yrXB6/bJPkjw3ODwoFcT+YRr/geN2qG50pMVhpCl5xVN3MnLElcNPVyNotRh8jlIQw7DMjki7i9sod4qvZT5NK5ouQ+GHb8pAIYN2YWbvq7axlEk/lSDm7G3JQdFqMOvpuJej9PUeL0sMzO4zDAe9qEWxedwjMdpNS7EeXsb/7eDsKraXBjFsxN2XHrohPxnULNDNPjMCA8N1iWMXUKCyiJVAHBezuIJnLwOI3wT9kx6TYheOFU1XkMrezWLQywe13quhFfo+vJPIL3thF5mIXFqKtbinSVjtW18JOq9+Kp57J+q1wHDnzi3GQ9mcewo/FTzeI7BeHCFUd/VjqZdJvAjZkRXs2UfS+0wi+yF1zcLjspHocB9z9xY9hhkPxeJd+EFedGzcjkix0Lfuwp883mYoGZfuGC8l7faFjiavSsg8jlIThteoTX0lXv+ad6AfAXYtl5WubP0+y4VdXAFzx/CgDwo88eI7i4LbzOMtLw3CA8V2JVx8TjMCB0yYlhhwFLMf4GlHOdBjh+ibMPPk0IpbtILIt4qoAb5wf4GkuNUjILmD/67LFQwuyU2XErAMB3MyHcN5FYFuncS9y66AQ3ZgZuln+nXmmWG+Mzj6VYVjKj8U/3Cse6mevbadNjvfQwonY7cOdG6JITk24T7q5lZP/IaDIP148fVP3ASCyLu2t80PI4jFXfC69mwM1vVuVE0WQe66UTIPU9MZdNj+CFU8jki7KqRu1iNujqlsSkuGx6zE3ZkckXZQU9OdtjJYF6xyLysHzl7Egsi0xe3SquxaiD06bnSy2ioAfw18XtlT04S6VRKcMOA26v7Mk+bi6bHsMOg+SxCS5uI5MvYtJtkizBWIxvIJMvYubvN6vS2gnsXFUGqchD/vpL7zd3LlmGGLwn/dv80/yzblrJFNVqOjlw4PPdTOBHnz2WbCdoBTs5zW6LXYDh1eqSixir4nJBZdKrJtYwHVreUyTtgRm+RBO8tyP5PjsX3tPlj/X0OAzC81HV4imV0GpV61laXbY3Jd9758cPmroRWbWrVtU5mmAZbXXJMbi4Dc/HsYY1D7WwdPgmespeZ3+H1+Sn0+s2CZml1O/zuk0wG3S4u5ap2wRTyaVyk8CBq7rpXFHRXI0rtcE0CmBi/uleoTRQr30sMNOPSbcJ8/d2EIllq25otYhvFotRJ/zdqIeM9WSGV9OwGHXgxsxw2d5EPPUckYfZpi+02XFrqeQrvU5/aHkXvgkr/NN24TxzY2aELvHVuEBYvRWq2XFhpax6nV9Smjk2wOtgX+t6isSymHSb4HEaJdvCmt1fO7HzduP8AN/WubyL4IVTODdq5tvgmrh//dN8aa/WuRcfN9YRyQol0dK+pI4NC3wWow6BmX5hpEI8VUB4Na348TxUw1m8bpNQvaj3Q30TViFn9zgMODdqxlIsW7dtwOMw4CrXp1obQj3sQph0m/DFf/xe2XtLsSz8dx5J3nDse97TJxD2D0r2tDbqTWfYBVyv9BhN5uH9yQYiHw2V2rT6YDbokMkXVe8JjybzQpus97SprLRhMergn+rMo0TlDPnoNHYew3ODuMr1CR0Td9cy8N1MyK49uGx6oW28UWmW3W8AhPM26TbBN2GF9ycbNTOUYYehqv31xvmBpq5tOQ5N4GO9bgDg/6z+D/SN95R1e2fy/BifWsFSvG2pE205ru7FG03mcXuFbzdiabYYdeBGzZh0mxD5aAiej2NVv4d1iFzl+nB3LSN09nCjFsyOWzE3ZUck9rThRWkx6uCb4Bu8G+X2Lrse6X3+whUHWu/pE6oPAQou7uAq14eFDwdxdy2DSKmjgrUpEWmsFCUe/uUsBbHATL/sgCI0jSzu1AyWrPZybtSM2yt78N95hHSuCI/DgMBMP86NmhG8cKqqPyASy2LkSgwWo06o+bBxh1e5PtnXtlyHIvCx7m+nTY/bK3sNf5z/ziMhp3XZ9fCN92Buyg7fhFUyYLCe0GvhJ5I5jcdhVLU9Jp0rSrY3+e88EobsBGb6qz7DAs/dNb6ThwmvZpDOFTE3ZYd/qrfx8ZvuhdmgazgURtw7eS38pFT17YF/2o6rXB9cNr2qvbqBhS1YjDrMTdlxbtQsDE1ZimURWtnFrYvOQzG4+rARj7rw3UwgmszDN2FFgOuXPSyJNY0A9TNLdl/eXtkr2yYb23uulLm7bPqqa6/y3owm80IQvHF+QNa1LVfHp6yJx/xUHqxaosk8IrEsIrEsQst8T9218BO+02LMUvZZr9uEuSl7w6rwYcHSKNVWlShdKFK/g7XTyRl0ykpIjS7g4AV++MjZ6xsILGwhniogsLAFz8cxZPJFzI5bVW+U9t95hJN/92ucvb6Bs9c3cPLvfg3v9Q2h6UOpDis2bqxWVZa9rvT4MqVxY3ygyeSL8FyJCcGF3TcAP9ylUZWddYTcXqnfscYyHqkMNZ4qCB1izVw3rL2f1XiU0NHA10rQq4XdxJVtPax4Puk24dXtkbJ/rA3iKteHV7dHhM92ErswpQa+iqvFtb7XiG/CKgylqfcd1nObSFUP0o2LevQqMxo1pHNFIeNjNyHrFKscetOq+A5/rKV6bcWvs88dVmx4l1RJKZ4qCE0utYYBAShrTmhUeGDno97Us1Ypeaw7VtVVMugBtXOQejc3m7XAOlMOQ+4tHhxaKZrMY9JtkmxfY7+/0RATNii31hgsuRpd4GrixsxCp5hS8znZWEWpXmSLUSeUrI9K1brW/cFqFhZj7VDgm+gRMstGvass4+FGzVVTGi1GnVBqa+a4scxVyZ7djpT4Wg16tXJf4HXJrvKA+u88gvf6huS/UCm3C5UGtqo1oNnjMEheiBajTghMUhcGK9Ve5fqqhsSwamm9C8o3YRUCfaO2EnaROW36qmq3uHOk05mF122S3SnWLHY9sGMrvF7aHystHWasmshmQ4lxY2ahZlFv+BirRclpKoom81iKZfnZQHODwnXOjZkRnhsU2pYr+SaswjCWsn2L5lYrOWyuIyU+Nn0I4HOiSI1J9JXDOu5/4uZv2rVMaYzPc7hsb4IbNQtzVw/DSPlGWE8Vq26mc0VhDiwbGCz1O+KpAubv7WBuyo77n7hxt3QcWKNzJl+se3H6xvl2Gjnj78T7CvsHEV7lB6Sy3meWTrVnv0QuDyGeKiCdKwrDnwB+WpnSHVSBhS143SbMjlvhdZsQTxWEWgJbqOCwYyMIZsetWPhwEOul6008be+DTxM1S1PizFJuKY2b3xQW4fjjfyh/JvV6jQVMXLY3y4baVPrg04SipeuOBD5x9aheY3xlNeruWgbnRvkJ8pXqjX87bKLJHBKpAiZLo+CZTL7YcNUTNjzAP20vm3DPfn+9YQaskVtugGDb841bheBamU61TbpNwrNQM/ki7q5lquZuNyL3Gknn+KmBwQun+E4Cm0lYGKKZlWkatae2m+9mAuG1NHzjPcI0xfVkXtax8ziMWIplm2oaYcfNP91bVluIxLI1V1BinXOVtYt6g54PQvHn6spZe+wga32JZzrU+9xRwNbSA1pbb4wdR7V+v8dhEEpbhBxZ3V1n6YHihBBtoQeKE0K0iAIfIURzKPARQjSHAh8hRHMo8BFCNIcCHyFEcyjwEUI0hwIfIURzKPARQjSHAh8hRHMo8BFCNIcCHyFEcyjwEUI0hwIfIURzKPARQjSHAh8hRHMo8BFCNIcCHyFEcyjwEUI0hwIfIURzKPARQjSHAh8hRHMo8BFCNIcCHyFEcyjwEUI0hwIfIURzKPARQjSHAh8hRHMo8BFCNIcCHyFEcyjwEUI0hwIfIURzKPARQjSHAh8hRHMo8BFCNIcCHyFEcyjwEUI0hwIfIURzKPARQjSHAh8hRHMo8BFCNIcCHyFEcyjwEUI0hwIfIURzKPARQjSHAh8hRHMo8BFCNIcCHyFEcyjwEUI0hwIfIURzKPARQjSHAh8hRHMo8BFCNIcCHyFEcyjwEUI0hwIfIURzKPARQjSHAh8hRHMo8BFCNIcCHyFEcyjwEUI0hwIfIURzKPARQjSHAh8hRHMo8BFCNKcbQLzTiSCEEBXFu9HdFep0KlvxpYAAAAB6SURBVAghRCVLCI3EuxEaiQBY6nRqCCGk7bq7AgBr4+vu8gHIdC41hBDSbq/mSwW9UuALjcTR3eUFkOhcogghpF1ezeO/jPnZX697dUMjUXR3eYBX10ClP0LI18MSurvOioMeAHTV/LjvvrfdKSKEkDaKIzQSl3rj/wNE9vQcPC4Z9wAAAABJRU5ErkJggg==", "e": 1}, {"id": "image_2", "w": 318, "h": 187, "u": "", "p": "data:image/png;base64,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", "e": 1}, {"id": "image_3", "w": 339, "h": 784, "u": "", "p": "data:image/png;base64,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", "e": 1}], "layers": [{"ddd": 0, "ind": 1, "ty": 2, "nm": "thanh cong.ai", "cl": "ai", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [188, 171.5, 0], "ix": 2}, "a": {"a": 0, "k": [25, 25, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 3, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 42, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 60, "s": [86, 86, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 83, "s": [86, 86, 100]}, {"t": 100.000004073084, "s": [0, 0, 100]}], "ix": 6}}, "ao": 0, "ip": 3.00000012219251, "op": 102.000004154545, "st": 3.00000012219251, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 2, "nm": "thanh cong.ai", "cl": "ai", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [188, 171.5, 0], "ix": 2}, "a": {"a": 0, "k": [25, 25, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 201, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 240, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 258, "s": [86, 86, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 281, "s": [86, 86, 100]}, {"t": 298.000012137789, "s": [0, 0, 100]}], "ix": 6}}, "ao": 0, "ip": 201.000008186898, "op": 300.00001221925, "st": 201.000008186898, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 2, "nm": "thanh cong.ai", "cl": "ai", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [188, 171.5, 0], "ix": 2}, "a": {"a": 0, "k": [25, 25, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 99, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 138, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 156, "s": [86, 86, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 179, "s": [86, 86, 100]}, {"t": 196.000007983244, "s": [0, 0, 100]}], "ix": 6}}, "ao": 0, "ip": 99.0000040323527, "op": 198.000008064705, "st": 99.0000040323527, "bm": 0}, {"ddd": 1, "ind": 4, "ty": 2, "nm": "mặt trước 2", "refId": "image_1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 102, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 117, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 168, "s": [100]}, {"t": 195.000007942513, "s": [0]}], "ix": 11}, "rx": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 102, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 168, "s": [0]}, {"t": 195.000007942513, "s": [0]}], "ix": 8}, "ry": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 102, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 168, "s": [0]}, {"t": 195.000007942513, "s": [0]}], "ix": 9}, "rz": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 102, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 168, "s": [0]}, {"t": 195.000007942513, "s": [0]}], "ix": 10}, "or": {"a": 1, "k": [{"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 102, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 168, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 195.000007942513, "s": [0, 0, 0]}], "ix": 7}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 102, "s": [311.5, 82.5, 0], "to": [-19.833, 3.167, 0], "ti": [19.833, -3.167, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 124, "s": [192.5, 101.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 168, "s": [192.5, 101.5, 0], "to": [19.833, -3.167, 0], "ti": [-19.833, 3.167, 0]}, {"t": 195.000007942513, "s": [311.5, 82.5, 0]}], "ix": 2}, "a": {"a": 0, "k": [158.75, 93.25, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 102, "s": [36, 36, 36]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 168, "s": [36, 36, 36]}, {"t": 195.000007942513, "s": [36, 36, 36]}], "ix": 6}}, "ao": 0, "ip": 102.000004154545, "op": 300.00001221925, "st": 102.000004154545, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "smartpos 2 Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [187.5, 200, 0], "ix": 2}, "a": {"a": 0, "k": [169.5, 392, 0], "ix": 1}, "s": {"a": 0, "k": [34, 34, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -1.657], [1.657, 0], [0, 1.657], [-1.657, 0]], "o": [[0, 1.657], [-1.657, 0], [0, -1.657], [1.657, 0]], "v": [[3, 0], [0, 3], [-3, 0], [0, -3]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.26699999641, 0.313999998803, 0.372999991623, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [168.045, 88], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -2.761], [2.761, 0], [0, 2.761], [-2.761, 0]], "o": [[0, 2.761], [-2.761, 0], [0, -2.761], [2.761, 0]], "v": [[5, 0], [0, 5], [-5, 0], [0, -5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.447000002394, 0.505999995213, 0.583999992819, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [168.045, 88], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[18.922, 0], [0, 0], [0, 18.922], [0, 0], [-18.922, 0], [0, 0], [0, -18.922], [0, 0]], "o": [[0, 0], [-18.922, 0], [0, 0], [0, -18.922], [0, 0], [18.922, 0], [0, 0], [0, 18.922]], "v": [[123.739, 348], [-123.739, 348], [-158, 313.739], [-158, -313.739], [-123.739, -348], [123.739, -348], [158, -313.739], [158, 313.739]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.051000000449, 0.051000000449, 0.075, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [168.045, 410.56], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-39.08, -11.3], [0, 0], [14.71, -5.03], [3.45, -0.62], [50.15, 0], [36.44, 6.58], [3.18, 0.65], [0.01, 0.01], [0, 16.45], [0, 0], [-68.17, 0]], "o": [[0, 0], [0, 16.41], [-3.26, 0.68], [-36.43, 6.57], [-50.17, 0], [-3.35, -0.6], [-0.01, 0], [-14.77, -5], [0, 0], [39.09, -11.29], [68.21, 0]], "v": [[168, -29.93], [168, 0.68], [142.71, 36.06], [132.64, 38], [-0.04, 48.5], [-132.77, 37.99], [-142.57, 36.11], [-142.6, 36.1], [-168, 0.68], [-168, -29.95], [-0.04, -48.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.447000002394, 0.505999995213, 0.583999992819, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [168, 729.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -20.64], [0, 0], [14.71, -5.03], [3.51, -0.18], [0.68, 0], [0, 0], [0.71, 0.05], [3.1, 1.04], [0.01, 0.01], [0, 16.45], [0, 0], [-20.65, 0], [0, 0]], "o": [[0, 0], [0, 16.41], [-3.18, 1.09], [-0.67, 0.04], [0, 0], [-0.73, 0], [-3.41, -0.19], [-0.01, 0], [-14.77, -5], [0, 0], [0, -20.64], [0, 0], [20.65, 0]], "v": [[168, -318.62], [168, 318.62], [142.71, 354], [132.64, 355.94], [130.62, 356], [-130.61, 356], [-132.77, 355.93], [-142.57, 354.05], [-142.6, 354.04], [-168, 318.62], [-168, -318.62], [-130.61, -356], [130.62, -356]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.447000002394, 0.505999995213, 0.583999992819, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [168.045, 410.56], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 6", "np": 2, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-39.08, -11.3], [0, 0], [14.71, -5.03], [3.45, -0.62], [50.15, 0], [36.44, 6.58], [3.18, 0.65], [0.01, 0.01], [0, 16.45], [0, 0], [-68.17, 0]], "o": [[0, 0], [0, 16.41], [-3.26, 0.68], [-36.43, 6.57], [-50.17, 0], [-3.35, -0.6], [-0.01, 0], [-14.77, -5], [0, 0], [39.09, -11.29], [68.21, 0]], "v": [[168, -29.93], [168, 0.68], [142.71, 36.06], [132.64, 38], [-0.04, 48.5], [-132.77, 37.99], [-142.57, 36.11], [-142.6, 36.1], [-168, 0.68], [-168, -29.95], [-0.04, -48.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.447000002394, 0.505999995213, 0.583999992819, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [168.045, 702.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 7", "np": 2, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -20.64], [0, 0], [14.71, -5.03], [3.51, -0.18], [0.68, 0], [0, 0], [0.71, 0.05], [3.1, 1.04], [0.01, 0.01], [0, 16.45], [0, 0], [-2.17, 1.94], [-2.05, 4.87], [-1.74, 4.14], [-1.64, 1.27], [0.73, 1.86], [-21.91, 4.13], [-3.68, 3.61], [-3.42, 0.7], [-6.73, 0.44], [0, 0]], "o": [[0, 0], [0, 16.41], [-3.18, 1.09], [-0.67, 0.04], [0, 0], [-0.73, 0], [-3.41, -0.19], [-0.01, 0], [-14.77, -5], [0, 0], [2.87, -0.31], [3.94, -3.52], [1.74, -4.14], [0.8, -1.91], [1.58, -1.22], [21.85, -4.49], [5.07, -0.96], [2.49, -2.44], [3.28, -0.67], [0, 0], [20.65, 0]], "v": [[168, -318.505], [168, 318.735], [142.71, 354.115], [132.64, 356.055], [130.62, 356.115], [-130.61, 356.115], [-132.77, 356.045], [-142.57, 354.165], [-142.6, 354.155], [-168, 318.735], [-168, -289.175], [-160.09, -292.765], [-151.94, -306.195], [-146.71, -318.615], [-143.4, -323.795], [-137.54, -329.445], [-74.51, -340.745], [-60.37, -346.425], [-50.54, -355.445], [-40.81, -355.885], [130.62, -355.885]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.447000002394, 0.505999995213, 0.583999992819, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [168.045, 384.445], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 8", "np": 2, "cix": 2, "bm": 0, "ix": 7, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -21.597], [0, 0], [14.71, -5.263], [3.51, -0.189], [0.68, 0], [0, 0], [0.71, 0.053], [3.1, 1.088], [0.01, 0.01], [0, 17.212], [0, 0], [-20.65, 0], [0, 0]], "o": [[0, 0], [0, 17.171], [-3.18, 1.14], [-0.67, 0.042], [0, 0], [-0.73, 0], [-3.41, -0.199], [-0.01, 0], [-14.77, -5.232], [0, 0], [0, -21.597], [0, 0], [20.65, 0]], "v": [[168, -333.388], [168, 333.387], [142.71, 370.407], [132.64, 372.437], [130.62, 372.5], [-130.61, 372.5], [-132.77, 372.426], [-142.57, 370.459], [-142.6, 370.449], [-168, 333.387], [-168, -333.388], [-130.61, -372.5], [130.62, -372.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.26699999641, 0.313999998803, 0.372999991623, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [168.045, 394.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 9", "np": 2, "cix": 2, "bm": 0, "ix": 8, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.105, 0], [0, 0], [0, 1.105], [0, 0], [-1.105, 0], [0, -1.105], [0, 0]], "o": [[0, 0], [-1.105, 0], [0, 0], [0, -1.105], [1.105, 0], [0, 0], [0, 1.105]], "v": [[0, 25.5], [0, 25.5], [-2, 23.5], [-2, -23.5], [0, -25.5], [2, -23.5], [2, 23.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.972999961703, 0.925, 0.136999990426, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [337, 238.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 10", "np": 2, "cix": 2, "bm": 0, "ix": 9, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.105, 0], [0, 0], [0, 1.105], [0, 0], [-1.105, 0], [0, -1.105], [0, 0]], "o": [[0, 0], [-1.105, 0], [0, 0], [0, -1.105], [1.105, 0], [0, 0], [0, 1.105]], "v": [[0, 16.5], [0, 16.5], [-2, 14.5], [-2, -14.5], [0, -16.5], [2, -14.5], [2, 14.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.051000000449, 0.051000000449, 0.075, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [337, 166.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 11", "np": 2, "cix": 2, "bm": 0, "ix": 10, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-39.08, -11.3], [0, 0], [14.71, -5.03], [3.45, -0.62], [50.15, 0], [36.44, 6.58], [3.18, 0.65], [0.01, 0.01], [0, 16.45], [0, 0], [-68.17, 0]], "o": [[0, 0], [0, 16.41], [-3.26, 0.68], [-36.43, 6.57], [-50.17, 0], [-3.35, -0.6], [-0.01, 0], [-14.77, -5], [0, 0], [39.09, -11.29], [68.21, 0]], "v": [[168, -29.93], [168, 0.68], [142.71, 36.06], [132.64, 38], [-0.04, 48.5], [-132.77, 37.99], [-142.57, 36.11], [-142.6, 36.1], [-168, 0.68], [-168, -29.95], [-0.04, -48.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.051000000449, 0.051000000449, 0.075, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [168, 735.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 12", "np": 2, "cix": 2, "bm": 0, "ix": 11, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300.00001221925, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 2, "nm": "mặt sau", "refId": "image_2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 201, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 225, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 275, "s": [100]}, {"t": 296.000012056327, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 180, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 201, "s": [68.543, 60.53, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 225, "s": [68.543, 60.53, 0], "to": [40.167, 0, 0], "ti": [-40.167, 0, 0]}, {"t": 247.000010060516, "s": [309.543, 60.53, 0]}], "ix": 2}, "a": {"a": 0, "k": [158.75, 93.25, 0], "ix": 1}, "s": {"a": 0, "k": [36, 36, 100], "ix": 6}}, "ao": 0, "ip": 201.000008186898, "op": 300.00001221925, "st": 201.000008186898, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 2, "nm": "smartpos 2.ai", "cl": "ai", "refId": "image_3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [187.5, 200, 0], "ix": 2}, "a": {"a": 0, "k": [169.5, 392, 0], "ix": 1}, "s": {"a": 0, "k": [34, 34, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 300.00001221925, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 2, "nm": "mặt trước", "refId": "image_1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 24, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 74, "s": [100]}, {"t": 95.0000038694293, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 90, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 0, "s": [187.064, 412.775, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 24, "s": [187.064, 412.775, 0], "to": [0, -14, 0], "ti": [0, 14, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 41, "s": [187.064, 328.775, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 74, "s": [187.064, 328.775, 0], "to": [0, 22.167, 0], "ti": [0, -22.167, 0]}, {"t": 95.0000038694293, "s": [187.064, 461.775, 0]}], "ix": 2}, "a": {"a": 0, "k": [158.75, 93.25, 0], "ix": 1}, "s": {"a": 0, "k": [36, 36, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 300.00001221925, "st": 0, "bm": 0}], "markers": []}