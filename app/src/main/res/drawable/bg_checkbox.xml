<?xml version="1.0" encoding="UTF-8" ?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
<!--    <item android:state_checked="true" android:drawable="@drawable/ic_checkbox_selected"/>-->
<!--    <item android:state_pressed="true" android:drawable="@drawable/ic_checkbox_selected"/>-->
<!--    <item android:drawable="@drawable/ic_checkbox_normal"/>-->
    <item android:state_enabled="false">
        <inset
            android:inset="5dp"
            android:drawable="@drawable/ic_checkbox_disable">
        </inset>
    </item>
    <item android:state_checked="true">
        <inset
            android:inset="5dp"
            android:drawable="@drawable/ic_checkbox_selected">
        </inset>
    </item>
    <item android:state_pressed="true">
        <inset
            android:inset="5dp"
            android:drawable="@drawable/ic_checkbox_selected">
        </inset>
    </item>
    <item>
        <inset
            android:inset="5dp"
            android:drawable="@drawable/ic_checkbox_normal">
        </inset>
    </item>
</selector>
