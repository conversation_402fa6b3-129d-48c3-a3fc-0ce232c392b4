<?xml version="1.0" encoding="UTF-8" ?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_enabled="false">
        <shape android:shape="rectangle">
            <solid android:color="@color/gray_dark" />
            <stroke android:width="1dp" android:color="@color/white" />
            <corners
                android:radius="@dimen/corner_4"
                />
        </shape>
    </item>
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/red" />
            <stroke android:width="1dp" android:color="@color/white" />
            <corners
                android:radius="@dimen/corner_4"
                />
        </shape>
    </item>
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/red" />
            <stroke android:width="1dp" android:color="@color/white" />
            <corners
                android:radius="@dimen/corner_4"
                />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/red_3" />
            <stroke android:width="1dp" android:color="@color/red_2" />
            <corners
                android:radius="@dimen/corner_4"
                />
        </shape>
    </item>
</selector>