<?xml version="1.0" encoding="UTF-8" ?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/bg_line" />
            <stroke android:width="1dp" android:color="@color/mp_red" />
            <corners
                android:radius="@dimen/corner_3"
                />
        </shape>
    </item>
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/bg_line" />
            <stroke android:width="1dp" android:color="@color/mp_red" />
            <corners
                android:radius="@dimen/corner_3"
                />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/white" />
            <stroke android:width="1dp" android:color="@color/mp_red" />
            <corners
                android:radius="@dimen/corner_3"
                />
        </shape>
    </item>
</selector>
