<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="fill_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:paddingBottom="@dimen/mp_padding_item"
    >

    <WebView
        android:id="@+id/webContents"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/btnApply"
        android:layout_marginBottom="65dp"
        />

    <LinearLayout
        android:id="@+id/reload"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintBottom_toTopOf="@id/btnApply"
        >

        <TextView
            android:id="@+id/textView1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/error_try_again" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/button1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/btn_orange_rounded"
            android:text="@string/ALERT_BTN_OK" />

    </LinearLayout>

    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@id/btnApply"
        tools:visibility="visible"
        />

    <ImageView
        android:id="@+id/imvClose"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:src="@drawable/ic_close"
        android:layout_marginStart="@dimen/padding_large"
        android:padding="@dimen/mp_padding_item"
        />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btnApply"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:layout_margin="@dimen/mp_padding_item"
        android:background="@drawable/bg_item_red"
        android:gravity="center"
        android:minWidth="100dp"
        android:padding="@dimen/padding_normal"
        android:text="@string/BTN_USE_PROMOTION"
        android:textColor="@color/white"
        android:textSize="@dimen/text_size_normal"
        android:textAllCaps="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        />



</androidx.constraintlayout.widget.ConstraintLayout>