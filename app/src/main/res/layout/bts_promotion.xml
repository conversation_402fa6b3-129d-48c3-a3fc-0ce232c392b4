<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="fill_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_big_corner_top_white"
    android:paddingBottom="@dimen/mp_padding_item"
    >

    <LinearLayout
        android:id="@+id/vTop"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginTop="@dimen/mp_padding_item"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="@color/black"
            android:textSize="@dimen/text_size_larger"
            android:text="@string/txt_swipe_card"
            tools:visibility="visible" />


        <TextView
            android:id="@+id/tvSubTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/PAYMENT_NORMAL"
            android:textColor="@color/gray_9"
            android:textSize="@dimen/text_size_normal" />

        <TextView
            android:id="@+id/tvGuide"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/promotion_guide"
            android:textColor="@color/orange_3"
            android:layout_marginStart="@dimen/mp_padding_item"
            android:layout_marginEnd="@dimen/mp_padding_item"
            android:paddingTop="@dimen/mp_padding_half_item"
            android:paddingBottom="@dimen/mp_padding_half_item"
            android:textSize="@dimen/text_size_normal" />
    </LinearLayout>

    <TextView
        android:id="@+id/tvCancelPay"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/BTN_CANCEL_TRANS"
        android:textColor="@color/gray_8"
        android:padding="@dimen/mp_padding_half_item"
        android:textSize="@dimen/text_size_normal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        />

    <ImageView
        android:id="@+id/imvClose"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:src="@drawable/ic_close"
        android:layout_margin="@dimen/padding_large"
        android:padding="@dimen/mp_padding_item"
        android:visibility="gone"
        />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/listView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:visibility="visible"
        android:layout_marginTop="@dimen/mp_padding_item"
        android:layout_marginBottom="@dimen/mp_padding_item"
        app:layout_constrainedHeight="true"
        app:layout_constraintBottom_toTopOf="@+id/tvTitleTotalAmount"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/vTop"
        tools:itemCount="11"
        tools:listitem="@layout/item_promotion" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btnPay"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/mp_padding_item"
        android:paddingTop="@dimen/mp_padding_item"
        android:paddingBottom="@dimen/mp_padding_item"
        android:background="@drawable/bg_item_red"
        android:gravity="center"
        android:minWidth="100dp"
        android:padding="@dimen/mp_padding_item"
        android:text="@string/PAYMENT_BTN_PAY_NOW"
        android:textColor="@color/white"
        android:textSize="@dimen/text_size_normal"
        app:layout_constraintTop_toTopOf="@id/tvTitleTotalAmount"
        app:layout_constraintBottom_toBottomOf="@id/tvTotalAmount"
        app:layout_constraintEnd_toEndOf="parent"
        />

    <TextView
        android:id="@+id/tvTitleTotalAmount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/mp_padding_item"
        android:text="@string/total_amount"
        android:textColor="@color/gray_8"
        android:textSize="@dimen/text_size_normal"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toTopOf="@id/tvTotalAmount"
        />

    <TextView
        android:id="@+id/tvTotalAmount"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintHorizontal_bias="0"
        android:layout_marginStart="@dimen/mp_padding_item"
        android:layout_marginBottom="@dimen/mp_padding_half_item"
        android:textColor="@color/red_2"
        android:textSize="@dimen/text_size_huge"
        android:textStyle="bold"
        android:maxLines="1"
        tools:text="************** đ"
        android:ellipsize="end"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toTopOf="@id/tvCancelPay"
        />



</androidx.constraintlayout.widget.ConstraintLayout>