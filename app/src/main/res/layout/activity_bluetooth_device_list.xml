<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="fill_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_grey_rounded_normal"
    android:orientation="vertical"
    tools:context=".view.BluetoothDeviceList">

    <TextView
        android:id="@+id/tv_guide"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:background="@color/red_1"
        android:gravity="center_horizontal"
        android:padding="@dimen/mp_padding_medium"
        android:text="@string/msg_choose_printer"
        android:textColor="@android:color/white"
        android:textSize="@dimen/text_size_normal"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ListView
        android:id="@+id/paired_devices"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toTopOf="@id/v_button"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_guide"/>

    <TextView
        android:id="@+id/tv_not_devices_bt"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:padding="@dimen/mp_padding_medium"
        android:text="@string/err_none_printer_connect"
        android:textColor="@android:color/black"
        android:textSize="@dimen/text_size_normal"
        android:visibility="visible"
        app:layout_constraintBottom_toTopOf="@id/v_button"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_guide" />

    <LinearLayout
        android:id="@+id/v_button"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:background="@color/bg_title_select_on_act_device"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/mp_padding_half_item"
        android:paddingTop="@dimen/mp_padding_half_item"
        android:paddingRight="@dimen/mp_padding_half_item"
        android:paddingBottom="@dimen/mp_padding_half_item"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <Button
            android:id="@+id/btn_back_printer"
            android:layout_width="match_parent"
            android:layout_height="fill_parent"
            android:layout_marginEnd="@dimen/mp_padding_half_item"
            android:layout_marginStart="@dimen/mp_padding_half_item"
            android:layout_marginTop="@dimen/mp_padding_half_item"
            android:layout_marginBottom="@dimen/mp_padding_half_item"
            android:layout_weight="1"
            android:background="@drawable/btn_grey_rounded"
            android:fontFamily="sans-serif"
            android:gravity="center"
            android:minWidth="100dp"
            android:text="@string/BTN_CLOSE"
            android:textAllCaps="false"
            android:textColor="@drawable/bg_for_text_left"
            android:textSize="@dimen/text_size_normal"
            android:textStyle="bold" />

        <Button
            android:id="@+id/btn_scan_printer"
            android:layout_width="match_parent"
            android:layout_height="fill_parent"
            android:layout_marginEnd="@dimen/mp_padding_half_item"
            android:layout_marginStart="@dimen/mp_padding_half_item"
            android:layout_marginTop="@dimen/mp_padding_half_item"
            android:layout_marginBottom="@dimen/mp_padding_half_item"
            android:layout_weight="1"
            android:background="@drawable/btn_orange_rounded"
            android:fontFamily="sans-serif"
            android:gravity="center"
            android:minWidth="100dp"
            android:padding="@dimen/mp_padding_half_item"
            android:text="@string/connect_new_printer"
            android:textAllCaps="false"
            android:textColor="@drawable/bg_for_text_right"
            android:textSize="@dimen/text_size_normal"
            android:textStyle="bold"
            android:visibility="visible"
            tools:visibility="visible" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>