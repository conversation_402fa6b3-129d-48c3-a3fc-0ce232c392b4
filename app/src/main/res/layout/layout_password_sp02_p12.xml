<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/relativeLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/mp_padding_item"
        android:fontFamily="sans-serif"
        android:gravity="center"
        android:textColor="@color/black"
        android:textSize="20.0sp"
        app:fontFamily="Roboto-Bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="online pin"
        />

    <TextView
        android:id="@+id/tvMessage"
        android:layout_width="0.0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/mp_padding_medium"
        android:layout_marginTop="@dimen/mp_padding_half_item"
        android:layout_marginEnd="@dimen/mp_padding_medium"
        android:fontFamily="sans-serif"
        android:gravity="center"
        android:textColor="@color/black"
        android:textSize="16.0sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvTitle"
        tools:text="message"
        />

    <EditText
        android:id="@+id/etPin"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/mp_padding_medium"
        android:layout_marginRight="@dimen/mp_padding_medium"
        android:background="@android:color/transparent"
        android:focusable="false"
        android:fontFamily="sans-serif-medium"
        android:includeFontPadding="false"
        android:inputType="numberPassword"
        android:lines="1"
        android:maxLength="12"
        android:textColor="@color/black"
        android:textColorHint="#ffe2ebee"
        android:textSize="50.0sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvMessage"
        tools:text="123456789012"
        />

    <!--<TextView
        android:id="@+id/tv_warning"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toTopOf="@id/layoutKeyboard"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginStart="@dimen/mp_padding_item"
        android:layout_marginEnd="@dimen/mp_padding_item"
        android:textColor="@color/red"
        android:visibility="gone"
        tools:visibility="visible"
        tools:text="warning"
        android:maxLines="2"
        />-->

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layoutKeyboard"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/mp_padding_half_item"
        app:layout_constraintBottom_toTopOf="@id/btnConfirm"
        app:layout_constraintStart_toStartOf="parent">

        <View
            android:layout_width="0.0dp"
            android:layout_height="1.0dp"
            android:background="@color/white_1"
            app:layout_constraintBottom_toTopOf="@id/btn1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <View
            android:layout_width="0.0dp"
            android:layout_height="1.0dp"
            android:background="@color/white_1"
            app:layout_constraintBottom_toTopOf="@id/btn4"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <View
            android:layout_width="0.0dp"
            android:layout_height="1.0dp"
            android:background="@color/white_1"
            app:layout_constraintBottom_toTopOf="@id/btn7"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <View
            android:layout_width="0.0dp"
            android:layout_height="1.0dp"
            android:background="@color/white_1"
            app:layout_constraintBottom_toTopOf="@id/btn0"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <View
            android:layout_width="0.0dp"
            android:layout_height="1.0dp"
            android:background="@color/white_1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/btn0" />

        <View
            android:layout_width="1.0dp"
            android:layout_height="0.0dp"
            android:background="@color/white_1"
            app:layout_constraintBottom_toBottomOf="@id/btn0"
            app:layout_constraintStart_toEndOf="@id/btn1"
            app:layout_constraintTop_toTopOf="@id/btn1" />

        <View
            android:layout_width="1.0dp"
            android:layout_height="0.0dp"
            android:background="@color/white_1"
            app:layout_constraintBottom_toBottomOf="@id/btn0"
            app:layout_constraintStart_toEndOf="@id/btn2"
            app:layout_constraintTop_toTopOf="@id/btn1" />

        <TextView
            android:id="@+id/btn1"
            android:layout_width="0.0dp"
            android:layout_height="@dimen/btn_height_ldpi"
            android:background="?selectableItemBackground"
            android:fontFamily="sans-serif"
            android:gravity="center"
            android:text="@string/btn_cal_1"
            android:textColor="@color/black"
            android:textSize="24.0sp"
            app:layout_constraintEnd_toStartOf="@id/btn2"
            app:layout_constraintHorizontal_chainStyle="spread"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/btn2"
            android:layout_width="0.0dp"
            android:layout_height="@dimen/btn_height_ldpi"
            android:background="?selectableItemBackground"
            android:fontFamily="sans-serif"
            android:gravity="center"
            android:text="@string/btn_cal_2"
            android:textColor="@color/black"
            android:textSize="24.0sp"
            app:layout_constraintBottom_toBottomOf="@id/btn1"
            app:layout_constraintEnd_toStartOf="@id/btn3"
            app:layout_constraintStart_toEndOf="@id/btn1"
            app:layout_constraintTop_toTopOf="@id/btn1" />

        <TextView
            android:id="@+id/btn3"
            android:layout_width="0.0dp"
            android:layout_height="@dimen/btn_height_ldpi"
            android:background="?selectableItemBackground"
            android:fontFamily="sans-serif"
            android:gravity="center"
            android:text="@string/btn_cal_3"
            android:textColor="@color/black"
            android:textSize="24.0sp"
            app:layout_constraintBottom_toBottomOf="@id/btn1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/btn2"
            app:layout_constraintTop_toTopOf="@id/btn1" />

        <TextView
            android:id="@+id/btn4"
            android:layout_width="0.0dp"
            android:layout_height="@dimen/btn_height_ldpi"
            android:background="?selectableItemBackground"
            android:fontFamily="sans-serif"
            android:gravity="center"
            android:text="@string/btn_cal_4"
            android:textColor="@color/black"
            android:textSize="24.0sp"
            app:layout_constraintEnd_toStartOf="@id/btn5"
            app:layout_constraintHorizontal_chainStyle="spread"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/btn1" />

        <TextView
            android:id="@+id/btn5"
            android:layout_width="0.0dp"
            android:layout_height="@dimen/btn_height_ldpi"
            android:background="?selectableItemBackground"
            android:fontFamily="sans-serif"
            android:gravity="center"
            android:text="@string/btn_cal_5"
            android:textColor="@color/black"
            android:textSize="24.0sp"
            app:layout_constraintBottom_toBottomOf="@id/btn4"
            app:layout_constraintEnd_toStartOf="@id/btn6"
            app:layout_constraintStart_toEndOf="@id/btn4"
            app:layout_constraintTop_toTopOf="@id/btn4" />

        <TextView
            android:id="@+id/btn6"
            android:layout_width="0.0dp"
            android:layout_height="@dimen/btn_height_ldpi"
            android:background="?selectableItemBackground"
            android:fontFamily="sans-serif"
            android:gravity="center"
            android:text="@string/btn_cal_6"
            android:textColor="@color/black"
            android:textSize="24.0sp"
            app:layout_constraintBottom_toBottomOf="@id/btn4"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/btn5"
            app:layout_constraintTop_toTopOf="@id/btn4" />

        <TextView
            android:id="@+id/btn7"
            android:layout_width="0.0dp"
            android:layout_height="@dimen/btn_height_ldpi"
            android:background="?selectableItemBackground"
            android:fontFamily="sans-serif"
            android:gravity="center"
            android:text="@string/btn_cal_7"
            android:textColor="@color/black"
            android:textSize="24.0sp"
            app:layout_constraintEnd_toStartOf="@id/btn8"
            app:layout_constraintHorizontal_chainStyle="spread"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/btn4" />

        <TextView
            android:id="@+id/btn8"
            android:layout_width="0.0dp"
            android:layout_height="@dimen/btn_height_ldpi"
            android:background="?selectableItemBackground"
            android:fontFamily="sans-serif"
            android:gravity="center"
            android:text="@string/btn_cal_8"
            android:textColor="@color/black"
            android:textSize="24.0sp"
            app:layout_constraintBottom_toBottomOf="@id/btn7"
            app:layout_constraintEnd_toStartOf="@id/btn9"
            app:layout_constraintStart_toEndOf="@id/btn7"
            app:layout_constraintTop_toTopOf="@id/btn7" />

        <TextView
            android:id="@+id/btn9"
            android:layout_width="0.0dp"
            android:layout_height="@dimen/btn_height_ldpi"
            android:background="?selectableItemBackground"
            android:fontFamily="sans-serif"
            android:gravity="center"
            android:text="@string/btn_cal_9"
            android:textColor="@color/black"
            android:textSize="24.0sp"
            app:layout_constraintBottom_toBottomOf="@id/btn7"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/btn8"
            app:layout_constraintTop_toTopOf="@id/btn7" />

        <TextView
            android:id="@+id/btnEsc"
            android:layout_width="0.0dp"
            android:layout_height="@dimen/btn_height_ldpi"
            android:background="?selectableItemBackground"
            android:fontFamily="sans-serif"
            android:gravity="center"
            android:text="@string/btn_esc"
            android:textColor="@color/black"
            android:textSize="24.0sp"
            app:layout_constraintBottom_toBottomOf="@id/btn0"
            app:layout_constraintEnd_toStartOf="@id/btn0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/btn7"
            app:layout_constraintTop_toTopOf="@id/btn0" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btnClear"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:padding="10dp"
            app:layout_constraintBottom_toBottomOf="@id/btn0"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/btn0"
            app:layout_constraintTop_toTopOf="@id/btn0"
            app:srcCompat="@drawable/mp_ic_clear"
            tools:ignore="VectorDrawableCompat"
            android:contentDescription="@string/BTN_DELETE" />

        <TextView
            android:id="@+id/btn0"
            android:layout_width="0.0dp"
            android:layout_height="@dimen/btn_height_ldpi"
            android:background="?selectableItemBackground"
            android:fontFamily="sans-serif"
            android:gravity="center"
            android:text="@string/btn_cal_0"
            android:textColor="@color/black"
            android:textSize="24.0sp"
            app:layout_constraintEnd_toStartOf="@id/btn9"
            app:layout_constraintStart_toEndOf="@id/btn7"
            app:layout_constraintTop_toBottomOf="@id/btn7" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <Button
        android:id="@+id/btnConfirm"
        android:layout_width="0.0dp"
        android:layout_height="@dimen/btn_height_ldpi"
        android:layout_margin="@dimen/mp_padding_half_item"
        android:background="@color/bg_color_blue"
        android:fontFamily="sans-serif"
        android:gravity="center"
        android:text="@string/btn_confirm"
        android:textColor="@color/white"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupKeyboard"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="layoutKeyboard,btnConfirm" />

</androidx.constraintlayout.widget.ConstraintLayout>
