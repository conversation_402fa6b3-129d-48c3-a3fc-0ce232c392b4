<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:orientation="vertical" >

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical" >

        <WebView
            android:id="@+id/webView1"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="visible" />

        <!--<LinearLayout
            android:id="@+id/reload"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/bg_enter"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone" >

            <TextView
                android:id="@+id/percen"
                style="@style/boldFont"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/error_network"
                android:textColor="@color/txt_a"
                android:textSize="@dimen/text_size_normal" />

            <android.widget.Button
                android:id="@+id/button_reload"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/padding_item"
                android:background="@drawable/btn_grey_rounded"
                android:paddingLeft="@dimen/padding_item"
                android:paddingRight="@dimen/padding_item"
                android:text="@string/reload"
                android:textColor="@color/white"
                android:textSize="@dimen/text_size_normal"
                android:visibility="visible" />
        </LinearLayout>-->

        <ProgressBar
            android:id="@+id/progressBar1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:visibility="visible" />

    </RelativeLayout>

</LinearLayout>