<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/v_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/red"
    tools:context="com.mpos.sdk.screen.MposPaymentActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/v_toolbar"
        style="@style/BaseTheme"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/red_1"
        android:contentInsetEnd="0dp"
        android:contentInsetRight="0dp"
        app:contentInsetEnd="0dp"
        app:contentInsetLeft="0dp"
        app:contentInsetRight="0dp"
        app:contentInsetStart="0dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:targetApi="lollipop">

        <TextView
            android:id="@+id/toolbar_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:text=""
            android:textAllCaps="false"
            android:textColor="@android:color/white"
            android:textSize="@dimen/text_size_large"
            tools:text="title" />

    </androidx.appcompat.widget.Toolbar>

    <ImageView
        android:id="@+id/ic_back_lite"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_margin="@dimen/mp_padding_item"
        android:padding="@dimen/mp_padding_half_item"
        android:src="@drawable/ic_close"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <TextSwitcher
        android:id="@+id/ts_stage"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/mp_padding_half_item"
        android:gravity="center"
        android:inAnimation="@anim/push_up_in"
        android:outAnimation="@anim/push_up_out"
        android:textSize="@dimen/text_size_normal"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v_toolbar"
        tools:text="@string/DEVICE_INSTRUCTION_INSERT_SWIPE_TAP_CARD_CHARGE"
        tools:visibility="visible" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/v_detail_payment"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_margin="@dimen/mp_padding_item"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ts_stage"
        app:layout_constraintVertical_bias="0"
        tools:visibility="visible">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_white_corner">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/v_connect_device"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/layout_guide"
                    app:layout_constraintStart_toStartOf="parent"
                    >

                    <!-- #topview: show reader serialnumber-->
                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/icon_status_connect"
                        android:layout_width="18dp"
                        android:layout_height="18dp"
                        android:layout_centerVertical="true"
                        android:src="@drawable/ic_checked_green"
                        app:layout_constraintBottom_toBottomOf="@id/tv_status_connect"
                        app:layout_constraintEnd_toStartOf="@+id/tv_status_connect"
                        app:layout_constraintHorizontal_bias="0.4"
                        app:layout_constraintHorizontal_chainStyle="packed"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="@id/tv_status_connect" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tv_status_connect"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="@dimen/mp_padding_half_item"
                        android:layout_marginTop="@dimen/mp_padding_medium"
                        android:text="@string/connected_device"
                        android:textColor="@color/black_light"
                        android:textSize="@dimen/text_size_small"
                        app:layout_constraintEnd_toStartOf="@+id/tv_device_name_connect"
                        app:layout_constraintHorizontal_bias="0.4"
                        app:layout_constraintStart_toEndOf="@+id/icon_status_connect"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tv_device_name_connect"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="@dimen/mp_padding_item"
                        android:textColor="@color/bg_color_blue"
                        android:textSize="@dimen/text_size_small"
                        android:textStyle="bold"
                        app:layout_constraintBottom_toBottomOf="@id/tv_status_connect"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.4"
                        app:layout_constraintStart_toEndOf="@+id/tv_status_connect"
                        app:layout_constraintTop_toTopOf="@id/tv_status_connect"
                        tools:text="MPOS 123456789" />
                    <!-- #end topview -->

                    <!-- #status_connect -->
                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/imv_bluetooth"
                        android:layout_width="64dp"
                        android:layout_height="64dp"
                        android:layout_centerHorizontal="true"
                        android:layout_marginTop="@dimen/padding_normal"
                        android:src="@drawable/ic_bluetooth_waiting_connect"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_device_name_connect" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tv_reconnect_reader"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/imv_bluetooth"
                        android:layout_centerHorizontal="true"
                        android:text="@string/retryconnect_device"
                        android:textColor="@color/orange"
                        android:textSize="@dimen/text_size_normal"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/imv_bluetooth" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/insert_card_tv_open_device"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/tv_reconnect_reader"
                        android:layout_centerHorizontal="true"
                        android:gravity="center"
                        android:text="@string/open_device"
                        android:textColor="@color/txt_stream_title"
                        android:textSize="@dimen/text_size_small"
                        android:textStyle="italic"
                        android:layout_margin="@dimen/mp_padding_half_item"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_reconnect_reader" />

                    <ProgressBar
                        android:id="@+id/progress_bluetooth"
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:layout_below="@+id/imv_bluetooth"
                        android:layout_margin="@dimen/mp_padding_half_item"
                        android:layout_marginRight="@dimen/mp_padding_half_item"
                        android:layout_toLeftOf="@+id/tv_reconnect_reader"
                        android:indeterminateTint="@color/orange"
                        app:layout_constraintBottom_toBottomOf="@id/tv_reconnect_reader"
                        app:layout_constraintEnd_toStartOf="@id/tv_reconnect_reader"
                        app:layout_constraintTop_toTopOf="@id/tv_reconnect_reader" />
                    <!-- #end status_connect -->
                </androidx.constraintlayout.widget.ConstraintLayout>

                <!--#guide connect reader-->
                <LinearLayout
                    android:id="@+id/layout_guide"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingStart="@dimen/mp_padding_item"
                    android:paddingTop="@dimen/mp_padding_medium"
                    android:paddingEnd="@dimen/mp_padding_item"
                    android:paddingBottom="@dimen/mp_padding_medium"
                    android:visibility="gone"
                    tools:visibility="visible"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toEndOf="@id/v_connect_device"
                    app:layout_constraintEnd_toEndOf="parent"
                    >

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/insert_card_label_guide_swipe_card"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/title_guide_swipe_card"
                        android:textColor="@color/gray_4"
                        android:textSize="@dimen/text_size_normal"
                        android:textStyle="bold" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/insert_card_label_guide_swipe_card_1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/mp_padding_item"
                        android:text="@string/title_guide_swipe_card_1"
                        android:textColor="@color/gray_5"
                        android:textSize="@dimen/text_size_normal" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/insert_card_label_guide_swipe_card_2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/mp_padding_half_item"
                        android:text="@string/title_guide_swipe_card_2"
                        android:textColor="@color/gray_5"
                        android:textSize="@dimen/text_size_normal" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/insert_card_label_guide_swipe_card_3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/mp_padding_half_item"
                        android:text="@string/title_guide_swipe_card_3"
                        android:textColor="@color/gray_5"
                        android:textSize="@dimen/text_size_normal" />
                </LinearLayout>


                <LinearLayout
                    android:id="@+id/v_anim"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:visibility="visible" />

                <androidx.constraintlayout.widget.Barrier
                    android:id="@+id/brr_2"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    app:barrierDirection="bottom"
                    app:constraint_referenced_ids="v_anim,layout_guide" />

                <!--we accept-->
                <View
                    android:id="@+id/insert_card_space"
                    android:layout_width="match_parent"
                    android:layout_height="0.8dp"

                    android:background="@color/gray_dark"
                    app:layout_constraintBottom_toBottomOf="@id/insert_card_label_bank_payment"
                    app:layout_constraintTop_toTopOf="@id/insert_card_label_bank_payment" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/insert_card_label_bank_payment"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true"
                    android:background="@color/white"
                    android:padding="@dimen/mp_padding_item"
                    android:text="@string/accept_payment"
                    android:textColor="@color/gray_6"
                    android:textSize="@dimen/text_size_small"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/brr_2" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/insert_card_img_accept_payment"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:adjustViewBounds="true"
                    android:paddingLeft="50dp"
                    android:paddingRight="50dp"
                    android:src="@drawable/ic_accept_payment"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/insert_card_label_bank_payment" />


                <!--amount-->
                <View
                    android:id="@+id/v_bg_amount"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginTop="@dimen/mp_padding_item"
                    android:background="@drawable/bg_orange_rounded_bottom"
                    android:paddingBottom="@dimen/mp_padding_half_item"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/insert_card_img_accept_payment" />

                <!-- amount-->
                <!--single amount-->
                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tv_amount_pay"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/mp_padding_half_item"
                    android:paddingBottom="@dimen/mp_padding_half_item"
                    android:textColor="@color/white"
                    android:textSize="@dimen/text_size_xxlarge"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/v_bg_amount"
                    tools:text="500.000.000" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/insert_card_tv_currency"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/mp_padding_half_item"
                    android:textColor="@color/white"
                    android:textSize="@dimen/text_size_large"
                    android:textStyle="bold"
                    app:layout_constraintStart_toEndOf="@id/tv_amount_pay"
                    app:layout_constraintTop_toTopOf="@id/tv_amount_pay"
                    tools:text="vnđ" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/insert_card_tv_bill_code"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true"
                    android:layout_marginBottom="@dimen/mp_padding_half_item"
                    android:paddingBottom="@dimen/mp_padding_half_item"
                    android:textColor="@color/white"
                    android:textSize="@dimen/text_size_normal"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_amount_pay"
                    tools:text="Thanh toán đơn hàng: #1234567"
                    tools:visibility="visible" />

                <!--double amount-->
                <TextView
                    android:id="@+id/v_bottom_center_hori"
                    android:layout_width="0.7dp"
                    android:layout_height="@dimen/mp_padding_medium"
                    android:background="@color/white"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/v_bg_amount"
                    tools:visibility="visible" />

                <!--left amount-->
                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/insert_card_tv_amount_pay_title_atm"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/domestic_atm_card"
                    android:textColor="@color/white"
                    android:textSize="@dimen/text_size_small"
                    android:visibility="gone"
                    app:layout_constraintBottom_toTopOf="@id/tv_amount_atm"
                    app:layout_constraintEnd_toStartOf="@id/v_bottom_center_hori"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/v_bg_amount"
                    app:layout_constraintVertical_chainStyle="packed"
                    tools:visibility="visible" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tv_amount_atm"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/white"
                    android:textSize="@dimen/text_size_larger"
                    android:textStyle="bold"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/v_bottom_center_hori"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/insert_card_tv_amount_pay_title_atm"
                    tools:text="500.000.000"
                    tools:visibility="visible" />


                <!--right amount-->
                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/insert_card_tv_amount_pay_title_inter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/international_card"
                    android:textColor="@color/white"
                    android:textSize="@dimen/text_size_small"
                    android:visibility="gone"
                    app:layout_constraintBottom_toTopOf="@id/tv_amount_inter"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/v_bottom_center_hori"
                    app:layout_constraintTop_toTopOf="@id/v_bg_amount"
                    app:layout_constraintVertical_chainStyle="packed"
                    tools:visibility="visible" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tv_amount_inter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/white"
                    android:textSize="@dimen/text_size_larger"
                    android:textStyle="bold"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/v_bottom_center_hori"
                    app:layout_constraintTop_toBottomOf="@id/insert_card_tv_amount_pay_title_inter"
                    tools:text="500.000.000"
                    tools:visibility="visible" />


                <androidx.constraintlayout.widget.Barrier
                    android:id="@+id/brr_1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:barrierDirection="bottom"
                    app:constraint_referenced_ids="insert_card_tv_bill_code,tv_amount_inter,tv_amount_atm" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="@dimen/mp_padding_item"
                    android:background="@android:color/transparent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/brr_1" />

                <!--guide input pin-->
                <TextView
                    android:id="@+id/tv_guide_customer_pin"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/mp_padding_high"
                    android:gravity="center"
                    android:text="@string/msg_guide_enter_pin_customer"
                    android:textColor="@color/bg_color_blue"
                    android:textSize="@dimen/text_size_larger"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:visibility="visible"
                    />

                <TextView
                    android:id="@+id/tv_guide_pin"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/mp_padding_high"
                    android:layout_marginTop="@dimen/mp_padding_item"
                    android:layout_marginEnd="@dimen/mp_padding_high"
                    android:gravity="center"
                    android:text="@string/msg_guide_pin_customer"
                    android:textColor="@color/black"
                    android:textSize="@dimen/text_size_normal"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_guide_customer_pin" />

                <ImageView
                    android:id="@+id/imv_reader_input_pin"
                    android:layout_width="@dimen/reader_w"
                    android:layout_height="@dimen/reader_h"
                    android:layout_marginTop="@dimen/mp_padding_medium"
                    android:background="@drawable/dspread_input_pin"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_guide_pin" />

                <TextView
                    android:id="@+id/tv_time_pin"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/mp_padding_high"
                    android:textColor="@color/red"
                    android:textSize="@dimen/text_size_larger"
                    android:textStyle="bold"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/imv_reader_input_pin"
                    tools:text="00:00" />

                <TextView
                    android:id="@+id/tv_title_time_remaining"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/mp_padding_medium"
                    android:layout_marginBottom="@dimen/mp_padding_xhigh"
                    android:text="@string/time_input_pin_remaining"
                    android:textColor="@color/black"
                    android:textSize="@dimen/text_size_normal"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_time_pin" />

            </androidx.constraintlayout.widget.ConstraintLayout>

<!--            btn cancel-->
            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/insert_card_btn_cancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="@dimen/mp_padding_item"
                android:layout_marginBottom="@dimen/mp_padding_item"
                android:padding="@dimen/mp_padding_item"
                android:text="@string/SEND_RECEIPT_BTN_CANCEL_TRANSACTION"
                android:textColor="@color/white" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>


    <!--progress-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/v_progress"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/red"
        android:padding="@dimen/mp_padding_item"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="gone">

        <TextView
            android:id="@+id/tv_stage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="@dimen/text_size_larger"
            app:layout_constraintBottom_toTopOf="@+id/tv_detail_progress"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            tools:layout_editor_absoluteX="10dp"
            tools:text="@string/transaction_processing" />

        <TextView
            android:id="@+id/tv_detail_progress"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/mp_padding_medium"
            android:gravity="center"
            android:text="@string/warning_close_app_turn_off_reader"
            android:textColor="@color/white"
            android:textSize="@dimen/text_size_normal"
            app:layout_constraintBottom_toTopOf="@+id/pgb_process"
            app:layout_constraintTop_toBottomOf="@+id/tv_stage"
            tools:layout_editor_absoluteX="25dp" />

        <ProgressBar
            android:id="@+id/pgb_process"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="280dp"
            android:layout_height="10dp"
            android:layout_margin="@dimen/mp_padding_medium"
            android:max="100"
            android:progress="0"
            android:progressDrawable="@drawable/progress_bar_horizontal"
            app:layout_constraintBottom_toTopOf="@+id/tv_percent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_detail_progress" />

        <TextView
            android:id="@+id/tv_percent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/mp_padding_medium"
            android:textColor="@color/white"
            android:textSize="@dimen/text_size_xlarge"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/pgb_process"
            tools:text="36%" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <include
        android:id="@+id/vNotify"
        layout="@layout/v_top_notify"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        />
</androidx.constraintlayout.widget.ConstraintLayout>

