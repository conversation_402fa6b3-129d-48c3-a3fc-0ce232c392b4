package com.mpos.sdk;

import android.app.Activity;
import android.content.Context;

public abstract class MposPresenter {

    protected Context context;
    protected Activity activity;

//    public MposPresenter(Context context) {
//        this.context = context;
//    }
    public MposPresenter(Activity activity) {
        this.context = activity;
        this.activity = activity;
    }

    protected String getString(int stringId) {
        return context == null ? "" : context.getString(stringId);
    }
    protected String getString(int stringId, Object... moreParams) {
        return context == null ? "" : context.getString(stringId, moreParams);
    }

}
