package com.mpos.sdk.view;

import android.app.Activity;
import android.app.AlertDialog;
import android.app.Dialog;
import android.app.DialogFragment;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.JavascriptInterface;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import com.mpos.sdk.R;

public class DialogEnterPin extends DialogFragment {


    Context context;
    public WebView wv;
    ItfResultInputPin callback;

    public static DialogEnterPin newInstance(String urlLoad) {

        Bundle bundle = new Bundle();
        bundle.putString("url", urlLoad);
        DialogEnterPin fragment = new DialogEnterPin();
        fragment.setArguments(bundle);
        return fragment;
    }

    public void setCallback(ItfResultInputPin callback) {
        this.callback = callback;
    }


    @Override
    public void onAttach(Activity activity) {
        super.onAttach(activity);
        context = activity;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(STYLE_NO_FRAME, android.R.style.Theme_Translucent);
    }

    @Override
    public Dialog onCreateDialog(Bundle savedInstanceState) {

        return new Dialog(getActivity(), getTheme()){
            @Override
            public void onBackPressed() {
                showDialogConfirmCancel();
            }
        };

//        Dialog dialog = super.onCreateDialog(savedInstanceState);
//        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
////        dialog.getWindow().getAttributes().windowAnimations = R.style.updownDialog;
//        dialog.getWindow().setLayout(LinearLayout.LayoutParams.MATCH_PARENT,
//                LinearLayout.LayoutParams.MATCH_PARENT);
//
//        return dialog;
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {

        String urlLoad = "";
        Bundle bundle = getArguments();
        if (bundle != null) {
            urlLoad = bundle.getString("url");
        }

        final View viewRoot = inflater.inflate(R.layout.view_enter_pin, container);

        wv = (WebView) viewRoot.findViewById(R.id.webView1);

        wv.getSettings().setJavaScriptEnabled(true);
        wv.addJavascriptInterface(new WebAppInterface(context), "Android");
        wv.loadUrl(urlLoad);
        wv.setWebViewClient(new WebViewClient() {
            @Override
            public void onPageFinished(WebView view, String url) {
                try {
                    viewRoot.findViewById(R.id.progressBar1).setVisibility(View.GONE);
                } catch (Exception e) {
                }
                super.onPageFinished(view, url);
            }

            @Override
            public void onPageStarted(WebView view, String url, Bitmap favicon) {
                super.onPageStarted(view, url, favicon);
            }

            @Override
            public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
                super.onReceivedError(view, errorCode, description, failingUrl);
            }
        });

        return viewRoot;
    }


    void onSuccess() {
        if (callback != null) {
            callback.onSuccessInputPin();
        }

        dismiss();
    }

    void onFailure() {
        if (callback != null) {
            callback.onFailureInputPin();
        }

        dismiss();
    }

    void showDialogConfirmCancel() {
        final AlertDialog.Builder builder = new AlertDialog.Builder(context);
        builder.setMessage(getString(R.string.ALERT_PAYMENT_CANCEL_TRANSACTION_MSG)).setCancelable(false)
                .setPositiveButton(getString(R.string.ALERT_BTN_OK), new DialogInterface.OnClickListener() {
                    public void onClick(final DialogInterface dialog, final int id) {
                        onFailure();
                    }
                }).setNegativeButton(getString(R.string.ALERT_BTN_NO), new DialogInterface.OnClickListener() {
            public void onClick(final DialogInterface dialog, final int id) {
            }
        });
        final AlertDialog alert = builder.create();
        alert.show();
    }

    public class WebAppInterface {
        Context mContext;

        WebAppInterface(Context c) {
            mContext = c;
        }

        @JavascriptInterface
        public void Done() {
            onSuccess();
        }
    }

    public interface ItfResultInputPin{
        void onSuccessInputPin();
        void onFailureInputPin();
    }

}
