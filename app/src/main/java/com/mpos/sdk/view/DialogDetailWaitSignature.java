package com.mpos.sdk.view;

import android.app.Dialog;
//import android.app.DialogFragment;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Patterns;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.fragment.app.DialogFragment;

import com.mpos.sdk.R;
import com.mpos.sdk.util.CardUtils;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.Utils;

public class DialogDetailWaitSignature extends BaseDialogFragment {

	Button buttonOk;
//    Button buttonCancel;
	TextView tvTime, tvPan, tvAmount, tvHolderName, tvDesc;
    EditText edtEmail;

    String time, pan, amount, holderName, desc, email;

    OnMyClickListener clickListener;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setColorStatusBar(R.color.red_1);
        setStyle(STYLE_NO_FRAME, android.R.style.Theme_Translucent);
    }

	@NonNull
    @Override
	public Dialog onCreateDialog(Bundle savedInstanceState) {
		Dialog dialog = super.onCreateDialog(savedInstanceState);
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        if (dialog.getWindow() != null) {
            dialog.getWindow().setLayout(LinearLayout.LayoutParams.MATCH_PARENT,
                    LinearLayout.LayoutParams.MATCH_PARENT);
        }
        return dialog;
	}

	@Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
            Bundle savedInstanceState) {
		View view = inflater.inflate(R.layout.dialog_details_wait_signature, container);
		setContentCustom(view);
		return view;
	}

	private void setContentCustom(View v) {

//		buttonCancel = v.findViewById(R.id.btn_cancel);
		buttonOk	 = v.findViewById(R.id.btn_ok);

		tvTime = v.findViewById(R.id.tv_time);
		tvHolderName	= v.findViewById(R.id.tv_holder_name);
		tvPan	= v.findViewById(R.id.tv_pan);
		tvAmount= v.findViewById(R.id.tv_amount);
		tvDesc	= v.findViewById(R.id.tv_desc);
		edtEmail= v.findViewById(R.id.edt_email);

		try {
			tvTime.setText(Utils.convertTimestamp(Long.parseLong(time)));
		} catch (Exception e) {
			Utils.LOGE("Exception", "convertTimeStamp: ", e);
		}
		tvHolderName.setText(holderName);
		tvPan.setText(CardUtils.getMaskedPan(pan));
		tvAmount.setText(String.format("%s%s", Utils.zenMoney(amount), ConstantsPay.CURRENCY_SPACE_PRE));
        tvDesc.setText(TextUtils.isEmpty(desc)?"":desc);

        if (!TextUtils.isEmpty(email)) {
            edtEmail.setText(email);
            edtEmail.setEnabled(false);
        } else {
            edtEmail.clearFocus();
        }

        buttonOk.setOnClickListener(v12 -> {
            // check email
            String email = edtEmail.getText().toString().trim();
            if (!TextUtils.isEmpty(email) && !Patterns.EMAIL_ADDRESS.matcher(email).matches()) {
                edtEmail.setError(getString(R.string.error_wrong_email));
                return;
            }
            if (clickListener != null) {
                clickListener.clickOk(DialogDetailWaitSignature.this, email);
            }
        });
//        buttonCancel.setOnClickListener(v1 -> {
//            if(clickListener!=null){
//                clickListener.clickCancel(DialogDetailWaitSignature.this);
//            }
//        });
	}

	public void initVariable(String time, String pan, String amount,
			String holderName, String desc) {
        initVariable(time, pan, amount, holderName, desc, null);
	}
	public void initVariable(String time, String pan, String amount,
			String holderName, String desc, String email) {
		this.time = time;
		this.pan = pan;
		this.amount = amount;
		this.holderName = holderName;
		this.desc = desc;
		this.email = email;
	}

	public void setClickListener(OnMyClickListener clickListener) {
		this.clickListener = clickListener;
	}

	public interface OnMyClickListener{
        void clickOk(DialogFragment d, String email);
        void clickCancel(DialogFragment d);
    }

}
