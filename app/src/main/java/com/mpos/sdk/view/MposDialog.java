package com.mpos.sdk.view;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.os.Handler;
import android.text.Spanned;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.mpos.sdk.R;
import com.mpos.sdk.core.common.UiCustomUtil;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.databinding.DialogMposErrorBinding;
import com.mpos.sdk.util.MyTextUtils;
import com.mpos.sdk.util.Utils;
import com.mpos.sdk.util.UtilsSystem;

import java.util.Objects;
import java.util.Timer;
import java.util.TimerTask;


/**
 * Created by noe on 5/13/16
 */
public class MposDialog extends Dialog {

    private boolean setContentCustom = false;
    private Context context;

    public final static int TYPE_DIALOG_INFO    = 1;
    public final static int TYPE_DIALOG_SUCCESS = 2;
    public final static int TYPE_DIALOG_WARNING = 3;
    public final static int TYPE_DIALOG_ERROR   = 4;

    DialogMposErrorBinding binding;

    private String title;
    private String desc;
    private String phoneSupport;
    private String emailSupport;
    private int type = 0;

    private boolean versionLite = false;

    View.OnClickListener onClickCancelListener;
    View.OnClickListener onClickOKListener;
    private boolean isHandlerButtonOK = false;
    public Handler handlerCountdownDismissDialog = new Handler();

    public MposDialog(@NonNull Context context) {
        super(context);
        this.context = context;
    }

    public MposDialog(@NonNull Context context, int themeResId) {
        super(context, themeResId);
        this.context = context;
    }

    protected MposDialog(@NonNull Context context, boolean cancelable, @Nullable DialogInterface.OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
        this.context = context;
    }


    public static MposDialog newInstance(final Context context, final int errorCode, final String desDetailErrorOnTop,
                                                    final String nameScene) {
        final MposDialog mposDialog = new MposDialog(context);

        /*
         * title message error
         */
        String title = context.getString(R.string.dialog_error_title_default);
        if (errorCode > 0) {
            title = context.getString(R.string.mp_dialog_error_title, errorCode);
        }
        mposDialog.setTitle(title);
        mposDialog.setType(MposDialog.TYPE_DIALOG_ERROR);

        /*
         * content message error
         */
        mposDialog.setDesDialogErrorTop(desDetailErrorOnTop);

        return mposDialog;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initView();
    }

    public void setHandlerButtonOK(boolean isActionOK) {
        isHandlerButtonOK = isActionOK;
    }

    @Override
    public void show() {
        if (PrefLibTV.getInstance(context).get(PrefLibTV.isAutoCloseDialog, Boolean.class)) {
            handlerCountdownDismissDialog.postDelayed(runnable, 10000);
        }
        super.show();
    }

    Runnable runnable = () -> {
        if (isShowing()) {
            try {
                if (type == TYPE_DIALOG_ERROR) {
                    if (onClickCancelListener != null) {
                        onClickCancelListener.onClick(null);
                    }
                    return;
                }

                if (type == TYPE_DIALOG_INFO) {
                    if (onClickOKListener != null) {
                        onClickOKListener.onClick(null);
                    }
                    return;
                }

                if (onClickOKListener == null) {
                    if (onClickCancelListener != null) {
                        onClickCancelListener.onClick(null);
                    }
                    return;
                }

                if (isHandlerButtonOK) {
                    if (onClickCancelListener != null) {
                        onClickCancelListener.onClick(null);
                    }
                }else {
                    if (onClickOKListener != null) {
                        onClickOKListener.onClick(null);
                    }
                }
            } catch (Exception e) {

            }
        }
    };

    @Override
    public void dismiss() {
        if (handlerCountdownDismissDialog != null) {
            handlerCountdownDismissDialog.removeCallbacks(runnable);
            handlerCountdownDismissDialog.removeCallbacksAndMessages(null);
        }
        super.dismiss();
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public void setType(int type) {
        this.type = type;
        initView();
    }


    private void initView() {
        if (setContentCustom)
            return;
        setContentCustom = true;
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        versionLite = PrefLibTV.getInstance(context).get(PrefLibTV.versionLite, Boolean.class, false);

//        setContentView(versionLite ? R.layout.dialog_mpos_lite_error : R.layout.dialog_mpos_error);

        binding = DialogMposErrorBinding.inflate(LayoutInflater.from(context));

        setContentView(binding.getRoot());
//        setContentView(R.layout.dialog_mpos_error);
        Objects.requireNonNull(getWindow()).setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        setCanceledOnTouchOutside(false);
        setCancelable(false);

//        unbinder = ButterKnife.bind(this);

        phoneSupport = UiCustomUtil.getInstance().getSdkCustom().getHotline();
        emailSupport = UiCustomUtil.getInstance().getSdkCustom().getContactEmail();

        if (android.text.TextUtils.isEmpty(phoneSupport)) {
            binding.btnViaPhone.setVisibility(View.GONE);
        }
        else {
            binding.btnViaPhone.setText(MyTextUtils.setTextHtml(context.getString(R.string.dialog_error_via_contact, phoneSupport)));
        }
        if (android.text.TextUtils.isEmpty(emailSupport)) {
            binding.btnViaEmail.setVisibility(View.GONE);
        }
        else {
            binding.btnViaEmail.setText(MyTextUtils.setTextHtml(context.getString(R.string.dialog_error_via_email, emailSupport)));
        }
        binding.vSpaceButtonVia.setVisibility(binding.btnViaPhone.getVisibility() == View.VISIBLE && binding.btnViaEmail.getVisibility() == View.VISIBLE
                ? View.VISIBLE : View.GONE);

        if (versionLite) {
            binding.bgTop.setBackgroundResource(R.drawable.bg_corner_top_white);
            binding.tvTitleDialogCodeError.setTextColor(Color.BLACK);
            binding.btnClose.setTextColor(ContextCompat.getColor(context, R.color.mp_blue_1));
            binding.btnViaEmail.setTextColor(ContextCompat.getColor(context, R.color.mp_blue_1));
            binding.btnOk.setBackgroundResource(R.drawable.btn_blue_rounded);
            binding.icStatus.setVisibility(View.GONE);
            binding.brBelowError.setVisibility(View.GONE);

            FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams) binding.tvDesDialogError.getLayoutParams();
            layoutParams.setMarginStart((int) context.getResources().getDimension(R.dimen.mp_padding_item));
            layoutParams.setMarginEnd((int) context.getResources().getDimension(R.dimen.mp_padding_item));
            binding.tvDesDialogError.setLayoutParams(layoutParams);

            if (type == TYPE_DIALOG_ERROR) {
                binding.tvTitleDialogCodeError.setText(title);
//                binding.tvTitleDialogCodeError.setTextColor(context.getResources().getColor(R.color.red));
            }
            else {
                binding.tvTitleDialogCodeError.setText(context.getString(R.string.mp_notice));
            }
        }
        else {
            switch (type) {
                case TYPE_DIALOG_INFO    :
                    binding.tvTitleDialogCodeError.setText(context.getString(R.string.mp_notice));
                    binding.bgTop.setBackgroundResource(R.drawable.bg_corner_top_blue);
                    binding.icStatus.setBackgroundResource(R.drawable.ic_info_white);
                    setVisibleViaPhoneEmail(true);
                    break;
                case TYPE_DIALOG_SUCCESS :
                    binding.tvTitleDialogCodeError.setText(context.getString(R.string.status_successful));
                    binding.bgTop.setBackgroundResource(R.drawable.bg_corner_top_green);
                    binding.icStatus.setBackgroundResource(R.drawable.ic_success_white);
                    setVisibleViaPhoneEmail(false);
                    break;
                case TYPE_DIALOG_WARNING :
                    binding.tvTitleDialogCodeError.setText(context.getString(R.string.mp_notice));
                    binding.bgTop.setBackgroundResource(R.drawable.bg_corner_top_orange);
                    binding.icStatus.setBackgroundResource(R.drawable.ic_notice_white);
                    setVisibleViaPhoneEmail(true);
                    break;
                case TYPE_DIALOG_ERROR   :
                    binding.tvTitleDialogCodeError.setText(title);
                    binding.bgTop.setBackgroundResource(R.drawable.bg_corner_top_red);
                    binding.icStatus.setBackgroundResource(R.drawable.ic_fail_red);
                    setVisibleViaPhoneEmail(true);
                    break;
            }
        }
        showMerchantInfo();
    }

    private void showMerchantInfo() {
        String merchantInfo = PrefLibTV.getInstance(context).get(PrefLibTV.MERCHANT_INFO, String.class);
        if (TextUtils.isEmpty(merchantInfo)) {
            binding.tvMerchantInfo.setVisibility(View.GONE);
        }
        else {
            binding.tvMerchantInfo.setText(merchantInfo);
        }
    }

//    @OnClick({R2.id.btn_via_email, R2.id.btn_via_phone, R2.id.v_count_down})
    protected void onClickView(View v) {
        if (v.getId() == R.id.btn_via_email) {
            UtilsSystem.shareViaEmail(context, emailSupport,null, "User: " + PrefLibTV.getInstance(context).getUserId() + "  SN: " + PrefLibTV.getInstance(context).getSerialNumber() + "  ErrorCode: " + title + "  Content: " + desc);
        }
        else if (v.getId() == R.id.btn_via_phone) {
            UtilsSystem.callToSupport(phoneSupport, context);
        }
    }

    public void setVisibleViaPhoneEmail(boolean show) {
        binding.layoutContainTwoBtnSupport.setVisibility(show ? View.VISIBLE : View.GONE);
    }

    /**
     *
     */

    int timeoutDisableButton = 5;
    Timer timer;
    TimerTask timerTask;
    Handler handler = new Handler();

    public void setTimeoutDisableButton(int timeoutDisableButton) {
        this.timeoutDisableButton = timeoutDisableButton;
    }

    public void startCountDownTime() {
//        Utils.LOGD(TAG, "startCountDownTime: ");
        binding.vCountDown.setVisibility(View.VISIBLE);
        binding.vCountDown.setOnClickListener((view)->{});
        timerTask = new TimerTask() {
            @Override
            public void run() {
                if (timeoutDisableButton < 0) {
                    cancelTimeCountDown();
                }
                else {
                    handler.post(() -> showTimeRemaining(timeoutDisableButton--));
                }
            }
        };

        timer = new Timer();
        timer.schedule(timerTask, 300, 1000);
    }

    private void showTimeRemaining(int time) {
        if (time == 0) {
            runAnimationFadeOut();
            binding.tvCountDown.setText("");
        }
        else {
            binding.tvCountDown.setText(context.getString(R.string.ALERT_COUNT_DOWN_TIME, String.valueOf(time)));
        }
    }

    private void runAnimationFadeOut() {
        Animation animation = AnimationUtils.loadAnimation(context, R.anim.fade_out);
        animation.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {
                hideViewCountDown();
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });
        binding.vCountDown.startAnimation(animation);
    }

    private void hideViewCountDown() {
        binding.vCountDown.setVisibility(View.GONE);
//        motionLayout.removeView(vCountDown);
    }

    private void cancelTimeCountDown() {
        if (timer != null) {
            timer.cancel();
            timer = null;
        }

        if (timerTask != null) {
            timerTask.cancel();
            timerTask = null;
        }
    }

    /**
     * listener when user click button CLOSE
     */
    public void setOnClickListenerDialogClose(View.OnClickListener onClickListener) {
        this.onClickCancelListener = onClickListener;
        binding.btnClose.setOnClickListener(onClickListener);
    }


    public void setButtonCloseLabel(String labelButtonClose) {
        binding.btnClose.setText(labelButtonClose);
    }

    /**
     * set title dialog error
     */
    public void setTitleDialogErrorCode(String title) {
        binding.tvTitleDialogCodeError.setText(title);
    }

    public TextView getTitleDialogErrorCode() {
        return binding.tvTitleDialogCodeError;
    }

    /**
     * set description dialog error on top
     *
     * @param des spanned
     */
    public void setDesDialogErrorTop(Spanned des) {
        binding.tvDesDialogError.setText(des);
    }

    public void setDesDialogErrorTop(String des) {
        binding.tvDesDialogError.setText(des);
    }

    public TextView getDesDialogError() {
        return binding.tvDesDialogError;
    }


    public void setEnableTwoButtonBottom(boolean isShow) {
        if (isShow) {
            binding.btnClose.setVisibility(View.GONE);
            binding.layoutContainTwoButton.setVisibility(View.VISIBLE);
        }
    }

    // BUTTON OK

    public Button getBtnOk() {
        return binding.btnOk;
    }

    public void setLabelForButtonOk(String label) {

        binding.btnOk.setText(label);
    }

    public void setOnClickListenerButtonOk(View.OnClickListener onClickListener) {
        this.onClickOKListener = onClickListener;
        binding.btnOk.setOnClickListener(onClickListener);
    }

    // BUTTON CANCEL
    public void setLabelForButtonCancel(String label) {

        binding.btnCancel.setText(label);
    }

    public void setEnableButtonCancel(boolean isShow) {

        if (isShow) {
            binding.btnCancel.setVisibility(View.VISIBLE);
            binding.vSpaceButton.setVisibility(View.VISIBLE);
        }
        else {
            binding.btnCancel.setVisibility(View.GONE);
            binding.vSpaceButton.setVisibility(View.GONE);
        }
    }

    public void setOnClickListenerButtonCancel(View.OnClickListener onClickListener) {
        this.onClickCancelListener = onClickListener;
        binding.btnCancel.setOnClickListener(onClickListener);
    }

}
