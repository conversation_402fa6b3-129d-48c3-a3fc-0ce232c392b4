package com.mpos.sdk.util;

import android.app.ProgressDialog;
import android.content.Context;
import android.content.pm.PackageManager;
import android.text.TextUtils;
import android.widget.Toast;

import androidx.annotation.NonNull;

import com.mpos.sdk.BuildConfig;
import com.mpos.sdk.R;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Locale;
import java.util.UUID;

public class Utils {

//	public final static boolean mDEBUG = false;
	public final static boolean mDEBUG = BuildConfig.debugLog;

    private static final String TAG = "Utils";

	public static void LOGI(String tag, String msg) {
		if (mDEBUG)
			android.util.Log.i(tag, msg!=null?msg:"");
	}

    public static void LOGE(String tag, String msg) {
//        if (mDEBUG)
//            android.util.Log.e(tag, msg != null ? msg : "");
        LOGE(tag, msg, null);
    }

    public static void LOGE(String tag, String msg, Throwable tr) {
        if (mDEBUG)
            android.util.Log.e(tag, msg != null ? msg : "", tr);
    }

	public static void LOGD(String tag, String msg) {
		if (mDEBUG)
			android.util.Log.d(tag, msg != null ? msg : "");
	}

	public static void LOGW(String tag, String msg) {
		if (mDEBUG)
			android.util.Log.w(tag, msg != null ? msg : "");
	}

	public static void mToast(Context c, String msg) {
		Toast.makeText(c, msg, Toast.LENGTH_SHORT).show();
	}

	public static String convertTimestamp(long timeStamp) {
        try {
            Calendar mydate = Calendar.getInstance();
            mydate.setTimeInMillis(timeStamp);
            return mydate.get(Calendar.HOUR_OF_DAY) + "h" + mydate.get(Calendar.MINUTE) + "p - " + mydate.get(Calendar.DAY_OF_MONTH) + "/" + (mydate.get(Calendar.MONTH) + 1) + "/"
                    + mydate.get(Calendar.YEAR);
        } catch (Exception e) {
            return "";
        }
    }

	public static long returnDate(long timeStamp) {
		Calendar mydate = Calendar.getInstance();
		mydate.setTimeInMillis(timeStamp);
		//		mydate.set(Calendar.HOUR_OF_DAY, 0);
		//		mydate.set(Calendar.MINUTE, 0);
		//		mydate.set(Calendar.SECOND, 0);
		Calendar result = Calendar.getInstance();
		result.set(mydate.get(Calendar.YEAR), mydate.get(Calendar.MONTH), mydate.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
		result.set(Calendar.MILLISECOND, 0);
		//		Calendar result = Calendar.getInstance();
		//		result.set(mydate.get(Calendar.YEAR), mydate.get(Calendar.MONTH), mydate.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
		return result.getTimeInMillis();
	}

	/**
	 * 
	 * @param timeStamp: timestamp
	 * @param flag: 1: HH:mm ; 2: dd/MM/yyyy ; 3: dd/MM/yyyy HH:mm:ss ; 4: HH:mm:ss dd/MM/yyyy ; 5: HH:mm:ss
	 * @return convert to string by flag
	 */
	public static String convertTimestamp(long timeStamp, int flag) {
//		Calendar mydate = Calendar.getInstance();
//		mydate.setTimeInMillis(timeStamp);
		switch (flag) {
			case 1 :
				return convertTimestamp(timeStamp, "HH:mm");
			case 2 :
				return convertTimestamp(timeStamp, "dd/MM/yyyy");
			case 3 :
				return convertTimestamp(timeStamp, "dd/MM/yyyy HH:mm:ss");
            case 4 :
                return convertTimestamp(timeStamp, "HH:mm:ss dd/MM/yyyy");
            case 5 :
                return convertTimestamp(timeStamp, "HH:mm:ss");
			case 6 :
				return convertTimestamp(timeStamp, "dd/MM/yy HH:mm:ss");
		}
		return "";
	}

    public static String convertTimestamp(long timeStamp, String format) {
        Calendar mydate = Calendar.getInstance();
        mydate.setTimeInMillis(timeStamp);

        try {
            return new SimpleDateFormat(format).format(mydate.getTime());
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    public static String getOnlyNumber(@NonNull String amountOrigin) {
        if (!TextUtils.isEmpty(amountOrigin)) {
            return amountOrigin.replaceAll("\\D", "");
        }
        return "";
    }

	public static String convertTimestampB(long timeStamp) {
		Calendar mydate = Calendar.getInstance();
		mydate.setTimeInMillis(timeStamp * 1000);
		return mydate.get(Calendar.DAY_OF_MONTH) + "/" + (mydate.get(Calendar.MONTH) + 1) + "/" + mydate.get(Calendar.YEAR);
	}

	public static boolean appInstalledOrNot(Context c, String uri) {
		PackageManager pm = c.getPackageManager();
//		PackageInfo p;
		//String version_name = "";
		boolean app_installed = false;
		try {
//			p = 
			pm.getPackageInfo(uri, PackageManager.GET_ACTIVITIES);
			//version_name = p.versionName;

			app_installed = true;

		} catch (PackageManager.NameNotFoundException e) {
			app_installed = false;
		}
		return app_installed;
	}

	public static String zenMoney(String money) {
        if (!TextUtils.isEmpty(money)) {
            try {
                return zenMoney(Long.parseLong(money));
//                return String.format("%,d", Long.parseLong(money)).replace(".", ",");
            } catch (NumberFormatException e) {
                e.printStackTrace();
                return "";
            }
        }
        return "";
    }
    public static String zenMoney(long money) {
        return zenMoney(money, ",");
//        return String.format("%,d", money).replace(".", ",");
    }

    public static String zenMoney(long money, @NonNull String charSplit) {
        if (TextUtils.isEmpty(charSplit)) {
            charSplit = Constants.AMOUNT_SPLIT;
        }
        return String.format(Locale.ENGLISH, "%,d", money).replace(",", charSplit);
    }

    public static String zenUdid(){
		return UUID.randomUUID().toString();
	}
	
	
	public static String zenPhoneNumber(String phoneNumber){
		return zenPhoneNumber(phoneNumber, "-");
	}
	
	public static String zenPhoneNumber(String phoneNumber, String spaceCharacter){
		return phoneNumber.replaceAll("(\\d{3})(\\d{3})(\\d+)", "$1%s$2%s$3".replace("%s", spaceCharacter));
	}

	public static ProgressDialog showLoading(final Context c, final int flag) {
		ProgressDialog pdialog = new ProgressDialog(c);
		pdialog.setCancelable(false);
		pdialog.setMessage(c.getString(R.string.LOADING_VIEW_MSG_DEFAULT_LOADING));
//		pdialog.setButton(DialogInterface.BUTTON_NEGATIVE, c.getString(R.string.BTN_CANCEL), new DialogInterface.OnClickListener() {
//			@Override
//			public void onClick(DialogInterface dialog, int which) {
//				System.out.println("kkkk");
//				MposRestClient.cancelRQ();
//				dialog.dismiss();
//				switch (flag) {
//					case 1 :
//						break;
//					case 2:
//						((Activity) (c)).finish();
//						break;
//					case 3:
//						Intent intent = new Intent(c, LoginActivity.class);
//						intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
//						c.startActivity(intent);
//						break;
//					default :
//						break;
//				}
//			}
//		});
		return pdialog;
	}

	public static boolean findBinary(String binaryName) {
		boolean found = false;
//		if (!found) {
			String[] places = {"/sbin/", "/system/bin/", "/system/xbin/", "/data/local/xbin/", "/data/local/bin/", "/system/sd/xbin/", "/system/bin/failsafe/", "/data/local/"};
			for (String where : places) {
				if (new File(where + binaryName).exists()) {
					found = true;
					break;
				}
			}
//		}
		return found;
	}

	public static boolean isRooted() {
		return findBinary("su");
	}

	public static String md5(String string) {
		byte[] hash;

		try {
			hash = MessageDigest.getInstance("MD5").digest(string.getBytes("UTF-8"));
		} catch (NoSuchAlgorithmException e) {
			throw new RuntimeException("Huh, MD5 should be supported?", e);
		} catch (UnsupportedEncodingException e) {
			throw new RuntimeException("Huh, UTF-8 should be supported?", e);
		}

		StringBuilder hex = new StringBuilder(hash.length * 2);

		for (byte b : hash) {
			int i = (b & 0xFF);
			if (i < 0x10)
				hex.append('0');
			hex.append(Integer.toHexString(i));
		}

		return hex.toString();
	}

	public static String sha256(final String base) {
		try{
			final MessageDigest digest = MessageDigest.getInstance("SHA-256");
			final byte[] hash = digest.digest(base.getBytes("UTF-8"));
			final StringBuilder hexString = new StringBuilder();
			for (int i = 0; i < hash.length; i++) {
				final String hex = Integer.toHexString(0xff & hash[i]);
				if(hex.length() == 1)
					hexString.append('0');
				hexString.append(hex);
			}
			return hexString.toString();
		} catch(Exception ex){
			throw new RuntimeException(ex);
		}
	}

    public static int getTypeBySeriNumber(String sn) {
        if (sn.toUpperCase().startsWith("MPOS")) {
            return ConstantsPay.DEVICE_DSPREAD;
        }
        else if (sn.toUpperCase().startsWith("PP")) {
            return ConstantsPay.DEVICE_PINPAD;
        }
        else {
            return ConstantsPay.DEVICE_NONE;
        }
    }

    public static String getSerialNumber(String sn) {
        if (sn.toUpperCase().startsWith("PP") && sn.length()>8) {
            sn = sn.substring(sn.length() - 9);
        }
//        else if (sn.toUpperCase().startsWith("MPOS")) {
//            sn = sn.substring(4);
//        }
        return sn;
    }

    // check field receipt null Emart
	public static String buildDataReceipt(String data) {
		if (TextUtils.isEmpty(data) || data.equalsIgnoreCase("null")) {
			return "";
		}
		return data;
	}

    public static int getNextFibonacciByCurrent(int current) {
        int previous = 0;
        int next = 1;
        int sum;
        while (next <= current) {
            sum = previous + next;
            previous = next;
            next = sum;
        }
        return next;
    }
    public static int getNextFibonacciByPosition(int position) {
        if (position <= 1) {
            return position;
        }
        int previous = 0;
        int current = 1;
        for (int i = 2; i <= position; i++) {
            int next = previous + current;
            previous = current;
            current = next;
        }
        return current;
    }

    public static boolean checkTypeBuildIsCertify() {
        return checkTypeBuild("certify");
    }
    public static boolean checkBuildTypeIsStaging() {
        return BuildConfig.BUILD_TYPE.contains("staging");
    }
    public static boolean checkTypeBuildIsRelease() {
        return checkTypeBuild("release");
    }
    public static boolean checkTypeBuildIsDebug() {
        return checkTypeBuild("debug");
    }
    public static boolean checkTypeBuild(String typeBuild) {
        return BuildConfig.typeBuild.equals(typeBuild);
    }
}
