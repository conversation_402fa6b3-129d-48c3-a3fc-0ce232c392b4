package com.mpos.sdk.util;

import android.os.Build;
import android.text.TextUtils;

public class DevicesUtil {

    // SMP
    public static final String MODEL_NAME_P20L = "P20L";
    // mini SMP
    public static final String MODEL_NAME_L200  = "L200";   // prod
    public static final String MODEL_NAME_P5    = "P5";     // dev
    // pro SMP
    public static final String MODEL_NAME_SP02  = "SP02";     // prod
    public static final String MODEL_NAME_P8    = "Smart_Prime";     // dev
    public static final String MODEL_NAME_P8_PROD    = "P8";     // prod
    public static final String MODEL_NAME_P8_DEV = "P8 NEO";     // DEV


    // N31
    public static final String MODEL_NAME_P12_PROD    = "N31";     // prod
    public static final String MODEL_NAME_P12_DEV    = "P12";     // DEV

    // N4
    public static final String MODEL_NAME_N4_PROD    = "mPOS PRO";     // prod
    public static final String MODEL_NAME_N4_PROD_OLD    = "MPOS_PRO";     // prod
    public static final String MODEL_NAME_N4_DEV    = "N4";     // DEV

    // P10
    public static final String MODEL_NAME_P10    = "P10";     // DEV
    public static final String MODEL_NAME_P10_PRO    = "N33";     // prod
    public static final String MODEL_NAME_P10_KOZEN    = "SP06";     // prod

    //pax
    public static final String MODEL_NAME_PAXA920 = "A920Pro";
    public static final String MODEL_NAME_PAXA50 = "A50";
    public static final String MODEL_NAME_PAXAA910 = "A910";
    public static final String MODEL_NAME_PAXA77 = "A77";
    public static final String MODEL_NAME_PAXA35 = "A35";
    public static final String MODEL_NAME_PAXA8500 = "A8500";
    public static final String MODEL_NAME_PAXIM30 = "IM30";



    public static boolean isP20L() {
        return MODEL_NAME_P20L.equalsIgnoreCase(Build.MODEL) || Build.MODEL.toUpperCase().startsWith(MODEL_NAME_P20L);
    }

    public static boolean isSP02() {
        return isSP02P5() || isSP02P8() || isSP02P12() || isSP02P10() || isSP02N4();
    }

    public static boolean isSP02P5() {
        return MODEL_NAME_L200.equalsIgnoreCase(Build.MODEL) || MODEL_NAME_P5.equalsIgnoreCase(Build.MODEL)|| MODEL_NAME_SP02.equalsIgnoreCase(Build.MODEL);
    }

    public static boolean isSP02P8() {
        return MODEL_NAME_P8_PROD.equalsIgnoreCase(Build.MODEL) || MODEL_NAME_P8.equalsIgnoreCase(Build.MODEL) || MODEL_NAME_P8_DEV.equalsIgnoreCase(Build.MODEL);
    }

    public static boolean isSP02P12() {
        return MODEL_NAME_P12_PROD.equalsIgnoreCase(Build.MODEL)  || MODEL_NAME_P12_DEV.equalsIgnoreCase(Build.MODEL);
    }
    public static boolean isSP02N4() {
        return MODEL_NAME_N4_DEV.equalsIgnoreCase(Build.MODEL) || MODEL_NAME_N4_PROD.equalsIgnoreCase(Build.MODEL)
                || MODEL_NAME_N4_PROD_OLD.equalsIgnoreCase(Build.MODEL);
    }

    public static boolean isSP02P10() {
        return MODEL_NAME_P10.equalsIgnoreCase(Build.MODEL) || MODEL_NAME_P10_PRO.equalsIgnoreCase(Build.MODEL)
                || MODEL_NAME_P10_KOZEN.equalsIgnoreCase(Build.MODEL);
    }

//    // only print in SmartPrime
//    public static boolean isPrintSp02() {
//        return MODEL_NAME_P8.equalsIgnoreCase(Build.MODEL);
//    }

    public static boolean isPax() {
        return MODEL_NAME_PAXA920.equalsIgnoreCase(Build.MODEL)
                || MODEL_NAME_PAXA50.equalsIgnoreCase(Build.MODEL)
                || MODEL_NAME_PAXAA910.equalsIgnoreCase(Build.MODEL)
                || MODEL_NAME_PAXA77.equalsIgnoreCase(Build.MODEL)
                || MODEL_NAME_PAXA35.equalsIgnoreCase(Build.MODEL)
                || MODEL_NAME_PAXIM30.equalsIgnoreCase(Build.MODEL)
                || MODEL_NAME_PAXA8500.equalsIgnoreCase(Build.MODEL);
    }

    public static boolean isPaxSupportPrint() {
        return MODEL_NAME_PAXA920.equalsIgnoreCase(Build.MODEL)
                || MODEL_NAME_PAXA8500.equalsIgnoreCase(Build.MODEL);
    }

    public static boolean isPaxA920() {
        boolean result = false;
        if (MODEL_NAME_PAXA920.equalsIgnoreCase(Build.MODEL)) {
            return true;
        }

        return result;
    }

    public static boolean isPaxA35() {
        boolean result = false;
        if (MODEL_NAME_PAXA35.equalsIgnoreCase(Build.MODEL)) {
            return true;
        }

        return result;
    }


    public static boolean isDeviceSupportPrint() {
        return isP20L() || isSP02() || isPaxSupportPrint();
    }

    private static String capitalize(String str) {
        if (TextUtils.isEmpty(str)) {
            return str;
        }
        char[] arr = str.toCharArray();
        boolean capitalizeNext = true;
        StringBuilder phrase = new StringBuilder();
        for (char c : arr) {
            if (capitalizeNext && Character.isLetter(c)) {
                phrase.append(Character.toUpperCase(c));
                capitalizeNext = false;
                continue;
            } else if (Character.isWhitespace(c)) {
                capitalizeNext = true;
            }
            phrase.append(c);
        }
        return phrase.toString();
    }

    private DevicesUtil() {

    }

    /** Returns the consumer friendly device name */
    public static String getDeviceNameBest() {
        String manufacturer = Build.MANUFACTURER;
        String model = Build.MODEL;
        if (model.startsWith(manufacturer)) {
            return capitalize(model);
        }
        if (manufacturer.equalsIgnoreCase("HTC")) {
            // make sure "HTC" is fully capitalized.
            return "HTC " + model;
        }
        return capitalize(manufacturer) + " " + model;
    }
    
    
}
