package com.mpos.sdk.util;

import android.text.TextUtils;

import com.mpos.sdk.core.common.MyGson;
import com.mpos.sdk.core.control.CryptoInterface;
import com.mpos.sdk.core.modelma.DataBaseObj;

import cz.msebera.android.httpclient.Header;

/**
 * Create by an<PERSON><PERSON><PERSON>n on 10/11/21
 */
public class MacqUtil {

    private static MacqUtil macqUtil;

    public static synchronized MacqUtil getInstance() {
        if (macqUtil == null) {
            macqUtil = new MacqUtil();
        }
        return macqUtil;
    }

    public String parseAndDecryptData(String rawJson) {
        DataBaseObj data = MyGson.parseJson(rawJson, DataBaseObj.class);
        if (data == null || TextUtils.isEmpty(data.getData())) {
            return "";
        }
        else {
            return CryptoInterface.getInstance().decryptData(data.getData());
        }
    }

    public String parseErrorCodeMacqFromHeader(Header[] headers) {
        if (headers != null) {
            for (Header header : headers) {
                if (header.getName().equals("Forbidden-Code")) {
                    return header.getValue();
                }
            }
        }
        return "";
    }

}
