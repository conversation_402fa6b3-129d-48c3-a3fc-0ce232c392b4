package com.mpos.sdk.util;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.ObjectOutputStream;
import java.io.OutputStream;
import java.util.zip.GZIPOutputStream;

/**
 * Created by AnhNT on 6/17/16.
 */
public class IOUtils {



    public static void closeQuietly(InputStream input) {
        try {
            if (input != null) {
                input.close();
            }
        } catch (IOException ioe) {
            ioe.printStackTrace();
        }
    }
    public static void closeQuietly(ByteArrayOutputStream input) {
        try {
            if (input != null) {
                input.close();
            }
        } catch (IOException ioe) {
            ioe.printStackTrace();
        }
    }
    public static void closeQuietly(GZIPOutputStream input) {
        try {
            if (input != null) {
                input.close();
            }
        } catch (IOException ioe) {
            ioe.printStackTrace();
        }
    }
    public static void closeQuietly(OutputStream input) {
        try {
            if (input != null) {
                input.close();
            }
        } catch (IOException ioe) {
            ioe.printStackTrace();
        }
    }
    public static void closeQuietly(ObjectOutputStream input) {
        try {
            if (input != null) {
                input.close();
            }
        } catch (IOException ioe) {
            ioe.printStackTrace();
        }
    }

}
