package com.mpos.sdk.util;

/**
 * Created by Anh<PERSON> on 4/12/16.
 */
public class Intents {

    // ------- DATA OBJECT ---------
    public static final String EXTRA_DATA_CARD      = "mp.it.DATA_CARD";
    public static final String EXTRA_DATA_PARTNER   = "mp.it.DATA_PARTNER";
    public static final String EXTRA_DATA_PAY_MP    = "mp.it.DATA_PAY_MP";
    public static final String EXTRA_DATA_PRE_PAY   = "mp.it.DATA_PRE_PAY";
    public static final String EXTRA_DATA_SDK_CUSTOM= "mp.it.DATA_SDK_CUSTOM";
    public static final String EXTRA_DATA_CALLBACK  = "mp.it.DATA_MP_CALLBACK";
    public static final String EXTRA_DATA_VERSION_LITE  = "mp.it.DATA_VERSION_LITE";
    public static final String EXTRA_DATA_APP_TYPE  = "mp.it.DATA_APP_TYPE";
    public static final String EXTRA_DATA_CARD_TRADE_MODE  = "mp.it.DATA_CARD_TRADE_MODE";
    public static final String EXTRA_DATA_EXTRA_PARAMS  = "mp.it.EXTRA_PARAMS";
    public static final String EXTRA_DATA_SKIP_WAIT_SIGNATURE = "mp.it.SKIP_SIGNATURE";
    public static final String EXTRA_DATA_BNAME     = "mp.it.bname";

    // ------ DATA OBJECT: BROADCAST ---------
    public static final String EXTRA_DATA_BC_ACTION             = "mp.it.DATA_BC_ACTION";
    public static final String EXTRA_DATA_BC_CANCEL_ORDER       = "mp.it.DATA_BC_CANCEL_ORDER";
    public static final String EXTRA_DATA_BC_RESULT             = "mp.it.DATA_BC_RESULT";
    public static final String EXTRA_DATA_BC_HANDLE_ACTION      = "mp.it.DATA_BC_HANDLE_ACTION";
    public static final String EXTRA_DATA_TRACKING              = "mp.it.DATA_BC_TRACKING";
//    public static final String EXTRA_DATA_BC_BEFORE_SIGNATURE   = "mp.it.DATA_BC_BEFORE_SIGNATURE";

    public static final String EXTRA_DATA_INSTALLMENT  = "mp.it.INSTALLMENT";

    public static final String nameFilterActionCancelOrder  = "vn.mpos.tcp_action_cancel_order";
    public static final String nameFilterActionToPayment    = "vn.mpos.action_to_payment";

    // ------- Action --------
//    public static final String EXTRA_ACTION         = "mp.it.ACTION";


    // ------- VARIABLE ---------

    public static final String EXTRA_V_AMOUNT               = "mp.it.V_AMOUNT";
    public static final String EXTRA_V_AMOUNT_INTERNATIONAL = "mp.it.V_AMOUNT_INTERNATIONAL";
    public static final String EXTRA_V_AMOUNT_DOMESTIC      = "mp.it.V_AMOUNT_DOMESTIC";

    public static final String EXTRA_V_UDID         = "mp.it.V_UDID";
    public static final String EXTRA_V_TRX_TYPE     = "mp.it.V_TRX_TYPE";
    public static final String EXTRA_V_DESC         = "mp.it.V_DESCRIPTION";
    public static final String EXTRA_V_EMAIL        = "mp.it.V_EMAIL";
    public static final String EXTRA_V_TITLE        = "mp.it.V_TITLE";
    public static final String EXTRA_V_INFO         = "mp.it.V_INFO";
//    public static final String EXTRA_V_ORDERID      = "mp.it.V_ORDERID";
    public static final String EXTRA_V_TERMINAL_ID  = "mp.it.V_TERMINAL_ID";
    public static final String EXTRA_V_TX_ID        = "mp.it.V_TX_ID";

    public static final String EXTRA_V_NAME_BLUETOOTH   = "mp.it.v_name_bt";

    public static final String EXTRA_V_HANDLER_SIGN = "mp.it.HANDLER_SIGNATURE";

}
