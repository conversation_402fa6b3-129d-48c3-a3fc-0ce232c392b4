package com.mpos.sdk.screen;

import android.animation.Animator;
import android.animation.ObjectAnimator;
import android.app.Activity;
import android.os.AsyncTask;
import android.util.DisplayMetrics;
import android.util.TypedValue;
import android.view.View;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;

import com.mpos.sdk.R;
import com.mpos.sdk.databinding.VAnimArGuideBinding;
import com.mpos.sdk.databinding.VAnimDespreadGuideBinding;
import com.mpos.sdk.databinding.VAnimPrGuideBinding;
import com.mpos.sdk.databinding.VAnimSp01GuideBinding;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.DevicesUtil;

import java.lang.ref.WeakReference;

public class AnimationHelper {

    private static final String TAG = "AnimationHelper";

    private int widthScene;
    private int heightScene;
    private int distanceMove;
    private float distanceChipCard;
    private ObjectAnimator animXMoveMagCard;
    private ObjectAnimator animXMoveChipCard;
    private ObjectAnimator animYMoveChipCard;


    int timeAnimAction = 3000;

//    private Thread threadAnimReplay;
    private ReStartAnimation reStartAnimation;
    private Activity context;
    private View vAnimReader;
    private LinearLayout vAnimation;

    ConstantsPay.ReaderType readerType;

    VAnimArGuideBinding arBinding;
    VAnimPrGuideBinding prBinding;
    VAnimSp01GuideBinding sp01Binding;
    VAnimDespreadGuideBinding pr02Binding;

    boolean isVersionLite;

    public AnimationHelper(@NonNull Activity activity, ConstantsPay.ReaderType readerType, @NonNull LinearLayout vAnimation) {
        this(activity, readerType, vAnimation, false);
    }
    public AnimationHelper(@NonNull Activity activity, ConstantsPay.ReaderType readerType, @NonNull LinearLayout vAnimation, boolean isVersionLite) {
        this.readerType = readerType;
        this.isVersionLite = isVersionLite;
        this.vAnimation = vAnimation;
        //Utils.LOGD(TAG, "AnimationHelper: -->" + readerType);

        if (DevicesUtil.isSP02P8()) {
            vAnimReader = activity.getLayoutInflater().inflate(R.layout.v_anim_sp03_lite_guide, null, false);
        }
        else if (DevicesUtil.isSP02P12()) {
            vAnimReader = activity.getLayoutInflater().inflate(R.layout.v_anim_sp04_lite_guide, null, false);
        }
        else if (DevicesUtil.isSP02P10()) {
            vAnimReader = activity.getLayoutInflater().inflate(R.layout.v_anim_sp06_guide, null, false);
        }
        else if (DevicesUtil.isSP02N4()) {
            vAnimReader = activity.getLayoutInflater().inflate(R.layout.v_anim_sp07_guide, null, false);
        }
        else if (readerType == ConstantsPay.ReaderType.AUDIO) {
            vAnimReader = activity.getLayoutInflater().inflate(R.layout.v_anim_ar_guide, null, false);
            arBinding = VAnimArGuideBinding.bind(vAnimReader);
        }
        else if (readerType == ConstantsPay.ReaderType.PR01) {
            vAnimReader = activity.getLayoutInflater().inflate(R.layout.v_anim_pr_guide, null, false);
            prBinding = VAnimPrGuideBinding.bind(vAnimReader);
        }
        else if (readerType == ConstantsPay.ReaderType.SP01) {
            vAnimReader = activity.getLayoutInflater().inflate(
                    isVersionLite ? R.layout.v_anim_sp01_lite_guide : R.layout.v_anim_sp01_guide, null, false);
            if (!isVersionLite) {
                sp01Binding = VAnimSp01GuideBinding.bind(vAnimReader);
            }
        }
        else if (readerType == ConstantsPay.ReaderType.SP02) {
            vAnimReader = activity.getLayoutInflater().inflate(R.layout.v_anim_sp02_lite_guide, null, false);
//            sp02Binding = VAnimSp02LiteGuideBinding.bind(vAnimReader);
        }
        else if (readerType == ConstantsPay.ReaderType.PAX) {
            vAnimReader = activity.getLayoutInflater().inflate(R.layout.v_anim_sp04_guide, null, false);
        }
        else {
            vAnimReader = activity.getLayoutInflater().inflate(R.layout.v_anim_despread_guide, null , true);
            pr02Binding = VAnimDespreadGuideBinding.bind(vAnimReader);
        }


        vAnimation.addView(vAnimReader, new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT));

        this.context = activity;
    }

    public View getViewById(int id) {
        View view = null;
        try {
            view = vAnimReader.findViewById(id);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return view;
    }


    public void runAnimGuideMpos() {
        if (isVersionLite || readerType == ConstantsPay.ReaderType.SP02) {
            return;
        }
        initVariableAnimation();
        if (readerType == ConstantsPay.ReaderType.AUDIO) {
            initObjAnimAR();
            startAnimationAr();
        }
        else if (readerType == ConstantsPay.ReaderType.PR01) {
            initObjAnimPr01();
            startAnimationPr();
        }
        else if (readerType == ConstantsPay.ReaderType.SP01) {
            if (!isVersionLite) {
                initObjAnimSp01();
                startAnimationSp01();
            }
        }
        else {
            initObjAnimDspread();
            startAnimationDespread();
        }
    }

    public void scaleImage(float scale){
        if (readerType == ConstantsPay.ReaderType.SP01 && !isVersionLite) {
            sp01Binding.imgCardMagSp01.setScaleX(scale);
            sp01Binding.imgCardMagSp01.setScaleY(scale);
            sp01Binding.imgCardChipSp01.setScaleX(scale);
            sp01Binding.imgCardChipSp01.setScaleY(scale);
            sp01Binding.imgHandPosFloor1Pr.setScaleX(scale);
            sp01Binding.imgHandPosFloor1Pr.setScaleY(scale);
            sp01Binding.imvLogoCustomSmartpos.setScaleX(scale);
            sp01Binding.imvLogoCustomSmartpos.setScaleY(scale);
        }
    }

    public void setLogoCustom(int id){
        try {
            if (sp01Binding!=null && sp01Binding.imvLogoCustomSmartpos != null) {
                sp01Binding.imvLogoCustomSmartpos.setImageResource(id);
//                imgSpLogoCustom.setBackgroundResource(id);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void showImageGuideMpos() {
        initVariableAnimation();
        if (readerType == ConstantsPay.ReaderType.AUDIO) {
            initObjAnimAR();
            startAnimationAr();
        }
        else if (readerType == ConstantsPay.ReaderType.PR01) {
            initObjAnimPr01();
            startAnimationPr();
        }
        else if (readerType == ConstantsPay.ReaderType.SP01) {
            if (sp01Binding != null) {
                sp01Binding.imgCardChipSp01.setVisibility(View.VISIBLE);
                sp01Binding.imgCardChipSp01.setVisibility(View.VISIBLE);
            }
        }
        else {
            pr02Binding.imgCardMagPrDspread.setVisibility(View.VISIBLE);
            pr02Binding.imgCardChipPrDspread.setVisibility(View.VISIBLE);
        }
    }

    private void initVariableAnimation() {

        DisplayMetrics displayMetrics = new DisplayMetrics();
        context.getWindowManager().getDefaultDisplay().getMetrics(displayMetrics);

        widthScene = displayMetrics.widthPixels;
        heightScene = displayMetrics.heightPixels;
    }

    private void initObjAnimAR() {
        animXMoveMagCard = ObjectAnimator.ofFloat(arBinding.imgCardMagenticAr, View.TRANSLATION_X, -widthScene, widthScene);
        animXMoveMagCard.setDuration(timeAnimAction);

        float distanceChipCard = TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 80, context.getResources().getDisplayMetrics());
        animYMoveChipCard = ObjectAnimator.ofFloat(arBinding.imgCardChipAr, View.TRANSLATION_Y, distanceChipCard);
        animYMoveChipCard.setDuration(1200);

    }

    private void initObjAnimPr01() {
        distanceChipCard = TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, -80, context.getResources().getDisplayMetrics());
        //Utils.LOGD(TAG, "initObjAnimPr01: -->>" + context.getResources().getDisplayMetrics()
//                + " --distanceChipCard=" + distanceChipCard + " imgPrCardChip.getHeight()=" + prBinding.imgCardChipPr.getHeight());

        animXMoveMagCard = ObjectAnimator.ofFloat(prBinding.imgCardMagPr, View.TRANSLATION_X, -widthScene, widthScene);
        animXMoveMagCard.setDuration(timeAnimAction);

        animYMoveChipCard = ObjectAnimator.ofFloat(prBinding.imgCardChipPr, View.TRANSLATION_Y, distanceChipCard);
        animYMoveChipCard.setDuration(800);
    }

    private void initObjAnimDspread() {
        distanceMove = (int) (pr02Binding.imgCardChipPrDspread.getX() - (pr02Binding.despreadImgDevice.getX() + pr02Binding.despreadImgDevice.getWidth()/2));
        distanceChipCard = -distanceMove;
        //Utils.LOGD(TAG, "initVariableAnimation: "+ pr02Binding.imgCardChipPrDspread.getX() +"-->"+ pr02Binding.despreadImgDevice.getX() + "-->"+pr02Binding.despreadImgDevice.getWidth());
        //Utils.LOGD(TAG, "initVariableAnimation: distanceChipCard="+distanceChipCard+" distanceMove="+distanceMove+" imgPrCardChipDspread.getWidth()="+pr02Binding.imgCardChipPrDspread.getWidth());

        animXMoveMagCard = ObjectAnimator.ofFloat(pr02Binding.imgCardMagPrDspread, View.TRANSLATION_X, -widthScene, widthScene);
        animXMoveMagCard.setDuration(3000);

//        //Utils.LOGD(TAG, "initObjAnimDspread: start=" + startChipY + " end=" + endChipY);
        animXMoveChipCard = ObjectAnimator.ofFloat(pr02Binding.imgCardChipPrDspread, View.TRANSLATION_X, distanceChipCard);

        animXMoveChipCard.setDuration(2000);
    }


    private void initObjAnimSp01() {
        if (isVersionLite) {
            return;
        }
        distanceChipCard = TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, -80, context.getResources().getDisplayMetrics());
        //Utils.LOGD(TAG, "initObjAnimPr01: -->>" + context.getResources().getDisplayMetrics()
//                + " --distanceChipCard=" + distanceChipCard + " imgPrCardChip.getHeight()=" + sp01Binding.imgCardChipSp01.getHeight());

        animXMoveMagCard = ObjectAnimator.ofFloat(sp01Binding.imgCardMagSp01, View.TRANSLATION_Y, -heightScene, heightScene);
        animXMoveMagCard.setDuration(4000);

        animYMoveChipCard = ObjectAnimator.ofFloat(sp01Binding.imgCardChipSp01, View.TRANSLATION_Y, distanceChipCard);
        animYMoveChipCard.setDuration(1000);
    }
    /**
     * guide animation for AR
     */
    private void startAnimationAr() {

        arBinding.imgCardMagenticAr.setVisibility(View.VISIBLE);

        animXMoveMagCard.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {}

            @Override
            public void onAnimationEnd(Animator animation) {
                arBinding.imgCardChipAr.setVisibility(View.VISIBLE);
                animYMoveChipCard.addListener(new Animator.AnimatorListener() {
                    @Override
                    public void onAnimationStart(Animator animation) {}

                    @Override
                    public void onAnimationEnd(final Animator animation) {
                        arBinding.imgCardChipAr.setVisibility(View.INVISIBLE);
                        autoReplayAnim();
                    }

                    @Override
                    public void onAnimationCancel(Animator animation) {}

                    @Override
                    public void onAnimationRepeat(Animator animation) {}
                });
                animYMoveChipCard.start();
            }

            @Override
            public void onAnimationCancel(Animator animation) {}

            @Override
            public void onAnimationRepeat(Animator animation) {}
        });
        animXMoveMagCard.start();
    }


    /**
     * guide animation for PR
     */
    private void startAnimationPr() {
        // NOTE: -------- animation guide for PR ---------
        prBinding.imgCardMagPr.setVisibility(View.VISIBLE);

        animXMoveMagCard.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
            }

            @Override
            public void onAnimationEnd(Animator animation) {
//                //Utils.LOGD(tag, "onAnimationEnd: ---------");

                prBinding.imgCardChipPr.setVisibility(View.VISIBLE);
                animYMoveChipCard.addListener(new Animator.AnimatorListener() {
                    @Override
                    public void onAnimationStart(Animator animation) {
                    }

                    @Override
                    public void onAnimationEnd(final Animator animation) {
                        prBinding.imgCardChipPr.setVisibility(View.GONE);
                        autoReplayAnim();
                    }

                    @Override
                    public void onAnimationCancel(Animator animation) {
                    }

                    @Override
                    public void onAnimationRepeat(Animator animation) {
                    }
                });
                animYMoveChipCard.start();

            }

            @Override
            public void onAnimationCancel(Animator animation) {
            }

            @Override
            public void onAnimationRepeat(Animator animation) {
            }
        });

        animXMoveMagCard.start();
    }

    private void startAnimationDespread() {
        // NOTE: -------- animation guide for Despread ---------
        pr02Binding.imgCardMagPrDspread.setVisibility(View.VISIBLE);

        animXMoveMagCard.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {}

            @Override
            public void onAnimationEnd(Animator animation) {

                pr02Binding.tvDesChipCardPrDspread.setVisibility(View.VISIBLE);

                pr02Binding.imgCardChipPrDspread.setVisibility(View.VISIBLE);

                animXMoveChipCard.addListener(new Animator.AnimatorListener() {
                    @Override
                    public void onAnimationStart(Animator animation) {}

                    @Override
                    public void onAnimationEnd(final Animator animation) {
                        pr02Binding.imgCardChipPrDspread.setVisibility(View.INVISIBLE);

                        autoReplayAnim();
                    }

                    @Override
                    public void onAnimationCancel(Animator animation) {}

                    @Override
                    public void onAnimationRepeat(Animator animation) {}
                });

                animXMoveChipCard.start();
            }

            @Override
            public void onAnimationCancel(Animator animation) {}

            @Override
            public void onAnimationRepeat(Animator animation) {}
        });

        animXMoveMagCard.start();
    }


    /**
     * guide animation for smart-pos
     */
    private void startAnimationSp01() {
        // NOTE: -------- animation guide for PR ---------
        sp01Binding.imgCardMagSp01.setVisibility(View.VISIBLE);

        animXMoveMagCard.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
            }

            @Override
            public void onAnimationEnd(Animator animation) {
//                //Utils.LOGD(tag, "onAnimationEnd: ---------");

                sp01Binding.imgCardChipSp01.setVisibility(View.VISIBLE);
                animYMoveChipCard.addListener(new Animator.AnimatorListener() {
                    @Override
                    public void onAnimationStart(Animator animation) {
                    }

                    @Override
                    public void onAnimationEnd(final Animator animation) {
                        sp01Binding.imgCardChipSp01.setVisibility(View.GONE);
                        autoReplayAnim();
                    }

                    @Override
                    public void onAnimationCancel(Animator animation) {
                    }

                    @Override
                    public void onAnimationRepeat(Animator animation) {
                    }
                });
                animYMoveChipCard.start();

            }

            @Override
            public void onAnimationCancel(Animator animation) {
            }

            @Override
            public void onAnimationRepeat(Animator animation) {
            }
        });

        animXMoveMagCard.start();
    }

    /*
     * replay animation when finish after 1 second
     */
    private void autoReplayAnim() {
//        //Utils.LOGD(TAG, "autoReplayAnim: ");
        if (reStartAnimation != null) {
            reStartAnimation.cancel(true);
            reStartAnimation = null;
        }
        reStartAnimation = new ReStartAnimation(this);
        reStartAnimation.doInBackground();
    }


    private boolean isFinishing() {
        return context == null || context.isFinishing();
    }

    /*
     * reset animation
     */
    public void clearAnimation() {
        //Utils.LOGD(TAG, "clearAnimation: =====>>");
        removeAllListenerAnimation();
        removeViewAnimation();
    }

    private void removeAllListenerAnimation() {
        if (animYMoveChipCard != null) animYMoveChipCard.removeAllListeners();
        if (animXMoveChipCard != null) animXMoveChipCard.removeAllListeners();
        if (animXMoveMagCard != null) animXMoveMagCard.removeAllListeners();
    }

    public void removeViewAnimation() {
        if (vAnimation != null && vAnimReader!=null) {
            //Utils.LOGD(TAG, "clearAnimation: ==> clear AnimReader");
//            vAnimReader.clearAnimation();
            vAnimation.removeView(vAnimReader);
        }
    }

    private static class ReStartAnimation extends AsyncTask<String, String, String> {

        WeakReference<AnimationHelper> weakReference;

        ReStartAnimation(AnimationHelper helper) {
            this.weakReference = new WeakReference<>(helper);
        }

        @Override
        protected String doInBackground(String... strings) {
            if (weakReference != null && !weakReference.get().isFinishing()) {
                weakReference.get().removeAllListenerAnimation();
                if (weakReference.get().readerType == ConstantsPay.ReaderType.AUDIO) {
                    weakReference.get().startAnimationAr();
                }
                else if (weakReference.get().readerType == ConstantsPay.ReaderType.PR01) {
                    weakReference.get().startAnimationPr();
                }
                else if (weakReference.get().readerType == ConstantsPay.ReaderType.SP01) {
                    weakReference.get().startAnimationSp01();
                }
                else {
                    weakReference.get().startAnimationDespread();
                }
            }
            return null;
        }
    }



}
