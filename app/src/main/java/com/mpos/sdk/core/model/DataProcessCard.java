package com.mpos.sdk.core.model;

public class DataProcessCard {

    private int type;
    private String mCard;
    private String track1;
    private String track2;
    private String ksn;
    private String mPin;
    private String mPin1;
    private String mPin2;

    private String nameTypeCard;
    private String mDataEmv;
    private String readerType;

    public DataProcessCard() {
    }

    public DataProcessCard(int type, String mCard, String track1, String track2, String ksn, String mPin) {
        this.type = type;
        this.mCard = mCard;
        this.track1 = track1;
        this.track2 = track2;
        this.ksn = ksn;
        this.mPin = mPin;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getmCard() {
        return mCard;
    }

    public void setmCard(String mCard) {
        this.mCard = mCard;
    }

    public String getTrack1() {
        return track1;
    }

    public void setTrack1(String track1) {
        this.track1 = track1;
    }

    public String getTrack2() {
        return track2;
    }

    public void setTrack2(String track2) {
        this.track2 = track2;
    }

    public String getKsn() {
        return ksn;
    }

    public void setKsn(String ksn) {
        this.ksn = ksn;
    }

    public String getmPin() {
        return mPin;
    }

    public void setmPin(String mPin) {
        this.mPin = mPin;
    }

    public String getmPin1() {
        return mPin1;
    }

    public void setmPin1(String mPin1) {
        this.mPin1 = mPin1;
    }

    public String getmPin2() {
        return mPin2;
    }

    public void setmPin2(String mPin2) {
        this.mPin2 = mPin2;
    }

    public String getNameTypeCard() {
        return nameTypeCard;
    }

    public void setNameTypeCard(String nameTypeCard) {
        this.nameTypeCard = nameTypeCard;
    }

    public String getmDataEmv() {
        return mDataEmv;
    }

    public void setmDataEmv(String mDataEmv) {
        this.mDataEmv = mDataEmv;
    }

    public String getReaderType() {
        return readerType;
    }

    public void setReaderType(String readerType) {
        this.readerType = readerType;
    }

    public void setPinByCounter(int counter, String mPin) {
        switch (counter) {
            case 2:
                this.mPin1 = mPin;
                break;
            case 3:
                this.mPin2 = mPin;
                break;
            default:
                this.mPin = mPin;
                break;
        }
    }

    @Override
    public String toString() {
        return "DataProcessCard{" +
                "type=" + type +
                ", mCard='" + mCard + '\'' +
                ", track1='" + track1 + '\'' +
                ", track2='" + track2 + '\'' +
                ", ksn='" + ksn + '\'' +
                ", mPin='" + mPin + '\'' +
                ", mPin1='" + mPin1 + '\'' +
                ", mPin2='" + mPin2 + '\'' +
                ", nameTypeCard='" + nameTypeCard + '\'' +
                ", mDataEmv='" + mDataEmv + '\'' +
                '}';
    }
}
