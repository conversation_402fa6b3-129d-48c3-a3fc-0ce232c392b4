package com.mpos.sdk.core.network;

import android.content.Context;
import android.os.Build;
import android.text.TextUtils;

import com.loopj.android.http.AsyncHttpClient;
import com.loopj.android.http.AsyncHttpResponseHandler;
import com.loopj.android.http.FileAsyncHttpResponseHandler;
import com.loopj.android.http.PersistentCookieStore;
import com.loopj.android.http.RequestParams;
import com.mpos.sdk.BuildConfig;
import com.mpos.sdk.R;
import com.mpos.sdk.core.control.CryptoInterface;
import com.mpos.sdk.core.control.GetData;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.Utils;

import java.util.Arrays;
import java.util.Locale;

import cz.msebera.android.httpclient.Header;
import cz.msebera.android.httpclient.entity.StringEntity;

public class MposRestClient {
    static String TAG = "MposRestClient";
    private static MposRestClient mposRestClient;
	private static AsyncHttpClient httpClient;
//	private static AsyncHttpClient client = new AsyncHttpClient(true, 80, 443);
	private static PersistentCookieStore cookie;
	private static final int MAX_RETRY_REQUEST = 0;
	private static final int TIME_OUT = 40000;
	private static final int TIME_OUT_PAYMENT = 60000;
	private static final int TIME_OUT_PAYMENT_MA = 70000;
//	private static final int TIME_OUT_PAYMENT = 120000;
//    private static final ExecutorService executorService = Executors.newFixedThreadPool(4);
	private static String userAgent = "";

    private static boolean isNewHttpClient = false;

	public static synchronized MposRestClient getInstance(Context context) {
//        Utils.LOGD(TAG, "getInstance: have context");
        initMyClient();

        boolean setCookie = false;
        if (cookie == null) {
            cookie = new PersistentCookieStore(context);
            Utils.LOGD(TAG, "null cookie-->create new cookie");
            setCookie = true;
        }
        if (httpClient != null && setCookie) {
            setCookieStore(cookie);
        }
        Utils.LOGD(TAG, "getInstance: have context, isNewHttpClient=" + isNewHttpClient);
        if (isNewHttpClient) {
            String sessionId = PrefLibTV.getInstance(context).get(PrefLibTV.HEADER_SESSION_ID, String.class, "");
            Utils.LOGD(TAG, "getInstance: sessionId=" + sessionId);
            if (!TextUtils.isEmpty(sessionId)) {
//                mposRestClient.addHeaderSessionId(sessionId);
                Utils.LOGD(TAG, "has cache sessionId");
            }

            String authBearer = PrefLibTV.getInstance(context).get(PrefLibTV.HEADER_AUTHORIZATION_BEARER, String.class, "");
            Utils.LOGD(TAG, "getInstance: authenToken=" + authBearer);
            if (!TextUtils.isEmpty(authBearer)) {
//                mposRestClient.addHeaderAuthorization(authBearer);
                Utils.LOGD(TAG, "has cache authBearer");
            }

            boolean useCacheAuthenMacq = false;
            if (!TextUtils.isEmpty(sessionId) && !TextUtils.isEmpty(authBearer)) {
                boolean resultLoadTink = CryptoInterface.getInstance().loadKeySavedToKeySet(context, PrefLibTV.getInstance(context).getSharedPreferences());
                if (resultLoadTink) {
                    mposRestClient.addHeaderSessionId(sessionId);
                    mposRestClient.addHeaderAuthorization(authBearer);
                    useCacheAuthenMacq = true;
                }
            }
            Utils.LOGD(TAG, "--> use cache key for MACQ: " + useCacheAuthenMacq);

            if (!useCacheAuthenMacq) {
                PrefLibTV.getInstance(context).put(PrefLibTV.HEADER_SESSION_ID, "");
                PrefLibTV.getInstance(context).put(PrefLibTV.HEADER_AUTHORIZATION_BEARER, "");
                CryptoInterface.getInstance().clearCacheTinkKey(context, PrefLibTV.getInstance(context).getSharedPreferences());
            }
        }

        return mposRestClient;
	}

    private static synchronized void initMyClient() {
//        Utils.LOGD(TAG, "initMyClient: ");
        if (mposRestClient == null) {
            mposRestClient = new MposRestClient();
        }
        if (httpClient == null) {
            httpClient = new AsyncHttpClient();
//            client = new AsyncHttpClient(true, 80, 443);
            Utils.LOGD(TAG, "null client-->create new client");

            httpClient.setLoggingEnabled(Utils.mDEBUG);
//        System.out.println("log enabled:"+client.isLoggingEnabled()+" level:"+client.getLoggingLevel());
            httpClient.setMaxRetriesAndTimeout(MAX_RETRY_REQUEST, TIME_OUT);
            httpClient.setTimeout(TIME_OUT);
//        client.addHeader("X-MACQ-UID", "");
//        client.setThreadPool(executorService);
//        client.setEnableRedirects(true);

            if (TextUtils.isEmpty(userAgent)) {
                userAgent = buildUserAgent();
            }
            httpClient.setUserAgent(userAgent);
            isNewHttpClient = true;
        }
        else {
            isNewHttpClient = false;
        }
//        try {
//            customSslSocket();
//        } catch (KeyStoreException e) {
//            e.printStackTrace();
//        } catch (CertificateException e) {
//            e.printStackTrace();
//        } catch (NoSuchAlgorithmException e) {
//            e.printStackTrace();
//        } catch (IOException e) {
//            e.printStackTrace();
//        } catch (UnrecoverableKeyException e) {
//            e.printStackTrace();
//        } catch (KeyManagementException e) {
//            e.printStackTrace();
//        }
    }

    private static String buildUserAgent() {
        String deviceLevelUserAgent = null;
        try {
            deviceLevelUserAgent = System.getProperty( "http.agent" );
        } catch (Exception e) {
            e.printStackTrace();
        }
//        Utils.LOGD(TAG, "buildUserAgent: 1--->" + deviceLevelUserAgent);
        if (TextUtils.isEmpty(deviceLevelUserAgent)) {
//            String webUserAgent = WebSettings.getDefaultUserAgent(context);
//            Utils.LOGD(TAG, "buildUserAgent: 2--->" + webUserAgent);
            return "Default Android " + BuildConfig.VERSION_NAME + "/Android " + Build.VERSION.RELEASE + "/" + Build.MODEL;
        }
        return deviceLevelUserAgent;
    }


//    private static void customSslSocket() throws KeyStoreException, CertificateException, NoSuchAlgorithmException, IOException, UnrecoverableKeyException, KeyManagementException {
//        /// We initialize a default Keystore
//        KeyStore trustStore = KeyStore.getInstance(KeyStore.getDefaultType());
//// We load the KeyStore
//        trustStore.load(null, null);
//// We initialize a new SSLSocketFacrory
//        TLSSocketFactory socketFactory = new TLSSocketFactory(trustStore);
//// We set that all host names are allowed in the socket factory
//        socketFactory.setHostnameVerifier(MySSLSocketFactory.ALLOW_ALL_HOSTNAME_VERIFIER);
//// We initialize the Async Client
////        AsyncHttpClient client = new AsyncHttpClient();
////// We set the timeout to 30 seconds
////        client.setTimeout(30*1000);
//// We set the SSL Factory
//        client.setSSLSocketFactory(socketFactory);
//// We initialize a GET http request
//
//    }
	
	public static AsyncHttpClient getHttpClient() {
		return httpClient;
	}

    public MposRestClient setPaymentTimeout(){
        httpClient.setTimeout(TIME_OUT_PAYMENT);
        return mposRestClient;
    }
    public MposRestClient setPaymentMATimeout(){
        httpClient.setTimeout(TIME_OUT_PAYMENT_MA);
        return mposRestClient;
    }

    public MposRestClient addHeader(String header, String value) {
        Utils.LOGD(TAG, "addHeader: " + header + " value=" + value);
        httpClient.addHeader(header, value);
        return mposRestClient;
    }

    public MposRestClient addHeaderAuthorization(String value) {
        Utils.LOGD(TAG, "addHeader-Authorization: "+value);
        if (httpClient != null) {
            httpClient.addHeader("Authorization", "Bearer " + value);
        }
        return mposRestClient;
    }
    public MposRestClient addHeaderSessionId(String value) {
        Utils.LOGD(TAG, "addHeader-SessionId: "+value);
        if (httpClient != null) {
            httpClient.addHeader("X-Secret-Session-Id", value);
        }
        return mposRestClient;
    }

    public MposRestClient setCustomTimeout(int milisecon){
        if(milisecon<=20000){
            System.out.println("*** timeout need more than 20s ***");
        }
        else{
            httpClient.setTimeout(milisecon);
        }
        return mposRestClient;
    }

	public static void get(String url, RequestParams params, AsyncHttpResponseHandler responseHandler) {
		httpClient.setTimeout(TIME_OUT);
		httpClient.get(url, params, responseHandler);
	}
	public void get(String url, AsyncHttpResponseHandler responseHandler) {
		httpClient.setTimeout(TIME_OUT);
		httpClient.get(url, responseHandler);
	}

	public void get(String url, FileAsyncHttpResponseHandler responseHandler) {
		httpClient.setTimeout(TIME_OUT);
		httpClient.get(url, responseHandler);
	}

	public void post(Context c, String url, RequestParams params, AsyncHttpResponseHandler responseHandler) {
        if (GetData.CheckInternet(c,c.getString(R.string.check_internet))) {
		    httpClient.post(url, params, responseHandler);
        }
	}

    public void post(String url, RequestParams params, AsyncHttpResponseHandler responseHandler) {
		httpClient.setTimeout(TIME_OUT);
		httpClient.post(url, params, responseHandler);
	}
    public void post(String url, StringEntity entity, String cType, AsyncHttpResponseHandler responseHandler) {
		httpClient.setTimeout(TIME_OUT);
		httpClient.post(null, url, entity, cType, responseHandler);
	}

	public static void post(String url, AsyncHttpResponseHandler responseHandler) {
		httpClient.setTimeout(TIME_OUT);
		httpClient.post(url, responseHandler);
	}

    public <T> void postMacq(Context c, String url, T object, AsyncHttpResponseHandler responseHandler) {
        StringEntity entity = ApiMultiAcquirerInterface.getInstance().buildStringEntity(object);
		post(c, url, entity, ConstantsPay.CONTENT_TYPE, responseHandler, false);
    }
    public void postMacq(Context c, String url, String data, AsyncHttpResponseHandler responseHandler) {
        StringEntity entity = ApiMultiAcquirerInterface.getInstance().buildStringEntity(data);
		post(c, url, entity, ConstantsPay.CONTENT_TYPE, responseHandler, false);
    }
    public void postMacq(Context c, String url, StringEntity entity, AsyncHttpResponseHandler responseHandler) {
		post(c, url, entity, ConstantsPay.CONTENT_TYPE, responseHandler, false);
    }
    public void post(Context c, String url, StringEntity entity, AsyncHttpResponseHandler responseHandler) {
		post(c, url, entity, ConstantsPay.CONTENT_TYPE, responseHandler, false);
    }
	public void post(Context c, String url, StringEntity entity, String cType, AsyncHttpResponseHandler responseHandler) {
		post(c, url, entity, cType, responseHandler, false);
	}
	public void post(Context c, String url, StringEntity entity, String cType, AsyncHttpResponseHandler responseHandler, boolean checkInternet) {
    	if (checkInternet && !GetData.CheckInternet(c,c.getString(R.string.check_internet))) {
			return;
    	}
		httpClient.post(c, url, entity, cType, responseHandler);
    }

    public void put(Context c, String url, StringEntity entity, String cType, AsyncHttpResponseHandler responseHandler) {
        httpClient.put(c, url, entity, cType, responseHandler);
    }
    public <T> void putMacq(Context c, String url, T object, String cType, AsyncHttpResponseHandler responseHandler) {
        StringEntity entity = ApiMultiAcquirerInterface.getInstance().buildStringEntity(object);
        httpClient.put(c, url, entity, cType, responseHandler);
    }

	public static void setCookieStore(PersistentCookieStore cookieStore) {
		httpClient.setCookieStore(cookieStore);
	}

	public static void cancelAllRQ() {
		httpClient.cancelAllRequests(true);
	}

    public static String buildMsgErrorOnFail(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
        return String.format(Locale.getDefault(),
                " httpCode=%d -> %s -msg=%s -header=%s", arg0, arg2 != null ? new String(arg2) : "", arg3.getMessage(), arg1 == null ? "" : Arrays.toString(arg1));
    }
}
