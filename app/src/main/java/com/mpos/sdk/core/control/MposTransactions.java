package com.mpos.sdk.core.control;

import android.content.Context;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.loopj.android.http.AsyncHttpResponseHandler;
import com.mpos.sdk.R;
import com.mpos.sdk.core.common.MyGson;
import com.mpos.sdk.core.model.DataError;
import com.mpos.sdk.core.model.LibError;
import com.mpos.sdk.core.model.PaymentItem;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.core.model.TransItem;
import com.mpos.sdk.core.network.MposRestClient;
import com.mpos.sdk.util.Constants;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.Utils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;

import cz.msebera.android.httpclient.Header;
import cz.msebera.android.httpclient.entity.StringEntity;

import static com.mpos.sdk.util.Constants.STR_SERVICE_NAME;
import static com.mpos.sdk.util.ConstantsPay.ERROR_CODE_DEFAULT;
import static com.mpos.sdk.util.ConstantsPay.ERROR_CODE_ON_FAIL_REQUEST;
import static com.mpos.sdk.util.ConstantsPay.ERROR_CODE_TIMEOUT_REQ;
import static com.mpos.sdk.util.ConstantsPay.SEND_RECEIPT;

/**
 * Create by anhnguyen on 3/18/20
 */
public class MposTransactions extends MposTransactionsMacq{

    private static final String TAG = "MposTransactions";

    public MposTransactions(@NonNull Context context) {
        super(context);
//        if (Constants.isTypeIntegrationMpos()) {
//            this.context = context;
//            sv = SaveLogController.getInstance(context);
//
//            checkCustomLanguage();
//        }
    }

    public void getListTransactionComplete(@NonNull ItfHandlerActionTrans<ArrayList<PaymentItem>> callback) {
        if (runMacq) {
            getListTransactionCompleteMacq(callback);
        }
        else {
            getListTransactionCompleteBank(callback);
        }
    }

    public void getListTransactionCompleteBank(@NonNull ItfHandlerActionTrans<ArrayList<PaymentItem>> callback) {
        StringEntity entity = null;
        String ssk = PrefLibTV.getInstance(context).getSessionKey();
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, ConstantsPay.GET_HISTORY);
            jo.put("readerSerialNo", getCurrSerialNumber());
            jo.put("udid", "0");
            jo.put("versionNo", android.os.Build.VERSION.RELEASE);
            jo.put("platform", ConstantsPay.PLATFORM);
            jo.put("sessionKey", ssk);
            jo.put("pagingNo", 1);
            jo.put("itemsPerPage", 500);
            jo.put("tokenL2", PrefLibTV.getInstance(context).getTKL2());
//            jo.put("tokenL2", getIntent().getStringExtra("tokenL2"));
            jo.put("userID", getCurrUserId());
            Utils.LOGD(TAG, jo.toString());
            entity = new StringEntity(EncodeDecode.doAESEncrypt(jo.toString(), ssk));
        } catch (Exception e1) {
            Utils.LOGE(TAG, "getListTran: " + e1.getMessage());
            saveLRq("error build GET_HISTORY: "+e1.getMessage());
        }
        MposRestClient.getInstance(context).post(context, ConstantsPay.getUrlServer(context),
                entity, ConstantsPay.CONTENT_TYPE, new AsyncHttpResponseHandler() {

                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                        DataError dataError = null;
                        try {
                            JSONObject response = new JSONObject(EncodeDecode.doAESDecrypt(new String(arg2), ssk));
                            Utils.LOGD(TAG, "getSalesHistoryBank: " + response);
                            PrefLibTV.getInstance(context).setSessionKey(response.getString("sessionKey"));
                            Utils.LOGD("Sales history: ", response.toString());
                            if (response.has("error")) {
                                dataError = buildDataError(response);
                                saveLRs(ConstantsPay.GET_HISTORY + " error server:"+dataError.getErrorCode()+" " + dataError.getMsg());
                            }
                            else {
                                handlerDataHistory(response);
                            }
                        } catch (Exception e) {
                            saveLRs(ConstantsPay.GET_HISTORY + " error(session timeout)");
                            Utils.LOGE(TAG, "Exception", e);
                            dataError =  new DataError(ConstantsPay.ERROR_CODE_TIMEOUT_REQ, getString(R.string.SERVICE_ERROR_SESSION_TIMEOUT_L2));
                        }
                        if (dataError != null) {
                            processCallbackFail(callback, dataError, ActionTrans.LIST_TRANS);
                        }
                    }

                    private void handlerDataHistory(JSONObject response) {
                        try {
                            String tokenL2 = response.getString("tokenL2");
                            PrefLibTV.getInstance(context).setTKL2(tokenL2);
                            // use config from mpos - don't use from bank (because mpos need synchronized to bank)
//                            PrefLibTV.getInstance(context).setPermitSettlement(response.getBoolean("canSettle"));
//                            PrefLibTV.getInstance(context).setPermitVoid(response.getBoolean("canVoid"));

                            JSONArray jsonArray = response.getJSONArray("transactionList");
                            JSONObject jItem;
                            ArrayList<PaymentItem> data = new ArrayList<>();
                            for (int j = 0, length = jsonArray.length(); j < length; j++) {
                                jItem = jsonArray.getJSONObject(j);
                                PaymentItem item = new PaymentItem(
                                        Utils.returnDate(jItem.getLong("transactionDate")),
                                        Utils.convertTimestamp(jItem.getLong("transactionDate"), 2),
                                        Utils.convertTimestamp(jItem.getLong("transactionDate"), 1),
                                        jItem.getString("transactionID"), "",
                                        ConstantsPay.CURRENCY,
                                        jItem.getString("amountAuthorized"),
                                        jItem.getString("applicationLabel"),
                                        jItem.getString("maskedPAN"),
                                        jItem.has("approvalCode") ? jItem.getString("approvalCode") : "",
                                        jItem.getString("invoiceNumber"),
                                        jItem.getInt("transactionStatus"),
                                        jItem.has("trxType") ? jItem.getString("trxType") : "",
                                        ""
                                );
                                item.setTransactionRequestID(JsonParser.getDataJson(jItem, "transactionRequestID"));
                                item.setUdid(JsonParser.getDataJson(jItem, "udid"));
                                data.add(item);
                            }
                            callback.onSuccessActionTrans(data, ActionTrans.LIST_TRANS);
                        } catch (JSONException e) {
                            e.printStackTrace();
                            processCallbackFail(callback, new DataError(ConstantsPay.ERROR_CODE_PROCESS_DATA,
                                    getString(R.string.error_try_again)), ActionTrans.LIST_TRANS);
                        }
                    }

                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        saveLRs(ConstantsPay.GET_HISTORY + " onfailure: " + MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2, arg3));
                        Utils.LOGE(TAG, "Sales history Error: "+ MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2,
                                arg3));
                        processCallbackFail(callback, new DataError(ConstantsPay.ERROR_CODE_ON_FAIL_REQUEST,
                                getString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT)), ActionTrans.LIST_TRANS);
                    }
                });
    }


    public void getTransactionDetail(String transId, @NonNull ItfHandlerActionTrans<TransItem> callback) {
        saveLRq(ConstantsPay.SALES_HISTORY_DETAIL + " transId=" + transId + " runMacq= " + runMacq);
        if (runMacq) {
            getTransactionDetailMacq(transId, callback);
        }
        else {
            getTransactionDetailBank(transId, callback);
        }
    }

    private void getTransactionDetailBank(String transId, @NonNull ItfHandlerActionTrans<TransItem> callback) {
        StringEntity entity = null;
        String ssk = PrefLibTV.getInstance(context).getSessionKey();
        try {
            JSONObject jo = new JSONObject();
            jo.put(STR_SERVICE_NAME, ConstantsPay.SALES_HISTORY_DETAIL);
            jo.put("udid", "0");
            jo.put("readerSerialNo", getCurrSerialNumber());
            jo.put("versionNo", android.os.Build.VERSION.RELEASE);
            jo.put("platform", ConstantsPay.PLATFORM);
            jo.put("userID", getCurrUserId());
            jo.put("sessionKey", ssk);
            jo.put("transactionID", transId);
            jo.put("tokenL2", PrefLibTV.getInstance(context).getTKL2());
            Utils.LOGD("Data: ", jo.toString());
            entity = new StringEntity(EncodeDecode.doAESEncrypt(jo.toString(), ssk));
        } catch (Exception e1) {
            Utils.LOGE(TAG, ConstantsPay.SALES_HISTORY_DETAIL+": " + e1.getMessage());
            saveLRq("error build SALES_HISTORY_DETAIL: "+e1.getMessage());
        }

        Utils.LOGD(TAG, "getSalesDetail: url=" + ConstantsPay.getUrlServer(context));

        MposRestClient.getInstance(context).post(context, ConstantsPay.getUrlServer(context),
                entity, ConstantsPay.CONTENT_TYPE, new AsyncHttpResponseHandler() {

                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                        DataError dataError = null;
                        try {
                            String content = new String(arg2);
                            JSONObject response = new JSONObject(EncodeDecode.doAESDecrypt(content, ssk));
                            PrefLibTV.getInstance(context).setSessionKey(response.getString("sessionKey"));
                            Utils.LOGD(TAG, "Sales detail: " + response);

                            if (response.has("error")) {
                                dataError = buildDataError(response);
                                saveLRs(ConstantsPay.SALES_HISTORY_DETAIL + " error server:"+dataError.getErrorCode()+" " + dataError.getMsg());

                            } else {
                                PrefLibTV.getInstance(context).setTKL2(response.getString("tokenL2"));

                                TransItem data = MyGson.parseJson(response.toString(), TransItem.class);

                                saveLRs(ConstantsPay.SALES_HISTORY_DETAIL + " success");
                                callback.onSuccessActionTrans(data, ActionTrans.TRANS_DETAIL);
                            }
                        } catch (Exception e) {
                            saveLRs(ConstantsPay.SALES_HISTORY_DETAIL + " exception(timeout)");
                            Utils.LOGE(TAG, "Exception", e);
                            dataError = new DataError(ERROR_CODE_TIMEOUT_REQ, getString(R.string.SERVICE_ERROR_SESSION_TIMEOUT_L2));
                        }
                        if (dataError != null) {
                            processCallbackFail(callback, dataError, ActionTrans.TRANS_DETAIL);
                        }
                    }

                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        Utils.LOGE(TAG, "Sales detail error: " + MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2, arg3));
                        saveLRs(ConstantsPay.SALES_HISTORY_DETAIL + " onFailure: " + MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2, arg3));
                        processCallbackFail(callback, new DataError(ConstantsPay.ERROR_CODE_ON_FAIL_REQUEST,
                                getString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT)), ActionTrans.TRANS_DETAIL);
                    }
                });
    }


    public void getTransactionStatus(String transId, String udid, @NonNull ItfHandlerActionTrans<TransItem> callback) {
        saveLRq(ConstantsPay.GET_TRANSACTION_STATUS + " TRANDID=" + transId + " UDID=" + udid);
        if (runMacq) {
            if (!TextUtils.isEmpty(transId)) {
                getTransactionStatusMacqByTxId(transId, callback);

            }
            else if (!TextUtils.isEmpty(udid)) {
                getTransactionStatusMacqByUdid(udid, callback, TransItem.class);
            }
            else {
                processCallbackFail(callback, new DataError(ERROR_CODE_DEFAULT, "transId or udid can not empty") , ActionTrans.TRANS_STATUS);
            }
        }
        else {
            getTransactionStatusBank(transId, udid, callback);
        }
    }

    private void getTransactionStatusBank(String transId, String udid, @NonNull ItfHandlerActionTrans<TransItem> callback) {
        StringEntity entity = null;
        String ssk = PrefLibTV.getInstance(context).getSessionKey();
        try {
            JSONObject jo = new JSONObject();
            jo.put(STR_SERVICE_NAME, ConstantsPay.GET_TRANSACTION_STATUS);
            jo.put("readerSerialNo", getCurrSerialNumber());
            jo.put("versionNo", android.os.Build.VERSION.RELEASE);
            jo.put("platform", ConstantsPay.PLATFORM);
            jo.put("userID", getCurrUserId());
            jo.put("sessionKey", ssk);
            if (!TextUtils.isEmpty(transId)) {
                jo.put("transactionID", transId);
            }
            if (!TextUtils.isEmpty(udid)) {
                jo.put("udid", udid);
            }
            Utils.LOGD("Data: ", jo.toString());
            entity = new StringEntity(EncodeDecode.doAESEncrypt(jo.toString(), ssk));
        } catch (Exception e1) {
            Utils.LOGE(TAG, ConstantsPay.GET_TRANSACTION_STATUS+": " + e1.getMessage());
            saveLRq("error build GET_TRANSACTION_STATUS: "+e1.getMessage());
        }

        Utils.LOGD(TAG, "getSalesDetail: url=" + ConstantsPay.getUrlServer(context));

        MposRestClient.getInstance(context).post(context, ConstantsPay.getUrlServer(context),
                entity, ConstantsPay.CONTENT_TYPE, new AsyncHttpResponseHandler() {

                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                        DataError dataError = null;
                        try {
                            String content = new String(arg2);
                            JSONObject response = new JSONObject(EncodeDecode.doAESDecrypt(content, ssk));
                            PrefLibTV.getInstance(context).setSessionKey(response.getString("sessionKey"));
                            Utils.LOGD(TAG, "Sales detail: " + response);

                            if (response.has("error")) {
                                dataError = buildDataError(response);
                                saveLRs(ConstantsPay.GET_TRANSACTION_STATUS + " error server:"+dataError.getErrorCode()+" " + dataError.getMsg());

                            } else {
                                TransItem data = MyGson.parseJson(response.toString(), TransItem.class);

                                saveLRs(ConstantsPay.GET_TRANSACTION_STATUS + " success");
                                callback.onSuccessActionTrans(data, ActionTrans.TRANS_STATUS);
                            }
                        } catch (Exception e) {
                            saveLRs(ConstantsPay.GET_TRANSACTION_STATUS + " exception(timeout)");
                            Utils.LOGE(TAG, "Exception", e);
                            dataError = new DataError(ERROR_CODE_TIMEOUT_REQ, getString(R.string.SERVICE_ERROR_SESSION_TIMEOUT_L2));
                        }
                        if (dataError != null) {
                            processCallbackFail(callback, dataError, ActionTrans.TRANS_STATUS);
                        }
                    }

                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        Utils.LOGE(TAG,"Sales detail error: " + MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2,
                                arg3));
                        saveLRs(ConstantsPay.GET_TRANSACTION_STATUS + " onFailure: " + MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2, arg3));
                        processCallbackFail(callback, new DataError(ConstantsPay.ERROR_CODE_ON_FAIL_REQUEST,
                                getString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT)), ActionTrans.TRANS_STATUS);
                    }
                });
    }


    public void getListTransactionWaitSignature(@NonNull ItfHandlerActionTrans<TransItem> callback) {
        saveLRq(ConstantsPay.GET_LIST_PENDING_SIG);
        if (runMacq) {
            getListTransactionWaitSignatureMacq(callback);
        }
        else {
            getListTransactionWaitSignatureBank(callback);
        }
    }

    private void getListTransactionWaitSignatureBank(@NonNull ItfHandlerActionTrans<TransItem> callback) {

        StringEntity entity = null;
        String ssk = PrefLibTV.getInstance(context).getSessionKey();
        try {
            JSONObject jo = new JSONObject();
            jo.put(STR_SERVICE_NAME, ConstantsPay.GET_LIST_PENDING_SIG);
            jo.put("readerSerialNo", getCurrSerialNumber());
            jo.put("versionNo", android.os.Build.VERSION.RELEASE);
            jo.put("platform", ConstantsPay.PLATFORM);
            jo.put("userID", getCurrUserId());
            jo.put("udid", "0");
            jo.put("sessionKey", ssk);

            Utils.LOGD("Data: ", jo.toString());
            entity = new StringEntity(EncodeDecode.doAESEncrypt(jo.toString(), ssk));
        } catch (Exception e1) {
            Utils.LOGE(TAG, ConstantsPay.GET_LIST_PENDING_SIG+": " + e1.getMessage());
            saveLRq("error build GET_LIST_PENDING_SIG: "+e1.getMessage());
        }

        Utils.LOGD(TAG, "getListTransactionWaitSignature: url=" + ConstantsPay.getUrlServer(context));

        MposRestClient.getInstance(context).post(context, ConstantsPay.getUrlServer(context),
                entity, ConstantsPay.CONTENT_TYPE, new AsyncHttpResponseHandler() {

                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                        DataError dataError = null;
                        try {
                            String content = new String(arg2);
                            JSONObject response = new JSONObject(EncodeDecode.doAESDecrypt(content, ssk));
                            PrefLibTV.getInstance(context).setSessionKey(response.getString("sessionKey"));
                            Utils.LOGD(TAG, "getListTransactionWaitSignature: " + response);

                            if (response.has("error")) {
                                dataError = buildDataError(response);
                                saveLRs(ConstantsPay.GET_LIST_PENDING_SIG + " error server:"+dataError.getErrorCode()+" " + dataError.getMsg());

                            } else {

                                TransItem data = MyGson.parseJson(response.toString(), TransItem.class);

                                saveLRs(ConstantsPay.GET_LIST_PENDING_SIG + " success");
                                if (data != null && data.getListPendingSig() != null) {
                                    Utils.LOGD(TAG, "onSuccess: number trans pending signature=" + data.getListPendingSig().size());
                                }
                                callback.onSuccessActionTrans(data, ActionTrans.LIST_TRANS_PENDING_SIG);
                            }
                        } catch (Exception e) {
                            saveLRs(ConstantsPay.GET_LIST_PENDING_SIG + " exception(timeout)");
                            Utils.LOGE(TAG, "Exception", e);
                            dataError = new DataError(ERROR_CODE_TIMEOUT_REQ, getString(R.string.SERVICE_ERROR_SESSION_TIMEOUT_L2));
                        }
                        if (dataError != null) {
                            processCallbackFail(callback, dataError, ActionTrans.LIST_TRANS_PENDING_SIG);
                        }
                    }

                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        Utils.LOGE(TAG, "GET_LIST_PENDING_SIG error: " + MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2,
                                arg3));
                        saveLRs(ConstantsPay.GET_LIST_PENDING_SIG + " onFailure: " + MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2,
                                arg3));
                        processCallbackFail(callback, new DataError(ConstantsPay.ERROR_CODE_ON_FAIL_REQUEST,
                                getString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT)), ActionTrans.LIST_TRANS_PENDING_SIG);
                    }
                });
    }


    public void getTransactionReceipt(String transId, String transRqId, @NonNull ItfHandlerActionTrans<String> callback) {
        if (runMacq) {
            getTransactionReceiptMacq(transId, callback);
        }
        else {
            getTransactionReceiptBank(transId, transRqId, callback);
        }
    }

    private void getTransactionReceiptBank(String transId, String transRqId, @NonNull ItfHandlerActionTrans<String> callback) {
        saveLRq(ConstantsPay.DOWNLOAD_RECEIPT_IMAGE + " tId="+transId+" trId="+transRqId);
        StringEntity entity = null;
        String ssk = PrefLibTV.getInstance(context).getSessionKey();
        try {
            JSONObject jo = new JSONObject();
            jo.put("serviceName", ConstantsPay.DOWNLOAD_RECEIPT_IMAGE);
            jo.put("udid", "0");
            jo.put("readerSerialNo", getCurrSerialNumber());
            jo.put("versionNo", android.os.Build.VERSION.RELEASE);
            jo.put("platform", ConstantsPay.PLATFORM);
            jo.put("userID", getCurrUserId());
            jo.put("sessionKey", ssk);
            jo.put("tokenL2", PrefLibTV.getInstance(context).getTKL2());
            if (!TextUtils.isEmpty(transRqId)) {
                jo.put("transactionRequestID", transRqId);
            }
            else if (!TextUtils.isEmpty(transId)) {
                jo.put("transactionID", transId);
            }
            jo.put("receiptWidth", Constants.WIDTH_IMAGE_RECEIPT);
            entity = new StringEntity(EncodeDecode.doAESEncrypt(jo.toString(), ssk));
            Utils.LOGD(TAG, "getTransactionReceipt: " + jo);
        } catch (Exception e1) {
            e1.printStackTrace();
            saveLRq("error build DOWNLOAD_RECEIPT_IMAGE: "+e1.getMessage());
        }

        MposRestClient.getInstance(context).post(context, ConstantsPay.getUrlServer(context),
                entity, ConstantsPay.CONTENT_TYPE, new AsyncHttpResponseHandler() {

                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                        DataError dataError = null;
                        try {
                            JSONObject response = new JSONObject(EncodeDecode.doAESDecrypt(new String(arg2), ssk));
                            PrefLibTV.getInstance(context).setSessionKey(response.getString("sessionKey"));
                            Utils.LOGD("Get receipt: ", response.toString());
                            if (response.has("error")) {
                                dataError = buildDataError(response);
                                saveLRs(ConstantsPay.DOWNLOAD_RECEIPT_IMAGE + " error server: " + dataError.getErrorCode() + " " + dataError.getMsg());
                            }
                            else {
                                saveLRs(ConstantsPay.DOWNLOAD_RECEIPT_IMAGE + " onsuccess");
                                String receiptDownloaded = response.getString("receipt");
                                callback.onSuccessActionTrans(receiptDownloaded, ActionTrans.GET_RECEIPT);
                            }
                        } catch (Exception e) {
                            saveLRs(ConstantsPay.DOWNLOAD_RECEIPT_IMAGE + " exception(timeout)");
                            Utils.LOGE(TAG, "Exception", e);
                            dataError = new DataError(ERROR_CODE_TIMEOUT_REQ, getString(R.string.SERVICE_ERROR_SESSION_TIMEOUT_L2));
                        }
                        if (dataError != null) {
                            processCallbackFail(callback, dataError, ActionTrans.GET_RECEIPT);
                        }
                    }

                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        Utils.LOGE(TAG,"Get receipt error: " + MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2,
                                arg3));
                        saveLRs(ConstantsPay.DOWNLOAD_RECEIPT_IMAGE + " onFailure: "  + MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2,
                                arg3));
                        processCallbackFail(callback, new DataError(ERROR_CODE_ON_FAIL_REQUEST, getString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT)), ActionTrans.GET_RECEIPT);
                    }
                });
    }



    public void getSettlementReceipt(@NonNull ItfHandlerActionTrans<String> callback) {
        saveLRq(ConstantsPay.DOWNLOAD_RECEIPT_SETTLEMENT );
        StringEntity entity = null;
        String ssk = PrefLibTV.getInstance(context).getSessionKey();
        try {
            JSONObject jo = new JSONObject();
            jo.put("serviceName", ConstantsPay.DOWNLOAD_RECEIPT_SETTLEMENT);
            jo.put("udid", "0");
            jo.put("readerSerialNo", getCurrSerialNumber());
            jo.put("versionNo", android.os.Build.VERSION.RELEASE);
            jo.put("platform", ConstantsPay.PLATFORM);
            jo.put("userID", getCurrUserId());
            jo.put("sessionKey", ssk);

            jo.put("receiptWidth", Constants.WIDTH_IMAGE_RECEIPT);
            entity = new StringEntity(EncodeDecode.doAESEncrypt(jo.toString(), ssk));
            Utils.LOGD(TAG, "getTransactionReceipt: " + jo);
        } catch (Exception e1) {
            e1.printStackTrace();
            saveLRq("error build DOWNLOAD_RECEIPT_SETTLEMENT: "+e1.getMessage());
        }

        MposRestClient.getInstance(context).post(context, ConstantsPay.getUrlServer(context),
                entity, ConstantsPay.CONTENT_TYPE, new AsyncHttpResponseHandler() {

                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                        DataError dataError = null;
                        try {
                            JSONObject response = new JSONObject(EncodeDecode.doAESDecrypt(new String(arg2), ssk));
                            PrefLibTV.getInstance(context).setSessionKey(response.getString("sessionKey"));
                            Utils.LOGD("Get receipt: ", response.toString());
                            if (response.has("error")) {
                                dataError = buildDataError(response);
                                saveLRs(ConstantsPay.DOWNLOAD_RECEIPT_SETTLEMENT + " error server: " + dataError.getErrorCode() + " " + dataError.getMsg());
                            }
                            else {
                                saveLRs(ConstantsPay.DOWNLOAD_RECEIPT_SETTLEMENT + " onsuccess");
                                String receiptDownloaded = response.getString("receipt");
                                callback.onSuccessActionTrans(receiptDownloaded, ActionTrans.GET_RECEIPT_SETTLE);
                            }
                        } catch (Exception e) {
                            saveLRs(ConstantsPay.DOWNLOAD_RECEIPT_SETTLEMENT + " exception(timeout)");
                            Utils.LOGE(TAG, "Exception", e);
                            dataError = new DataError(ERROR_CODE_TIMEOUT_REQ, getString(R.string.SERVICE_ERROR_SESSION_TIMEOUT_L2));
                        }
                        if (dataError != null) {
                            processCallbackFail(callback, dataError, ActionTrans.GET_RECEIPT_SETTLE);
                        }
                    }

                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        Utils.LOGE(TAG, "Get receipt error: " + MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2,
                                arg3));
                        saveLRs(ConstantsPay.DOWNLOAD_RECEIPT_SETTLEMENT + " onFailure: "  + MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2,
                                arg3));
                        processCallbackFail(callback, new DataError(ERROR_CODE_ON_FAIL_REQUEST, getString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT)), ActionTrans.GET_RECEIPT_SETTLE);
                    }
                });
    }


    public void voidTransaction(String transId, @NonNull ItfHandlerActionTrans<Boolean> callback) {
        if (runMacq) {
            voidTransactionMacq(transId, callback);
        }
        else {
            voidTransactionBank(transId, callback);
        }
    }

    private void voidTransactionBank(String transId, @NonNull ItfHandlerActionTrans<Boolean> callback) {
        saveLRq(ConstantsPay.CONFIRM_VOID + " transId=" + transId);
        if (!PrefLibTV.getInstance(context).getPermitSettlement()) {
            saveLRq(ConstantsPay.CONFIRM_VOID +" permission denied");
            processCallbackFail(callback, new DataError(ConstantsPay.ERROR_CODE_PERMISSION_DENIED, getString(R.string.error_not_permission)), ActionTrans.SETTLE);
            return;
        }
        StringEntity entity = null;
        String ssk = PrefLibTV.getInstance(context).getSessionKey();
        try {
            JSONObject jo = new JSONObject();
            jo.put(STR_SERVICE_NAME, ConstantsPay.CONFIRM_VOID);
            jo.put("udid", "0");
            jo.put("readerSerialNo", getCurrSerialNumber());
            jo.put("versionNo", android.os.Build.VERSION.RELEASE);
            jo.put("platform", ConstantsPay.PLATFORM);
            jo.put("userID", getCurrUserId());
            jo.put("sessionKey", ssk);
            jo.put("transactionID", transId);
            jo.put("tokenL2", PrefLibTV.getInstance(context).getTKL2());
            Utils.LOGD("Data: ", jo.toString());
            entity = new StringEntity(EncodeDecode.doAESEncrypt(jo.toString(), ssk));
        } catch (Exception e1) {
            Utils.LOGE(TAG, "voidPayment: " + e1.getMessage());
            saveLRq("error build CONFIRM_VOID: "+e1.getMessage());
        }

        MposRestClient.getInstance(context).post(context, ConstantsPay.getUrlServer(context),
                entity, ConstantsPay.CONTENT_TYPE, new AsyncHttpResponseHandler() {

                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                        DataError dataError = null;
                        try {
                            JSONObject response = new JSONObject(EncodeDecode.doAESDecrypt(new String(arg2), ssk));
                            PrefLibTV.getInstance(context).setSessionKey(response.getString("sessionKey"));
                            Utils.LOGD("Confirm void: ", response.toString());
                            if (response.has("error")) {
                                dataError = buildDataError(response);
                                saveLRs(ConstantsPay.CONFIRM_VOID + " error server: "+dataError.getErrorCode()+" " + dataError.getMsg());
                            } else {
                                saveLRs(ConstantsPay.CONFIRM_VOID + " onsuccess");
                                callback.onSuccessActionTrans(true, ActionTrans.VOID_TRANS);
                            }
                        } catch (Exception e) {
                            saveLRs(ConstantsPay.CONFIRM_VOID + " exception(timeout)");
                            Utils.LOGE(TAG, "Exception", e);
                            dataError = new DataError(ERROR_CODE_TIMEOUT_REQ, getString(R.string.SERVICE_ERROR_SESSION_TIMEOUT_L2));
                        }
                        if (dataError != null) {
                            processCallbackFail(callback, dataError, ActionTrans.VOID_TRANS);
                        }
                    }

                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        Utils.LOGE(TAG, "Confirm void error: " + MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2,
                                arg3));
                        saveLRs(ConstantsPay.CONFIRM_VOID + " onFailure: " + MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2,
                                arg3));
                        processCallbackFail(callback, new DataError(ERROR_CODE_ON_FAIL_REQUEST, getString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT)), ActionTrans.VOID_TRANS);
                    }
                });
    }

    public void settleAllTransaction_Complete(@NonNull ItfHandlerActionTrans<Boolean> callback) {
        saveLRq(ConstantsPay.CREDIT_SETTLEMENT);
        if (!PrefLibTV.getInstance(context).getPermitSettlement()) {
            saveLRq(ConstantsPay.CREDIT_SETTLEMENT +" permission denied");
            processCallbackFail(callback, new DataError(ConstantsPay.ERROR_CODE_PERMISSION_DENIED, getString(R.string.error_not_permission)), ActionTrans.SETTLE);
            return;
        }
        if (runMacq) {
            settleAllTransactionMacq(callback);
        }
        else {
            settleAllTransactionBank(callback);
        }
    }

    private void settleAllTransactionBank(@NonNull ItfHandlerActionTrans<Boolean> callback) {

        StringEntity entity = null;
        String ssk = PrefLibTV.getInstance(context).getSessionKey();
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, ConstantsPay.CREDIT_SETTLEMENT);
            jo.put("udid", "0");
            jo.put("readerSerialNo", getCurrSerialNumber());
            jo.put("versionNo", android.os.Build.VERSION.RELEASE);
            jo.put("platform", ConstantsPay.PLATFORM);
            jo.put("userID", getCurrUserId());
            jo.put("sessionKey", ssk);
            jo.put("tokenL2", PrefLibTV.getInstance(context).getTKL2());
            Utils.LOGD("Data: ", jo.toString());
            entity = new StringEntity(EncodeDecode.doAESEncrypt(jo.toString(), ssk));
        } catch (Exception e1) {
            Utils.LOGE(TAG, "creditSetttlement: " + e1.getMessage());
            saveLRq("error build creditSetttlement: "+e1.getMessage());
        }

        MposRestClient.getInstance(context).post(context, ConstantsPay.getUrlServer(context),
                entity, ConstantsPay.CONTENT_TYPE, new AsyncHttpResponseHandler() {

                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                        DataError dataError = null;
                        try {
                            JSONObject response = new JSONObject(EncodeDecode.doAESDecrypt(new String(arg2), ssk));
                            PrefLibTV.getInstance(context).setSessionKey(response.getString("sessionKey"));
                            Utils.LOGD("settlement: ", response.toString());
                            if (response.has("error")) {
                                dataError = buildDataError(response);
                                saveLRs(ConstantsPay.CREDIT_SETTLEMENT + " error server: "+dataError.getErrorCode()+" " + dataError.getMsg());

                            } else {
                                saveLRs(ConstantsPay.CREDIT_SETTLEMENT + " success");
                                callback.onSuccessActionTrans(true, ActionTrans.SETTLE);
                            }
                        } catch (Exception e) {
                            saveLRs(ConstantsPay.CREDIT_SETTLEMENT + " exception(timeout)");
                            Utils.LOGE("Exception", e.getMessage());
                            dataError = new DataError(ERROR_CODE_TIMEOUT_REQ, getString(R.string.SERVICE_ERROR_SESSION_TIMEOUT_L2));
                        }
                        if (dataError != null) {
                            processCallbackFail(callback, dataError, ActionTrans.SETTLE);
                        }
                    }

                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        Utils.LOGE(TAG, "settlement Error: " + MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2,
                                arg3));
                        saveLRs(ConstantsPay.CREDIT_SETTLEMENT + " onFailure: " + MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2,
                                arg3));
                        processCallbackFail(callback, new DataError(ERROR_CODE_ON_FAIL_REQUEST, getString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT)), ActionTrans.SETTLE);
                    }
                });
    }


    public void sendReceiptWithTransaction(String transId, String email, @NonNull ItfHandlerActionTrans<Boolean> callback) {
        if (runMacq) {
            sendReceiptWithTransactionMacq(transId, email, callback);
        }
        else {
            sendReceiptWithTransactionBank(transId, email, callback);
        }
    }

    private void sendReceiptWithTransactionBank(String transId, String email, @NonNull ItfHandlerActionTrans<Boolean> callback) {
        saveLRq(ConstantsPay.SEND_RECEIPT );
        StringEntity entity = null;
        String ssk = PrefLibTV.getInstance(context).getSessionKey();
        try {
            JSONObject jo = new JSONObject();
            jo.put(STR_SERVICE_NAME, SEND_RECEIPT);
            jo.put("udid", "0");
            jo.put("readerSerialNo", getCurrSerialNumber());
            jo.put("versionNo", android.os.Build.VERSION.RELEASE);
            jo.put("platform", ConstantsPay.PLATFORM);
            jo.put("userID", getCurrUserId());
            jo.put("sessionKey", ssk);
            jo.put("transactionID", transId);
            jo.put("email", email);
            Utils.LOGD("Data: ", jo.toString());
            entity = new StringEntity(EncodeDecode.doAESEncrypt(jo.toString(), ssk));

        } catch (Exception e1) {
            Utils.LOGE(TAG, "sendEmailByType: ", e1);
            saveLRq("error build sendEmailByType: "+e1.getMessage());
        }

        MposRestClient.getInstance(context).post(context, ConstantsPay.getUrlServer(context),
                entity, ConstantsPay.CONTENT_TYPE, new AsyncHttpResponseHandler() {

                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                        DataError dataError = null;
                        try {
                            JSONObject response = new JSONObject(EncodeDecode.doAESDecrypt(new String(arg2), ssk));
                            PrefLibTV.getInstance(context).setSessionKey(response.getString("sessionKey"));
                            Utils.LOGD("Send receipt: ", response.toString());
                            if (response.has("error")) {
                                dataError = buildDataError(response);
                                saveLRs(ConstantsPay.SEND_RECEIPT + " error server: " +dataError.getErrorCode()+" "+ dataError.getMsg());
                            } else {
                                saveLRs(ConstantsPay.SEND_RECEIPT + " success");
                                callback.onSuccessActionTrans(true, ActionTrans.SEND_RECEIPT);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        if (dataError != null) {
                            processCallbackFail(callback, dataError, ActionTrans.SEND_RECEIPT);
                        }
                    }

                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        String error = MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2,
                                arg3);
                        Utils.LOGD(TAG, "onFailure: "+error);
                        saveLRs(ConstantsPay.SEND_RECEIPT + " onFailure: " + MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2,
                                arg3));
                        processCallbackFail(callback, new DataError(ERROR_CODE_ON_FAIL_REQUEST,
                                getString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT)), ActionTrans.SEND_RECEIPT);
                    }
                });
    }

    private DataError buildDataError(JSONObject response) throws JSONException{
        final JSONObject jo = response.getJSONObject("error");
        int errorCode = jo.getInt("code");
        String msg = getString(R.string.error) + " " + errorCode + ": " + LibError.getErrorMsg(errorCode, contextRes == null ? context : contextRes);

        return new DataError(errorCode, msg);
    }

}
