package com.mpos.sdk.core.model;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Environment;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.security.crypto.EncryptedSharedPreferences;
import androidx.security.crypto.MasterKeys;

import com.mpos.sdk.core.control.EncodeDecode;
import com.mpos.sdk.core.control.LibReaderController;
import com.mpos.sdk.util.Constants;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.Utils;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStreamReader;
import java.security.GeneralSecurityException;


public final class PrefLibTV {

    private static final String TAG = "PrefLibTV";

//	public static final String PREFIX_NUM_OF_WIDGET = "LIBNOTIFY";

	public static final String PREFIX_BLUETOOTH_NAME 	= "pref_bluetooth_name";
	public static final String PREFIX_BLUETOOTH_ADDRESS = "pref_bluetooth_address";
	public static final String PREFIX_IS_SAVE_LOGIN		= "pref_is_save_login";
	public static final String PREFIX_PW                = "pref_pou";
	public static final String PREFIX_EMAIL_MERCHANT	= "pref_email_merchant";
	public static final String PREFIX_RESTRICT_INTERNATIONAL_CARD	    = "pref_restrict_international_card";
    public static final String PREFIX_EMV_RESTRICT_INTERNATIONAL_CARD	= "pref_emv_restrict_international_card";

    public static final String PREFIX_TID				= "pref_tid";
	public static final String PREFIX_MID				= "pref_mid";
	public static final String PREFIX_MERCHANT_ID		= "pref_merchantId";
    public static final String PREFIX_COUNT_FALL_BACK	= "pref_count_fall_back";

    public static final String SKIP_SIGNATURE           = "skip_signature"; // skipSignature: macq skip signature of mobileUser
    public static final String MAX_AMOUNT_SIGNATURE     = "max_amount_signature";

    public static final String PREFIX_FLAG_DEVICES		= "flag_devices";
    public static final String PREFIX_FLAG_SERVER		= "flag_server";
    // config issuerValuesList
    public static final String PREFIX_CONFIG_MASTER		= "pref_config_master";
    public static final String PREFIX_CONFIG_VISA		= "pref_config_visa";
    public static final String PREFIX_CONFIG_JCB		= "pref_config_jcb";

    private static final String PREFIX_SERIAL_NUMBER		= "pref_serial_number";
    private final String PREFIX_SN_MUST_CONNECT	= "pref_sn_must_connect";

    public static final String PREFIX_WKK_MAILINH		= "wkk_mailinh";
    public static final String WORKING_KEY  			= "working_key";
    public static final String WORKING_KEY_LAST_OK      = "working_key_last_success";

    public static final String CUSTOM_LANGUAGE       	= "custom_language";
    public static final String MP_K                     = "mP_k";
    public static final String PACKAGE_NAME       	    = "package_name";
    public static final String BANK_NAME       	        = "bank_name";
    public static final String MPOS_TID       	        = "mpos-tid";
    public static final String MPOS_MID       	        = "mpos-mid";
    public static final String MPOS_MUID       	        = "mpos-muid";
    public static final String TKL2       	            = "pref_tkl2";

    public static final String PRE_SALE_CONFIG          = "pre_sale_config";
    public static final String LAST_TIME_PRE_SALE_CACHE = "last_time_pre_sale_cache";

    public static final String BLOCK_PAN_RANGE       	= "block_pan_range";
    public static final String MERCHANT_INFO           	= "merchant_info"; // show in dialog error
    public static final String BANK_ACQUIRE           	= "bank_acquire"; // show in dialog error
    public static final String APP_VERSION_CODE         = "app_ver_code"; // show in dialog error
    public static final String BUSINESS_NAME           	= "BUSINESS_NAME";
    public static final String BUSINESS_ADDRESS         = "BUSINESS_ADDRESS";

    // version bin local
    public static final String currVerBinLocal      = "currVerBinLocal";
    public static final String serverVerBinLocal    = "serverVerBinLocal";
    public static final String listBinLocal         = "listBinLocal";
    public static final String reportError          = "reportError";

    public static final String lastTimeCallApi      = "lastTimeCA";     // check session for smart-pos
    public static final String lastErrorSale        = "lastErrorSale";     //
    public static final String lastErrorSaleTime    = "lastErrorSaleTime"; // last time save lastErrorSale

    public static final String installmentInfo      = "installmentInfo";

//    public static final String cacheMpos            = "cacheMpos";
//    public static final String lastTimeCacheMpos    = "lastTimeCacheMpos";

    public static final String LAST_TIME_LOGIN_MA = "last_time_login";
    public static final String LAST_TIME_LOAD_CONFIG_MC_OK      = "last_time_load_config_mc_ok";
//    public static final String DATA_LOGIN_CACHE                 = "data_login_cache";
    private static final String DATA_MERCHANT_CONFIG_CACHE_SDK   = "data_merchant_config_cache_sdk";

    public static final String HEADER_AUTHORIZATION_BEARER   = "header_authorization_bearer";
    public static final String HEADER_SESSION_ID   = "header_session_id";

    public static final String versionLite          = "versionLite";
    public static final String versionLiteNew       = "versionLite_new";
    public static final String timeTrans            = "timeTrans";

    public static final String upgradeFw    = "upgradeFw";
    public static final String urlUpgradeFw = "urlUpgradeFw";
    public static final String versionFwUpgrade = "versionFwUpgrade";

    public static final String upgradeEMVConfig = "upgradeEMVConfig";
    public static final String lastTimeUpgradeEMVConfig = "lastTimeUpgradeEMVConfig";
    public static final String urlEmvApp = "urlEmvApp";
    public static final String urlEmvCapk = "urlEmvCapk";

    public static final String fileNameEmvConfigPax = "data.txt";
    public static final String childEmvConfigPax = "/emv_config";

	public static final String numSaleRemain = "numSaleRemain";

    public static final String readersInjected = "readersInjected";
    public static final String commonConfig = "commonConfig";
    public static final String motoBinRanges = "motoBinRanges";
    public static final String maskPanInstallmentNeedCheck = "maskPanInstallmentNeedCheck";
    public static final String lastTimeCommonConfigOk = "lastTimeCommonConfigOk";
    public static final String lastTimeCommonConfigFail = "lastTimeCommonConfigFail";

    public static final String DATA_LOGIN_MACQ_CACHE = "DATA_LOGIN_MACQ_CACHE";


    public static final String canSaleService = "canSaleService";
    public static final String restrictionNFC = "restrictionNFC";
    public static final String sendTrxReceipt = "sendTrxReceipt";   // app sent email or not => 1: app sent; other: mpos sent

    private final String dataLoginMerchant = "dataLoginMerchant";

	private static final String BLUETOOTH_ADDRESS_PRINTER 	= "BLUETOOTH_ADDRESS_PRINTER";
	private static final String BLUETOOTH_NAME_PRINTER 	= "BLUETOOTH_NAME_PRINTER";
	private static final String BLUETOOTH_ADDRESS_PAX 	= "BLUETOOTH_ADDRESS_PAX";
	private static final String SSID_PAX 	= "SSID_PAX";

    private static final String PREFIX_IS_DISABLE_CHECK_GPS = "IS_DISABLE_CHECK_GPS";

    public static final String isAutoCloseDialog = "isAutoCloseDialog";

    private static final String ld = "lgdt";
    private static PrefLibTV prefLibTV;
    static SharedPreferences sharedPreferences;
//    EncryptedSharedPreferences encryptedSharedPreferences;


	public static final String isReceiverCancelOrder = "isReceiverCancelOrder";
	private static final String isPrint = "isPrint";
    private static String binLocals = "binLocals";
    private static String permitPayGiftCard = "permitPayGiftCard";
    private static String isMerEmart = "isMerEmart";
    public static final String allowDeposit = "allowDeposit";
    public static final String maxDaySettleDeposit = "maxDaySettleDeposit";
//    public static String hasPermitCachePreSale = "hasPermitCachePreSale";

	public static final String currencyCertify = "currencyCertify";

    private PrefLibTV(@NonNull Context context) {
        if (context != null) {
//            sharedPreferences = context.getSharedPreferences(PREFIX_NUM_OF_WIDGET, Context.MODE_MULTI_PROCESS);
            try {
                String masterKeyAlias = MasterKeys.getOrCreate(MasterKeys.AES256_GCM_SPEC);
                sharedPreferences = EncryptedSharedPreferences.create(
                        "encrypted_preferences", // fileName
                        masterKeyAlias, // masterKeyAlias
                        context.getApplicationContext(), // context
                        EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV, // prefKeyEncryptionScheme
                        EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM // prefvalueEncryptionScheme
                );
            } catch (GeneralSecurityException | IOException e) {
                e.printStackTrace();
            }

            put(PACKAGE_NAME, context.getPackageName());
        }
    }

    public static synchronized PrefLibTV getInstance(@NonNull Context context) {
        if (context == null) {
            Log.e(TAG, "PrefLibTV: context must not null.");
        }
        if (prefLibTV == null || prefLibTV.isInitFail()) {
            prefLibTV = new PrefLibTV(context);
        }
        return prefLibTV;
    }

    public SharedPreferences getSharedPreferences() {
        return sharedPreferences;
    }

	public String getBinLocals() {
		return sharedPreferences.getString(binLocals, "");
	}

    public void setBinLocals(String bins) {
        sharedPreferences.edit().putString(binLocals, bins).apply();
    }

    public String getPermitPayGiftCard() {
        return sharedPreferences.getString(permitPayGiftCard, "");
    }

    public void setPermitPayGiftCard(String data) {
        sharedPreferences.edit().putString(permitPayGiftCard, data).apply();
    }

	public boolean getMerEmart() {
		return sharedPreferences.getBoolean(isMerEmart, false);
	}

	public void saveMerEmart(boolean b) {
        sharedPreferences.edit().putBoolean(isMerEmart, b).apply();
	}

	public boolean getPermitPrint() {
		return sharedPreferences.getBoolean(isPrint, true);
	}

	public void setPermitPrint(boolean b) {
        sharedPreferences.edit().putBoolean(isPrint, b).apply();
	}

    public boolean getEnableReceiverCancelOrder() {
        return sharedPreferences.getBoolean(isReceiverCancelOrder, false);
    }

    public void setEnableReceiverCancelOrder(boolean data) {
        sharedPreferences.edit().putBoolean(isReceiverCancelOrder, data).apply();
    }

	private boolean isInitFail() {
//        Log.i(TAG, "isInitOk: " + (sharedPreferences == null ? "not ok" : "ok"));
        return sharedPreferences == null;
    }

	public void setPermitSettlement(boolean flag) {
        sharedPreferences.edit().putBoolean("pref_permitSettlement", flag).apply();
	}

	public boolean getPermitSettlement() {
        return sharedPreferences.getBoolean("pref_permitSettlement", false);
	}

	public void setPermitVoid( boolean index) {
		sharedPreferences.edit().putBoolean("pref_permitVoid", index).apply();
	}

	public boolean getPermitVoid() {
        return sharedPreferences.getBoolean("pref_permitVoid", false);
	}

	public void setDisableCheckGps( boolean value) {
		sharedPreferences.edit().putBoolean(PREFIX_IS_DISABLE_CHECK_GPS, value).apply();
	}

	public boolean getDisableCheckGps() {
        return sharedPreferences.getBoolean(PREFIX_IS_DISABLE_CHECK_GPS, false);
	}

    /**
     * set sessionkey and lasttime call api
     * @param index: session key
     */
	public void setSessionKey(String index) {
		sharedPreferences.edit()
                .putString("pref_session_key", index)
                .putLong(lastTimeCallApi, System.currentTimeMillis()).apply();
	}

	public String getSessionKey() {
        return sharedPreferences.getString("pref_session_key", "");
	}

	public long getLastTimeCA() {
        return sharedPreferences.getLong(lastTimeCallApi, System.currentTimeMillis());
	}

    /**
     * lastErrorSale: save last status transaction and current time
     */
    public void setLastErrorSale(int status) {
        sharedPreferences.edit()
                .putInt(lastErrorSale, status)
                .putLong(lastErrorSaleTime, System.currentTimeMillis()).apply();
    }
    public long getLastErrorSaleTime() {
        return sharedPreferences.getLong(lastErrorSaleTime, 0);
    }
    public int getLastErrorSale() {
        return sharedPreferences.getInt(lastErrorSale, LibReaderController.TYPE_ERROR_DEFAULT);
    }

	public void setTKL2( String index) {
		sharedPreferences.edit().putString(TKL2, index).apply();
	}

	public String getTKL2() {
        return sharedPreferences.getString(TKL2, "");
	}

	public void setSerialNumber( String index) {
		sharedPreferences.edit().putString(PREFIX_SERIAL_NUMBER, index).apply();
	}

	public String getSerialNumber() {
        return sharedPreferences.getString(PREFIX_SERIAL_NUMBER, "");
	}

	public void setMcConfigCache( String dataCache) {
        Utils.LOGD(TAG, "setMcConfigCache: "+dataCache);
		sharedPreferences.edit().putString(DATA_MERCHANT_CONFIG_CACHE_SDK, dataCache).apply();
	}

	public String getMcConfigCache() {
        Utils.LOGD(TAG, "getMcConfigCache: ---------------->");
        return sharedPreferences.getString(DATA_MERCHANT_CONFIG_CACHE_SDK, "");
	}

    public void setSerialNumberMustConnect(String sn) {
        if (Constants.isTypeIntegrationMpos() && sn.startsWith("MPOS") && sn.length() == 14) {
            sharedPreferences.edit().putString(PREFIX_SN_MUST_CONNECT, sn).apply();
        }
    }
    public String getSerialNumberMustConnect() {
        return sharedPreferences.getString(PREFIX_SN_MUST_CONNECT, "");
    }


    // bluetooth name
	public void setBluetoothName( String index) {
		sharedPreferences.edit().putString(PREFIX_BLUETOOTH_NAME, index).apply();
	}
	
	public String getBluetoothName() {
        return sharedPreferences.getString(PREFIX_BLUETOOTH_NAME, "");
	}
	
	// bluetooth address
	public void setBluetoothAddress( String index) {
		sharedPreferences.edit().putString(PREFIX_BLUETOOTH_ADDRESS, index).apply();
	}
	
	public String getBluetoothAddress() {
        return sharedPreferences.getString(PREFIX_BLUETOOTH_ADDRESS, "");
	}

	// is save login
	public void setIsSaveLogin( boolean index) {
		sharedPreferences.edit().putBoolean(PREFIX_IS_SAVE_LOGIN, index).apply();
	}
	
	public boolean getIsSaveLogin() {
        return sharedPreferences.getBoolean(PREFIX_IS_SAVE_LOGIN, false);
	}
	
	// PREFIX_PASSWORD
	public void setPW( String p) {
//		sharedPreferences.edit().putString(PREFIX_PW, Base64.encodeToString(p.getBytes(), Base64.DEFAULT)).apply();

        try {
            String encryptKey = EncodeDecode.doAESEncrypt(p, get(PACKAGE_NAME, String.class));
            put(PREFIX_PW, encryptKey);
        } catch (Exception e) {
            e.printStackTrace();
        }
	}
	
	public String getPW() {
//		SharedPreferences settings = c.getSharedPreferences(PREFIX_NUM_OF_WIDGET, 0);
//		String p = settings.getString(PREFIX_PW, "");
//		if(!TextUtils.isEmpty(p)){
//			try {
//				p = new String(Base64.decode(p.getBytes(), Base64.DEFAULT));
//			} catch (Exception e) {
//			}
//		}
//		return p;

        try {
            String encryptKey = get(PREFIX_PW, String.class);
            return EncodeDecode.doAESDecrypt(encryptKey, get(PACKAGE_NAME, String.class));
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
	}
	
	//
	public void setRestrictInternationalCard( boolean index) {
		sharedPreferences.edit().putBoolean(PREFIX_RESTRICT_INTERNATIONAL_CARD, index).apply();
	}
	
	public boolean getRestrictInternationalCard() {
        return sharedPreferences.getBoolean(PREFIX_RESTRICT_INTERNATIONAL_CARD, false);
	}
	public void setEmvRestrictInternationalCard( boolean index) {
		sharedPreferences.edit().putBoolean(PREFIX_EMV_RESTRICT_INTERNATIONAL_CARD, index).apply();
	}

	public boolean getEmvRestrictInternationalCard() {
        return sharedPreferences.getBoolean(PREFIX_EMV_RESTRICT_INTERNATIONAL_CARD, false);
	}

    // email merchant
	public void setEmailMerchant( String index) {
		sharedPreferences.edit().putString(PREFIX_EMAIL_MERCHANT, index).apply();
	}
	
	public String getEmailMerchant() {
        return sharedPreferences.getString(PREFIX_EMAIL_MERCHANT, "");
	}
	// mid
	public void setMId( String index) {
		sharedPreferences.edit().putString(PREFIX_MID, index).apply();
	}
	
	public String getMId() {
        return sharedPreferences.getString(PREFIX_MID, "");
	}
	// tid
	public void setTId( String index) {
		sharedPreferences.edit().putString(PREFIX_TID, index).apply();
	}
	
	public String getTId() {
        return sharedPreferences.getString(PREFIX_TID, "");
	}
	// PREFIX_MERCHANT_ID
	public void setMerchantId( String index) {
		sharedPreferences.edit().putString(PREFIX_MERCHANT_ID, index).apply();
	}
	
	public String getMerchantsId() {
        return sharedPreferences.getString(PREFIX_MERCHANT_ID, "");
	}

	public void setUserId( String id) {
		sharedPreferences.edit().putString("pref_user_id", id).apply();
	}

	public String getUserId() {
        return sharedPreferences.getString("pref_user_id", "");
	}

	public void setLatitude( String id) {
		sharedPreferences.edit().putString("pref_latitude", id).apply();
	}

	public String getLatitude() {
        return sharedPreferences.getString("pref_latitude", "0");
	}

	public void setLongtitude( String id) {
		sharedPreferences.edit().putString("pref_longtitude", id).apply();
	}

	public String getLongtitude() {
        return sharedPreferences.getString("pref_longtitude", "0");
	}

	public void setGa( String ads) {
		sharedPreferences.edit().putString("lib_ga", ads).apply();
	}

	public String getGa() {
        return sharedPreferences.getString("lib_ga", "UA-27981359-1");
	}

	public void setShopName( String ads) {
		sharedPreferences.edit().putString("shop_name", ads).apply();
	}

	public String getShopName() {
        return sharedPreferences.getString("shop_name", "MPOS");
	}

	public void setTagConfig( String ads) {
		sharedPreferences.edit().putString("tag_config", ads).apply();
	}

	public String getTagConfig() {
        return sharedPreferences.getString("tag_config", "");
	}

	public void setCAKey( boolean ads) {
		sharedPreferences.edit().putBoolean("ca_key", ads).apply();
	}

	public boolean getCAKey() {
        return sharedPreferences.getBoolean("ca_key", false);
	}

	public void setCAData( String ads) {
		sharedPreferences.edit().putString("ca_data", ads).apply();
	}

	public String getCAData() {
        return sharedPreferences.getString("ca_data", "");
	}

	public void setReaderEncryptMode( String ads) {
		sharedPreferences.edit().putString("readerEncryptMode", ads).apply();
	}

	public String getReaderEncryptMode() {
        return sharedPreferences.getString("readerEncryptMode", "1");
	}

	public void setFlagDevices( int ads) {
		sharedPreferences.edit().putInt(PREFIX_FLAG_DEVICES, ads).apply();
	}

	public int getFlagDevices() {
        return sharedPreferences.getInt(PREFIX_FLAG_DEVICES, 0);
	}

	public void setFlagServer( int ads) {
		sharedPreferences.edit().putInt(PREFIX_FLAG_SERVER, ads).apply();
	}

	public int getFlagServer() {
        return sharedPreferences.getInt(PREFIX_FLAG_SERVER, 0);
	}

	public void setPartner( String ads) {
		sharedPreferences.edit().putString("flag_amount", ads).apply();
	}

	public String getPartner() {
        return sharedPreferences.getString("flag_amount","");
	}
	
	public void setRouter( String ads) {
		sharedPreferences.edit().putString("flag_router", ads).apply();
	}

	public String getRouter() {
        return sharedPreferences.getString("flag_router","");
	}
    public void setCountFallBack( int ads) {
        sharedPreferences.edit().putInt(PREFIX_COUNT_FALL_BACK, ads).apply();
    }

    public int getCountFallBackk() {
        return sharedPreferences.getInt(PREFIX_COUNT_FALL_BACK, 0);
    }
    // master
    public void setTagConfigMaster( String ads) {
        sharedPreferences.edit().putString(PREFIX_CONFIG_MASTER, ads).apply();
    }

    public String getTagConfigMaster() {
        return sharedPreferences.getString(PREFIX_CONFIG_MASTER, "");
    }
    // visa
    public void setTagConfigVisa( String ads) {
        sharedPreferences.edit().putString(PREFIX_CONFIG_VISA, ads).apply();
    }

    public String getTagConfigVisa() {
        return sharedPreferences.getString(PREFIX_CONFIG_VISA, "");
    }
    // jcb
    public void setTagConfigJCB( String ads) {
        sharedPreferences.edit().putString(PREFIX_CONFIG_JCB, ads).apply();
    }

    public String getTagConfigJCB() {
        return sharedPreferences.getString(PREFIX_CONFIG_JCB, "");
    }


    // data Login Merchant
    public void createDataLoginMerchant(String data) {
        put(dataLoginMerchant, data);
//        SharedPrefs.getInstance().put(dataLoginMerchant, data);
    }

    public String getDataLoginMerchant() {
//        return SharedPrefs.getInstance().get(dataLoginMerchant, String.class);
        return get(dataLoginMerchant, String.class, "");
    }

    // skip signature
    public void setSkipSignature(boolean value) {
        sharedPreferences.edit().putBoolean(SKIP_SIGNATURE, value).apply();
    }

    public boolean getSkipSignature() {
        return sharedPreferences.getBoolean(SKIP_SIGNATURE, false);
    }
    // max amount signature
    public void setMaxAmountSignature( String value) {
        sharedPreferences.edit().putString(MAX_AMOUNT_SIGNATURE, value).apply();
    }

    public String getMaxAmountSignature() {
        return sharedPreferences.getString(MAX_AMOUNT_SIGNATURE, ConstantsPay.VALUE_DEF_NEGATIVE_1);
    }

    // custom language
    public void saveCustomLanguage(String value) {
        put(CUSTOM_LANGUAGE, value);
    }

    public String getCustomLanguage() {
        return get(CUSTOM_LANGUAGE, String.class, "");
    }
    // BANK_NAME
    public void saveBankName(String value) {
        put(BANK_NAME, value);
    }

    public String getBankName() {
        return get(BANK_NAME, String.class, "");
    }
    // key
    public void saveMpK(String value) {
        try {
            String encryptKey = EncodeDecode.doAESEncrypt(value, get(PACKAGE_NAME, String.class));
            put(MP_K, encryptKey);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public String getMpK() {
        try {
            String encryptKey = get(MP_K, String.class);
            return EncodeDecode.doAESDecrypt(encryptKey, get(PACKAGE_NAME, String.class));
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }


    public void savePreSale(String data) {
        sharedPreferences.edit()
                .putString(PRE_SALE_CONFIG, data)
                .putLong(LAST_TIME_PRE_SALE_CACHE, System.currentTimeMillis())
                .apply();
    }

    // working key
    public void setWorkingKey( String wk) {
        sharedPreferences.edit().putString(WORKING_KEY, wk).apply();
    }

    public String getWorkingKey() {
        return sharedPreferences.getString(WORKING_KEY, "");
    }


    public void setWkkMaiLinh( String value) {
        sharedPreferences.edit().putString(PREFIX_WKK_MAILINH, value).apply();
    }

    public String getWkkMaiLinh() {
        return sharedPreferences.getString(PREFIX_WKK_MAILINH, ConstantsPay.VALUE_DEF_EMPTY);
    }

	public void clearDataAuto(){
		sharedPreferences.edit().putString(PREFIX_BLUETOOTH_ADDRESS, "")
		        .putString(PREFIX_BLUETOOTH_NAME, "")
		        .putBoolean(PREFIX_IS_SAVE_LOGIN, false)
		        .putString(PREFIX_PW, "")
		        .putString(PREFIX_TID, "")
		        .putString(PREFIX_MID, "")
		        .putString(TKL2, "")
		        .putString(readersInjected, "")
		        .putString(WORKING_KEY_LAST_OK, "")
		        .putString(commonConfig, "")
		        .putInt(numSaleRemain, Constants.NUM_SALE_REMAIN_DEFAULT)
                .putInt(PREFIX_FLAG_DEVICES, ConstantsPay.DEVICE_NONE)
		        .putString(MAX_AMOUNT_SIGNATURE, ConstantsPay.VALUE_DEF_NEGATIVE_1)
		        .putString(BLUETOOTH_ADDRESS_PRINTER, "")
                .putString(BLUETOOTH_NAME_PRINTER, "")
//                .putString(DATA_MERCHANT_CONFIG_CACHE_SDK, "") // mr Luong said: cache forever
                .putString(DATA_LOGIN_MACQ_CACHE, "")
                .putString(HEADER_AUTHORIZATION_BEARER, "")
                .putString(HEADER_SESSION_ID, "")
                .putString(PRE_SALE_CONFIG, "")
                .putString(PREFIX_SN_MUST_CONNECT, "")
                .apply();

        DataCache.getInstance().clearCache();
	}

    public void clearHeaderMacq() {
        sharedPreferences.edit()
                .putString(HEADER_AUTHORIZATION_BEARER, "")
                .putString(HEADER_SESSION_ID, "")
                .apply();
    }

	//BLUETOOTH_ADDRESS_PRINTER
	public void createBluetoothAddressPrinter(String address, String name) {
		put(BLUETOOTH_ADDRESS_PRINTER, address);
		put(BLUETOOTH_NAME_PRINTER, name);
	}

	public String getBluetoothAddressPrinter() {
		return get(BLUETOOTH_ADDRESS_PRINTER, String.class);
	}
	public String getBluetoothNamePrinter() {
		return get(BLUETOOTH_NAME_PRINTER, String.class);
	}

	public void createBluetoothAddressPAX(String value) {
		put(BLUETOOTH_ADDRESS_PAX, value);
	}

	public String getBluetoothAddressPAX() {
		return get(BLUETOOTH_ADDRESS_PAX, String.class);
	}

	public void createSSIDPAX(String value) {
		put(SSID_PAX, value);
	}

	public String getSSIDPAX() {
		return get(SSID_PAX, String.class);
	}

    // Data Log
	//CHANGE CURRENTCY CERTIFY
	public void setCurrencyCertify(String ads) {
        sharedPreferences.edit().putString(currencyCertify, ads).apply();
	}

	public String getCurrencyCertify() {
		return sharedPreferences.getString(currencyCertify, "");
	}


	// Data Log
    public void saveLogData(String data) {
        put(ld, data);
    }

    public String getLogData() {
        return get(ld, String.class);
    }

    public <T> void put(String key, T data) {
        if (sharedPreferences == null) {
            Log.e(TAG, "context is null");
            return;
        }
        SharedPreferences.Editor editor = sharedPreferences.edit();
        if (data instanceof String) {
            editor.putString(key, (String) data);
        } else if (data instanceof Boolean) {
            editor.putBoolean(key, (Boolean) data);
        } else if (data instanceof Float) {
            editor.putFloat(key, (Float) data);
        } else if (data instanceof Integer) {
            editor.putInt(key, (Integer) data);
        } else if (data instanceof Long) {
            editor.putLong(key, (Long) data);
        }
//        else {
//            editor.putString(key, MyApplication.self().getGSon().toJson(data));
//        }
        editor.apply();
    }

    public <T> T get(String key, Class<T> anonymousClass) {
        if (anonymousClass == String.class) {
            return get(key, anonymousClass, "");
        } else if (anonymousClass == Boolean.class) {
            return get(key, anonymousClass, false);
        } else if (anonymousClass == Float.class) {
            return get(key, anonymousClass,  0);
        } else if (anonymousClass == Integer.class) {
            return get(key, anonymousClass,  0);
        } else if (anonymousClass == Long.class) {
            return get(key, anonymousClass,  0L);
        }
//        else {
//            return (T) MyApplication.self()
//                    .getGSon()
//                    .fromJson(mSharedPreferences.getString(key, ""), anonymousClass);
//        }
        return (T) "";
    }

    @SuppressWarnings("unchecked")
    public <T> T get(String key, Class<T> anonymousClass, Object defaultValue) {
        if (sharedPreferences == null) {
            Log.e(TAG, "context is null");
            return (T) defaultValue;
        }
        if (anonymousClass == String.class) {
            return (T) sharedPreferences.getString(key, String.valueOf(defaultValue));
        } else if (anonymousClass == Boolean.class) {
            return (T) Boolean.valueOf(sharedPreferences.getBoolean(key, (boolean) defaultValue));
        } else if (anonymousClass == Float.class) {
            return (T) Float.valueOf(sharedPreferences.getFloat(key, (Float) defaultValue));
        } else if (anonymousClass == Integer.class) {
            return (T) Integer.valueOf(sharedPreferences.getInt(key, (int) defaultValue));
        } else if (anonymousClass == Long.class) {
            return (T) Long.valueOf(sharedPreferences.getLong(key, (Long) defaultValue));
        } else {
            return (T) "";
        }
    }

    public String getDataEmvConfigStorage() {
        StringBuilder stringBuilder = new StringBuilder();
        File dir = new File(
                Environment.getExternalStorageDirectory().toString() + childEmvConfigPax,
                fileNameEmvConfigPax
        );
        if (dir.exists()) {
            FileInputStream inputStream;
            try {
                inputStream = new FileInputStream(dir);
                InputStreamReader inputStreamReader = new InputStreamReader(inputStream);
                BufferedReader bufferedReader = new BufferedReader(inputStreamReader);

                String line = "";
                while ((line = bufferedReader.readLine()) != null) {
                    stringBuilder.append(line);
                }

                Utils.LOGD(TAG, "result = " + stringBuilder);
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return stringBuilder.toString();
    }

    public void saveEmvConfigStorage(String data) {
        File dir = new File(
                Environment.getExternalStorageDirectory().toString() + childEmvConfigPax
        );
        if(!dir.exists()){
            dir.mkdir();
        }

        try {
            File file = new File(dir, fileNameEmvConfigPax);
            FileWriter writer = new FileWriter(file);
            writer.append(data);
            writer.flush();
            writer.close();
        } catch (Exception e){
            e.printStackTrace();
        }
    }
}