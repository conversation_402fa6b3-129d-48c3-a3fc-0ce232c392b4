package com.mpos.sdk.core.control;

import android.content.Context;
import android.content.res.Configuration;
import android.text.TextUtils;
//import android.util.Log;

import androidx.annotation.NonNull;

import com.custom.mdm.CustomAPI;
import com.google.gson.JsonSyntaxException;
import com.loopj.android.http.AsyncHttpResponseHandler;
import com.mpos.sdk.BuildConfig;
import com.mpos.sdk.R;
import com.mpos.sdk.core.common.MyGson;
import com.mpos.sdk.core.common.MyTextHttpResponseHandler;
import com.mpos.sdk.core.model.BaseObjJson;
import com.mpos.sdk.core.model.ChangePass;
import com.mpos.sdk.core.model.CommonConfig;
import com.mpos.sdk.core.model.DataCache;
import com.mpos.sdk.core.model.DataError;
import com.mpos.sdk.core.model.DataReversalLogin;
import com.mpos.sdk.core.model.LanguageCode;
import com.mpos.sdk.core.model.LibError;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.core.modelma.LoginRes;
import com.mpos.sdk.core.modelma.PreSaleRes;
import com.mpos.sdk.core.modelma.PreSaleSend;
import com.mpos.sdk.core.modelma.WfDetailRes;
import com.mpos.sdk.core.network.ApiMultiAcquirerInterface;
import com.mpos.sdk.core.network.MposRestClient;
import com.mpos.sdk.util.Constants;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.DevicesUtil;
import com.mpos.sdk.util.MacqUtil;
import com.mpos.sdk.util.Utils;
import com.mpos.sdk.view.MposDialog;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.Arrays;
import java.util.List;
import java.util.Locale;

import cz.msebera.android.httpclient.Header;
import cz.msebera.android.httpclient.HttpStatus;
import cz.msebera.android.httpclient.entity.StringEntity;

public class LibLoginHandler {

	String TAG = this.getClass().getName();
	
	private final Context context;
    private Context contextRes;

	private ItfHandlerResultLogin handlerResultLogin;
	private ItfHandlerDataLogin handlerDataLogin;
    private ItfHandlerTransWaitSignature itfHandlerWaitSignatureMA;
    private ItfHandlerBankSelected itfHandlerBankSelected;
    private ItfHandlerMposConfig itfHandlerMposConfig;
    private ItfHandlerLoadOnlyMposConfig itfHandlerLoadOnlyMposConfig;  // used for bvl
//    private ItfProcessUpdateReader itfProcessUpdateReader;

	private int levelLogin = 1; // level login
    private boolean forceReversal = false;
    private boolean handlerTranWaitSignature = false;
    private boolean isCheckWaitSignature = false;

    private boolean isUseReaderFetchMcMpos = true; // true: use device; false: use email

    private boolean isUseSnFetchMcMacq = false; // true: use device + user (use for N31); false: use device + user + pass
    private boolean isUseReaderSaleMacq = false; // true: use device + user (use for N31); false: use device + user + pass
    private boolean isUseUserPassLogin = false; // true: use user + pass; false: other case

    private final SaveLogController sv;

    private String bankName, serialNumber, mAcc, pin, deviceIdentifier;
    private String appType;
    private final int readerType;

    private LanguageCode languageCode = LanguageCode.LANGUAGE_VI;

    private boolean useCacheLoginMA;
    private boolean callLoginBankWithConfigMpos = false;
    private String merchantConfig;
    private PreSaleRes preSaleRes;
    private String versionCode;

    private int numFetchMcConfigFail = 0;
    private long lastTimeFetchMcConfigFail = 0;

    private boolean canUseStorageCacheFetch = false;
    private int rangeMinutesCacheFetch = 60;  // default 1h = 60min
    private int rangeMinutesCacheOnFail = 60;  // default 1h = 60min

    private int typeUseCache = Constants.TYPE_CACHE_SESSION_IN_DAY;

    // todo fake test
//    int counterLoadMcConfig = 0;

    public LibLoginHandler(@NonNull Context context, ItfHandlerResultLogin handlerResultLogin, int readerType) {
        this.context = context;
        this.handlerResultLogin = handlerResultLogin;
        this.sv = SaveLogController.getInstance(context);
        this.readerType = readerType;
        initVariable();
	}

    public LibLoginHandler(@NonNull Context context, ItfHandlerLoadOnlyMposConfig itfHandlerLoadOnlyMposConfig, int readerType) {
        this.context = context;
        this.itfHandlerLoadOnlyMposConfig = itfHandlerLoadOnlyMposConfig;
        this.sv = SaveLogController.getInstance(context);
        this.readerType = readerType;
        initVariable();
	}

    /**
     * use for getCmConfigFromCache and fetchCommonConfig
     */
    public LibLoginHandler(@NonNull Context context, int readerType) {
        this.context = context;
        this.sv = SaveLogController.getInstance(context);
        this.readerType = readerType;
        initVariable();
	}

    private void initVariable() {
        if (!(Constants.isTypeIntegrationMpos() || Constants.isTypeIntegrationMpos3rd())) {
            rangeMinutesCacheFetch = 60;
            typeUseCache = Constants.TYPE_CACHE_SESSION_IN_RANGE_TIME;
        }
    }

    public void setDeviceIdentifier(String deviceIdentifier) {
        this.deviceIdentifier = deviceIdentifier;
    }

    public void setLevelLogin(int levelLogin) {
		this.levelLogin = levelLogin;
	}

    public void setForceReversal(boolean forceReversal) {
        this.forceReversal = forceReversal;
    }

    public void setHandlerTranWaitSignature(boolean handlerTranWaitSignature) {
        this.handlerTranWaitSignature = handlerTranWaitSignature;
    }

    public void setCheckWaitSignature(boolean checkWaitSignature) {
        isCheckWaitSignature = checkWaitSignature;
    }

    public void setCustomLanguage(@NonNull LanguageCode language) {
        setCustomLanguage(language, false);
    }
    public void setCustomLanguage(@NonNull LanguageCode language, boolean isAllScreen) {
        this.languageCode = language;
        if (isAllScreen) {
            PrefLibTV.getInstance(context).saveCustomLanguage(languageCode.getLanguageCode());
        }
        changeToLanguage(new Locale(languageCode.getLanguageCode()));
    }

    public void setHandlerDataLogin(ItfHandlerDataLogin handlerDataLogin) {
        this.handlerDataLogin = handlerDataLogin;
    }

    public void setItfHandlerWaitSignatureMA(ItfHandlerTransWaitSignature itfHandlerWaitSignatureMA) {
        this.itfHandlerWaitSignatureMA = itfHandlerWaitSignatureMA;
    }

    public void setItfHandlerBankSelected(ItfHandlerBankSelected itfHandlerBankSelected) {
        this.itfHandlerBankSelected = itfHandlerBankSelected;
    }

    public void setItfHandlerMposConfig(ItfHandlerMposConfig itfHandlerMposConfig) {
        this.itfHandlerMposConfig = itfHandlerMposConfig;
    }

    public void setItfHandlerLoadOnlyMposConfig(ItfHandlerLoadOnlyMposConfig itfHandlerLoadOnlyMposConfig) {
        if (Constants.isTypeIntegrationMpos() || Constants.isTypeIntegrationMpos3rd()) {
            this.itfHandlerLoadOnlyMposConfig = itfHandlerLoadOnlyMposConfig;
        }
    }

    public void setCanUseStorageCacheFetch(boolean canUseStorageCacheFetch) {
        if (Constants.isTypeIntegrationMpos() || Constants.isTypeIntegrationMpos3rd()) {
            this.canUseStorageCacheFetch = canUseStorageCacheFetch;
        }
    }

    /**
     * set range minutes cache
     * @param rangeMinutesCacheFetch max 18h: 1080p
     */
    public void setRangeMinutesCacheFetch(int rangeMinutesCacheFetch) {
        // 18*60 = 1080 minute
        if ((Constants.isTypeIntegrationMpos() || Constants.isTypeIntegrationMpos3rd())
                && 0 < rangeMinutesCacheFetch && rangeMinutesCacheFetch < 1080) {
            this.rangeMinutesCacheFetch = rangeMinutesCacheFetch;
        }
    }

    /**
     * set range minutes cache -> use in onFail
     * @param rangeMinutesCacheOnFail max 6h: 360p
     */
    public void setRangeMinutesCacheOnFail(int rangeMinutesCacheOnFail) {
        // 6*60 = 360 minute
        if ((Constants.isTypeIntegrationMpos() || Constants.isTypeIntegrationMpos3rd())
                && 0 < rangeMinutesCacheOnFail && rangeMinutesCacheOnFail < 360) {
            this.rangeMinutesCacheOnFail = rangeMinutesCacheOnFail;
        }
    }

    public void setTypeUseCache(int typeUseCache) {
        if (Constants.isTypeIntegrationMpos() || Constants.isTypeIntegrationMpos3rd()) {
            this.typeUseCache = typeUseCache;
        }
    }

    private void changeToLanguage(Locale requestedLocale) {
        Configuration config = new Configuration(context.getResources().getConfiguration());
        config.setLocale(requestedLocale);
        contextRes = context.createConfigurationContext(config);
    }

    public void setAppType(String appType) {
        if (Constants.isTypeIntegrationMpos() || Constants.isTypeIntegrationMpos3rd()) {
            this.appType = appType;
        }
    }

    public void fetchCommonConfig(boolean isRunMacq) {
	    fetchCommonConfig(isRunMacq, false, null);
    }

    public void fetchCommonConfig(boolean isRunMacq, boolean visibleLoading, ItfHandlerCommonConfig callback) {
        fetchCommonConfig(isRunMacq, visibleLoading, mAcc, serialNumber, callback);
    }

    /*
     * mAcc, serialNumber -> use for get only common config (not need login/mc-config)
     */
    public void fetchCommonConfig(boolean isRunMacq, boolean visibleLoading, String mAcc, String serialNumber
            , ItfHandlerCommonConfig callback) {
        if (visibleLoading) {
            showLoading(true);
        }
        StringEntity entity = null;
        try {
            /*
             * if server change merchant to another bank -> need fetch new mc_config
             */
            appendLog(ConstantsPay.GET_COMMON_CONFIG);
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, ConstantsPay.GET_COMMON_CONFIG);
            jo.put("readerSerial", serialNumber);
            jo.put("versionNo", android.os.Build.VERSION.RELEASE);
            jo.put("platform", ConstantsPay.PLATFORM);
            jo.put("muid", mAcc);
            jo.put("isMacqFlow", isRunMacq ? 1 : 0);
            if (preSaleRes != null) {
                jo.put("bankCode", preSaleRes.getAcquirerMagstripe());
            }
            Utils.LOGD(TAG, "fetchCommonConfig send: " + jo);
            entity = new StringEntity(jo.toString());
        } catch (Exception e1) {
            Utils.LOGE(TAG, "fetchCommonConfig: " + e1.getMessage());
        }

        MposRestClient.getInstance(context).post(context, ConstantsPay.URL_MPOS_GATEWAY, entity,
                ConstantsPay.CONTENT_TYPE, new AsyncHttpResponseHandler() {

                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                        if (visibleLoading) {
                            showLoading(false);
                        }
                        String content = new String(arg2);
                        appendLog(ConstantsPay.GET_COMMON_CONFIG + " success: " + content);
                        CommonConfig cmConfig = MyGson.getGson().fromJson(content, CommonConfig.class);
                        if (cmConfig != null && cmConfig.getError() != null && Constants.CODE_REQUEST_SUCCESS.equals(cmConfig.getError().code)) {
                            cmConfig.setMuid(mAcc);
                            cmConfig.setTimeResponse(System.currentTimeMillis());
                            PrefLibTV.getInstance(context).put(PrefLibTV.commonConfig, MyGson.getGson().toJson(cmConfig));
                            PrefLibTV.getInstance(context).put(PrefLibTV.motoBinRanges, MyGson.getGson().toJson(cmConfig.getMotoBinRanges()));
                            callbackResult(true, cmConfig);
                        }
                        else {
                            callbackResult(false, cmConfig);
                        }
                    }

                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        if (visibleLoading) {
                            showLoading(false);
                        }
                        Utils.LOGE(TAG, "GET_COMMON_CONFIG Error: ", arg3);
                        appendLog(ConstantsPay.GET_COMMON_CONFIG + " error: request time out");
                        callbackResult(false, null);
                    }

                    void callbackResult(boolean result, CommonConfig cmConfig) {
                        if (callback != null) {
                            callback.onHandlerCommonConfig(result, cmConfig);
                        }
                    }
                });
    }

    public void fetchMerchantConfigByEmail(String email, String serialNumber, final String pin) {
        fetchMerchantConfigByType(false, email, serialNumber, pin);
    }
    public void fetchMerchantConfig(String mAcc, String serialNumber, final String pin) {
        fetchMerchantConfigByType(true, mAcc, serialNumber, pin);
    }

    private void fetchMerchantConfigByType(boolean useReader, String mAcc, String serialNumber, final String pin) {
        if (!GetData.CheckInternet(context)) {
            callbackFailNoInternet();
            return;
        }
        this.serialNumber = serialNumber;
        this.mAcc = mAcc;
        this.pin = pin;
        this.isUseReaderFetchMcMpos = useReader;

        saveMerchantInfo();
        if (checkContinueWithCacheMpos()) {
            appendLog("use cache mpos");
            if (this.itfHandlerMposConfig != null) {
                this.itfHandlerMposConfig.onHandlerMposConfig(merchantConfig);
            }

            if (itfHandlerLoadOnlyMposConfig != null) {
                callbackMcConfigSuccess(merchantConfig);
            }
            else {
                selectFlowByBankName();
            }
            return;
        }
        clearCacheInRam();

        if (checkWaitCountDownFetchMcConfig()) {
            return;
        }

        showLoading(true);

        StringEntity entity = null;
        try {

            JSONObject jo = new JSONObject();
            jo.put("serviceName", ConstantsPay.GATEWAY_MERCHANT_LOGIN_SDK);

            jo.put("deviceIdentifier", deviceIdentifier);
            jo.put("os", ConstantsPay.PLATFORM_Android);
            jo.put("verCode", BuildConfig.VERSION_CODE);

//            jo.put("language", language);
            if (useReader) {
                jo.put("muid", mAcc);
                jo.put("readerSerial", serialNumber);
            }
            else {
                jo.put("email", mAcc);
                jo.put("password", pin);
            }
            jo.put("bundle", context.getPackageName());
            if ((Constants.isTypeIntegrationMpos() || Constants.isTypeIntegrationMpos3rd())
                    && !TextUtils.isEmpty(appType)) {
                jo.put("appType", appType);
            }

            Utils.LOGD(TAG, "REQUEST--MERCHANT_INFO--: " + jo);

            entity = new StringEntity(jo.toString());
            appendLog(ConstantsPay.GATEWAY_MERCHANT_LOGIN_SDK);
        } catch (Exception e) {
            Utils.LOGE(TAG, "Exception", e);
        }
        Utils.LOGD(TAG, "fetchMerchantConfig: " + ConstantsPay.URL_MPOS_GATEWAY);
        MposRestClient.getInstance(context).post(context, ConstantsPay.URL_MPOS_GATEWAY, entity, ConstantsPay.CONTENT_TYPE, new AsyncHttpResponseHandler() {

            @Override
            public void onFailure(int arg0, Header[] arg1, byte[] error, Throwable arg3) {
                increaseNumConfigFail();
                appendLog(ConstantsPay.GATEWAY_MERCHANT_LOGIN_SDK + " numFail=" + numFetchMcConfigFail
                        + " onFailure: " + arg3.getMessage()+ (error != null ? " ->"+new String(error) : ""));
                showLoading(false);

                if (checkCanUseCacheWhenOnFailMcConfig()) {
                    appendLog("use cache mpos when onFail mc_config");
                    selectFlowByBankName();
                }
                else {
                    if (itfHandlerLoadOnlyMposConfig != null) {
                        callbackMcConfigFail(new DataError(ConstantsPay.ERROR_CODE_DEFAULT, getString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT)));
                    }
                    else {
                        showDialogFailTimeout();
                    }
                }
            }

            @Override
            public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                // original code
                showLoading(false);
                numFetchMcConfigFail = 0;
                handlerMerchantConfig(new String(arg2));

                // todo fake test cache
//                Utils.LOGD(TAG, "onSuccess: counterLoadMcConfig=" + counterLoadMcConfig);
//                if (counterLoadMcConfig % 3 == 0) {
//                    showLoading(false);
//                    numFetchMcConfigFail = 0;
//                    handlerMerchantConfig(new String(arg2));
//                }
//                else {
//                    if (counterLoadMcConfig % 3 == 2) {
//                        // test get cache from storage
//                        DataCache.getInstance().clearCache();
//                    }
//                    if (checkCanUseCacheWhenOnFailMcConfig()) {
//                        appendLog("use cache mpos when onFail mc_config");
//                        selectFlowByBankName();
//                    }
//                    else {
//                        showDialogFailTimeout();
//                    }
//                }
//                counterLoadMcConfig++;
//                increaseNumConfigFail();
                // todo fake test counter block request
//                counterLoadMcConfig++;
//                increaseNumConfigFail();
//                showLoading(false);
//                showDialogFailTimeout();

            }
        });
    }

    // use in device: P12
    public void fetchMerchantAllConfigMacq(String mAcc, String serialNumber, String merchantId) {
        fetchMerchantConfigMacqByType(true, mAcc, serialNumber, "", merchantId);
    }

    // use in app BVLife
    public void fetchMerchantConfigMacq(String mAcc, String serialNumber, final String pin) {
        fetchMerchantConfigMacqByType(false, mAcc, serialNumber, pin, null);
    }

    private void fetchMerchantConfigMacqByType(boolean isUseSnFetchMcMacq, String mAcc, String serialNumber, final String pin, final String merchantId) {
        if (!GetData.CheckInternet(context)) {
            callbackFailNoInternet();
            return;
        }
        this.serialNumber = serialNumber;
        this.mAcc = mAcc;
        this.pin = pin;
        this.isUseSnFetchMcMacq = isUseSnFetchMcMacq;

        saveMerchantInfo();
        if (checkContinueWithCacheMpos()) {
            appendLog("use cache mpos");

            if (this.itfHandlerMposConfig != null) {
                this.itfHandlerMposConfig.onHandlerMposConfig(merchantConfig);
            }

            if (itfHandlerLoadOnlyMposConfig != null) {
                callbackMcConfigSuccess(merchantConfig);
            }
            else {
                selectFlowByBankName();
            }
            return;
        }
        clearCacheInRam();

        if (checkWaitCountDownFetchMcConfig()) {
            return;
        }

        showLoading(true);

        LibLoginMacq libLoginMacq = new LibLoginMacq(context, mAcc, pin, serialNumber, new LibLoginMacq.ItfHandlerGetConfigMacq() {
            @Override
            public void appendLogMacq(String s) {
                appendLog(s);
            }

            @Override
            public void onFailGetConfigMacq(int typeFail, int statusCode, String rawJsonData) {
                increaseNumConfigFail();
                appendLog("GetConfigMacq onFailure--" + rawJsonData+" numFail="+numFetchMcConfigFail);
                showLoading(false);
                DataError dataError = new DataError(true);
                dataError.build(statusCode, rawJsonData, getString(R.string.error_ma_default_code0, String.valueOf(statusCode)));
                appendLog("error:" + dataError.getMsg());
                // server used http code: 500 when check have error
                if (typeFail == LibLoginMacq.FAIL_TYPE_GET_MC_CONFIG_ONFAIL && statusCode != 500) {
                    if (checkCanUseCacheWhenOnFailMcConfig()) {
                        appendLog("use cache macq when onFail mc_config");
                        handlerMerchantConfig(merchantConfig);
                        return;
                    }
                }

                if (itfHandlerLoadOnlyMposConfig != null) {
                    callbackMcConfigFail(dataError);
                }
                else {
                    dataError.setErrorCode(ConstantsPay.ERROR_CODE_TIMEOUT_REQ);
                    callbackFail(dataError, TYPE_FAIL_SHOW_TEXT);
                }
            }

            @Override
            public void onSuccessGetConfigMacq(String contentResponse) {
                showLoading(false);
                numFetchMcConfigFail = 0;
                Utils.LOGD(TAG, "RESPONSE--MERCHANT_INFO-->: " + contentResponse);
                handlerMerchantConfig(contentResponse);
            }
        });
        libLoginMacq.setLanguageCode(languageCode);
        libLoginMacq.setDeviceIdentifier(deviceIdentifier);
        libLoginMacq.setAppType(appType);
        if (isUseSnFetchMcMacq) {
            libLoginMacq.getAllMerchantConfigMacq(merchantId);
        }
        else {
            libLoginMacq.initAuthenMA();
        }
    }

    private void increaseNumConfigFail() {
        numFetchMcConfigFail++;
        lastTimeFetchMcConfigFail = System.currentTimeMillis();
    }

    private boolean checkWaitCountDownFetchMcConfig() {
        if (numFetchMcConfigFail >= 1) {
            int secondWait;
            if (numFetchMcConfigFail > 10) {
                secondWait = 180;
            }
            else {
                secondWait = Utils.getNextFibonacciByPosition(numFetchMcConfigFail) * 5;
            }

            secondWait = (int) ((System.currentTimeMillis() - (lastTimeFetchMcConfigFail + secondWait * 1000))/1000);
            if (secondWait < 0) {
                appendLog("need wait before call API: " + secondWait);
                int timeWaitShow = Math.abs(secondWait);
                String msgShow = getString(R.string.error_sdk_count_down_call_api,
                        String.format(context.getResources().getQuantityString(R.plurals.numberOfSecond, timeWaitShow), timeWaitShow)
                );
//                callbackFail(new DataError(ConstantsPay.ERROR_CODE_COUNT_DOWN_CALL_API, msgShow));

                showAlertCountDownStop(msgShow, String.valueOf(timeWaitShow), new DataError(ConstantsPay.ERROR_CODE_COUNT_DOWN_CALL_API, msgShow));

                return true;
            }
        }
        return false;
    }

    private boolean checkCanUseCacheWhenOnFailMcConfig() {
        appendLog("Checking for cache McConfig when onFail, " +
                "typeUseCache="+typeUseCache +" isUseReaderSaleMacq="+isUseReaderSaleMacq);
        if ((Constants.isTypeIntegrationMpos() || Constants.isTypeIntegrationMpos3rd())
                && (typeUseCache == Constants.TYPE_CACHE_WHEN_ONFAIL_API
                    || typeUseCache == Constants.TYPE_CACHE_SESSION_IN_DAY_AND_ONFAIL_API
                    || typeUseCache == Constants.TYPE_CACHE_RANGE_TIME_AND_ONFAIL_API
                    || typeUseCache == Constants.TYPE_CACHE_SESSION_IN_DAY_AND_ONFAIL_API_FOREVER
                )
        ) {
            if (isUseReaderSaleMacq && !DataCache.getInstance().hasDataWithoutPw()) {
                loadCacheFromStorageToRam();
            }
            else if (!DataCache.getInstance().hasData()) {
                loadCacheFromStorageToRam();
            }

            if (compareUserWithCache()
//                && !checkMustReloadFromServer()
                && ( (typeUseCache == Constants.TYPE_CACHE_SESSION_IN_DAY_AND_ONFAIL_API && DataCache.getInstance().checkSessionInDay())
                    || (typeUseCache == Constants.TYPE_CACHE_RANGE_TIME_AND_ONFAIL_API && DataCache.getInstance().checkInSessionRangeTime(rangeMinutesCacheFetch + rangeMinutesCacheOnFail))
                    || (typeUseCache == Constants.TYPE_CACHE_WHEN_ONFAIL_API && DataCache.getInstance().checkInSessionRangeTime(rangeMinutesCacheOnFail))
                    || typeUseCache == Constants.TYPE_CACHE_SESSION_IN_DAY_AND_ONFAIL_API_FOREVER
                    )
            ) {
                initDataByDataCache();
                return true;
            }
        }
        return false;
    }

    private void loadCacheFromStorageToRam() {
        String cacheInStorage = PrefLibTV.getInstance(context).getMcConfigCache();
//        String cacheInStorage = PrefLibTV.getInstance(context).get(PrefLibTV.DATA_MERCHANT_CONFIG_CACHE_SDK, String.class, "");
        DataCache.getInstance().putDataCacheByJson(cacheInStorage);
        if (isUseReaderSaleMacq) {
            appendLog("get cache in storage-> hasDataWithoutPw="+DataCache.getInstance().hasDataWithoutPw());
        }
        else {
            appendLog("get cache in storage-> hasData="+DataCache.getInstance().hasData());
        }
        appendLog(" lastTimeCache="+Utils.convertTimestamp(DataCache.getInstance().getLastTimeCache()));
    }

    private void handlerMerchantConfig(@NonNull String content) {
        try {
            Utils.LOGD(TAG, "handlerMerchantConfig: " + content);

            JSONObject jRoot = new JSONObject(content);

            boolean outage = Constants.SVALUE_TRUE.equals(JsonParser.getDataJson(jRoot, "outage", Constants.SVALUE_FALSE));
            if (outage) {
                String outageMsg = JsonParser.getDataJson(jRoot, "outageMessage");
                if (TextUtils.isEmpty(outageMsg)) {
                    outageMsg = getString(R.string.error_outage);
                }
                appendLog("outage: "+outageMsg);
                if (itfHandlerLoadOnlyMposConfig != null) {
                    callbackMcConfigFail(new DataError(ConstantsPay.ERROR_CODE_OUTAGE, outageMsg));
                }
                else {
                    callbackFail(new DataError(ConstantsPay.ERROR_CODE_OUTAGE, outageMsg), TYPE_FAIL_NONE);
                }
            } else {
                JsonParser jsonParser = new JsonParser();
                BaseObjJson errorObj = jsonParser.checkHaveError(jRoot);
                if (Constants.CODE_REQUEST_SUCCESS.equals(errorObj.code)) {
                    handlerSuccessMerchantConfig(jRoot);
                }
                else {
                    handlerErrorGetMerchantConfig(errorObj);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            appendLog("getMC-config success but error: " + e.getMessage());
            showDialogFailDefault();
        }
    }

    private void handlerSuccessMerchantConfig(JSONObject jRoot) throws Exception {
        PrefLibTV.getInstance(context).createDataLoginMerchant(jRoot.toString());

        readMerchantConfig(jRoot);

        String isMacqFlow = JsonParser.getDataJson(jRoot, "isMacqFlow", Constants.SVALUE_0);

        if (Constants.SVALUE_1.equals(isMacqFlow)) {
            bankName = ConstantsPay.MPOS_MULTI_ACQUIRER;
        }
        else {
            String encryptData = JsonParser.getDataJson(jRoot, "kData");
            if (!TextUtils.isEmpty(encryptData)) {
                String clearData = EncodeDecode.doAESDecrypt(encryptData, context.getPackageName());
                Utils.LOGD(TAG, "encryptData=" + encryptData + " clearData=" + clearData);

                JSONObject jKeyData = new JSONObject(clearData);
                String mposKey = JsonParser.getDataJson(jKeyData, "keyInit");

                PrefLibTV.getInstance(context).saveMpK(mposKey);
            }
            bankName = JsonParser.getDataJson(jRoot, "bankName");
        }

        if (itfHandlerMposConfig != null) {
            itfHandlerMposConfig.onHandlerMposConfig(jRoot.toString());
        }

        if (itfHandlerBankSelected != null) {
            itfHandlerBankSelected.onBankSelected(bankName);
        }

        saveMerchantInfo();

        processCacheMerchantConfig(jRoot);
        String popupMessage = JsonParser.getDataJson(jRoot, "popupMessage");
        Utils.LOGD(TAG, "popupMessage: " + popupMessage);
        if (TextUtils.isEmpty(popupMessage) || Utils.checkTypeBuildIsCertify()) {
            PrefLibTV.getInstance(context).put(PrefLibTV.LAST_TIME_LOAD_CONFIG_MC_OK, System.currentTimeMillis());
            if (isUseReaderSaleMacq) {
                checkLoadPreSaleOrCallbackSuccess();
            }
            else if (itfHandlerLoadOnlyMposConfig != null) {
                callbackMcConfigSuccess(merchantConfig);
            }
            else {
                if (isUseReaderFetchMcMpos || isUseSnFetchMcMacq) {
                    selectFlowByBankName();
                }
                else {
                    callbackSuccessLogin();
                }
            }
        }
        else {
            String timeShow = JsonParser.getDataJson(jRoot, "popupDisplayTime");
            showAlertCountDownContinue(popupMessage, timeShow);
        }
    }

    private void showAlertCountDownContinue(String msgCountDown, String timeShow) {
        MposDialog mposDialogAlert = new MposDialog(context);
        mposDialogAlert.setType(MposDialog.TYPE_DIALOG_WARNING);
        mposDialogAlert.setDesDialogErrorTop(msgCountDown);
        mposDialogAlert.setEnableTwoButtonBottom(true);
        mposDialogAlert.setVisibleViaPhoneEmail(false);

        mposDialogAlert.setLabelForButtonOk(context.getString(R.string.LOGIN_BTN_CONTINUE));
        mposDialogAlert.setLabelForButtonCancel(context.getString(R.string.BTN_CANCEL));
        mposDialogAlert.setOnClickListenerButtonOk(view -> {
            Utils.LOGD(TAG, "showDialogAlert: click ok");
            mposDialogAlert.dismiss();
            if (itfHandlerLoadOnlyMposConfig != null) {
                callbackMcConfigSuccess(merchantConfig);
            }
            else {
                selectFlowByBankName();
            }
        });
        mposDialogAlert.setOnClickListenerButtonCancel(view -> {
            Utils.LOGD(TAG, "showDialogAlert: click cancel");
            mposDialogAlert.dismiss();
            if (itfHandlerLoadOnlyMposConfig != null) {
                callbackMcConfigSuccess(merchantConfig);
            }
            else {
                callbackFail(new DataError(ConstantsPay.ERROR_CODE_WARNING, msgCountDown), TYPE_FAIL_FINISH);
            }
        });
        if (!TextUtils.isEmpty(timeShow) && TextUtils.isDigitsOnly(timeShow)) {
            try {
                int timeCountdown = Integer.parseInt(timeShow);
                mposDialogAlert.setTimeoutDisableButton(timeCountdown);
            } catch (NumberFormatException e) {
                e.printStackTrace();
            }
        }

        mposDialogAlert.show();
        mposDialogAlert.startCountDownTime();
    }

    private void showAlertCountDownStop(String msgCountDown, String timeShow, DataError dataError) {
        MposDialog mposDialogAlert = new MposDialog(context);
        mposDialogAlert.setType(MposDialog.TYPE_DIALOG_WARNING);
        mposDialogAlert.setDesDialogErrorTop(msgCountDown);
        mposDialogAlert.setEnableTwoButtonBottom(false);
        mposDialogAlert.setVisibleViaPhoneEmail(false);

        mposDialogAlert.setOnClickListenerDialogClose(view -> {
            Utils.LOGD(TAG, "showDialogAlert: click cancel");
            mposDialogAlert.dismiss();
            if (itfHandlerLoadOnlyMposConfig != null) {
                callbackMcConfigSuccess(merchantConfig);
            }
            else {
                callbackFail(dataError, TYPE_FAIL_FINISH);
            }
        });
        if (!TextUtils.isEmpty(timeShow) && TextUtils.isDigitsOnly(timeShow)) {
            try {
                int timeCountdown = Integer.parseInt(timeShow);
                mposDialogAlert.setTimeoutDisableButton(timeCountdown);
            } catch (NumberFormatException e) {
                e.printStackTrace();
            }
        }

        mposDialogAlert.show();
        mposDialogAlert.startCountDownTime();
    }

    public void selectFlowByBankName(String mAcc, final String pin, String bankName, String serialNumber) {
        if (Constants.isTypeIntegrationMpos() || Constants.isTypeIntegrationMpos3rd()) {
            this.serialNumber = serialNumber;
            this.mAcc = mAcc;
            this.pin = pin;
            this.bankName = bankName;
            if (TextUtils.isEmpty(merchantConfig) && compareUserWithCache() && !TextUtils.isEmpty(DataCache.getInstance().getDataJson())) {
                merchantConfig = DataCache.getInstance().getDataJson();
            }
            selectFlowByBankName();
        }
    }

    private void selectFlowByBankName() {
        appendLog("->" + bankName);
        if (ConstantsPay.MPOS_MULTI_ACQUIRER.equals(bankName)) {
            processMultiAcquirer();
        } else {
            processLoginBank(bankName, serialNumber, mAcc, pin);
        }
    }

    public void readMerchantConfig(JSONObject jRoot) {
        if (jRoot == null || !(Constants.isTypeIntegrationMpos() || Constants.isTypeIntegrationMpos3rd())) {
            return;
        }

        // merchant can action
        String restrictInternationalCard = JsonParser.getDataJson(jRoot, "restrictInternationalCard");
        String restrictEmvInternationalCard = JsonParser.getDataJson(jRoot, "emvRestrictInternationalCard");
        String emailMerchant = JsonParser.getDataJson(jRoot, "emailMerchant");

        String isSaleService = JsonParser.getDataJson(jRoot, "isSaleService");
        String restrictionNFC = JsonParser.getDataJson(jRoot, "restrictionNFC");
        String allowTapToPhone = JsonParser.getDataJson(jRoot, "allowTapToPhone");
        String skipSignature = JsonParser.getDataJson(jRoot, "skipSignature", "");
        PrefLibTV.getInstance(context).setMerchantId(JsonParser.getDataJson(jRoot, "merchantId"));

        PrefLibTV.getInstance(context).setEmailMerchant(emailMerchant);
        PrefLibTV.getInstance(context).setRestrictInternationalCard(Constants.SVALUE_1.equals(restrictInternationalCard));
        PrefLibTV.getInstance(context).setEmvRestrictInternationalCard(Constants.SVALUE_1.equals(restrictEmvInternationalCard));

        PrefLibTV.getInstance(context).put(PrefLibTV.canSaleService, Constants.SVALUE_1.equals(isSaleService));
        PrefLibTV.getInstance(context).put(PrefLibTV.restrictionNFC, Constants.SVALUE_1.equals(restrictionNFC));
        PrefLibTV.getInstance(context).put(PrefLibTV.allowTapToPhone, Constants.SVALUE_1.equals(allowTapToPhone));
        PrefLibTV.getInstance(context).setSkipSignature(Constants.SVALUE_1.equals(skipSignature));

        PrefLibTV.getInstance(context).put(PrefLibTV.BUSINESS_NAME, JsonParser.getDataJson(jRoot, "businessName"));
        PrefLibTV.getInstance(context).put(PrefLibTV.BUSINESS_ADDRESS, JsonParser.getDataJson(jRoot, "businessAddress"));

        PrefLibTV.getInstance(context).setPermitVoid(Constants.SVALUE_1.equals(JsonParser.getDataJson(jRoot, "permitVoid", Constants.SVALUE_0)));
        PrefLibTV.getInstance(context).setPermitSettlement(Constants.SVALUE_1.equals(JsonParser.getDataJson(jRoot, "permitSettlement", Constants.SVALUE_0)));

        PrefLibTV.getInstance(context).put(PrefLibTV.mandatoryCheckLimit, JsonParser.getDataJson(jRoot, "mandatoryCheckLimit", Constants.SVALUE_0));
        PrefLibTV.getInstance(context).put(PrefLibTV.MPOS_MID_BANK, JsonParser.getDataJson(jRoot, "mid", ""));

        // more
        if (jRoot.has("mores")) {
            try {
                JSONObject jMores = jRoot.getJSONObject("mores");

                String onOffPromotion = JsonParser.getDataJson(jMores, "onOffPromotion", "0");
                PrefLibTV.getInstance(context).put(PrefLibTV.onOffPromotion, Constants.SVALUE_1.equals(onOffPromotion));

                String permitPrintVoidTrans = JsonParser.getDataJson(jMores, "isPrintedAfterCancel", "0");
                PrefLibTV.getInstance(context).put(PrefLibTV.permitPrintVoidTrans, Constants.SVALUE_1.equals(permitPrintVoidTrans));

                String nfcCvmLimit = JsonParser.getDataJson(jMores, "maxAmountNFC");
                Utils.LOGD(TAG, "readMerchantConfig: nfcCvmLimit=" + nfcCvmLimit);
                if (TextUtils.isEmpty(nfcCvmLimit)) {
                    nfcCvmLimit = "0";
                }
                long amountNfcCvmLimit = 0;
                try {
                    amountNfcCvmLimit = Long.parseLong(nfcCvmLimit);
                } catch (NumberFormatException e) {
                    Utils.LOGE(TAG, "readMerchantConfig - NfcCvmLimit:"+e.getMessage());
                }
                PrefLibTV.getInstance(context).put(PrefLibTV.amountNfcCvmLimit, amountNfcCvmLimit);

                boolean allowSaleInternational = Constants.SVALUE_1.equalsIgnoreCase(JsonParser.getDataJson(jMores, "isAllowInternationalIPPayment", "0"));
                PrefLibTV.getInstance(context).put(PrefLibTV.allowSaleInternational, allowSaleInternational);

                if (DevicesUtil.isSP02() && jMores.has("appIdBootStart")) {
                    String appIdBootStart = jMores.getString("appIdBootStart");
                    if (!TextUtils.isEmpty(appIdBootStart)) {
                        CustomAPI.setBootStartPkgName("");// remove package name before that
                        if (appIdBootStart.contains(".")) {
                            CustomAPI.setBootStartPkgName(appIdBootStart);
                        }
                    }
                }

            } catch (JSONException e) {
                e.printStackTrace();
            }
        }

        //deposit
        PrefLibTV.getInstance(context).put(PrefLibTV.allowDeposit, JsonParser.getDataJson(jRoot, "allowDeposit", Constants.SVALUE_0));
        try {
            int intValue = jRoot.getInt("maxDaySettleDeposit");
            PrefLibTV.getInstance(context).put(PrefLibTV.maxDaySettleDeposit, intValue);
        } catch (Exception e) {
            e.printStackTrace();
        }

        boolean isDisableCheckGps = false;
        if (jRoot.has("isDisableCheckGps")) {
            isDisableCheckGps = Constants.SVALUE_1.equals(JsonParser.getDataJson(jRoot, "isDisableCheckGps"));
        }
        Utils.LOGD(TAG, "readMerchantConfig: isDisableCheckGps=" + isDisableCheckGps + " skipSignature=" + skipSignature
                + " mandatoryCheckLimit=" + PrefLibTV.getInstance(context).get(PrefLibTV.mandatoryCheckLimit, String.class));

        PrefLibTV.getInstance(context).setDisableCheckGps(isDisableCheckGps);

        String signatureMinAmount = JsonParser.getDataJson(jRoot, "signatureMinAmount", ConstantsPay.VALUE_DEF_NEGATIVE_1);
        PrefLibTV.getInstance(context).setMaxAmountSignature(signatureMinAmount);

        // app check mask pan in list -> block tran
        String blockBinRange = JsonParser.getDataJson(jRoot, "blockPinRange", ConstantsPay.VALUE_DEF_EMPTY);
        Utils.LOGD(TAG, "readMerchantConfig: BLOCK_PAN_RANGE=" + blockBinRange);
        PrefLibTV.getInstance(context).put(PrefLibTV.BLOCK_PAN_RANGE, blockBinRange);

        readMoreConfigIntegration(jRoot);

        // update emv config
        checkUpgradeEmvConfig(jRoot);

        // update firmware pr02
        checkUpgradeFwPr02(jRoot);

        // version bin local
        int version = 0;
        String sVersion = JsonParser.getDataJson(jRoot, "versionBinLocal", Constants.SVALUE_0);
        if (TextUtils.isDigitsOnly(sVersion)) {
            version = Integer.parseInt(sVersion);
        }
        PrefLibTV.getInstance(context).put(PrefLibTV.serverVerBinLocal, version);
        checkVersionBinLocal();
    }

    private void readMoreConfigIntegration(JSONObject jRoot) {
        if (jRoot.has("config")) {
            JSONObject jConfigIntegration;
            try {
                jConfigIntegration = jRoot.getJSONObject("config");
                Utils.LOGD(TAG, "readConfigFromMpos: jConfigIntegration=" + jConfigIntegration);
                String connectType = JsonParser.getDataJson(jConfigIntegration, "connectType");
                PrefLibTV.getInstance(context).put(PrefLibTV.connectType, connectType);

                if (connectType.equals("4")) {
                    PrefLibTV.getInstance(context).savePermitSocket(true);

                    String hasPermitCachePreSale = JsonParser.getDataJson(jConfigIntegration, "appPresaleCache", Constants.SVALUE_0);
                    PrefLibTV.getInstance(context).put(PrefLibTV.hasPermitCachePreSale, Constants.SVALUE_1.equals(hasPermitCachePreSale));
                } else if (connectType.equals("3")) {
                    PrefLibTV.getInstance(context).setEnableReceiverCancelOrder(true);
                }

                PrefLibTV.getInstance(context).put(PrefLibTV.permitVoidSocket, JsonParser.getDataJson(jConfigIntegration, "permitVoidSocket"));
//                String permitVoidSocket = JsonParser.getDataJson(jConfigIntegration, "permitVoidSocket");
//                DataStoreApp.getInstance().savePermitVoidSocket(permitVoidSocket);

                String permitPayGiftCard = JsonParser.getDataJson(jConfigIntegration, "permitPayGiftCard");
                PrefLibTV.getInstance(context).setPermitPayGiftCard(permitPayGiftCard);

                //config QR NL
                PrefLibTV.getInstance(context).put(PrefLibTV.permitQrNl, JsonParser.getDataJson(jConfigIntegration, "permitQrNl"));
//                permitQrNl = JsonParser.getDataJson(jConfigIntegration, "permitQrNl");
//                DataStoreApp.getInstance().savePermitQrNl(permitQrNl);

                PrefLibTV.getInstance(context).put(PrefLibTV.permitQrMomoNl, JsonParser.getDataJson(jConfigIntegration, "permitQrMomoNl"));
//                permitQrMomoNl = JsonParser.getDataJson(jConfigIntegration, "permitQrMomoNl");        // QR MOMO NL
//                DataStoreApp.getInstance().saveDataByKey(DataStoreApp.permitQrMomoNl, permitQrMomoNl);

                PrefLibTV.getInstance(context).put(PrefLibTV.permitQrZaloNl, JsonParser.getDataJson(jConfigIntegration, "permitQrZaloNl"));
//                permitPayZaloQr = JsonParser.getDataJson(jConfigIntegration, "permitQrZaloNl");
//                DataStoreApp.getInstance().saveDataByKey(DataStoreApp.permitQrZaloNl, permitPayZaloQr);

                PrefLibTV.getInstance(context).put(PrefLibTV.qrNlSiteCode, JsonParser.getDataJson(jConfigIntegration, "qrNlSiteCode"));
//                String qrNlSiteCode = JsonParser.getDataJson(jConfigIntegration, "qrNlSiteCode");
//                DataStoreApp.getInstance().setQrNlSiteCode(qrNlSiteCode);

                PrefLibTV.getInstance(context).put(PrefLibTV.qrNlPassCode, JsonParser.getDataJson(jConfigIntegration, "qrNlPassCode"));
//                String qrNlPassCode = JsonParser.getDataJson(jConfigIntegration, "qrNlPassCode");
//                DataStoreApp.getInstance().setQrNlPassCode(qrNlPassCode);

                String binLocals = JsonParser.getDataJson(jConfigIntegration, "binLocals");
                PrefLibTV.getInstance(context).setBinLocals( binLocals);
            } catch (JSONException e) {
                throw new RuntimeException(e);
            }
        }
    }

    private void checkUpgradeEmvConfig(JSONObject jRoot) {
	    boolean upgrade = false;
        if (jRoot != null && jRoot.has("upgradeEMVConfig")) {
            try {
                upgrade = jRoot.getBoolean("upgradeEMVConfig");
                String urlEmvApp = "";
                if (jRoot.has("urlEmvApp")) {
                    urlEmvApp = jRoot.getString("urlEmvApp");
                }
                PrefLibTV.getInstance(context).put(PrefLibTV.urlEmvApp, urlEmvApp);
                String urlEmvCapk = "";
                if (jRoot.has("urlEmvCapk")) {
                    urlEmvCapk = jRoot.getString("urlEmvCapk");
                }
                PrefLibTV.getInstance(context).put(PrefLibTV.urlEmvCapk, urlEmvCapk);

            } catch (JSONException e) {
                e.printStackTrace();
                upgrade = false;
            }
        }
        PrefLibTV.getInstance(context).put(PrefLibTV.upgradeEMVConfig, upgrade);

        // todo fake test
//        DataStoreApp.getInstance().saveUpgradeEmvConfig(true);
//        DataStoreApp.getInstance().saveUrlEmvApp("http://api.mpos.vn/assets/emv_app.bin");
//        DataStoreApp.getInstance().saveUrlEmvCapk("http://api.mpos.vn/assets/emv_capk.bin");
//        PrefLibTV.getInstance(context).put(PrefLibTV.urlEmvApp, "https://mpos.vn/mpos-api/assets/json_emv_profile_tlv.txt");
//        PrefLibTV.getInstance(context).put(PrefLibTV.urlEmvCapk, urlEmvCapk);
//        PrefLibTV.getInstance(context).put(PrefLibTV.upgradeEMVConfig, true);
    }

    private void checkUpgradeFwPr02(JSONObject jRoot) {
	    boolean upgrade = false;
        if (jRoot != null && jRoot.has("isFirmwareUpdate")) {
            try {
                upgrade = jRoot.getBoolean("isFirmwareUpdate");
                String urlFw = "";
                if (upgrade && jRoot.has("linkFirmwareUpdate")) {
                    urlFw = jRoot.getString("linkFirmwareUpdate");
                }
                PrefLibTV.getInstance(context).put(PrefLibTV.urlUpgradeFw, urlFw);
                String versionFw = "";
                if (upgrade && jRoot.has("versionFirmwareUpdate")) {
                    versionFw = jRoot.getString("versionFirmwareUpdate");
                }
                PrefLibTV.getInstance(context).put(PrefLibTV.versionFwUpgrade, versionFw);
            } catch (JSONException e) {
                e.printStackTrace();
                upgrade = false;
            }
        }
        PrefLibTV.getInstance(context).put(PrefLibTV.upgradeFw, upgrade);
    }

    private boolean checkContinueWithCacheMpos() {
        appendLog("check compare data with cache: hasCache=" + DataCache.getInstance().hasData() + " useCacheStorage=" + canUseStorageCacheFetch);
        if (!DataCache.getInstance().hasData() && canUseStorageCacheFetch) {
            loadCacheFromStorageToRam();
        }
        if (compareUserWithCache() && !checkMustReloadFromServer()) {
            appendLog("typeUseCache: " + typeUseCache);
            if ((typeUseCache == Constants.TYPE_CACHE_SESSION_IN_DAY
                    && DataCache.getInstance().checkSessionInDay())
                || (typeUseCache == Constants.TYPE_CACHE_SESSION_IN_RANGE_TIME
                    && DataCache.getInstance().checkInSessionRangeTime(rangeMinutesCacheFetch))
                || (typeUseCache == Constants.TYPE_CACHE_RANGE_TIME_AND_ONFAIL_API
                    && DataCache.getInstance().checkInSessionRangeTime(rangeMinutesCacheFetch))
                || (typeUseCache == Constants.TYPE_CACHE_SESSION_IN_DAY_AND_ONFAIL_API_FOREVER
                    && DataCache.getInstance().checkSessionInDay())
            ){
                initDataByDataCache();
                return true;
            }
        }
        return false;
    }

    private void initDataByDataCache() {
        bankName = DataCache.getInstance().getBankName();
        merchantConfig = DataCache.getInstance().getDataJson();
        appendLog("cache -> approval: " + bankName);
    }

    private boolean checkMustReloadFromServer() {
        CommonConfig cmConfig = getCmConfigFromCache();
        boolean mustReload = cmConfig != null && cmConfig.getReloadConfig() && mAcc.equals(cmConfig.getMuid());
        appendLog("mustReloadFromServer: " + mustReload);
        return mustReload;
    }

    public CommonConfig getCmConfigFromCache() {
        CommonConfig cmConfig = null;
        String commonConfig = PrefLibTV.getInstance(context).get(PrefLibTV.commonConfig, String.class, "");
        if (!TextUtils.isEmpty(commonConfig)) {
            try {
                appendLog("cmConfig: " + commonConfig);
                cmConfig = MyGson.getGson().fromJson(commonConfig, CommonConfig.class);
            } catch (JsonSyntaxException e) {
                e.printStackTrace();
            }
        }
        return cmConfig;
    }

    private String encP(String p) {
        if (!TextUtils.isEmpty(p)) {
            try {
                return EncodeDecode.doAESEncrypt(p, context.getPackageName());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return "";
    }

    private void processCacheMerchantConfig(JSONObject jRoot) {
        merchantConfig = jRoot.toString();
    }

    /**
     * cache data to RAM and storage
     */
    private void processCacheData() {
        if (TextUtils.isEmpty(pin)) {
            return;
        }
        String passSave = encP(pin);
        Utils.LOGD(TAG, "processCacheData: passSave=" + passSave);
        if (!TextUtils.isEmpty(passSave) && !TextUtils.isEmpty(merchantConfig)) {
//            removeDataNotUse(merchantConfig);
            DataCache.getInstance().putDataCache(bankName, serialNumber, mAcc, passSave, merchantConfig);
//            Utils.LOGD(TAG, "processCacheMpos: ->" + DataCache.getInstance().getDataJson());
            PrefLibTV.getInstance(context).setMcConfigCache(MyGson.getGson().toJson(DataCache.getInstance()));
//            PrefLibTV.getInstance(context).put(PrefLibTV.DATA_MERCHANT_CONFIG_CACHE_SDK, MyGson.getGson().toJson(DataCache.getInstance()));
            appendLog("save cache");
        }
    }

    private void processCacheDataWithoutPw() {
        if (!TextUtils.isEmpty(serialNumber) && !TextUtils.isEmpty(mAcc) && !TextUtils.isEmpty(merchantConfig)) {
            DataCache.getInstance().putDataCache(bankName, serialNumber, mAcc, "", merchantConfig);
            Utils.LOGD(TAG, "processCacheMpos: ->" + DataCache.getInstance().getDataJson());
//            PrefLibTV.getInstance(context).put(PrefLibTV.DATA_MERCHANT_CONFIG_CACHE_SDK, MyGson.getGson().toJson(DataCache.getInstance()));
            PrefLibTV.getInstance(context).setMcConfigCache(MyGson.getGson().toJson(DataCache.getInstance()));
            appendLog("save cache");
        }
    }

    private void clearCacheInRam() {
        if (typeUseCache == Constants.TYPE_CACHE_SESSION_IN_DAY || typeUseCache == Constants.TYPE_CACHE_SESSION_IN_RANGE_TIME) {
            DataCache.getInstance().clearCache();
        }
    }

    public void setVersionCode(String versionCode) {
        this.versionCode = versionCode;
    }

    /**----------*----------*---- check version bin local -----*----------*----------*
     * service payment -> app check mask-pan with list bin -> restrict internation card for service trans
     * ----------*----------*----------*----------*----------*/
    private void checkVersionBinLocal() {
        int currVerBinLocal = PrefLibTV.getInstance(context).get(PrefLibTV.currVerBinLocal, Integer.class, 0);
        int serverVerBinLocal = PrefLibTV.getInstance(context).get(PrefLibTV.serverVerBinLocal, Integer.class, 0);
        if (currVerBinLocal < serverVerBinLocal) {
            startLoadListBinLocal();
        }
    }

    private void startLoadListBinLocal() {
        loadListBinLocal(1);
    }
    private void loadListBinLocal(int counter) {

        appendLog(ConstantsPay.GET_BIN_LOCAL + "->"+counter);
        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, ConstantsPay.GET_BIN_LOCAL);
            Utils.LOGD(TAG, "Data: " + jo);
            entity = new StringEntity(jo.toString());
        } catch (Exception e1) {
            Utils.LOGE(TAG, "loadListBinLocal: "+ e1.getMessage());
        }

        MposRestClient.getInstance(context).post(context, ConstantsPay.URL_MPOS_GATEWAY, entity,
                ConstantsPay.CONTENT_TYPE, new AsyncHttpResponseHandler() {
                    @Override
                    public void onStart() {
                        super.onStart();
                    }

                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                        String content = new String(arg2);
                        Utils.LOGD(TAG, "loadListBinLocal onSuccess: "+content);
                        PrefLibTV.getInstance(context).put(PrefLibTV.listBinLocal, content);
                        PrefLibTV.getInstance(context).put(PrefLibTV.currVerBinLocal,
                                PrefLibTV.getInstance(context).get(PrefLibTV.serverVerBinLocal, Integer.class, 0));
                    }

                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        Utils.LOGE(TAG, "loadListBinLocal Error: ", arg3);
                        appendLog(ConstantsPay.GET_BIN_LOCAL+" error: request time out"+(counter));
                        if (counter < 3) {
                            loadListBinLocal(counter + 1);
                        }
                    }
                });
    }
    // <<________________<END>_____________  check version bin local _____________________________

    /**
     * save merchant info -> show in dialog error
     */
    private void saveMerchantInfo() {
        try {
            String detailBankName = "";
            if (!TextUtils.isEmpty(bankName)) {
                detailBankName = ConstantsPay.getShortBankName(bankName);
                if (bankName.equalsIgnoreCase(ConstantsPay.MPOS_MULTI_ACQUIRER)) {
                    detailBankName += "-" + ConstantsPay.getShortBankName(
                            PrefLibTV.getInstance(context).get(PrefLibTV.BANK_ACQUIRE, String.class, ""));
                }
            }

            versionCode = PrefLibTV.getInstance(context).get(PrefLibTV.APP_VERSION_CODE, String.class, "");

            String merchantInfo =
                    (TextUtils.isEmpty(versionCode) ? "" : versionCode+"-")
                            + mAcc + "|" + serialNumber
                            + (TextUtils.isEmpty(detailBankName) ? "" : "|" + detailBankName);

            PrefLibTV.getInstance(context).put(PrefLibTV.MERCHANT_INFO, merchantInfo);
            Utils.LOGD(TAG, "saveMerchantInfo: " + merchantInfo);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void handlerErrorGetMerchantConfig(BaseObjJson errorObj) {
	    String msgError = TextUtils.isEmpty(errorObj.getMessageByLanguage(languageCode)) ?
                getString(R.string.error_get_info_merchant, errorObj.code)
                : errorObj.getMessageByLanguage(languageCode);
        int code = TextUtils.isDigitsOnly(errorObj.code) ? Integer.parseInt(errorObj.code) : ConstantsPay.ERROR_CODE_SDK_DEFAULT;
        Utils.LOGD(TAG, "ERROR CODE: "+errorObj.code);
        appendLog("error: "+code+" msg: "+msgError);
        if (itfHandlerLoadOnlyMposConfig != null) {
            callbackMcConfigFail(new DataError(code, msgError));
//            itfHandlerLoadOnlyMposConfig.onHandlerLoadOnlyMposConfig(false, msgError);
        }
        else {
            DataError dataError = new DataError(code, msgError);
            callbackFail(dataError);
        }
    }

    /**
     * use for app mpos lite (without P12)
     * app mpos lite will fetch config and call login by sdk
     */
    public void loginBankWithConfigMpos(@NonNull String config, String serialNumber, String mAcc, String pin) {
        if (Constants.isTypeIntegrationMpos() || Constants.isTypeIntegrationMpos3rd()) {
            this.callLoginBankWithConfigMpos = true;
            this.serialNumber = serialNumber;
            this.mAcc = mAcc;
            this.pin = pin;
            handlerMerchantConfig(config);
        }
    }

    private void processLoginBank(String bankName, String serialNumber, String mAcc, String pin) {
        setupConfigServer(bankName);
        if (!isCheckWaitSignature && checkCanUseCacheLoginBank()) {
            processAfterLoginLv1();
        }
        else {
            initBank(serialNumber, mAcc, pin, readerType);
        }
    }

    private boolean checkCanUseCacheLoginBank() {
        if (!compareUserWithCache()) {
            return false;
        }
        int lastStatusTrans = PrefLibTV.getInstance(context).getLastErrorSale();
        appendLog("checkCanUseCacheLoginBank: lastStatus=" + lastStatusTrans);
        if (lastStatusTrans == LibReaderController.TYPE_ERROR_NONE || lastStatusTrans == LibReaderController.TYPE_ERROR_CHIP_AID_FAILURE) {
            long lastTimeErrorSale = PrefLibTV.getInstance(context).get(PrefLibTV.lastErrorSaleTime, Long.class);
            long aboutTime = System.currentTimeMillis() - lastTimeErrorSale;
            appendLog("-> aboutTime=" + aboutTime + " lastTime=" + lastTimeErrorSale);
            // 4 hour = 4 * 3600 * 1000
            return aboutTime < ********;
        }
        return false;
    }

    private boolean compareUserWithCache() {
//        Utils.LOGD(TAG, "compareUserWithCache: isUseReaderSaleMacq=" + isUseReaderSaleMacq + " sn=" + serialNumber + " mAcc=" + mAcc + " pin=" + pin);
//        Utils.LOGD(TAG, "dataCache=" + DataCache.getInstance().toString());

        if (isUseReaderSaleMacq) {
            return DataCache.getInstance().compareDataWithoutPw(serialNumber, mAcc);
        }
        else {
            if (TextUtils.isEmpty(pin)) {
                return false;
            }
            return DataCache.getInstance().compareData(serialNumber, mAcc, encP(pin));
        }
    }

    private void setupConfigServer(String bankName) {
	    appendLog("bank: "+bankName);
        PrefLibTV.getInstance(context).saveBankName(bankName);
        ConstantsPay.initApiByBankName(context, bankName);
    }

    /**
     * use for pvi, bvl
     */
    public void processBankFor(final String serialNumber, final String mAcc, final String mPin, final int mFlagDevices) {
        if (Constants.isTypeIntegrationMpos() || Constants.isTypeIntegrationMpos3rd()) {
            initBank(serialNumber, mAcc, mPin, mFlagDevices);
        }
    }

    private void initBank(final String serialNumber, final String mAcc, final String mPin, final int mFlagDevices){
		if (!GetData.CheckInternet(context)) {
//			Utils.mToast(context, getString(R.string.check_internet));
            callbackFail( getString(R.string.check_internet));
			return;
		}
		if(TextUtils.isEmpty(mAcc) || TextUtils.isEmpty(mPin)){
			callbackFail( getString(R.string.error_info_login_bank));
			return;
		}

        showLoading(true);

		String mpK = PrefLibTV.getInstance(context).getMpK();
		StringEntity entity = null;
		try {
			JSONObject jo = new JSONObject();
			jo.put("readerSerialNo", serialNumber);
			jo.put("udid", "0");
			jo.put("versionNo", android.os.Build.VERSION.RELEASE);
			jo.put("platform", ConstantsPay.PLATFORM);
			Utils.LOGD(TAG, "Data 1: "+ jo);
			Utils.LOGD(TAG, "Data 2: "+EncodeDecode.doAESEncrypt(jo.toString(), mpK));
			entity = new StringEntity(EncodeDecode.doAESEncrypt(jo.toString(), mpK));
		} catch (Exception e) {
			e.printStackTrace();
		}

        if (TextUtils.isEmpty(ConstantsPay.getINIT())) {
            ConstantsPay.getUrlServer(context);
        }
        appendLog("init");
        Utils.LOGD(TAG, "init: "+ConstantsPay.getINIT());

        MposRestClient.getInstance(context).post(context, ConstantsPay.getINIT(), entity, ConstantsPay.CONTENT_TYPE, new AsyncHttpResponseHandler() {

            @Override
            public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                appendLog("init onFailure");
                showDialogFailTimeout();
            }

            @Override
            public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                try {
                    JSONObject response = new JSONObject(EncodeDecode.doAESDecrypt(new String(arg2), mpK));
                    PrefLibTV.getInstance(context).setSessionKey(response.getString("sessionKey"));
                    logInBank(serialNumber, mAcc, mPin, mFlagDevices);
                } catch (Exception e) {
                    e.printStackTrace();
                    appendLog("init success but error: " + e.getMessage());
                    showDialogFailDefault();
                }
            }
        });
	}
	
	private void logInBank(String serialNumber, final String mAcc, final String mPin, final int mFlagDevices) {
		StringEntity entity = null;
		try {
			JSONObject jo = new JSONObject();
			jo.put("serviceName", ConstantsPay.LOGIN);
			jo.put("readerSerialNo", serialNumber);
			jo.put("udid", "0");
			jo.put("versionNo", android.os.Build.VERSION.RELEASE);
			jo.put("platform", ConstantsPay.PLATFORM);
			jo.put("userID", mAcc);
			jo.put("userPIN", mPin);
			jo.put("appId", "mpos.vn");
            jo.put("forceReversal", forceReversal?1:0); //false

            Utils.LOGD(TAG, "Data send: "+ jo);
			entity = new StringEntity(EncodeDecode.doAESEncrypt(jo.toString(), PrefLibTV.getInstance(context).getSessionKey()));
		} catch (Exception e) {
			e.printStackTrace();
            appendLog("error build LOGIN: "+e.getMessage());
		}

        appendLog("LOGIN");
		    Utils.LOGD(TAG, "logIn: url:"+ConstantsPay.getUrlServer(context));
        MposRestClient.getInstance(context).post(context, ConstantsPay.getUrlServer(context), entity, ConstantsPay.CONTENT_TYPE, new AsyncHttpResponseHandler() {
            @Override
            public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                try {
                    JSONObject jRoot = new JSONObject(EncodeDecode.doAESDecrypt(new String(arg2), PrefLibTV.getInstance(context).getSessionKey()));
                        PrefLibTV.getInstance(context).setSessionKey(jRoot.getString("sessionKey"));

                        Utils.LOGD("Login: ", jRoot.toString());
                        if (jRoot.has("error")) {
                            try {
                                clearCacheInRam();
                                final JSONObject jo = jRoot.getJSONObject("error");
                                int code = jo.getInt("code");
                                appendLog("error: " + code);
                                showDialogFailBank(code, TYPE_FAIL_SHOW_TEXT);
                            } catch (JSONException e) {
                                e.printStackTrace();
                            }
                        } else {
                            PrefLibTV.getInstance(context).setUserId(mAcc);
                            PrefLibTV.getInstance(context).setSerialNumber(serialNumber);
                            PrefLibTV.getInstance(context).setFlagDevices( mFlagDevices);
                            PrefLibTV.getInstance(context).setTKL2("");

                            if (jRoot.has("applicationList")) {
                                JSONArray jAppList = jRoot.getJSONArray("applicationList");
                                if (jAppList.length() > 0) {
                                    JSONObject jApp = jAppList.getJSONObject(0);
                                    PrefLibTV.getInstance(context).setMId(JsonParser.getDataJson(jApp, "MID",""));
                                    PrefLibTV.getInstance(context).setTId(JsonParser.getDataJson(jApp, "TID",""));
                                }
                            }

                            try {
                                if (jRoot.has("readerEncryptMode"))
                                    PrefLibTV.getInstance(context).setReaderEncryptMode(jRoot.getString("readerEncryptMode"));
                                if (jRoot.has("parameterList"))
                                    PrefLibTV.getInstance(context).setShopName(((JSONObject) jRoot.getJSONArray("parameterList").get(0)).getString("value"));
                                if (mFlagDevices == ConstantsPay.DEVICE_PINPAD) {
                                    PrefLibTV.getInstance(context).setTagConfig((jRoot.getJSONObject("readerConfiguration").getJSONArray("initTagsValuesList").get(0)).toString());
                                    if(jRoot.has("readerConfiguration")){
                                        initTagsValueList(jRoot.getJSONObject("readerConfiguration"));
                                    }


                                    PrefLibTV.getInstance(context).setCAKey(jRoot.getBoolean("caPublicKeyExpired"));
                                    if (PrefLibTV.getInstance(context).getCAKey()) {
                                        PrefLibTV.getInstance(context).setCAData(jRoot.getJSONObject("caPublicKey").getJSONArray("keyList").toString());
                                    }
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }

                            String ezpk = "";
                            if (jRoot.has("eZPK")) {
                                ezpk = jRoot.getString("eZPK");
                            }
                            Utils.LOGD(TAG, "ezpk=" + ezpk);
                            PrefLibTV.getInstance(context).setWorkingKey(ezpk);


                            Utils.LOGD(TAG, "onSuccess: handerTranWaitSignature="+ handlerTranWaitSignature);
                            if (handlerTranWaitSignature && handlerResultLogin != null) {
                                DataReversalLogin dataReversalLogin = checkHaveForceReversal(jRoot);
                                if (dataReversalLogin != null) {
                                    showLoading(false);
                                    sv.saveLog();
                                    handlerResultLogin.onHaveTranWaitSignature(dataReversalLogin);
                                    return;
                                }
                            }
                            // reset last sale error
                            PrefLibTV.getInstance(context).setLastErrorSale(LibReaderController.TYPE_ERROR_NONE);
                            processAfterLoginLv1();
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        showDialogFailDefault();
                        appendLog("login success but error: " + e.getMessage());
                    }
                }

                @Override
                public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                    Utils.LOGE(TAG,"Login Error: " + MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2, arg3));
                    appendLog("LOGIN onFailure>>" + MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2, arg3));
                    showDialogFailTimeout();
                }
            });
	}

    private void processAfterLoginLv1() {
        if (levelLogin == 1) {
            callbackSuccessLogin();
        }
        else if (levelLogin == 2) {
            loginLevel2(mAcc, pin);
        }
    }

    public void loginLevel2(String mAcc, String mPin) {
		if (!GetData.CheckInternet(context)) {
//			Utils.mToast(getString(R.string.check_internet));
            callbackFail( getString(R.string.check_internet));
			return;
		}

		StringEntity entity = null;
		try {
			JSONObject jo = new JSONObject();
			jo.put("serviceName", ConstantsPay.LOGIN_LEVEL_2);
			jo.put("readerSerialNo", PrefLibTV.getInstance(context).getSerialNumber());
			jo.put("udid", "0");
			jo.put("versionNo", android.os.Build.VERSION.RELEASE);
			jo.put("platform", ConstantsPay.PLATFORM);
			jo.put("sessionKey", PrefLibTV.getInstance(context).getSessionKey());
			jo.put("userID", mAcc);
			jo.put("userPIN", mPin);
            Utils.LOGD(TAG, "Data: " + jo);
			entity = new StringEntity(EncodeDecode.doAESEncrypt(jo.toString(), PrefLibTV.getInstance(context).getSessionKey()));
		} catch (Exception e1) {
			e1.printStackTrace();
            appendLog("error build LOGIN_LV2: "+e1.getMessage());
		}

        MposRestClient.getInstance(context).post(context, ConstantsPay.getUrlServer(context), entity, ConstantsPay.CONTENT_TYPE, new AsyncHttpResponseHandler() {
            @Override
            public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                try {
                    JSONObject data = new JSONObject(EncodeDecode.doAESDecrypt(new String(arg2), PrefLibTV.getInstance(context).getSessionKey()));
                    PrefLibTV.getInstance(context).setSessionKey(data.getString("sessionKey"));
                        Utils.LOGD(TAG,"Login lv2: "+ data);
                        if (data.has("error")) {
                            final JSONObject jo = data.getJSONObject("error");
                            int code = jo.getInt("code");
                            int typeButton = 2;
                            if (LibError.isSessionExpired(code)) {
                                typeButton = 1;
                            }
                            showDialogFailBank(code, typeButton);
                        } else {
                            PrefLibTV.getInstance(context).setTKL2(data.getString("tokenL2"));
                            callbackSuccessLogin();

                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        appendLog("lin lv2 success but error: " + e.getMessage());
                        callbackFail(new DataError(ConstantsPay.ERROR_CODE_TIMEOUT_REQ, getString(R.string.SERVICE_ERROR_SESSION_TIMEOUT_L2)), TYPE_FAIL_SHOW_RELOGIN_FINISH);
                    }
                }

                public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                    Utils.LOGE(TAG,"Login lv2 Error: " + MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2, arg3));
                    appendLog("LOGIN_LV2 onFailure>>" + MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2, arg3));
                    showDialogFailTimeout();
                }

        });
	}

    public void changePasswordWithNewPass(String currentPass, String newPass, @NonNull ItfResultChangePass callback) {
        StringEntity entity = null;

        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, ConstantsPay.CHANGE_PASSWORD);
            jo.put("readerSerialNo", PrefLibTV.getInstance(context).getSerialNumber());
            jo.put("udid", "0");
            jo.put("versionNo", android.os.Build.VERSION.RELEASE);
            jo.put("platform", ConstantsPay.PLATFORM);
            jo.put("userID", PrefLibTV.getInstance(context).getUserId());
            jo.put("userPIN", currentPass);
            jo.put("newPIN", newPass);
            jo.put("sessionKey", PrefLibTV.getInstance(context).getSessionKey());

            Utils.LOGD("Data: ", jo.toString());
            entity = new StringEntity(EncodeDecode.doAESEncrypt(jo.toString(), PrefLibTV.getInstance(context).getSessionKey()));
        }
        catch (Exception e) {
            Utils.LOGE(TAG, "Exception", e);
        }

        MposRestClient.getInstance(context).post(context, ConstantsPay.getUrlServer(context),
                entity, ConstantsPay.CONTENT_TYPE, new AsyncHttpResponseHandler() {

                    @Override
                    public void onStart() {
                        showLoading(true);
                        super.onStart();
                    }
                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                        try {
                            showLoading(false);
                            JSONObject response = new JSONObject(EncodeDecode.doAESDecrypt(new String(arg2), PrefLibTV.getInstance(context).getSessionKey()));
                            PrefLibTV.getInstance(context).setSessionKey(response.getString("sessionKey"));
                            Utils.LOGD("Login: ", response.toString());
                            if (response.has("error")) {
                                final JSONObject jo = response.getJSONObject("error");
                                int codeError = jo.getInt("code");
                                String msgError = LibError.getErrorMsg(codeError, context);
                                callbackFail(new DataError(codeError, msgError));
                            } else {
                                callback.onSuccessChangePass();
                            }
                        } catch (Exception e) {
                            Utils.LOGE(TAG, "Exception", e);
                            callbackFail(new DataError(ConstantsPay.ERROR_CODE_TIMEOUT_REQ, getString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT)));
                        }
                    }

                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        Utils.LOGE("Login Error: ", arg3.getMessage());
                        showLoading(false);
                        callbackFail(new DataError(ConstantsPay.ERROR_CODE_TIMEOUT_REQ, getString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT)));
                    }

                    private void callbackFail(DataError dataError) {
                        callback.onFailChangePass(dataError);
                    }
                });
    }

    public boolean checkNeedLoginLevel2() {
        return TextUtils.isEmpty(PrefLibTV.getInstance(context).getTKL2());
    }

    private String getString(int id) {
        if (contextRes == null) {
            return context.getString(id);
        }
        else {
            return contextRes.getString(id);
        }
    }
    private String getString(int id, Object... obj) {
        if (contextRes == null) {
            return context.getString(id, obj);
        }
        else {
            return contextRes.getString(id, obj);
        }
    }

    private void initTagsValueList(JSONObject jReaderConfiguration) {

        if(jReaderConfiguration==null){
            Utils.LOGD(TAG, "jReaderConfiguration null");
            return;
        }
        try {
            JSONArray jInitTagsValuesList = jReaderConfiguration.getJSONArray("initTagsValuesList");
            if(jInitTagsValuesList.length()>0){
                String readType;
                JSONObject jItemTemp;
                for (int i = 0; i < jInitTagsValuesList.length(); i++) {
                    jItemTemp = jInitTagsValuesList.getJSONObject(i);
                    readType = JsonParser.getDataJson(jItemTemp, "readerType");

                    if(String.valueOf(ConstantsPay.DEVICE_PINPAD).equals(readType)){
                        PrefLibTV.getInstance(context).setTagConfig(jItemTemp.toString());

                        // parse isserValuesList
                        JSONArray jIsserValuesList = jItemTemp.getJSONArray("isserValuesList");
                        if(jIsserValuesList.length()>0){
                            JSONObject jTemp;
                            for (int j = 0; j < jIsserValuesList.length(); j++) {
                                jTemp = jIsserValuesList.getJSONObject(j);
                                String issuerName = JsonParser.getDataJson(jTemp, "issuerName");
                                if(issuerName.equalsIgnoreCase("MasterCard")){
                                    PrefLibTV.getInstance(context).setTagConfigMaster(jTemp.toString());
                                }
                                else if(issuerName.equalsIgnoreCase("VISA")){
                                    PrefLibTV.getInstance(context).setTagConfigVisa(jTemp.toString());
                                }
                                else if(issuerName.equalsIgnoreCase("JCB")){
                                    PrefLibTV.getInstance(context).setTagConfigJCB(jTemp.toString());
                                }
                            }
                        }
                        break;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private DataReversalLogin checkHaveForceReversal(JSONObject jRoot) {
        String amount = JsonParser.getDataJson(jRoot, "amount");
        String trxType = JsonParser.getDataJson(jRoot, "trxType");
        Utils.LOGD(TAG, "amount:"+amount+" trxType:"+trxType+"<---");
        appendLog("check waitSignature amount:"+amount+" trxType:"+trxType+"<---");

        DataReversalLogin dataReversalLogin = null;
        if (!TextUtils.isEmpty(amount)) {    //&& ConstantsPay.TRX_TYPE_NORMAL.equals(trxType) -> sometime have trxType == 0
            dataReversalLogin = new DataReversalLogin();
            dataReversalLogin.amount = amount;
            dataReversalLogin.trxType = trxType;
            dataReversalLogin.pan = JsonParser.getDataJson(jRoot, "PAN");
            dataReversalLogin.itemDesc = JsonParser.getDataJson(jRoot, "itemDesc");
            dataReversalLogin.cardholderName = JsonParser.getDataJson(jRoot, "cardholderName");
            dataReversalLogin.transReqId = JsonParser.getDataJson(jRoot, "transactionRequestID");
            dataReversalLogin.transactionDate = JsonParser.getDataJson(jRoot, "transactionDate");
            String udid = JsonParser.getDataJson(jRoot, "udid");
            dataReversalLogin.paymentIdentify = udid;
//            dataReversalLogin.setPw(mPin);

            if (TextUtils.isEmpty(dataReversalLogin.itemDesc) && !TextUtils.isEmpty(udid)) {
                int indexCharSplit = udid.lastIndexOf(ConstantsPay.CHAR_SPLIT_DESC_UDID);
                if (indexCharSplit > 0) {
                    dataReversalLogin.itemDesc = udid.substring(0, indexCharSplit).replace(ConstantsPay.CHAR_REPLACE_SPACE_OF_UDID," ");
                }
            }
        }
        return dataReversalLogin;
    }

    /**----------*----------*-----multi acquirer login-----*----------*----------*
     *
     * ----------*----------*----------*----------*----------*/
    // use for new mpos.vn: login with user + pass + serialnumber(optional);
    public void processMultiAcquirer(String mAcc, String serialNumber, String pin) {
        isUseUserPassLogin = true;
        processMultiAcquirer(ConstantsPay.MPOS_MULTI_ACQUIRER, mAcc, serialNumber, pin);
    }


    // use for P12 in mpos.vn lite : login with user + serialnumber;
    public void processMultiAcquirer(String mAcc, String serialNumber) {
        isUseSnFetchMcMacq = true;
        processMultiAcquirer(ConstantsPay.MPOS_MULTI_ACQUIRER, mAcc, serialNumber, null);
    }

    /**
     * flow macq for N31: init + login + loadAllConfigMacq
     * use user + serial number
     *
     * @param mAcc         user mobile
     * @param serialNumber serial number
     */
    public void processLoginMacqForSale(String mAcc, String serialNumber) {
        if (!(DevicesUtil.isSP02P12() || DevicesUtil.isSP02N4()) || !(Constants.isTypeIntegrationMpos() || Constants.isTypeIntegrationMpos3rd())) {
            return;
        }
        isUseSnFetchMcMacq = true;
        isUseReaderSaleMacq = true;
        processMultiAcquirer(ConstantsPay.MPOS_MULTI_ACQUIRER, mAcc, serialNumber, null);
    }


    // use for inject-key/ bvl
    public void processMultiAcquirer(String bankName, String mAcc, String serialNumber, final String pin) {
        if (Constants.isTypeIntegrationMpos() || Constants.isTypeIntegrationMpos3rd()) {
            this.bankName = bankName;
            this.serialNumber = serialNumber;
            this.mAcc = mAcc;
            this.pin = pin;
            processMultiAcquirer();
        }
    }

    private void processMultiAcquirer() {
        showLoading(true);
        useCacheLoginMA = checkCanUseCacheMA();
        setupLanguageMacq();
        setupConfigServer(bankName);
        appendLog("useCacheLoginMa=" + useCacheLoginMA);
        if (useCacheLoginMA) {
            if (PrefLibTV.getInstance(context).getPermitSocket() && checkCanUseCachePreSale()) {
                appendLog("use cache preSale");
                callbackSuccessLogin();
            } else {
                checkLoadPreSaleOrCallbackSuccess();
            }
            // use mpos-lite in P12 -> don't load presale when open app, because can use QR-static
//            else if (isUseReaderFetchMcMacq) {
//                callbackSuccessLogin();
//            }
//            else {
//                loadPreSale();
//            }
        }
        else {
            setupInitAndLoginMA();
        }
    }

    private void checkLoadPreSaleOrCallbackSuccess() {
        appendLog("check use cache Presale: isUseReaderSaleMacq=" + isUseReaderSaleMacq
                + " useReaderFetchMcMacq=" + isUseSnFetchMcMacq + " useUserPassLogin=" + isUseUserPassLogin);
        // - isUseReaderFetchMcMacq:  use mpos-lite in P12
        //      + isUseReaderSaleMacq = false -> don't load presale when open app, because can use QR-static
        //      + isUseReaderSaleMacq = true -> need load presale for sale
        // isUseUserPassLogin:      use for new mpos.vn -> login by user + pass
        if ( (!isUseReaderSaleMacq && isUseSnFetchMcMacq) || isUseUserPassLogin) {
            if (handlerDataLogin != null) {
                handlerDataLogin.onSuccessLoginWithData(PrefLibTV.getInstance(context).get(PrefLibTV.DATA_LOGIN_MACQ_CACHE, String.class, ""));
            }
            callbackSuccessLogin();
        }
        else {
            if (checkCanUseCachePreSale()) {
                appendLog("use cache PreSale");
                callbackSuccessLogin();
            }
            else {
                fetchPreSale();
            }
        }
    }

    private boolean isSameUserCachePreSale() {
        String preSaleCache = PrefLibTV.getInstance(context).get(PrefLibTV.PRE_SALE_CONFIG, String.class, "");
        if (!TextUtils.isEmpty(preSaleCache)) {
            PreSaleRes preSale = MyGson.getGson().fromJson(preSaleCache, PreSaleRes.class);
            if (preSale != null && !TextUtils.isEmpty(mAcc) && mAcc.equals(preSale.getMobileUser())) {
                return true;
            }
            PrefLibTV.getInstance(context).put(PrefLibTV.PRE_SALE_CONFIG, "");
        }
        return false;
    }

    private boolean checkCanUseCachePreSale() {
//        boolean hasPermitCachePreSale = PrefLibTV.getInstance(context).get(PrefLibTV.hasPermitCachePreSale, Boolean.class, false);
//        if (hasPermitCachePreSale) {
//            int lastStatusTrans = PrefLibTV.getInstance(context).getLastErrorSale();
//            appendLog("checkCanUseCachePreSale: lastStatus=" + lastStatusTrans);
//            if (lastStatusTrans == LibReaderController.TYPE_ERROR_NONE) {
//                String preSaleCache = PrefLibTV.getInstance(context).get(PrefLibTV.PRE_SALE_CONFIG, String.class, "");
//                if (!TextUtils.isEmpty(preSaleCache)) {
//                    return true;
//                }
//            }
//        }
//        return false;
        if (isSameUserCachePreSale()) {
            int lastStatusTrans = PrefLibTV.getInstance(context).getLastErrorSale();
            appendLog("checkCanUseCachePreSale: lastStatus=" + lastStatusTrans);
            if (lastStatusTrans == LibReaderController.TYPE_ERROR_NONE) {
                return true;
            }
//            }
        }
        return false;
    }

    private void setupLanguageMacq() {
        String language;
        if (languageCode != null) {
            language = languageCode.getLanguageCode();
            Utils.LOGD(TAG, "loginMultiAcquirer: 1="+language);
        } else {
            language = Locale.getDefault().getLanguage();
            Utils.LOGD(TAG, "loginMultiAcquirer: 2="+language);
        }
        Utils.LOGD(TAG, "loginMultiAcquirer: setLanguage="+language);
        MposRestClient.getInstance(context).addHeader("Accept-Language", language);
    }

    private void setupInitAndLoginMA() {
        LibLoginMacq.ItfHandlerResultLoginMacq itfHandlerResultLoginMacq = new LibLoginMacq.ItfHandlerResultLoginMacq() {
            @Override
            public void appendLogMacq(String log) {
                appendLog(log);
            }

            @Override
            public void onFailLoginMacq(int typeFail, int statusCode, String rawJsonResponse) {
                showFailMultiAcquirer(typeFail, statusCode, rawJsonResponse);
            }

            @Override
            public void onSuccessLoginMacq(LoginRes loginRes) {
                handleSuccessLoginMacq(loginRes);
            }
        };
        LibLoginMacq libLoginMacq;
        if (isUseSnFetchMcMacq) {
            libLoginMacq = new LibLoginMacq(context, mAcc, serialNumber, itfHandlerResultLoginMacq);
        }
        else {
            libLoginMacq = new LibLoginMacq(context, mAcc, pin, serialNumber, itfHandlerResultLoginMacq);
        }
        libLoginMacq.setLanguageCode(languageCode);
        libLoginMacq.setDeviceIdentifier(deviceIdentifier);
        libLoginMacq.setAppType(appType);
        libLoginMacq.initAuthenMA();
    }

    private boolean checkCanUseCacheMA() {
        if (callLoginBankWithConfigMpos || !compareUserWithCache()) {
            return false;
        }
        long lastTimeLoginMA = PrefLibTV.getInstance(context).get(PrefLibTV.LAST_TIME_LOGIN_MA, Long.class, 0L);
        if (PrefLibTV.getInstance(context).getPermitSocket()) {
            // 6h am -> 24h: 18h = 18*3600*1000 = ********
            return System.currentTimeMillis() - lastTimeLoginMA < ********;
        }
        else {
//        12h - 5min = (12 * 3600 * 1000) - (5*60 * 1000) = ********
            return System.currentTimeMillis() - lastTimeLoginMA < ********;
        }
    }

    private void handleSuccessLoginMacq(LoginRes loginRes) {
        PrefLibTV.getInstance(context).put(PrefLibTV.MPOS_MID, loginRes.getmId());
        PrefLibTV.getInstance(context).put(PrefLibTV.MPOS_TID, loginRes.gettId());
        PrefLibTV.getInstance(context).put(PrefLibTV.MPOS_MUID, loginRes.getUsername());
        PrefLibTV.getInstance(context).setMerchantId(loginRes.getMerchantId());
        PrefLibTV.getInstance(context).put(PrefLibTV.LAST_TIME_LOGIN_MA, System.currentTimeMillis());

        PrefLibTV.getInstance(context).put(PrefLibTV.DATA_LOGIN_MACQ_CACHE, MyGson.getGson().toJson(loginRes));

        Utils.LOGD(TAG, "onSuccessApi: mId="+loginRes.getmId()+" tId="+loginRes.gettId());
        PrefLibTV.getInstance(context).setUserId(loginRes.getUsername());
        PrefLibTV.getInstance(context).setFlagDevices(readerType);
        PrefLibTV.getInstance(context).setSerialNumber(serialNumber);

        if (isUseReaderSaleMacq) {
            fetchMerchantAllConfigMacq(mAcc, serialNumber, loginRes.getMerchantId());
        }
        else {
            checkLoadPreSaleOrCallbackSuccess();
        }
    }


    private void fetchPreSale() {
        appendLog("loadPreSale: handlerWaitSign=" + handlerTranWaitSignature);
        PreSaleSend preSaleSend = new PreSaleSend();

        preSaleSend.setMuid(PrefLibTV.getInstance(context).get(PrefLibTV.MPOS_MUID, String.class));
        preSaleSend.setTid(PrefLibTV.getInstance(context).get(PrefLibTV.MPOS_TID, String.class));
        preSaleSend.setMid(PrefLibTV.getInstance(context).get(PrefLibTV.MPOS_MID, String.class));

        MposRestClient.getInstance(context).postMacq(context, ApiMultiAcquirerInterface.URL_PRE_SALE,
                preSaleSend, new MyTextHttpResponseHandler(context) {

            @Override
            public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                Utils.LOGD(TAG, "loadPreSale onFailure() called with: statusCode = [" + statusCode + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");
                appendLog("onFailure loadPreSale: statusCode=" + statusCode);
                if ((statusCode == HttpStatus.SC_UNAUTHORIZED || statusCode == HttpStatus.SC_BAD_REQUEST)
                        && useCacheLoginMA) {
                    // use cacheLoginMacq but error (sessionId + token) -> need relogin
                    setupInitAndLoginMA();
                }
                else {
                    boolean useCachePreSale = false;
//                    if (PrefLibTV.getInstance(context).getPermitSocket()){
                    if (isSameUserCachePreSale()) {
                        useCachePreSale = true;
                    }
//                    }
                    appendLog("onFailure loadPreSale: useCachePreSale=" + useCachePreSale);

                    if (useCachePreSale) {
                        callbackSuccessLogin();
                    }
                    else {
                        if (useCacheLoginMA) {
                            useCacheLoginMA = false;
                            clearCacheInRam();
                        }
                        showFailMultiAcquirer(LibLoginMacq.FAIL_TYPE_PRE_SALE_ONFAIL, statusCode, rawJsonData);
                    }
                }
            }

            @Override
            public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                Utils.LOGD(TAG, "loadPreSale onSuccess() called with: statusCode = [" + statusCode + "], rawJsonResponse = [" + rawJsonResponse + "]");
                if (statusCode == HttpStatus.SC_OK) {

                    String clearData = MacqUtil.getInstance().parseAndDecryptData(rawJsonResponse);

                    Utils.LOGD(TAG, "loadPreSale onSuccess: " + clearData);
                    appendLog(clearData);

                    PrefLibTV.getInstance(context).savePreSale(clearData);
//                    PrefLibTV.getInstance(context).put(PrefLibTV.PRE_SALE_CONFIG, clearData);

                    preSaleRes = MyGson.parseJson(clearData, PreSaleRes.class);
                    preSaleRes.setMobileUser(mAcc);
                    PrefLibTV.getInstance(context).put(PrefLibTV.PRE_SALE_CONFIG, MyGson.getGson().toJson(preSaleRes));
                    PrefLibTV.getInstance(context).put(PrefLibTV.BANK_ACQUIRE, preSaleRes.getAcquirerMagstripe());

                    PrefLibTV.getInstance(context).setMId(preSaleRes.getMid());
                    PrefLibTV.getInstance(context).setTId(preSaleRes.getTid());

                    saveMerchantInfo();

                    if (handlerTranWaitSignature && preSaleRes.getTrxPendings() != null && preSaleRes.getTrxPendings().size() > 0) {
                        if (itfHandlerWaitSignatureMA != null) {
                            showLoading(false);
                            sv.saveLog();
                            itfHandlerWaitSignatureMA.onHaveTranWaitSignatureMultiAcquirer(preSaleRes.getTrxPendings());
                        }
                        else {
                            appendLog("loadPreSale have wait trans but not implement Itf");
                            Utils.LOGD(TAG, "You have transaction wait signature, but you don't implement ItfHandlerTransWaitSignature.");
                        }
                    }
                    else {
                        if (preSaleRes.getTrxPendings() == null || preSaleRes.getTrxPendings().size() == 0) {
                            PrefLibTV.getInstance(context).setLastErrorSale(LibReaderController.TYPE_ERROR_NONE);
                        }
                        callbackSuccessLogin();
                    }
                } else {
                    showFailMultiAcquirer(LibLoginMacq.FAIL_TYPE_PRE_SALE_FAIL, statusCode, rawJsonResponse);
                }
            }
        });
    }

    public void changePasswordWithNewPassMacq(String user, String newPass, String oldPass, @NonNull ItfResultChangePass callback) {
        ChangePass changePass = new ChangePass(user, newPass, newPass, oldPass);
//        changePass.setUsername(user);
//        changePass.setNewPassword(newPass);
//        changePass.setConfirmPassword(newPass);
//        changePass.setPassword(oldPass);
//        StringEntity entity = ApiMultiAcquirerInterface.getInstance().buildStringEntity(changePass);
       /* try {
            Utils.LOGD(TAG, "DataClear: "+ MyGson.getGson().toJson(changePass));

            String cipherText = CryptoInterface.getInstance().encryptData(MyGson.getGson().toJson(changePass));

            DataBaseObj dataBaseObj = new DataBaseObj(cipherText);
            entity = new StringEntity(MyGson.getGson().toJson(dataBaseObj));
        } catch (Exception e) {
            e.printStackTrace();
        }*/

        MposRestClient.getInstance(context).putMacq(context, ApiMultiAcquirerInterface.URL_CHANGE_PASS, changePass,
                ConstantsPay.CONTENT_TYPE, new MyTextHttpResponseHandler(context) {
            @Override
            public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                Utils.LOGD(TAG, "changePinMA onFailure() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");
                DataError dataError = new DataError();
                dataError.build(statusCode, rawJsonData, getString(R.string.error_ma_default, String.valueOf(statusCode)));
                callbackFail(dataError);
            }

            @Override
            public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                Utils.LOGD(TAG, "changePinMA onSuccess() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonResponse = [" + rawJsonResponse + "]");
                String clearData = MacqUtil.getInstance().parseAndDecryptData(rawJsonResponse);
                if (statusCode == HttpStatus.SC_OK) {
                    Utils.LOGD(TAG, "onSuccess changePinMA: " + clearData);
                    callback.onSuccessChangePass();
                } else {
                    DataError dataError = new DataError();
                    dataError.build(statusCode, clearData, getString(R.string.error_ma_default, String.valueOf(statusCode)));
                    callbackFail(dataError);
                }
            }

            private void callbackFail(DataError dataError) {
                callback.onFailChangePass(dataError);
            }
        });
    }

    private void callbackSuccessLogin() {
        Utils.LOGD(TAG, "callbackSuccessLogin: isUseReaderSaleMacq=" + isUseReaderSaleMacq);
        showLoading(false);
        if (isUseReaderSaleMacq) {
            processCacheDataWithoutPw();
        }
        else {
            processCacheData();
        }
        sv.saveLog();
        if (handlerResultLogin != null) {
            handlerResultLogin.onSuccessLogin();
        }
    }

    private void callbackMcConfigSuccess(String jsonData) {
        itfHandlerLoadOnlyMposConfig.onHandlerLoadOnlyMposConfig(true, null, jsonData);
    }
    
    private void callbackMcConfigFail(DataError dataError) {
        itfHandlerLoadOnlyMposConfig.onHandlerLoadOnlyMposConfig(false, dataError, null);
    }

    /**----------*----------*-----show error-----*----------*----------*
     * ----------*----------*----------*----------*----------*/

    private void showDialogFailDefault() {
        callbackFail(new DataError(ConstantsPay.ERROR_CODE_DEFAULT, getString(R.string.error_try_again)), TYPE_FAIL_SHOW_TEXT);
    }
    private void showDialogFailTimeout() {
        callbackFail(new DataError(ConstantsPay.ERROR_CODE_TIMEOUT_REQ, getString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT)), TYPE_FAIL_SHOW_TEXT);
    }

    private void callbackFailNoInternet() {
        DataError dataError = new DataError(ConstantsPay.ERROR_CODE_NO_INTERNET, getString(R.string.check_internet));
        if (itfHandlerLoadOnlyMposConfig != null) {
            callbackMcConfigFail(dataError);
        }
        else {
            callbackFail(dataError);
        }
    }
    private void callbackFail(String msg){
        callbackFail(new DataError(ConstantsPay.ERROR_CODE_SDK_DEFAULT, msg), TYPE_FAIL_NONE);
    }
    private void callbackFail(DataError dataError){
        callbackFail(dataError, TYPE_FAIL_NONE);
    }

    private void showFailMultiAcquirer(int typeFail, int statusCode, String rawJsonData) {
        appendLog("fail MA: typeFail="+typeFail+" code="+statusCode+" ->"+rawJsonData);
        DataError dataError = new DataError(true);
        if (statusCode == 0) {
            dataError.build(statusCode, rawJsonData, getString(R.string.error_ma_default_code0, String.valueOf(statusCode)));
        }
        else {
            dataError.build(statusCode, rawJsonData, getString(R.string.error_ma_default, String.valueOf(statusCode)));
        }
        appendLog("error:" + dataError.getMsg());
        callbackFail(dataError);
    }

    private void showDialogFailBank(int code, int typeBtn) {
        DataError dataError = new DataError(code);
        if (contextRes == null) {
            dataError.setMsg(LibError.getErrorMsg(code, context));
        }
        else {
            dataError.setMsg(LibError.getErrorMsg(code, contextRes));
        }
        callbackFail(dataError, typeBtn);
    }

    /**
     * show dialog fail
     * @param typeFail 0: only show text; 1: re_login + finish; 2: finish
     */
	private void callbackFail(@NonNull DataError dataError, final int typeFail){
        showLoading(false);
        sv.saveLog();
		if(handlerResultLogin!=null){
			handlerResultLogin.onFailureLogin(dataError, typeFail);
		}
	}

	private void showLoading(boolean show) {
        if(handlerResultLogin!=null){
            handlerResultLogin.showLoading(show);
        }
	}

    private void appendLog(String content) {
        Utils.LOGD(TAG, content);
        if (sv != null) {
            sv.appendLogAction(content);
        }
    }

	public static final int TYPE_FAIL_NONE = -1;
	public static final int TYPE_FAIL_SHOW_TEXT = 0;
	public static final int TYPE_FAIL_SHOW_RELOGIN_FINISH = 1;
	public static final int TYPE_FAIL_FINISH = 2;

	public interface ItfHandlerResultLogin{
        void showLoading(boolean show);
        void onFailureLogin(@NonNull DataError dataError, int typeFail);
        void onHaveTranWaitSignature(DataReversalLogin dataReversalLogin);
		void onSuccessLogin();
	}

    public interface ItfHandlerDataLogin {
        void onSuccessLoginWithData(String data);
    }

	public interface ItfResultChangePass{
        void onSuccessChangePass();
        void onFailChangePass(@NonNull DataError dataError);
    }

    public interface ItfHandlerTransWaitSignature{
        void onHaveTranWaitSignatureMultiAcquirer(List<WfDetailRes> arr);
    }

    public interface ItfHandlerMposConfig{
	    void onHandlerMposConfig(String jsonData);
    }

    /**
     * use for bvl: load merchant -> get only merchant config
     */
    public interface ItfHandlerLoadOnlyMposConfig{
	    void onHandlerLoadOnlyMposConfig(boolean isSuccess, DataError dataError, String jsonData);
    }

    public interface ItfHandlerBankSelected{
        void onBankSelected(String bankName);
    }

    public interface ItfHandlerCommonConfig{
        void onHandlerCommonConfig(boolean result, CommonConfig cmConfig);
    }


}
