package com.mpos.sdk.core.model;


public class KeyServer {

    private String caPublicKeyExpired;
    private String checkPANOnly;
    private String emvProcessOfflineResult;
    private String IPEK;
//    private String fixedKey;
    private String offlinePIN;
    private String tmkKIS;
//    private String pinMasterKey;
    private String tmkKCV;
//    private String kcvPinMasterKey;
    private String platform;
    private String readerSerialNo;
    private String receiptWidth;
    private String restrictInternationCard;
    private String serviceName;
    private String sessionKey;
    private String settlementBatchId;
    private String transactionDate;
    private String udid;
    private String versionNo;
    private String KSN;
    private String capkDspread;

    public String getCaPublicKeyExpired() {
        return caPublicKeyExpired;
    }

    public void setCaPublicKeyExpired(String caPublicKeyExpired) {
        this.caPublicKeyExpired = caPublicKeyExpired;
    }

    public String getCheckPANOnly() {
        return checkPANOnly;
    }

    public void setCheckPANOnly(String checkPANOnly) {
        this.checkPANOnly = checkPANOnly;
    }

    public String getEmvProcessOfflineResult() {
        return emvProcessOfflineResult;
    }

    public void setEmvProcessOfflineResult(String emvProcessOfflineResult) {
        this.emvProcessOfflineResult = emvProcessOfflineResult;
    }

    public String getFixedKey() {
        return IPEK;
    }

    public void setFixedKey(String fixedKey) {
        this.IPEK = fixedKey;
    }

    public String getOfflinePIN() {
        return offlinePIN;
    }

    public void setOfflinePIN(String offlinePIN) {
        this.offlinePIN = offlinePIN;
    }

    public String getPinMasterKey() {
        return tmkKIS;
    }

    public void setPinMasterKey(String pinMasterKey) {
        this.tmkKIS = pinMasterKey;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getReaderSerialNo() {
        return readerSerialNo;
    }

    public void setReaderSerialNo(String readerSerialNo) {
        this.readerSerialNo = readerSerialNo;
    }

    public String getReceiptWidth() {
        return receiptWidth;
    }

    public void setReceiptWidth(String receiptWidth) {
        this.receiptWidth = receiptWidth;
    }

    public String getRestrictInternationCard() {
        return restrictInternationCard;
    }

    public void setRestrictInternationCard(String restrictInternationCard) {
        this.restrictInternationCard = restrictInternationCard;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getSessionKey() {
        return sessionKey;
    }

    public void setSessionKey(String sessionKey) {
        this.sessionKey = sessionKey;
    }

    public String getSettlementBatchId() {
        return settlementBatchId;
    }

    public void setSettlementBatchId(String settlementBatchId) {
        this.settlementBatchId = settlementBatchId;
    }

    public String getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(String transactionDate) {
        this.transactionDate = transactionDate;
    }

    public String getUdid() {
        return udid;
    }

    public void setUdid(String udid) {
        this.udid = udid;
    }

    public String getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(String versionNo) {
        this.versionNo = versionNo;
    }

    public String getKSN() {
        return KSN;
    }

    public void setKSN(String KSN) {
        this.KSN = KSN;
    }

    public String getKcvPinMasterKey() {
        return tmkKCV;
    }

    public void setKcvPinMasterKey(String kcvPinMasterKey) {
        this.tmkKCV = kcvPinMasterKey;
    }

    public String getCapkDspread() {
        return capkDspread;
    }

    public void setCapkDspread(String capkDspread) {
        this.capkDspread = capkDspread;
    }
}