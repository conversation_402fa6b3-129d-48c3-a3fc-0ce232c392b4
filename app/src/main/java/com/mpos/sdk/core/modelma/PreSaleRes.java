package com.mpos.sdk.core.modelma;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * Create by anhnguyen on 9/15/20
 */
public class PreSaleRes {

    @SerializedName("trxPendings")
    @Expose
    private List<WfDetailRes> trxPendings = null;
    @SerializedName("eZPKDomestic")
    @Expose
    private String eZPKDomestic;
    @SerializedName("eZPKIntenational")
    @Expose
    private String eZPKInternational;
    @SerializedName("requirePinPrefixes")
    @Expose
    private String requirePinPrefixes;
    @SerializedName("acquirerMagstripe")
    @Expose
    private String acquirerMagstripe;

    @SerializedName("mid")
    @Expose
    private String mid;
    @SerializedName("tid")
    @Expose
    private String tid;
    @SerializedName("mobileUser")
    @Expose
    private String mobileUser;  // use for compare cache

    public List<WfDetailRes> getTrxPendings() {
        return trxPendings;
    }

    public void setTrxPendings(List<WfDetailRes> trxPendings) {
        this.trxPendings = trxPendings;
    }

    public String geteZPKDomestic() {
        return eZPKDomestic;
    }

    public void seteZPKDomestic(String eZPKDomestic) {
        this.eZPKDomestic = eZPKDomestic;
    }

    public String getRequirePinPrefixes() {
        return requirePinPrefixes;
    }

    public void setRequirePinPrefixes(String requirePinPrefixes) {
        this.requirePinPrefixes = requirePinPrefixes;
    }

    public String geteZPKInternational() {
        return eZPKInternational;
    }

    public void seteZPKInternational(String eZPKInternational) {
        this.eZPKInternational = eZPKInternational;
    }

    public String getAcquirerMagstripe() {
        return acquirerMagstripe;
    }

    public void setAcquirerMagstripe(String acquirerMagstripe) {
        this.acquirerMagstripe = acquirerMagstripe;
    }

    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getMobileUser() {
        return mobileUser;
    }

    public void setMobileUser(String mobileUser) {
        this.mobileUser = mobileUser;
    }
}
