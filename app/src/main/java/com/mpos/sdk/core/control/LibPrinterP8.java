package com.mpos.sdk.core.control;

import android.content.Context;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.mpos.sdk.util.DevicesUtil;
import com.pos.sdk.printer.POIPrinterManager;
import com.pos.sdk.printer.models.PrintLine;
import com.pos.sdk.printer.models.TextPrintLine;

import java.util.List;

/**
 * Create by anhnguyen on 06/07/2022
 */
public class LibPrinterP8 {

    private Context context;

    private POIPrinterManager printerManager;
    private POIPrinterManager.IPrinterListener printerListener;

    public LibPrinterP8(@NonNull Context context) throws Exception {
        this(context, null);
    }

    public LibPrinterP8(@NonNull Context context, String pathFont)  throws Exception{
        if (!DevicesUtil.isSP02P8() && !DevicesUtil.isSP02P10()) {
            throw new Exception("Library is only use for reader SP02 SmartPrime.");
        }
        else {
            this.context = context;
            initPrinter(context, pathFont);
        }
    }

    public void setPrinterListener(POIPrinterManager.IPrinterListener printerListener) {
        this.printerListener = printerListener;
    }

    public POIPrinterManager getPrinterManager() {
        return printerManager;
    }

    private void initPrinter(Context context, String path) {
        try {
            printerManager = new POIPrinterManager(context);
            printerManager.open();
            if (TextUtils.isEmpty(path)) {
                printerManager.setPrintFont("/system/fonts/DroidSansMono.ttf");
            }
            else {
                try {
                    printerManager.setPrintFont(path);
                } catch (Exception e) {
                    e.printStackTrace();
                    printerManager.setPrintFont("/system/fonts/DroidSansMono.ttf");
                }
            }
            printerManager.setPrintGray(2000);
            printerManager.setLineSpace(2);
            printerManager.cleanCache();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void addPrintLine(PrintLine printLine) {
        if (printerManager != null) {
            try {
                printerManager.addPrintLine(printLine);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public void addArrTextPrintLine(List<TextPrintLine> arrPrintLine) {
        if (printerManager != null) {
            try {
                printerManager.addPrintLine(arrPrintLine);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public void beginPrint() {
        if (printerManager == null) {
            return;
        }
        if (printerListener == null) {
            printerListener = new POIPrinterManager.IPrinterListener() {
                @Override
                public void onStart() {

                }

                @Override
                public void onFinish() {

                }

                @Override
                public void onError(int i, String s) {

                }
            };
        }
        try {
            printerManager.beginPrint(printerListener);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void disconnectPrinter() {
        if (printerManager != null) {
            try {
                printerManager.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}
