package com.mpos.sdk.core.control;

import android.content.Context;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.google.crypto.tink.internal.Util;
import com.loopj.android.http.TextHttpResponseHandler;
import com.mpos.sdk.core.common.MyGson;
import com.mpos.sdk.core.common.MyTextHttpResponseHandler;
import com.mpos.sdk.core.model.LanguageCode;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.core.modelma.ConfigSend;
import com.mpos.sdk.core.modelma.LoginRes;
import com.mpos.sdk.core.modelma.LoginSend;
import com.mpos.sdk.core.network.ApiMultiAcquirerInterface;
import com.mpos.sdk.core.network.MposRestClient;
import com.mpos.sdk.util.Constants;
import com.mpos.sdk.util.DevicesUtil;
import com.mpos.sdk.util.KozenSnUtil;
import com.mpos.sdk.util.MacqUtil;
import com.mpos.sdk.util.Utils;

import java.util.Arrays;

import cz.msebera.android.httpclient.Header;
import cz.msebera.android.httpclient.HttpStatus;

/**
 * Create by anhnguyen on 10/11/21
 */
public class LibLoginMacq {

    private static final String TAG = "LibLoginMacq";

    public static final int TYPE_NORMAL             = 1;
    public static final int TYPE_GET_CONFIG         = 2;
    public static final int TYPE_LOGIN_BY_DEVICE    = 3;
    public static final int TYPE_GET_ALL_CONFIG     = 4;


    public static final int FAIL_TYPE_INIT_ONFAIL           = 1;
    public static final int FAIL_TYPE_INIT_FAIL             = 2;
    public static final int FAIL_TYPE_LOGIN_ONFAIL          = 3;
    public static final int FAIL_TYPE_LOGIN_FAIL            = 4;
    public static final int FAIL_TYPE_GET_MC_CONFIG_ONFAIL  = 5;
    public static final int FAIL_TYPE_GET_MC_CONFIG_FAIL    = 6;
    public static final int FAIL_TYPE_VERIFY_SN_ONFAIL      = 7;
    public static final int FAIL_TYPE_VERIFY_SN_FAIL        = 8;
    public static final int FAIL_TYPE_PRE_SALE_ONFAIL        = 9;
    public static final int FAIL_TYPE_PRE_SALE_FAIL        = 10;

    private TextHttpResponseHandler responseHandler;
    private final Context context;
    private final String mAcc;
    private final String mPin;
    private final String serialNumber;
    private String appType;

    private String deviceIdentifier = null;
    private boolean useGetConfigAll = false;

    private LanguageCode languageCode = LanguageCode.LANGUAGE_VI;

    private ItfHandlerResultLoginMacq callback;
    private ItfHandlerGetConfigMacq callbackConfig;

    private int typeProcess = TYPE_NORMAL;

    public LibLoginMacq(@NonNull Context context, @NonNull String mAcc, @NonNull String mPin, @NonNull String serialNumber, @NonNull ItfHandlerResultLoginMacq cb) {
        this.context = context;
        this.mAcc = mAcc;
        this.mPin = mPin;
        this.serialNumber = serialNumber;
        this.callback = cb;
    }

    public LibLoginMacq(@NonNull Context context, @NonNull String mAcc, @NonNull String mPin, @NonNull String serialNumber, @NonNull ItfHandlerGetConfigMacq cb) {
        this.context = context;
        this.mAcc = mAcc;
        this.mPin = mPin;
        this.serialNumber = serialNumber;
        this.callbackConfig = cb;
        this.typeProcess = TYPE_GET_CONFIG;
    }

    public LibLoginMacq(@NonNull Context context, @NonNull String mAcc, @NonNull String serialNumber, @NonNull ItfHandlerResultLoginMacq cb) {
        this.context = context;
        this.mAcc = mAcc;
        this.mPin = null;
        this.serialNumber = serialNumber;
        this.callback = cb;
        this.typeProcess = TYPE_LOGIN_BY_DEVICE;
    }

    public void setLanguageCode(LanguageCode languageCode) {
        if (languageCode != null) {
            this.languageCode = languageCode;
        }
    }

    public String getAppType() {
        return appType;
    }

    public void setAppType(String appType) {
        this.appType = appType;
    }

    public void setDeviceIdentifier(String deviceIdentifier) {
        this.deviceIdentifier = deviceIdentifier;
    }

    public void setUseGetConfigAll(boolean useGetConfigAll) {
        this.useGetConfigAll = useGetConfigAll;
    }

    void initResponseHandler(){
        PrefLibTV.getInstance(context).put(PrefLibTV.HEADER_SESSION_ID, "");

        responseHandler =  new TextHttpResponseHandler() {

            @Override
            public void onFailure(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                Utils.LOGD(TAG, "onFailure() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");
                appendLog("init fail: code=" + statusCode + " msgE=" + (throwable == null ? "can not get throwable" : throwable.toString()));
                showFailMultiAcquirer(FAIL_TYPE_INIT_ONFAIL, statusCode, rawJsonData);
            }

            @Override
            public void onSuccess(int statusCode, Header[] headers, String rawJsonResponse) {
                Utils.LOGD(TAG, "onSuccess() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers)
                        + "], rawJsonResponse = [" + rawJsonResponse + "]" + " typeProcess=" + typeProcess);
                appendLog("init success: " + statusCode + " typeProcess=" + typeProcess);
                if (statusCode == HttpStatus.SC_CREATED) {

                    String sessionId = MacqUtil.getInstance().parseAndDecryptData(rawJsonResponse);
                    MposRestClient.getInstance(context).addHeaderSessionId(sessionId);
                    PrefLibTV.getInstance(context).put(PrefLibTV.HEADER_SESSION_ID, sessionId);
                    appendLog(sessionId);
                    if (typeProcess == TYPE_NORMAL) {
                        loginMultiAcquirer(mAcc, mPin, serialNumber);
                    }
                    else if (typeProcess == TYPE_LOGIN_BY_DEVICE) {
                        loginMacqByDevice();
                    }
                    else {
                        fetchMerchantConfigMacq(null);
                    }
                }
                else {
                    showFailMultiAcquirer(FAIL_TYPE_INIT_FAIL, statusCode, rawJsonResponse);
                }
            }
        };
    }


    public void initAuthenMA() {
        if (responseHandler == null) {
            initResponseHandler();
        }
        ApiMultiAcquirerInterface.getInstance().initAuthen(context, responseHandler);
    }

    public void getAllMerchantConfigMacq(String merchantId) {
        useGetConfigAll = true;
        fetchMerchantConfigMacq(merchantId);
    }
    private void fetchMerchantConfigMacq(String merchantId) {
        ConfigSend configSend = new ConfigSend(languageCode.getLanguageCode(), serialNumber, mAcc, deviceIdentifier, context.getPackageName());
        if (!TextUtils.isEmpty(merchantId)) {
            configSend.setMerchantId(merchantId);
        }
        if (!TextUtils.isEmpty(appType)) {
            configSend.setAppType(appType);
        }
        // todo fake test in P8; only use in P10 + only for mpos app
        if (DevicesUtil.isSP02P10() && !(Utils.checkTypeBuildIsDebug() || Utils.checkTypeBuildIsCertify())
                && (Constants.isTypeIntegrationMpos() || Constants.isTypeIntegrationMpos3rd())) {
            try {
                KozenSnUtil kozenSnUtil = new KozenSnUtil();
                String xxsn = kozenSnUtil.getDeviceUnique();
                if (TextUtils.isEmpty(xxsn)) {
                    boolean resultWrite = kozenSnUtil.writeDeviceUnique();
                    xxsn = kozenSnUtil.getDeviceUnique();
                    appendLog("xxsn is empty, write and get again result="+resultWrite);
                }
                appendLog("xxsn="+xxsn);
                configSend.setDeviceUnique(xxsn);
            } catch (Exception e) {
                appendLog("error sending xxsn: " + e.getMessage());
            }
        }
        String url = useGetConfigAll ? ApiMultiAcquirerInterface.URL_GET_CONFIG_ALL : ApiMultiAcquirerInterface.URL_GET_CONFIG;
        Utils.LOGD(TAG, "getMerchantConfigMacq: " + url);
        MposRestClient.getInstance(context).postMacq(context, url, configSend,
                 new MyTextHttpResponseHandler(context) {

            @Override
            public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                Utils.LOGD(TAG, "getMCConfig onFailure() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");
                appendLog("getMCConfig fail: "+statusCode);

                String clearData = MacqUtil.getInstance().parseAndDecryptData(rawJsonData);
                Utils.LOGD(TAG, "onFailApi: clearContent="+clearData);
                showFailMultiAcquirer(FAIL_TYPE_GET_MC_CONFIG_ONFAIL, statusCode, rawJsonData);
            }

            @Override
            public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                Utils.LOGD(TAG, "getMCConfig onSuccess() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonResponse = [" + rawJsonResponse + "]");
                if (statusCode == HttpStatus.SC_OK) {
                    String clearData = MacqUtil.getInstance().parseAndDecryptData(rawJsonResponse);
                    Utils.LOGD(TAG, "onSuccess getMCConfig: " + clearData);
                    appendLog("getMCConfig success");
                    if (callbackConfig != null) {
                        callbackConfig.onSuccessGetConfigMacq(clearData);
                    }
                } else {
                    showFailMultiAcquirer(FAIL_TYPE_GET_MC_CONFIG_FAIL, statusCode, rawJsonResponse);
                }
            }
        });
    }

    private void loginMacqByDevice() {
        LoginSend loginSend = new LoginSend(mAcc, null, serialNumber);

        Utils.LOGD(TAG, "login by device DataClear: "+ MyGson.getGson().toJson(loginSend));
        Utils.LOGD(TAG, "login by device url: "+ ApiMultiAcquirerInterface.URL_LOGIN_BY_DEVICE);

        PrefLibTV.getInstance(context).put(PrefLibTV.HEADER_AUTHORIZATION_BEARER, "");

        MposRestClient.getInstance(context).postMacq(context, ApiMultiAcquirerInterface.URL_LOGIN_BY_DEVICE, loginSend, new MyTextHttpResponseHandler(context) {

            @Override
            public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                Utils.LOGD(TAG, "loginMacqByDevice onFailure() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");

                String clearData = MacqUtil.getInstance().parseAndDecryptData(rawJsonData);
                Utils.LOGD(TAG, "onFailApi: clearContent="+clearData);
                appendLog("loginMacqByDevice fail: " + statusCode + " -> " + clearData);
                showFailMultiAcquirer(FAIL_TYPE_LOGIN_ONFAIL, statusCode, rawJsonData);
            }

            @Override
            public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                Utils.LOGD(TAG, "loginMacqByDevice onSuccess() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonResponse = [" + rawJsonResponse + "]");
                if (statusCode == HttpStatus.SC_OK) {

                    String clearData = MacqUtil.getInstance().parseAndDecryptData(rawJsonResponse);
                    Utils.LOGD(TAG, "onSuccess loginMacqByDevice: " + clearData);

                    LoginRes loginRes = MyGson.parseJson(clearData, LoginRes.class);
                    PrefLibTV.getInstance(context).put(PrefLibTV.HEADER_AUTHORIZATION_BEARER, loginRes.getToken());

                    MposRestClient.getInstance(context).addHeaderAuthorization(loginRes.getToken());
                    appendLog("loginMacqByDevice success");

                    if (callback != null) {
                        callback.onSuccessLoginMacq(loginRes);
                    }
                } else {
                    showFailMultiAcquirer(FAIL_TYPE_LOGIN_FAIL, statusCode, rawJsonResponse);
                }
            }
        });
    }

    private void loginMultiAcquirer(String user, String pass, String serialNumber) {
        LoginSend loginSend = new LoginSend(user, pass, serialNumber);

        PrefLibTV.getInstance(context).put(PrefLibTV.HEADER_AUTHORIZATION_BEARER, "");

        MposRestClient.getInstance(context).postMacq(context, ApiMultiAcquirerInterface.URL_LOGIN,
                loginSend, new MyTextHttpResponseHandler(context) {

            @Override
            public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                Utils.LOGD(TAG, "Login onFailure() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");
                appendLog("loginMACQ fail: "+statusCode);
                showFailMultiAcquirer(FAIL_TYPE_LOGIN_ONFAIL, statusCode, rawJsonData);
            }

            @Override
            public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                Utils.LOGD(TAG, "Login onSuccess() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonResponse = [" + rawJsonResponse + "]");
                if (statusCode == HttpStatus.SC_OK) {

                    String clearData = MacqUtil.getInstance().parseAndDecryptData(rawJsonResponse);
                    Utils.LOGD(TAG, "onSuccess login: " + clearData);

                    LoginRes loginRes = MyGson.parseJson(clearData, LoginRes.class);
                    PrefLibTV.getInstance(context).put(PrefLibTV.HEADER_AUTHORIZATION_BEARER, loginRes.getToken());
                    CryptoInterface.getInstance().saveKeyToCache(context, PrefLibTV.getInstance(context).getSharedPreferences());

                    MposRestClient.getInstance(context).addHeaderAuthorization(loginRes.getToken());
                    appendLog("loginMACQ success");

                    if (callback != null) {
                        callback.onSuccessLoginMacq(loginRes);
                    }
                } else {
                    showFailMultiAcquirer(FAIL_TYPE_LOGIN_FAIL, statusCode, rawJsonResponse);
                }
            }
        });
    }

    public void verifySerialNumber() {
        LoginSend loginSend = new LoginSend(null, mPin, serialNumber);

        Utils.LOGD(TAG, "verifySerialNumber DataClear: "+ MyGson.getGson().toJson(loginSend));

        MposRestClient.getInstance(context).postMacq(context, ApiMultiAcquirerInterface.URL_VERIFY_SERIAL, loginSend, new MyTextHttpResponseHandler(context) {

            @Override
            public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                Utils.LOGD(TAG, "verifySerialNumber onFailure() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");
                appendLog("verifySerialNumber fail: " + statusCode);

                String clearData = MacqUtil.getInstance().parseAndDecryptData(rawJsonData);
                Utils.LOGD(TAG, "onFailApi: clearContent=" + clearData);
                showFailMultiAcquirer(FAIL_TYPE_VERIFY_SN_ONFAIL, statusCode, rawJsonData);
            }

            @Override
            public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                Utils.LOGD(TAG, "verifySerialNumber onSuccess() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonResponse = [" + rawJsonResponse + "]");
                if (statusCode == HttpStatus.SC_OK) {

                    String clearData = MacqUtil.getInstance().parseAndDecryptData(rawJsonResponse);
                    Utils.LOGD(TAG, "onSuccess verifySerialNumber: " + clearData);

                    LoginRes loginRes = MyGson.parseJson(clearData, LoginRes.class);
                    appendLog("verifySerialNumber success");

                    if (callback != null) {
                        callback.onSuccessLoginMacq(loginRes);
                    }
//                    if (callbackLoginDevice != null) {
//                        callbackLoginDevice.onSuccessLoginDeviceMacq(loginRes);
//                    }
                }
                else {
                    showFailMultiAcquirer(FAIL_TYPE_VERIFY_SN_FAIL, statusCode, rawJsonResponse);
                }
            }
        });

    }
    private void showFailMultiAcquirer(int typeFail, int statusCode, String rawJsonResponse) {
        if (callback != null) {
            callback.onFailLoginMacq(typeFail, statusCode, rawJsonResponse);
        }
        else if (callbackConfig != null) {
            callbackConfig.onFailGetConfigMacq(typeFail, statusCode, rawJsonResponse);
        }
//        else if (callbackLoginDevice != null) {
//            callbackLoginDevice.onFailGetLoginDeviceMacq(statusCode, rawJsonResponse);
//        }
    }

    private void appendLog(String log) {
        if (callback != null) {
            callback.appendLogMacq(log);
        }
        else if (callbackConfig != null) {
            callbackConfig.appendLogMacq(log);
        }
//        else if (callbackLoginDevice != null) {
//            callbackLoginDevice.appendLogMacq(log);
//        }
    }

    public interface ItfHandlerResultLoginMacq{

        void appendLogMacq(String log);
        void onFailLoginMacq(int typeFail, int statusCode, String rawJsonResponse);
        void onSuccessLoginMacq(LoginRes loginRes);
    }

    public interface ItfHandlerGetConfigMacq{
        void appendLogMacq(String log);
        void onFailGetConfigMacq(int typeFail, int statusCode, String rawJsonResponse);
        void onSuccessGetConfigMacq(String data);
    }


}
