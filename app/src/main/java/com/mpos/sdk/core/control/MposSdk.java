package com.mpos.sdk.core.control;


import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;

import com.dspread.xpos.QPOSService;
import com.mpos.sdk.R;
import com.mpos.sdk.core.model.BluetoothReaderPair;
import com.mpos.sdk.core.model.DataPay;
import com.mpos.sdk.core.model.DataPrePay;
import com.mpos.sdk.core.model.MposCustom;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.screen.MposPaymentActivity;
import com.mpos.sdk.util.Constants;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.DevicesUtil;
import com.mpos.sdk.util.Intents;
import com.mpos.sdk.util.Utils;

import org.json.JSONObject;

import static com.mpos.sdk.util.ConstantsPay.FORMAT_UDID_MPOS_SDK;

public class MposSdk {

    private static final String TAG = "MposSdk";
    private String mobileUser;
    private String mobilePass;
    private String appType;
    private String bName;
    private MposCustom mposCustom;
    private ConstantsPay.ReaderType readerType;
    private BluetoothReaderPair bluetoothReaderPair;
    private QPOSService.CardTradeMode cardTradeModePr02;


    // cache data
    private DataPrePay dataPrePayCache;

    private String handlerStatePartner;
    private String handlerActionPayment;
    private String handlerActionTracker;
    private boolean versionLite;
    private boolean isLoginByDevice;

    private Activity activity; // use for startActivityForResult

    private long lastTimeStartActivityPay = 0;
    private int milliSecondBetweenStartActivityPay = 1500; // time*2 for P20L, time for other


    public MposSdk(String mobileUser) {
        this(mobileUser, null);
    }

    public MposSdk(@NonNull String mobileUser, String mobilePass) {
        this(mobileUser, mobilePass,
                DevicesUtil.isP20L() ? ConstantsPay.ReaderType.SP01 :
                        (DevicesUtil.isSP02() ? ConstantsPay.ReaderType.SP02 : (DevicesUtil.isPax() ? ConstantsPay.ReaderType.PAX : ConstantsPay.ReaderType.PR02))
        );
    }

    public MposSdk(String mobileUser, String mobilePass, ConstantsPay.ReaderType readerType) {
        this.mobileUser = mobileUser;
        this.mobilePass = mobilePass;
        this.readerType = readerType;
    }

    /**
     * activity use startActivityForResult
     * @param activity Activity
     */
    public void setActivity(Activity activity) {
        this.activity = activity;
    }

    public void setMposCustom(MposCustom mposCustom) {
        this.mposCustom = mposCustom;
    }

    public void setBluetoothReaderPair(BluetoothReaderPair bluetoothReaderPair) {
        this.bluetoothReaderPair = bluetoothReaderPair;
    }

    public void setAutoConnectDevice(String deviceNumber) {
        if (TextUtils.isEmpty(deviceNumber) || !deviceNumber.startsWith(ConstantsPay.PREFIX_NAME_READER_PR02) || deviceNumber.length()!=14) {
            Utils.LOGD(TAG, "setAutoConnectDevice: device invalid: " + deviceNumber);
            return;
        }
        if (bluetoothReaderPair == null) {
            bluetoothReaderPair = new BluetoothReaderPair(deviceNumber, "");
        }
        else {
            bluetoothReaderPair.setName(deviceNumber);
        }
    }

    public void setHandleState(String handlerStatePartner) {
        this.handlerStatePartner = handlerStatePartner;
    }

    public void setHandlerActionPayment(String handlerActionPayment) {
        this.handlerActionPayment = handlerActionPayment;
    }

    public void setHandlerActionTracker(String handlerActionTracker) {
        this.handlerActionTracker = handlerActionTracker;
    }

    public void setAppType(String appType) {
        if (Constants.isTypeIntegrationMpos() || Constants.isTypeIntegrationMpos3rd()) {
            this.appType = appType;
        }
    }

    /**
     * use BVL, PVI, Emart
     */
    public void setbName(String bName) {
        if (Constants.isTypeIntegrationMpos() || Constants.isTypeIntegrationMpos3rd()) {
            this.bName = bName;
        }
    }

    public void setLoginByDevice(boolean loginByDevice) {
        isLoginByDevice = loginByDevice;
    }

    /**
     * set disable expand action in smartpos SP01
     * @param context context
     * @param arr {@link com.whty.smartpos.tysmartposapi.pos.PosConstrants}
     */
    public void setDisableExpandSP01(Context context, @NonNull int[] arr) {
        if (DevicesUtil.isP20L() && arr.length > 0) {
            LibP20L libP20L = new LibP20L(context);
            libP20L.getSmartPosApi().disableExpand(arr);
        }
    }

    public void setCardTradeModePr02(QPOSService.CardTradeMode cardTradeModePr02) {
        if (Constants.isTypeIntegrationMpos() || Constants.isTypeIntegrationMpos3rd()) {
            this.cardTradeModePr02 = cardTradeModePr02;
        }
    }

    private void checkHaveCustomLanguage(Context context) {
        if (mposCustom != null && !TextUtils.isEmpty(mposCustom.getLanguage())) {
            PrefLibTV.getInstance(context).saveCustomLanguage(mposCustom.getLanguage());
        }
    }

    private boolean isWrongAmount(long amount) {
        return amount <= 0 || 9999999999L < amount;
    }

    //>>>>>>>>------------------------------ ONLY FOR MPOS ------------------------------>>>>>>>>
    /**
     * charge amount (addition data: amountDomestic, amountInternational)
     * only for mpos
     *
     * @param c                   : context
     * @param orderId             : (optional) partner's orderId
     * @param amount              : The amount to be charged in the logged in users.
     * @param amountDomestic      : The amount domestic to be charged in the logged in users.
     * @param amountInternational : The amount international to be charged in the logged in users.
     * @param desc                : (optional): Description for payment detail. Max length 128.
     * @param email               : (optional): Payment system will be send invoice to customer via email.
     * @param udid                : (optional): paymentIdentifier
     * @param requestCode         : request code for startActivityForResult
     * @return paymentIdentifier
     * Exception: invalid amount, not set mobileUser
     */
    public synchronized String chargeAmount(@NonNull Context c, String orderId, long amount, long amountDomestic, long amountInternational, String desc, String email, String udid, Bundle extraParams, int requestCode) throws Exception {
        return chargeAmount(c, orderId, amount, amountDomestic, amountInternational, desc, email, udid, extraParams, requestCode, false);
    }
    public synchronized String chargeAmount(@NonNull Context c, String orderId, long amount, long amountDomestic, long amountInternational, String desc, String email, String udid, Bundle extraParams, int requestCode, boolean skipWaitSignature) throws Exception {
        if (!(Constants.isTypeIntegrationMpos() || Constants.isTypeIntegrationMpos3rd())) {
            throw new Exception(c.getString(R.string.error_not_permission));
        }
        if (isWrongAmount(amount)) {
            throw new Exception(c.getString(R.string.error_wrong_amount));
        }
        else if (TextUtils.isEmpty(mobileUser)) {
            throw new Exception(c.getString(R.string.mp_error_not_found_user_pay));
        }

        checkHaveCustomLanguage(c);

        DataPrePay dataPrePay = createDataPrePay(orderId, amount, amountDomestic, amountInternational, desc, email, udid, "", "");

        Intent intent = getIntentChargeAmount(c, dataPrePay);
        if (extraParams != null) {
            intent.putExtra(Intents.EXTRA_DATA_EXTRA_PARAMS, extraParams);
        }
        // use BVL: check wait signature before goto payment activity
        intent.putExtra(Intents.EXTRA_DATA_SKIP_WAIT_SIGNATURE, skipWaitSignature);
        startActivityForResult(c,intent, requestCode);
        return dataPrePay.getUdid();
    }

    /**
     * charge amount
     * only for mpos
     *
     * @param c           : context
     * @param orderId     : (optional) partner's orderId
     * @param amount      : The amount to be charged in the logged in users.
     * @param desc        : (optional): Description for payment detail. Max length 128.
     * @param email       : (optional): Payment system will be send invoice to customer via email.
     * @param udid        : (optional): paymentIdentifier
     * @param requestCode : request code for startActivityForResult
     *
     * @return paymentIdentifier
     * Exception: invalid amount, not set mobileUser
     */
    public synchronized String chargeAmount(@NonNull Context c, String orderId, long amount, String desc, String email, String udid, int requestCode) throws Exception {
        return chargeAmount(c, orderId, amount, 0, 0, desc, email, udid, null, requestCode);
    }

    public synchronized String chargeAmountDeposit(@NonNull Context c, String orderId, long amount, String phone, String email, String udid, int requestCode) throws Exception {
        if (isWrongAmount(amount)) {
            throw new Exception(c.getString(R.string.error_wrong_amount));
        }
        else if (TextUtils.isEmpty(mobileUser)) {
            throw new Exception(c.getString(R.string.mp_error_not_found_user_pay));
        }

        checkHaveCustomLanguage(c);

        DataPrePay dataPrePay = createDataPrePay(orderId, amount, 0, 0, "", email, udid, phone, ConstantsPay.TRX_TYPE_PAY_DEPOSIT);

        Intent intent = getIntentChargeAmount(c, dataPrePay);
        startActivityForResult(c,intent, requestCode);
        return dataPrePay.getUdid();
    }

    public void setVersionLite(boolean versionLite) {
        if (Constants.isTypeIntegrationMpos() || Constants.isTypeIntegrationMpos3rd()) {
            this.versionLite = versionLite;
        }
    }

    //<<<<<<<<<<------------------------------ ONLY FOR MPOS ------------------------------<<<<<<<<<<

    /**
     * charge amount
     * @param c             : context
     * @param orderId       : (optional) partner's orderId
     * @param amount        : The amount to be charged in the logged in users.
     * @param desc          : (optional): Description for payment detail. Max length 128.
     * @param email         : (optional): Payment system will be send invoice to customer via email.
     * @param requestCode   : request code for startActivityForResult
     * @return paymentIdentifier
     * Exception: invalid amount, not set mobileUser
     */
    public synchronized String chargeAmount(@NonNull Context c, String orderId, long amount, String desc, String email, int requestCode) throws Exception {
        if (isWrongAmount(amount)) {
            throw new Exception(c.getString(R.string.error_wrong_amount));
        }
        else if (TextUtils.isEmpty(mobileUser)) {
            throw new Exception(c.getString(R.string.mp_error_not_found_user_pay));
        }

        checkHaveCustomLanguage(c);

        DataPrePay dataPrePay = createDataPrePay(orderId, amount, desc, email);

        Intent intent = getIntentChargeAmount(c, dataPrePay);
        startActivityForResult(c,intent, requestCode);
        return dataPrePay.getUdid();
    }

    private DataPrePay createDataPrePay(String orderId, long amount, String desc, String email) {
        return createDataPrePay(orderId, amount, 0, 0, desc, email, null, null, null);
    }
    private DataPrePay createDataPrePay(String orderId, long amount, long amountDomestic, long amountInternational, String desc, String email, String udid, String phone, String trxType) {
        DataPrePay dataPrePay = new DataPrePay(amount, desc, email, mobileUser);
        dataPrePay.setMobilePass(mobilePass);
        dataPrePay.setReaderType(readerType);
        dataPrePay.setBluetoothReaderPair(bluetoothReaderPair);
        dataPrePay.setLoginByDevice(isLoginByDevice);
        if (TextUtils.isEmpty(udid)) {
            dataPrePay.setUdid(createUdidByOrderId(orderId));
        }
        else {
            dataPrePay.setUdid(udid);
        }
        dataPrePay.setOrderId(orderId);
        if (amountDomestic > 0 && amountInternational > 0) {
            dataPrePay.setAmountDomestic(amountDomestic);
            dataPrePay.setAmountInternational(amountInternational);
            dataPrePay.setTrxType(ConstantsPay.TRX_TYPE_SERVICE);
        }
        if (Constants.isTypeIntegrationMpos() || Constants.isTypeIntegrationMpos3rd()) {
            dataPrePay.setHandlerStatePartner(handlerStatePartner);
            dataPrePay.setHandlerActionPayment(handlerActionPayment);
            dataPrePay.setHandlerTrackingActionPay(handlerActionTracker);
        }

        if (!TextUtils.isEmpty(trxType)) {
            dataPrePay.setTrxType(trxType);
        }
        if (!TextUtils.isEmpty(phone)) {
            dataPrePay.setPhoneNumber(phone);
        }

        return dataPrePay;
    }


    /**
     * create paymentIdentifier
     * @param orderId       : (optional) partner's orderId
     * @param amount        : The amount to be charged in the logged in users.
     * @param desc          : (optional): Description for payment detail. Max length 128.
     * @param email         : (optional): Payment system will be send invoice to customer via email.
     * @return paymentIdentifier
     */
    public synchronized String genPaymentIdentifier(String orderId, long amount, String desc, String email) throws Exception{
        if (isWrongAmount(amount)) {
            throw new Exception("Invalid amount");
        }
        else if (TextUtils.isEmpty(mobileUser)) {
            throw new Exception("Not found MobileUser");
        }
        dataPrePayCache = createDataPrePay(orderId, amount, desc, email);
        return dataPrePayCache.getUdid();
    }

    /**
     * charge amount with created paymentIdentifier
     * @param c             : context
     * @param requestCode   : request code for startActivityForResult
     *  @throws Exception N/A
     */
    public synchronized void chargeAmountWithGeneratedPI(@NonNull Context c, int requestCode) throws Exception{
        if (dataPrePayCache == null || TextUtils.isEmpty(dataPrePayCache.getUdid())) {
            throw new Exception(c.getString(R.string.mp_error_not_found_dataPrePay_cache));
        }
        else {
            checkHaveCustomLanguage(c);
            Intent intent = getIntentChargeAmount(c, dataPrePayCache);
            startActivityForResult(c,intent, requestCode);
            dataPrePayCache.resetUdid();
        }
    }

    /**
     *
     * @param c: context
     * @param dataPrePay    :
     */
    private Intent getIntentChargeAmount(@NonNull Context c, DataPrePay dataPrePay) {

        Intent intent = buildIntentToMposPayment(c);
        intent.putExtra(Intents.EXTRA_DATA_PRE_PAY, dataPrePay);
//        intent.putExtra(Intents.EXTRA_V_ORDERID, orderId);
//        intent.putExtra(Intents.EXTRA_DATA_SDK_CUSTOM, mposCustom);

        return intent;
    }

    private String createUdidByOrderId(String orderId) {
        return String.format(FORMAT_UDID_MPOS_SDK, mobileUser, TextUtils.isEmpty(orderId) ? "" : orderId, Utils.zenUdid());
    }

    private synchronized void startActivityForResult(Context c, Intent intent, int requestCode) throws Exception{
        if ( (System.currentTimeMillis() - lastTimeStartActivityPay < milliSecondBetweenStartActivityPay)
                || (DevicesUtil.isP20L() && System.currentTimeMillis() - lastTimeStartActivityPay < milliSecondBetweenStartActivityPay*2L)
                || ((DevicesUtil.isSP02P12() || DevicesUtil.isSP02N4()) && System.currentTimeMillis() - lastTimeStartActivityPay < milliSecondBetweenStartActivityPay*2L)
        ) {
            Log.e(TAG, "SDK start payment: " + c.getString(R.string.error_sdk_multi_click));
            if (PrefLibTV.getInstance(c).getPermitSocket()) {
                Utils.LOGD(TAG, "handlerActionPayment: " + handlerActionPayment);
                Intent intentBroadcastAction = new Intent(handlerActionPayment);
                //chỗ này chỉ cần 1 2 feild thông báo code và mess theo format result thôi. ko cần nhiều.
                JSONObject jsonObject = new JSONObject();
//                jsonObject.put("serviceName", "UPDATE_TRANSACTION");
//                jsonObject.put("status", "FAILED");
                jsonObject.put("responseCode", "-6");
                jsonObject.put("responseMsg", "" + c.getString(R.string.error_sdk_multi_click));
                Utils.LOGD(TAG, "sendResultSuccessToEmart: data=" + jsonObject.toString());
                intentBroadcastAction.putExtra(Intents.EXTRA_DATA_BC_ACTION, Constants.AP_ACTION_ERR);
                intentBroadcastAction.putExtra(Intents.EXTRA_DATA_BC_RESULT, jsonObject.toString());
//                c.sendBroadcast(intentBroadcastAction);

//                intentBroadcastAction.putExtra(Intents.EXTRA_DATA_BC_ACTION, Constants.AP_ACTION_CALLBACK_DATA);
                c.sendBroadcast(intentBroadcastAction);
            }
            throw new Exception(c.getString(R.string.error_sdk_multi_click));
        }
        if (Constants.isTypeIntegrationMpos() || Constants.isTypeIntegrationMpos3rd()) {
            intent.putExtra(Intents.EXTRA_DATA_VERSION_LITE, versionLite);
            intent.putExtra(Intents.EXTRA_DATA_APP_TYPE, appType);
            if (cardTradeModePr02 != null) {
                intent.putExtra(Intents.EXTRA_DATA_CARD_TRADE_MODE, cardTradeModePr02);
            }
            if (!TextUtils.isEmpty(bName)) {
                intent.putExtra(Intents.EXTRA_DATA_BNAME, bName);
            }
        }
        lastTimeStartActivityPay = System.currentTimeMillis();
        if (activity != null) {
            activity.startActivityForResult(intent, requestCode);
        }
        else {
            ((Activity) c).startActivityForResult(intent, requestCode);
        }
    }

    /**
     * process Transaction's unsign.
     * @param c                 context
     * @param paymentIdentifier paymentIdentifier return in {@link com.mpos.sdk.core.model.ResultPay}
     * @param requestCode
     * @throws  Exception
     * @see com.mpos.sdk.core.model.ResultPay
     */
    public synchronized void processUnsignedTransaction(@NonNull Context c, @NonNull String paymentIdentifier, int requestCode) throws Exception{
        if (TextUtils.isEmpty(paymentIdentifier)) {
            throw new Exception(c.getString(R.string.error_udid_empty));
        }

        checkHaveCustomLanguage(c);

        Intent intent = getIntentContinueTransaction(c, paymentIdentifier);
        startActivityForResult(c,intent, requestCode);
    }

    public synchronized void processUnsignedTransaction(@NonNull Context c, @NonNull DataPay dataPay, int requestCode) throws Exception{
        if (Constants.isTypeIntegrationMpos() || Constants.isTypeIntegrationMpos3rd()) {
            checkHaveCustomLanguage(c);
            Intent intent = getIntentContinueTransaction(c, dataPay.getUdid());
            intent.putExtra(Intents.EXTRA_DATA_PAY_MP, dataPay);
            startActivityForResult(c,intent, requestCode);
        }
    }


    private Intent getIntentContinueTransaction(@NonNull Context c, String paymentIdentifier) {
        DataPrePay dataPrePay = new DataPrePay(0, mobileUser);
        dataPrePay.setMobilePass(mobilePass);
        dataPrePay.setReaderType(readerType);
        dataPrePay.setBluetoothReaderPair(bluetoothReaderPair);
        dataPrePay.setActionType(MposIntegrationHelper.A_CONTINUE_TRANS);
        dataPrePay.setUdid(paymentIdentifier);
        dataPrePay.setLoginByDevice(isLoginByDevice);

        Intent intent = buildIntentToMposPayment(c);
        intent.putExtra(Intents.EXTRA_DATA_PRE_PAY, dataPrePay);
        return intent;
    }

    private synchronized Intent buildIntentToMposPayment(Context c) {
        Intent intent = new Intent(c, MposPaymentActivity.class);
        intent.putExtra(Intents.EXTRA_DATA_SDK_CUSTOM, mposCustom);
        intent.setPackage(c.getPackageName());
        return intent;
    }


    /**
     * check transaction wait signature
     * @param c
     * @param requestCode
     *  @throws Exception N/A
     */
    private void checkUnSignTransaction(@NonNull Context c, int requestCode) throws Exception{

        checkHaveCustomLanguage(c);

        DataPrePay dataPrePay = new DataPrePay(0, mobileUser);
        dataPrePay.setActionType(MposIntegrationHelper.A_CHECK_UNSIGNATURE_TRANS);
        dataPrePay.setLoginByDevice(isLoginByDevice);

        Intent intent = buildIntentToMposPayment(c);
        startActivityForResult(c,intent, requestCode);
    }

    /**
     * void transaction
     * @param c
     * @param paymentIdentifier paymentIdentifier return in {@link com.mpos.sdk.core.model.ResultPay}
     * @param transId           transId return in {@link com.mpos.sdk.core.model.ResultPay}
     * @param requestCode
     *  @throws Exception N/A
     * @see com.mpos.sdk.core.model.ResultPay
     */
    public synchronized void voidTransaction(@NonNull Context c, @NonNull String paymentIdentifier, @NonNull String transId, int requestCode) throws Exception{
        if (TextUtils.isEmpty(paymentIdentifier)) {
            throw new Exception(c.getString(R.string.error_udid_empty));
        }
        else if (TextUtils.isEmpty(transId)) {
            throw new Exception(c.getString(R.string.error_txid_empty));
        }
        checkHaveCustomLanguage(c);

        DataPrePay dataPrePay = new DataPrePay(0, mobileUser);
        dataPrePay.setMobilePass(mobilePass);
        dataPrePay.setReaderType(readerType);
        dataPrePay.setBluetoothReaderPair(bluetoothReaderPair);
        dataPrePay.setActionType(MposIntegrationHelper.A_VOID_TRANS);
        dataPrePay.setUdid(paymentIdentifier);
        dataPrePay.setTxId(transId);
        dataPrePay.setLoginByDevice(isLoginByDevice);

        Intent intent = buildIntentToMposPayment(c);
        intent.putExtra(Intents.EXTRA_DATA_PRE_PAY, dataPrePay);
        startActivityForResult(c,intent, requestCode);
    }

    public synchronized void getStatus(@NonNull Context c, @NonNull String paymentIdentifier, @NonNull String transId, int requestCode) throws Exception{
        if (TextUtils.isEmpty(paymentIdentifier) && TextUtils.isEmpty(transId)) {
            throw new Exception(c.getString(R.string.error_udid_txid_empty));
        }

        checkHaveCustomLanguage(c);

        DataPrePay dataPrePay = new DataPrePay(0, mobileUser);
        dataPrePay.setMobilePass(mobilePass);
        dataPrePay.setReaderType(readerType);
        dataPrePay.setBluetoothReaderPair(bluetoothReaderPair);
        dataPrePay.setActionType(MposIntegrationHelper.A_GET_STATUS);
        dataPrePay.setUdid(paymentIdentifier);
        dataPrePay.setTxId(transId);
        dataPrePay.setLoginByDevice(isLoginByDevice);

        Intent intent = buildIntentToMposPayment(c);
        intent.putExtra(Intents.EXTRA_DATA_PRE_PAY, dataPrePay);
        startActivityForResult(c,intent, requestCode);
    }

    public synchronized void getListPendingSignature(@NonNull Context c, int requestCode) throws Exception{

        checkHaveCustomLanguage(c);

        DataPrePay dataPrePay = new DataPrePay(0, mobileUser);
        dataPrePay.setMobilePass(mobilePass);
        dataPrePay.setReaderType(readerType);
        dataPrePay.setBluetoothReaderPair(bluetoothReaderPair);
        dataPrePay.setActionType(MposIntegrationHelper.A_GET_LIST_WAIT_SIGN);
        dataPrePay.setLoginByDevice(isLoginByDevice);

        Intent intent = buildIntentToMposPayment(c);
        intent.putExtra(Intents.EXTRA_DATA_PRE_PAY, dataPrePay);
        startActivityForResult(c,intent, requestCode);
    }

    public synchronized void updatePOSConfig(@NonNull Context c, int requestCode) throws Exception{
        updatePOSConfig(c, requestCode, true);
    }
    public synchronized void updatePOSConfig(@NonNull Context c, int requestCode, boolean showDialogConfirm) throws Exception{

        checkHaveCustomLanguage(c);

        DataPrePay dataPrePay = new DataPrePay(0, mobileUser);
        dataPrePay.setMobilePass(mobilePass);
        dataPrePay.setReaderType(readerType);
        dataPrePay.setBluetoothReaderPair(bluetoothReaderPair);
        dataPrePay.setActionType(MposIntegrationHelper.A_UPDATE_POS_CONFIG);
        dataPrePay.setLoginByDevice(isLoginByDevice);

        if (Constants.isTypeIntegrationMpos() || Constants.isTypeIntegrationMpos3rd()) {
            dataPrePay.setShowDialogConfirm(showDialogConfirm);
        }
        Intent intent = buildIntentToMposPayment(c);
        intent.putExtra(Intents.EXTRA_DATA_PRE_PAY, dataPrePay);
        startActivityForResult(c,intent, requestCode);
    }
}
