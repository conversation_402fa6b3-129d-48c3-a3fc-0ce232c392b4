package com.mpos.sdk.core.model;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * Created by AnhNT on 9/1/16.
 */
public class BluetoothReaderPair implements Parcelable {
    public String name;
    public String addr;
    public BluetoothReaderPair(String name, String address){
        this.name = name;
        this.addr = address;
    }

    protected BluetoothReaderPair(Parcel in) {
        name = in.readString();
        addr = in.readString();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAddr() {
        return addr;
    }

    public void setAddr(String addr) {
        this.addr = addr;
    }

    public static final Creator<BluetoothReaderPair> CREATOR = new Creator<BluetoothReaderPair>() {
        @Override
        public BluetoothReaderPair createFromParcel(Parcel in) {
            return new BluetoothReaderPair(in);
        }

        @Override
        public BluetoothReaderPair[] newArray(int size) {
            return new BluetoothReaderPair[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel parcel, int i) {
        parcel.writeString(name);
        parcel.writeString(addr);
    }
}
