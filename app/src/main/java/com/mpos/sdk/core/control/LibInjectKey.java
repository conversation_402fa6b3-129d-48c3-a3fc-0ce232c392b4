package com.mpos.sdk.core.control;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;

import com.dspread.xpos.QPOSService;
import com.mpos.sdk.BuildConfig;
import com.mpos.sdk.core.common.MyGson;
import com.mpos.sdk.core.model.KeyServer;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.core.modelma.IpekRes;
import com.mpos.sdk.core.modelma.TmkKey;
import com.mpos.sdk.util.AppExecutors;
import com.mpos.sdk.util.CardUtils;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.DevicesUtil;
import com.mpos.sdk.util.Utils;
import com.pax.dal.entity.EPedKeyType;
import com.pos.sdk.security.POIHsmManage;
import com.pos.sdk.security.PedKcvInfo;
import com.pos.sdk.security.PedKeyInfo;
import com.kozen.utils.tlv.HexUtil;
import com.pos.sdk.utils.PosByteArray;
import com.whty.smartpos.tysmartposapi.OperationResult;
import com.whty.smartpos.tysmartposapi.pinpad.PinPadConstrants;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;

/**
 * Create by anhnguyen on 5/25/20
 */
public class LibInjectKey extends LibDownloadKey{

    private static final String TAG = "LibInjectKey";


    private LibDspreadReader dspreadControl;

    private LibP20L libP20L;

    private LibPax libPax;

    public LibInjectKey(@NonNull Context context, int typeDevice, int typeServer, @NonNull String serialNumber) {
        this(context, typeDevice, typeServer, serialNumber, null);
    }

    public LibInjectKey(@NonNull Context context, int typeDevice, int typeServer, @NonNull String serialNumber, LibP20L libP20L) {
        super(context, typeDevice, typeServer, serialNumber);

        if (DevicesUtil.isP20L()) {
            initVariableP20l(libP20L);
        }

//        if (DevicesUtil.isPax()) {
//            initPax();
//        }
    }

//    private void initPax() {
//        if (libPax == null) {
//            this.libPax = new LibPax(context);
//        }
//        KEY_PIN_IPEK_POSITION = this.libPax.getKeyPosition(PrefLibTV.getInstance(context).getFlagServer());
//    }

    private void initVariableP20l(LibP20L libP20L) {
        if (libP20L == null) {
            this.libP20L = new LibP20L(context);
        }
        else {
            this.libP20L = libP20L;
        }
        KEY_PIN_IPEK_POSITION = this.libP20L.getKeyIndexByFlagServer(PrefLibTV.getInstance(context).getFlagServer());
    }


    public boolean checkNeedAutoInjectKey(String readerInjected, boolean isRunMacq
            , String serialNumber, String currBankName) {
        try {
            showLogInfo("auto inject -> injected: " + readerInjected);
            String lastBankInject = "";
            if (!TextUtils.isEmpty(readerInjected)) {
                JSONObject jRoot = new JSONObject(readerInjected);
                Iterator<String> keys = jRoot.keys();
                while (keys.hasNext()) {
                    String snInjected = keys.next();
                    if (snInjected.equals(serialNumber)) {
                        lastBankInject = jRoot.getString(snInjected);
                    }
                }
            }
            showLogInfo("isMutilAcquire=" + isRunMacq + " lastBankInjected=" + lastBankInject + " currBank=" + currBankName);
            if (isRunMacq && (TextUtils.isEmpty(lastBankInject) || !lastBankInject.equals(ConstantsPay.MPOS_MULTI_ACQUIRER))) {
//                processInject();
                return true;
            }
            else if (!isRunMacq && (TextUtils.isEmpty(lastBankInject) || !lastBankInject.equals(currBankName))) {
//                processInject();
                return true;
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return false;
    }

    public void saveInjected(String readerInjected, String currBankName) {
        try {
            JSONObject jRoot;
            if (TextUtils.isEmpty(readerInjected)) {
                jRoot = new JSONObject();
            }
            else {
                jRoot = new JSONObject(readerInjected);
            }
            jRoot.put(serialNumber, currBankName);
            showLogInfo("save injected: " + jRoot);
            PrefLibTV.getInstance(context).put(PrefLibTV.readersInjected, jRoot.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void handlerKeyFromBank(String data) {
        KeyServer keyServer = MyGson.parseJson(data, KeyServer.class);
        PrefLibTV.getInstance(context).setSessionKey(keyServer.getSessionKey());
        Utils.LOGD(TAG, "onResponse: key="+keyServer.getFixedKey()+" ksn="+keyServer.getKSN());
        IPEK_FROM_SERVER 	= keyServer.getFixedKey();
        KSN_FROM_SERVER = keyServer.getKSN().replaceAll("[^0-9A-F]", "F");
        Utils.LOGD(TAG, "handlerKeyFromServer: ksn="+KSN_FROM_SERVER);

        PIN_MASTER_KEY_FROM_SERVER = keyServer.getPinMasterKey();
        String kcvFromServer = keyServer.getKcvPinMasterKey();
        StringBuilder kcvBuilder = new StringBuilder(kcvFromServer);
        if (typeDevice != ConstantsPay.DEVICE_KOZEN) {
            if (kcvFromServer.length() / 2 < 8) {
                for (int i = kcvFromServer.length()/2; i<8; i++) {
                    kcvBuilder.append("00");
                }
            }
        }
        KCV_PIN_MASTER_KEY_FROM_SERVER = kcvBuilder.toString();
        Utils.LOGD(TAG, "handlerKeyFromServer: pmk="+PIN_MASTER_KEY_FROM_SERVER+" kcvPmk="+KCV_PIN_MASTER_KEY_FROM_SERVER);

        if (typeDevice == ConstantsPay.DEVICE_DSPREAD) {
            KEY_PIN_IPEK_POSITION = dspreadControl.getKeyIndexByFlagServer(PrefLibTV.getInstance(context).getFlagServer());
            initPosPr02();
            injectIpekKey();
        }
        else if (typeDevice == ConstantsPay.DEVICE_P20L) {
            startInjectP20l(kcvFromServer);
        }
        else if (typeDevice == ConstantsPay.DEVICE_KOZEN) {
            LibKozenP5 libKozenP5 = new LibKozenP5(context);
            KEY_PIN_IPEK_POSITION = libKozenP5.getKeyIndexByFlagServer(PrefLibTV.getInstance(context).getFlagServer()) + LibKozenP5.INDEX_KEY_ADD;
            startInjectSP02();
        } else if (typeDevice == ConstantsPay.DEVICE_PAX) {
            KEY_PIN_IPEK_POSITION = LibPax.getKeyIndexPaxByFlagServer(PrefLibTV.getInstance(context).getFlagServer());
            startInjectPax();
        }
    }

    private void startInjectPax() {
        showLogInfo("startInjectPaxMA");
        injectIPEKPax();
        boolean result = libPax.writekey(EPedKeyType.TMK, KEY_PIN_IPEK_POSITION, PIN_MASTER_KEY_FROM_SERVER, KCV_PIN_MASTER_KEY_FROM_SERVER);
        //todo test because RMK don't exits, RMK only exit index 2
//        boolean result = libPax.writekey(EPedKeyType.TMK, KEY_PIN_IPEK_POSITION, PIN_MASTER_KEY_FROM_SERVER, null);
        showLogInfo( "update TMK -> result=" + result);
        showLogInfo( "update TMK pax-pending review follow inject TMK");
        callbackResult(TYPE_KEY_END, CODE_INJECT_SUCCESS, "Update MA-SP end");
        this.showLoading(false);
    }

    private void injectIPEKPax() {
        boolean result = libPax.writeIpekKey(KEY_PIN_IPEK_POSITION, IPEK_FROM_SERVER, KSN_FROM_SERVER);
        showLogInfo("IpekKey result--> " + (result ? "Success" : "Fail "));
    }

    @Override
    protected void handlerKeyFromMA(IpekRes ipekRes, String ksn) {
        AppExecutors.getInstance().diskIO().execute(() -> {
            showLogInfo(" handlerKey MA: typeDevice="+typeDevice);
            if (typeDevice == ConstantsPay.DEVICE_DSPREAD) {
                initPosPr02();
                processInjectTmkPr02Ma(ipekRes, ksn);
            }
            else if (typeDevice == ConstantsPay.DEVICE_P20L) {
                processInjectP20lMA(ipekRes, ksn);
            }
            else if (typeDevice == ConstantsPay.DEVICE_KOZEN) {
                startInjectSP02MA(ipekRes, ksn);
            }
            else if (typeDevice == ConstantsPay.DEVICE_PAX) {
                startInjectPaxMA(ipekRes, ksn);
            }
        });
//        showLoading(false);
    }

    private void startInjectPaxMA(IpekRes ipekRes, String ksn) {
        showLogInfo("startInjectPaxMA");
        KSN_FROM_SERVER = ksn;
        IPEK_FROM_SERVER = ipekRes.getIpek();
        // todo: don't inject TMK in uat-production => need check: re-inject alot of times
        if (ipekRes.getTmkKeys() != null && ipekRes.getTmkKeys().size() > 0) {
            boolean result;
            for (TmkKey tmkKey : ipekRes.getTmkKeys()) {
                try {
                    String prefix_pax = LibPax.PREFIX_SN_PAX;
                    if (BuildConfig.DEBUG) {
                        prefix_pax = "SP01";
                    }
                    if ((typeDevice == ConstantsPay.DEVICE_PAX && tmkKey.getReaderType().equals(prefix_pax))) {
                        KEY_PIN_IPEK_POSITION = LibPax.getKeyIndexByShortBankName(tmkKey.getBankCode());
                        showLogInfo(" bank=" + tmkKey.getBankCode() + " ->pos=" + KEY_PIN_IPEK_POSITION);
                        KCV_PIN_MASTER_KEY_FROM_SERVER = tmkKey.getKcv();
                        PIN_MASTER_KEY_FROM_SERVER = tmkKey.getEncryptedTMK();
                        result = libPax.writekey(EPedKeyType.TMK, KEY_PIN_IPEK_POSITION, PIN_MASTER_KEY_FROM_SERVER, KCV_PIN_MASTER_KEY_FROM_SERVER);
                        showLogInfo("TMK result--> " + (result ? "Success" : "Fail "));
                        // inject ipek MACQ to all group
                        result = libPax.writeIpekKey(KEY_PIN_IPEK_POSITION, IPEK_FROM_SERVER, KSN_FROM_SERVER);
                        showLogInfo("IpekKey result--> " + (result ? "Success" : "Fail "));
                    }
                } catch (Exception e) {
                    Utils.LOGD(TAG, "startInjectPax: e= " + e);
                }
            }
        }
        callbackResult(TYPE_KEY_END, CODE_INJECT_SUCCESS, "Update MA-PAX Complete");
        this.showLoading(false);
    }

    List<TmkKey> arrTmkKeys;
    int currIndexTmkKey = 0;
    private void processInjectTmkPr02Ma(IpekRes ipekRes, String ksn) {
        showLogInfo("startInjectPr02MA");
        KSN_FROM_SERVER = ksn;
        IPEK_FROM_SERVER = ipekRes.getIpek();
        if (ipekRes.getTmkKeys() != null && ipekRes.getTmkKeys().size() > 0) {
            arrTmkKeys = new ArrayList<>();
            for (TmkKey tmkKey : ipekRes.getTmkKeys()) {
                if (typeDevice == ConstantsPay.DEVICE_DSPREAD && tmkKey.getReaderType().equals(ConstantsPay.ReaderType.PR02.toString())) {
                    arrTmkKeys.add(tmkKey);
                }
            }
            sortOrderByKey();

            injectTmkPr02Ma();
        }
    }

    private void injectNextTmkMa() {
        currIndexTmkKey++;
        injectTmkPr02Ma();
    }
    private void injectTmkPr02Ma() {
        if (currIndexTmkKey < arrTmkKeys.size()) {
            TmkKey tmkKey = arrTmkKeys.get(currIndexTmkKey);
            KEY_PIN_IPEK_POSITION = getKeyIndexByShortBankName(tmkKey.getBankCode());
            KCV_PIN_MASTER_KEY_FROM_SERVER = tmkKey.getKcv();
            PIN_MASTER_KEY_FROM_SERVER = tmkKey.getEncryptedTMK();
            injectPinKey();
        } else {
            callbackResult(TYPE_KEY_END, CODE_INJECT_SUCCESS, "Update MA-PR02 Success");
        }
    }

    private void processInjectP20lMA(IpekRes ipekRes, String ksn) {
        showLogInfo("startInjectP20lMA");
        KSN_FROM_SERVER = ksn;
        IPEK_FROM_SERVER = ipekRes.getIpek();
        updateP20lKSN();
        if (ipekRes.getTmkKeys() != null && ipekRes.getTmkKeys().size() > 0) {

            arrTmkKeys = new ArrayList<>();
            for (TmkKey tmkKey : ipekRes.getTmkKeys()) {
                if ((typeDevice == ConstantsPay.DEVICE_P20L && tmkKey.getReaderType().equals(ConstantsPay.ReaderType.SP01.toString()))){
                    arrTmkKeys.add(tmkKey);
                }
            }
            sortOrderByKey();

            for (TmkKey tmkKey : arrTmkKeys) {
                KEY_PIN_IPEK_POSITION = getKeyIndexByShortBankName(tmkKey.getBankCode());
                showLogInfo(" bank=" + tmkKey.getBankCode()+" ->pos=" + KEY_PIN_IPEK_POSITION );
                KCV_PIN_MASTER_KEY_FROM_SERVER = tmkKey.getKcv();
                PIN_MASTER_KEY_FROM_SERVER = tmkKey.getEncryptedTMK();
                injectP20lTMK(false);
                updateP20lIPEK();
            }
        }
        callbackResult(TYPE_KEY_END, CODE_INJECT_SUCCESS, "Update MA-SP Success");
    }

    private void sortOrderByKey() {
        Collections.sort(arrTmkKeys, (o1, o2) -> o1.getOrderBy() - o2.getOrderBy());
    }

    private int getKeyIndexByShortBankNameForSP02(String shortName) {
        int index = getKeyIndexByShortBankName(shortName);
        if (!shortName.equals(ConstantsPay.SHORTNAME_NEXTPAY)) {
            index+= LibKozenP5.INDEX_KEY_ADD;
        }
        return index;
    }
    private int getKeyIndexByShortBankName(String shortName) {
        switch (shortName) {
            case ConstantsPay.SHORTNAME_SACOMBANK:
                return LibDspreadReader.POSITION_KEY_STB;
            case ConstantsPay.SHORTNAME_VIETINBANK:
                return LibDspreadReader.POSITION_KEY_VTB;
            case ConstantsPay.SHORTNAME_BIDV:
                return LibDspreadReader.POSITION_KEY_BIDV;
            case ConstantsPay.SHORTNAME_VIETCOMBANK:
                return LibDspreadReader.POSITION_KEY_VCB;
            case ConstantsPay.SHORTNAME_TECHCOMBANK:
                return LibDspreadReader.POSITION_KEY_TCB;
            case ConstantsPay.SHORTNAME_NEXTPAY:
                return LibDspreadReader.POSITION_KEY_NEXTPAY;
            default:
                return -1;
        }
    }

    /**
     * wait inject one bank
     */
    private final String STATUS_OK 				= "OK";
    private final String STATUS_FAIL 			= "FAIL";

    private final String[] waitInject = new String[]{STATUS_FAIL};

    private void waitInjectBank() {
        Utils.LOGD(TAG, "waitInjectBank: ");
        showLogInfo("wait result inject");
        synchronized (waitInject) {
            try {
                waitInject.wait();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void responseInjectKeySuccess() {
        responseInjectKey(STATUS_OK);
    }

    private void responseInjectKeyFail() {
        responseInjectKey(STATUS_FAIL);
    }

    private void responseInjectKey(String status) {
        waitInject[0] = status;
        synchronized (waitInject) {
            waitInject.notify();
        }
    }

    public void setLibPax(LibPax libPax) {
        this.libPax = libPax;
    }

    public void setDspreadControl(LibReaderController dspreadControl) {
        this.dspreadControl = (LibDspreadReader) dspreadControl;
    }

    public void setPos(QPOSService pos) {
//        this.pos = pos;
    }

    /**----------*----------* dspread PR02 *----------*----------*
     *      get + inject KEY
     *----------*----------*----------*----------*----------*/
    private void initPosPr02() {
        if (dspreadControl == null) {
            showLogInfo("not set setDspreadControl");
            return;
        }
        else if (dspreadControl.getPos() == null) {
            showLogInfo("pos is null");
            return;
        }
        dspreadControl.setCbResultInject(resultInject);

        showLogInfo("initPosPr02 pos success");
//        injectIpekKey();
    }

    private void injectIpekKey() {
        Utils.LOGD(TAG, "begin inject IPEK");

        Utils.LOGD(TAG, "onOptionsItemSelected: inject with key from server: " + IPEK_FROM_SERVER + " - ksn=" + KSN_FROM_SERVER + " -position:" + KEY_PIN_IPEK_POSITION);
        showLogInfo("inject IPEK: " + KEY_PIN_IPEK_POSITION);

        dspreadControl.getPos().doUpdateIPEKOperation(String.valueOf(KEY_PIN_IPEK_POSITION),
                KSN_FROM_SERVER, IPEK_FROM_SERVER, "0000000000000000",
                KSN_FROM_SERVER, IPEK_FROM_SERVER, "0000000000000000",
                KSN_FROM_SERVER, IPEK_FROM_SERVER, "0000000000000000");

    }

    private void injectPinKey() {
        Utils.LOGD(TAG, "begin inject --FROM SERVER-- Pin MasterKey: " +
                " encPMKbyTMK=" + PIN_MASTER_KEY_FROM_SERVER
                + " kcv=" + KCV_PIN_MASTER_KEY_FROM_SERVER
                + " pos=" + KEY_PIN_IPEK_POSITION);

        showLogInfo("inject TMK: "+KEY_PIN_IPEK_POSITION);

//        dspreadControl.requestDeviceStartPayment();
        dspreadControl.getPos().setMasterKey(PIN_MASTER_KEY_FROM_SERVER, KCV_PIN_MASTER_KEY_FROM_SERVER, KEY_PIN_IPEK_POSITION, 10);

        Utils.LOGD(TAG, "end inject Pin MasterKey");

    }

    LibDspreadReader.ResultInject resultInject = new LibDspreadReader.ResultInject() {
        @Override
        public void onReturnUpdateIPEKResult(boolean result) {
            String newText = "Inject IPEK(key data)";//statusEditText.getText().toString();

            if(result){
                newText+= " success";
            }
            else{
                newText+= " failed";
            }

            showLogInfo(newText);

            if (processMA) {
                injectNextTmkMa();
//                processInjectTmkMa();
            }
            else {
                injectPinKey();
            }
        }

        @Override
        public void onReturnSetMasterKeyResult(boolean isSuccess) {
            Utils.LOGD(TAG, "onReturnSetMasterKeyResult: " + isSuccess);
            String text = "set Master Key(Key Pin) result: ";
            if (isSuccess) {
                text+= "Success";
            }
            else {
                text += "Failed(Có thể đã success trước đó ->Thử quẹt lại thẻ có PIN)" +
                        "->Kiểm tra KCV...";
            }
            showLogInfo(text);

            if (processMA) {
//                injectNextTmkMa();
                injectIpekKey();
            }
            else {
                callbackResultPr02(isSuccess, text);
                callbackResult(TYPE_KEY_END, CODE_INJECT_SUCCESS, "Update PR02 Success");
            }

        }

        @Override
        public void onGetKeyCheckValue(List<String> list) {
            Utils.LOGD(TAG, "onGetKeyCheckValue: ");
            StringBuilder kcv = new StringBuilder();
            if (list != null && list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    kcv.append(list.get(i)).append(",");
                    Utils.LOGD(TAG, "onGetKeyCheckValue: -->" + list.get(i));
                }
            }
            else {
                Utils.LOGD(TAG, "onGetKeyCheckValue: ----> empty");
            }
            Utils.LOGD(TAG, "onGetKeyCheckValue: kcv=" + kcv + " kcvServer=" + KCV_PIN_MASTER_KEY_FROM_SERVER);
            String threeByteKcv = TextUtils.isEmpty(KCV_PIN_MASTER_KEY_FROM_SERVER) ? "" : KCV_PIN_MASTER_KEY_FROM_SERVER.substring(0, 6);
            showLogInfo("curr:"+kcv + "->" + threeByteKcv);
            if (kcv.toString().contains(threeByteKcv)) {
                showLogInfo("đã inject TMK success trước đó");
            } else {
                showLogInfo("chưa inject TMK");
            }
        }

        @Override
        public void onErrorInject(QPOSService.Error errorState) {
            Utils.LOGD(TAG, "onErrorInject: " + errorState.toString());
            if (processMA) {
                injectNextTmkMa();
            }
            else {
                callbackResultPr02(false, errorState.toString());
            }
        }

        private void callbackResultPr02(boolean success, String msg) {
            callbackResult(TYPE_KEY_TMK, success ? 1 : 0, msg);
        }
    };

    /**----------*----------* smart-pos p20l *----------*----------*
     *      get + inject KEY
     *----------*----------*----------*----------*----------*/
    String posKcvP20LIpek = "00000000";
    int lengthKcvP20L = 6;  //3byte

    private String getKcvP20L() {
        OperationResult operationResult = libP20L.getSmartPosApi().getCheckValue(KEY_PIN_IPEK_POSITION, PinPadConstrants.KEY_ID_FIRST_MAINKEY);
        String resultData = operationResult.getData();
        Utils.LOGD(TAG, "keyPosition="+KEY_PIN_IPEK_POSITION+" getKCV: data="+resultData+" status="+operationResult.getStatusCode());
        return resultData.substring(0, lengthKcvP20L);
    }

    private void startInjectP20l(String kcvFromServer) {
        updateP20lKSN();
        updateP20lIPEK();
        String kcvFromDevice = getKcvP20L();
        kcvFromServer = kcvFromServer.substring(0, lengthKcvP20L);
        if (kcvFromServer.equals(kcvFromDevice)) {
            callbackResult(TYPE_KEY_TMK, CODE_INJECT_SUCCESS, "Update TMK Success(injected)");
        }
        else {
            injectP20lTMK();
        }
        callbackResult(TYPE_KEY_END, CODE_INJECT_SUCCESS, "Update SP Success");
        showLoading(false);
    }

    public void startInjectP20lKsnIpek(String ksn, String ipek) {
        KSN_FROM_SERVER = ksn;
        IPEK_FROM_SERVER = ipek;
        updateP20lKSN();
        updateP20lIPEK();
    }

    private void updateP20lKSN() {
        showLogInfo(">>> DUKPT updateKSN <<< pos="+KEY_PIN_IPEK_POSITION);
        Utils.LOGD(TAG, "updateKSN: ----->>"+KSN_FROM_SERVER);

        selectKeyGroupInject(25); // group 25: default main key is 16*0xFF

        OperationResult operationResult = libP20L.getSmartPosApi().dukptUpdateKSN(PinPadConstrants.KSN_UPDATE, KSN_FROM_SERVER);
//      operationResult = smartPosApi.dukptUpdateKSN(PinPadConstrants.KSN_UPDATE, "FFFF9876543210E00001");
        showLogInfo("result status code : " + operationResult.getStatusCode());
//                36864 can be changed into hex from oct
//                36864 -> 9000 which means success
        int statusCode = operationResult.getStatusCode();
        String msg;
        if (9000 <=  statusCode && statusCode <= 36864) {
            showLogInfo("--updateKSN success--");
            msg = "Update KSN Success";
        }
        else {
            showLogInfo("--updateKSN fail--");
            msg = "Update KSN  Fail (" + statusCode+")";
        }
        callbackResult(TYPE_KEY_KSN, statusCode, msg);

        selectKeyGroupInject(KEY_PIN_IPEK_POSITION);

//        showLogInfo();
    }

    private void selectKeyGroupInject(int keyPosition) {
        libP20L.getSmartPosApi().selectKeyGroup(keyPosition);
    }

    private void updateP20lIPEK() {
        String oldKcvIpek = libP20L.getPosKcvP20LIpek(KEY_PIN_IPEK_POSITION);

        int res = libP20L.getSmartPosApi().dukptUpdateIPEK(IPEK_FROM_SERVER + posKcvP20LIpek);
        showLogInfo("IPEK result--> " + (res == CODE_INJECT_SUCCESS ? "Success" : "Fail " + res)+" -> "
                + (Utils.checkTypeBuildIsCertify()?IPEK_FROM_SERVER:CardUtils.getMaskedPan(IPEK_FROM_SERVER)));

        String newKcvIpek = libP20L.getPosKcvP20LIpek(KEY_PIN_IPEK_POSITION);
        showLogInfo("IPEK-KCV: old=" + oldKcvIpek + " new=" + newKcvIpek + " server=" + posKcvP20LIpek);

        callbackResult(TYPE_KEY_IPEK, res, res == CODE_INJECT_SUCCESS ? "Update IPEK Success" : "Update IPEK Fail (" + res+")");
    }

    private void injectP20lTMK() {
        injectP20lTMK(true);
    }

    private void injectP20lTMK(boolean callback) {
        String kcvP20l = KCV_PIN_MASTER_KEY_FROM_SERVER.substring(0, 6) + "00";
        Utils.LOGD(TAG, "injectTMK: --->index="+KEY_PIN_IPEK_POSITION+" kcv="+kcvP20l);
        selectKeyGroupInject(KEY_PIN_IPEK_POSITION);

        int res = libP20L.getSmartPosApi().updateMainKey(PIN_MASTER_KEY_FROM_SERVER, kcvP20l);

        showLogInfo("TMK result--> " + (res == CODE_INJECT_SUCCESS ? "Success" : "Fail " + res));
        if (res != CODE_INJECT_SUCCESS) {// && !Utils.checkTypeBuildIsRelease()
            String oldKcvTmk = libP20L.getPosKcvP20LTMK(KEY_PIN_IPEK_POSITION);
            showLogInfo("old KcvTmk=" + oldKcvTmk+" new KcvTmk=" + kcvP20l);
        }

        if (callback) {
            callbackResult(TYPE_KEY_TMK, res, res == CODE_INJECT_SUCCESS ? "Update TMK Success" : "Update TMK Fail (" + res+")");
        }
    }

    private String getPosKcvP20LIpek(int keyPosition) {
        OperationResult operationResultIpek = libP20L.getSmartPosApi().getCheckValue(keyPosition, PinPadConstrants.KEY_ID_FIRST_TDKEY);
        return operationResultIpek.getData();
    }

    /**----------*----------* Kozen SP02 *----------*----------*
     *      get + inject KEY
     *----------*----------*----------*----------*----------*/
//    public static String PIN_MASTER_KEY_OLD = "0123456789abcdeffedcba9876543210";// default

    private void startInjectSP02() {
        injectIPEKSP02();
        int result = updateSP02Tmk(KEY_PIN_IPEK_POSITION, HexUtil.parseHex(PIN_MASTER_KEY_FROM_SERVER), HexUtil.parseHex(KCV_PIN_MASTER_KEY_FROM_SERVER));
        showLogInfo( "update TMK -> result=" + result);
        callbackResult(TYPE_KEY_END, CODE_INJECT_SUCCESS, "Update MA-SP end");
    }
    private void startInjectSP02MA(IpekRes ipekRes, String ksn) {
        showLogInfo("startInject SP02 MA");
        KSN_FROM_SERVER = ksn;
        IPEK_FROM_SERVER = ipekRes.getIpek();
        if (ipekRes.getTmkKeys() != null && ipekRes.getTmkKeys().size() > 0) {
            boolean needGetKcv = false;
            for (TmkKey tmkKey : ipekRes.getTmkKeys()) {
                if ((typeDevice == ConstantsPay.DEVICE_KOZEN && tmkKey.getReaderType().equals(ConstantsPay.ReaderType.SP02.toString()))) {
                    KEY_PIN_IPEK_POSITION = getKeyIndexByShortBankNameForSP02(tmkKey.getBankCode());
                    KCV_PIN_MASTER_KEY_FROM_SERVER = tmkKey.getKcv();
                    PIN_MASTER_KEY_FROM_SERVER = tmkKey.getEncryptedTMK();
                    injectIPEKSP02();

                    int result = updateSP02Tmk(KEY_PIN_IPEK_POSITION, HexUtil.parseHex(PIN_MASTER_KEY_FROM_SERVER), HexUtil.parseHex(KCV_PIN_MASTER_KEY_FROM_SERVER));
//                    update TMK -> result=65211 | FEBB(-325) | PED_RET_ERR_KCV_CHECK_FAIL 	        | KCV checksum failed		=>> key not match	==> key injected
//                    update TMK -> result=65235 | FED3(-301) | PED_RET_ERR_NO_KEY 			        | The key does not exist	=>> haven't key
//                    update TMK -> result=65204 | FEB4(-332) | PED_RET_ERR_KCV_ODD_CHECK_FAIL KCV  | odd parity failed
                    if (result != 0) {
//                    if (result == 65235 || result == 65204) {
                        needGetKcv = true;
                    }
                    showLogInfo("update TMK: result=" + result + " kcv=" + KCV_PIN_MASTER_KEY_FROM_SERVER);
                }
            }
            if (needGetKcv) {
                StringBuilder logKcv = new StringBuilder();
                for (int i = 1; i < 10; i++) {
                    try {
                        logKcv.append(getKcvFromGroup(i)).append(" | ");
                    } catch (Exception e) {
                        logKcv.append("index: ").append(i).append(" error: ").append(e.getMessage()).append(" | ");
                    }
                }
                showLogInfo(logKcv.toString());
            }
        }
        callbackResult(TYPE_KEY_END, CODE_INJECT_SUCCESS, "Update MA-SP end");
    }

    private void injectIPEKSP02() {
        int result = writeSP02DukptKey(KEY_PIN_IPEK_POSITION, IPEK_FROM_SERVER, KSN_FROM_SERVER);
        showLogInfo("injectIPEK: result=" + result + " index=" + KEY_PIN_IPEK_POSITION);
    }

    private int writeSP02DukptKey(int keyIndex, String keyData, String ksnData) {
//        showLogInfo( "writeDukptKey() called with: keyIndex = [" + keyIndex + "], keyData = [" + keyData + "], ksnData = [" + ksnData + "]");
        PedKcvInfo kcvInfo = new PedKcvInfo(0, new byte[5]);
        return POIHsmManage.getDefault().PedWriteTIK(keyIndex, 0, 16, HexUtil.parseHex(keyData), HexUtil.parseHex(ksnData), kcvInfo);
    }

    public int updateSP02ClearTmk(int keyIdx, byte[] data, byte[] aucCheckBufIn) {
        byte[] kcv = new byte[aucCheckBufIn.length + 1];
        kcv[0] = (byte) aucCheckBufIn.length;
        System.arraycopy(aucCheckBufIn, 0, kcv, 1, aucCheckBufIn.length);

        PedKeyInfo pedKeyInfo = new PedKeyInfo(0, 0, 0x02, keyIdx, 0, data.length, data);
        PedKcvInfo pedKcvInfo = new PedKcvInfo(1, kcv);
        return POIHsmManage.getDefault().PedWriteKey(pedKeyInfo, pedKcvInfo);
    }

    public int updateSP02Tmk(int keyIdx, byte[] data, byte[] aucCheckBufIn) {
        Utils.LOGD(TAG, "updateSP02Tmk: ");
        try {
            byte[] kcv = new byte[aucCheckBufIn.length + 1];
            kcv[0] = (byte) aucCheckBufIn.length;
            System.arraycopy(aucCheckBufIn, 0, kcv, 1, aucCheckBufIn.length);

            PedKeyInfo pedKeyInfo = new PedKeyInfo(0x02, keyIdx, 0x02, keyIdx,
                    POIHsmManage.PED_CALC_DES_MODE_ECB_DEC, data.length, data);
            PedKcvInfo pedKcvInfo = new PedKcvInfo(1, kcv);
            Utils.LOGD(TAG, "updateSP02Tmk: -----11-----------");
            int result = POIHsmManage.getDefault().PedWriteKey(pedKeyInfo, pedKcvInfo);
            String log = ("1st result="+result);
            // comment not use, because sometimes and some devices got error: inject successfully all time, and don't know what is TMK key
            if (result == 65204) {
                log += "--inject key error--";
//                PedKcvInfo pedKcvInfoRetry = new PedKcvInfo(1, aucCheckBufIn);
//                result = POIHsmManage.getDefault().PedWriteKey(pedKeyInfo, pedKcvInfoRetry);
//                log += ( " 2nd result="+result+ " key="+HexUtil.toHexString(data));
//                log += " kcv injected: "+getKcvFromGroup(keyIdx);
                String kcvFromServer = HexUtil.toHexString(aucCheckBufIn);
                if (DevicesUtil.isSP02P12() && kcvFromServer.equals("EFA0B5")) {
                    // try kcv 5byte
                    String kcv5byte = "05EFA0B54945";
                    PedKcvInfo kcvInfo = new PedKcvInfo(0x01, HexUtil.parseHex(kcv5byte));
                    result = POIHsmManage.getDefault().PedWriteKey(pedKeyInfo, kcvInfo);
                    log += " try 5byte kcv: result=" + result;
                    if (result == 65204) {
                        showLogInfo(log);
                        log = "";
                        result = testInjectHardCodeKeyMacq();
                    }
                }
            }
            if (!TextUtils.isEmpty(log)) {
                showLogInfo(log);
            }
            return result;
        } catch (Exception e) {
            showLogInfo("Kozen error inject TMK: "+e.getMessage());
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    private int testInjectHardCodeKeyMacq() {
        String tmkEncrypted = "AF622AF3AABA49DFD26C50E9CB1A0E48";
        String kcv = "03EFA0B5";
        int index = 9;
        // STB index 1
        PedKeyInfo keyInfo = new PedKeyInfo(0x02, index, 0x02, index,
                POIHsmManage.PED_CALC_DES_MODE_ECB_DEC, 0x10, HexUtil.parseHex(tmkEncrypted));
        PedKcvInfo kcvInfo = new PedKcvInfo(0x01, HexUtil.parseHex(kcv));


        int result = POIHsmManage.getDefault().PedWriteKey(keyInfo, kcvInfo);
        showLogInfo("inject hardcode result=" + result+" index=" + index);
        return result;
    }

    private String getKcvFromGroup(int index) throws Exception{
        PedKcvInfo pedKcvInfo = new PedKcvInfo(0, new byte[5]);

        byte[] dataOut = new byte[5];
        PosByteArray rspBuf = new PosByteArray(dataOut, dataOut.length);

        String result = "index: " + index;
        // param functin: PedGetKcv => keyType, index
        // keyType = 2 => TMK
        if (POIHsmManage.getDefault().PedGetKcv(2, index, pedKcvInfo, rspBuf) == 0) {
//        if (POIHsmManage.getDefault().PedGetKcv(3, 5, pedKcvInfo, rspBuf) == 0) {
//        if (POIHsmManage.getDefault().PedGetKcv(2, 5, pedKcvInfo, rspBuf) == 0) { // kcv tmk
//            Log.d(TAG, "onClickTest: ===> TRUE");
            if (rspBuf.buffer != null) {
                Log.d(TAG, "onClickTest: "+index+" =>" + HexUtil.toHexString(rspBuf.buffer));
//                Log.d(TAG, "onClickTest: =>" + rspBuf.toString());
//                showLogInfo("index: " + index+ " kcv: " + HexUtil.toHexString(rspBuf.buffer));
                result += " kcv: " + HexUtil.toHexString(rspBuf.buffer);
            }
        }
        else {
//            Log.d(TAG, "onClickTest: ===> FALSE");
//            showLogInfo("index: " + index+ " kcv: can not get");
            result += " kcv: can not get";

        }

        return result;
    }

}
