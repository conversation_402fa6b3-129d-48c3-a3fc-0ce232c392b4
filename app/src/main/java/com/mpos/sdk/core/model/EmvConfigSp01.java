package com.mpos.sdk.core.model;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * Create by anhnguyen on 4/14/21
 */
public class EmvConfigSp01 {

    // sp01
    @SerializedName("aid")
    @Expose
    private List<String> aid = null;
    @SerializedName("aidContactless")
    @Expose
    private List<String> aidContactless = null;
    @SerializedName("capk")
    @Expose
    private List<String> capk = null;

    // sp02
    @SerializedName("appSP02")
    @Expose
    private List<AppSp02> appSP02 = null;
    @SerializedName("capkSP02")
    @Expose
    private List<CapkSp02> capkSP02 = null;
    @SerializedName("emvConfigSP02")
    @Expose
    private List<EmvConfigSP02> emvConfigSP02 = null;

    public List<String> getAid() {
        return aid;
    }

    public void setAid(List<String> aid) {
        this.aid = aid;
    }

    public List<String> getCapk() {
        return capk;
    }

    public void setCapk(List<String> capk) {
        this.capk = capk;
    }

    public List<AppSp02> getAppSP02() {
        return appSP02;
    }

    public void setAppSP02(List<AppSp02> appSP02) {
        this.appSP02 = appSP02;
    }

    public List<CapkSp02> getCapkSP02() {
        return capkSP02;
    }

    public void setCapkSP02(List<CapkSp02> capkSP02) {
        this.capkSP02 = capkSP02;
    }

    public List<String> getAidContactless() {
        return aidContactless;
    }

    public void setAidContactless(List<String> aidContactless) {
        this.aidContactless = aidContactless;
    }

    public List<EmvConfigSP02> getEmvConfigSP02() {
        return emvConfigSP02;
    }

    public void setEmvConfigSP02(List<EmvConfigSP02> emvConfigSP02) {
        this.emvConfigSP02 = emvConfigSP02;
    }
}
