package com.mpos.sdk.core.control;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.content.Context;
import android.content.Intent;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.widget.Toast;

import com.dspread.xpos.QPOSService;
import com.mpos.dspread.DeviceListActivity;
import com.mpos.dspread.QPOSUtil;
import com.mpos.sdk.BuildConfig;
import com.mpos.sdk.R;
import com.mpos.sdk.core.model.BluetoothReaderPair;
import com.mpos.sdk.core.model.DataError;
import com.mpos.sdk.core.model.DataPrePay;
import com.mpos.sdk.core.model.DataProcessCard;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.core.model.TagLengthValue;
import com.mpos.sdk.core.modelma.DataNotifyPay;
import com.mpos.sdk.core.modelma.DataSaleRes;
import com.mpos.sdk.core.modelma.DataSaleSend;
import com.mpos.sdk.core.mposinterface.MposPr02Listener;
import com.mpos.sdk.util.CardUtils;
import com.mpos.sdk.util.ConsErrorDspread;
import com.mpos.sdk.util.Constants;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.HexUtil;
import com.mpos.sdk.util.Intents;
import com.mpos.sdk.util.MyTextUtils;
import com.mpos.sdk.util.PayUtils;
import com.mpos.sdk.util.Utils;
import com.mpos.sdk.util.UtilsSystem;
import com.pos.sdk.emvcore.POIEmvCoreManager;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.lang.ref.WeakReference;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Hashtable;
import java.util.List;
import java.util.Set;

import cz.msebera.android.httpclient.util.ByteArrayBuffer;

import static com.mpos.sdk.util.ConsErrorDspread.CANCEL;
import static com.mpos.sdk.util.ConsErrorDspread.CAPK_FAIL;
import static com.mpos.sdk.util.ConsErrorDspread.CARD_BLOCKED_OR_NO_EMV_APPS;
import static com.mpos.sdk.util.ConsErrorDspread.CARD_NOT_SUPPORTED;
import static com.mpos.sdk.util.ConsErrorDspread.DECLINED;
import static com.mpos.sdk.util.ConsErrorDspread.DEVICE_ERROR;
import static com.mpos.sdk.util.ConsErrorDspread.INVALID_ICC_DATA;
import static com.mpos.sdk.util.ConsErrorDspread.MISSING_MANDATORY_DATA;
import static com.mpos.sdk.util.ConsErrorDspread.NFC_TERMINATED;
import static com.mpos.sdk.util.ConsErrorDspread.NOT_ICC;
import static com.mpos.sdk.util.ConsErrorDspread.SELECT_APP_FAIL;
import static com.mpos.sdk.util.ConsErrorDspread.TERMINATED;
import static com.mpos.sdk.util.ConsErrorDspread.UPDATE_WK_FALSE;

/**
 * Created by AnhNT on 8/31/16.
 */
public class LibDspreadReader extends LibReaderController {

    final String TAG = "LibDspreadReader";
//
//    /* type control of lib */
//    public static final int TYPE_CONTROLLER_GET_SERIAL_NUMBER = 1;
//    public static final int TYPE_CONTROLLER_SWIPE_CARD = 2;

//    public static final int TYPE_PROCESS_NORMAL = 0;
//    public static final int TYPE_PROCESS_GET_CARD = 1;
//    public static final int TYPE_PROCESS_GET_CARD_PIN = 2;
//    public static final int TYPE_PROCESS_CHANGE_PIN = 3;    // mcc card

    public static final int TIME_PENDING_CALL_INPUT_PIN = 2000;
    public static final int TIME_PENDING_DO_CHECK_CARD = 40000;
    public static final int TIME_OUT_BUILD_PIN_BLOCK = 30000;


//    private int typeController  = TYPE_CONTROLLER_SWIPE_CARD;
//    private int typeProcessCard = TYPE_PROCESS_NORMAL;

    private final String msgShowEnterPin = "Please Enter Pin";

    private QPOSService pos;

//    private final String readerType = ConstantsPay.READER_TYPE_DSPREAD;
    //    public String currencyCode = "840"; // usd
    public String currencyCode = "704"; // vnd

    public String currBluetoothAddressConnected;

    private BluetoothReaderPair devicePair;

    // magstripe
    private String mEZPK;
    private final String WK_O99 = "5A1E899A392282C75A1E899A392282C7";


    // EMV
    private String mPrivateTag = "";
    private String emv_script;

    private boolean isFirstRunUpdateWK = true;
    private boolean isDestroy = false;
    private boolean enableProcessRemoveCard = false;
    private boolean runEmvScript = false;

    /*
     * config device list activity reader
     */
    private boolean cfrFilterNameReader = false;
    private boolean cfrShowImgInfo = false;
    private boolean cfrAutoScanReader = false;
    private boolean cfrDisconnectWhenDestroy = false;
    private String cfrTitleDeviceActivity;

    private MyPr02Listener posListener;

    private ResultConnectDspread callBackConnectDevice;
    private ResultUpdateConfig cbUpdateConfig;
    private ResultInject cbResultInject;
    private ItfNPRequestPermission cbRequestPermission;

    private final int numBeepRemoveCard = 3;
    private int counterInputPin = 1;
    private int maxInputPin = 3;
    private QPOSService.CardTradeMode cardTradeMode;
    private DataProcessCard dataProcessCard;

    private ArrayList<BluetoothDevice> arrReaderDiscovery;
    private int timeDelayEndScanDevice = 2000;
    private BluetoothReaderPair deviceAutoConnect;

    public LibDspreadReader(Context c) {
        super(c);
    }

    public LibDspreadReader(Context c, DataPrePay dataPrePay, ItfUpdateViewDspread updateUi, ItfResultPay callbackResult) {
        super(c, dataPrePay);

        this.cbUpdateUI = updateUi;
        initVariable(callbackResult);
        if (dataPrePay != null && dataPrePay.getBluetoothReaderPair() != null) {
            this.deviceAutoConnect = dataPrePay.getBluetoothReaderPair();
            Utils.LOGD(TAG, "PR02-> auto connect SN="+deviceAutoConnect.getName()+" add="+deviceAutoConnect.getAddr());
        }

        checkNeedInitCertify();
    }

    private void checkNeedInitCertify() {
        if (Utils.checkTypeBuildIsCertify() && testNapas) {
            itfCertifyMacq = new ItfCertifyMacq() {
                @Override
                public void showDialogUseChip() {
                    udid = udid + "-fb";
                    showDialogCaseFallBack(getString(R.string.FALLBACK_NOTI_SWIPE_CARD_NAPAS));
                }

                @Override
                public void sendErrorCodeToPosMacq(int codeError, String emvScript) {
                    sendErrorCodeToPos(String.valueOf(codeError), emvScript);
                }

                @Override
                public void sendDenialMacq() {
                    sendDenialToTerminal();
                }
            };
        }
    }

    public void setDeviceAutoConnect(BluetoothReaderPair deviceAutoConnect) {
        this.deviceAutoConnect = deviceAutoConnect;
    }

    public void setCardTradeMode(QPOSService.CardTradeMode cardTradeMode) {
        this.cardTradeMode = cardTradeMode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public void setTypeController(int typeController) {
        this.typeController = typeController;
    }

    public void setCallBackConnectDevice(ResultConnectDspread callBackConnectDevice) {
        this.callBackConnectDevice = callBackConnectDevice;
    }

    public void setCbUpdateUI(ItfUpdateViewDspread cbUpdateUI) {
        this.cbUpdateUI = cbUpdateUI;
    }

    public void setCbUpdateConfig(ResultUpdateConfig cbUpdateConfig) {
        this.cbUpdateConfig = cbUpdateConfig;
    }

    public void setCbResultInject(ResultInject cbResultInject) {
        Utils.LOGD(TAG, "setCbResultInject: =====>>" + (cbResultInject == null ? "null" : "exit"));
        this.cbResultInject = cbResultInject;
    }

    public void setMaxInputPin(int maxInputPin) {
        if (maxInputPin > 3 || maxInputPin < 1) {
            this.maxInputPin = 3;
        }
        else {
            this.maxInputPin = maxInputPin;
        }
    }

    public void setEnableProcessRemoveCard(boolean enableProcessRemoveCard) {
        this.enableProcessRemoveCard = enableProcessRemoveCard;
    }

    public void setTimeDelayEndScanDevice(int timeDelayEndScanDevice) {
        this.timeDelayEndScanDevice = timeDelayEndScanDevice;
    }

    public void setCbRequestPermission(ItfNPRequestPermission cbRequestPermission) {
        this.cbRequestPermission = cbRequestPermission;
    }

    public QPOSService getPos() {
        return pos;
    }

    @Override
    protected String getReaderType() {
        return ConstantsPay.READER_TYPE_DSPREAD;
    }
    /*
     * auto scan - connect to reader
     */
    public void startAutoConnectReader() {
        if (openConnectionToPos()) {
            arrReaderDiscovery = new ArrayList<>();
            try {
                pos.scanQPos2Mode(context, timeDelayEndScanDevice / 1000);
            } catch (Exception e) {
                e.printStackTrace();
            }
            updateViewByStage(UI_STAGE_SCANNING_DEVICE);
//            handler.postDelayed(this::checkDeviceCanAutoConnect, timeDelayEndScanDevice);
        }
        else {
            showToast(getString(R.string.mp_error_cannot_open_connect_pr02));
        }
    }

    public void stopAutoConnectReader() {
        if (pos != null) {
            try {
                pos.stopScanQPos2Mode();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @SuppressLint("MissingPermission")
    private void handlerDeviceFound(BluetoothDevice bluetoothDevice) {
//        Utils.LOGD(TAG, "handlerDeviceFound: --->" + (arrReaderDiscovery == null ? "null" : arrReaderDiscovery.size()));
        if (arrReaderDiscovery != null) {
//            Utils.LOGD(TAG, "handlerDeviceFound: " + bluetoothDevice.getName());
            if (!TextUtils.isEmpty(bluetoothDevice.getName())
                    && bluetoothDevice.getName().toUpperCase().startsWith(ConstantsPay.PREFIX_NAME_READER_PR02)) {
                Utils.LOGD(TAG, "put device to array------>");
                arrReaderDiscovery.add(bluetoothDevice);
            }
        }
    }

    @SuppressLint("MissingPermission")
    private void checkDeviceCanAutoConnect() {
        updateViewByStage(UI_STAGE_END_SCAN_DEVICE);
        Utils.LOGD(TAG, "checkDeviceCanAutoConnect: --->num device scanned:" + (arrReaderDiscovery == null ? "null" : arrReaderDiscovery.size()));
        if (arrReaderDiscovery != null && arrReaderDiscovery.size() == 1) {
            boolean connect = false;
            if (deviceAutoConnect != null && !TextUtils.isEmpty(deviceAutoConnect.getName())) {
                if (arrReaderDiscovery.get(0).getName().equals(deviceAutoConnect.getName())) {
                    connect = true;
                }
                else {
                    arrReaderDiscovery.clear();
                }
            }
            else {
                connect = true;
            }
            if (connect) {
                updateViewByStage(UI_STAGE_CONNECTING_DEVICE);
                connectToDeviceByBluetoothAddress(new BluetoothReaderPair(arrReaderDiscovery.get(0).getName(), arrReaderDiscovery.get(0).getAddress()));
                return;
            }
        }
        if (callBackConnectDevice != null) {
            callBackConnectDevice.foundDevices(arrReaderDiscovery);
        }
    }

    /* <======= end auto connect reader */

    public boolean openConnectionToPos() {
        return openConnection(QPOSService.CommunicationMode.BLUETOOTH);
//        return openConnection(QPOSService.CommunicationMode.BLUETOOTH_BLE);
    }

    private boolean openConnection(QPOSService.CommunicationMode mode) {
        if (posListener == null) {
            posListener = new MyPr02Listener(mposPr02Listener);
        }
        pos = QPOSService.getInstance(mode);
        if (pos == null) {
            showToast("CommunicationMode unknow");
            appendLogAction("init POS error: CommunicationMode unknow");
            return false;
        }
        pos.setConext(context.getApplicationContext());
        Handler handler = new Handler(Looper.myLooper());

        try {
            pos.initListener(handler, posListener);
        } catch (Exception e) {
            e.printStackTrace();
            appendLogAction("error open connect to reader: " + e.getMessage());
            callbackFailConnectPos();
            return false;
        }
        Utils.LOGD(TAG, "openConnection: ----->>>");
        return true;
//		sdkVersion = QPOSService.getSdkVersion();
    }

    public void connectToDeviceByBluetoothAddress(BluetoothReaderPair device) {
        if (device != null) {
            this.devicePair = device;
            if (openConnectionToPos()) {
                Utils.LOGD(TAG, "connectToDeviceByBluetoothAddress: openConnectionToPos success");
                connectToDeviceByBluetoothAddress(device.addr);
            }
            else {
                Utils.LOGD(TAG, "connectToDeviceByBluetoothAddress: openConnectionToPos fail");
            }
        }
        else {
            Utils.LOGD(TAG, "connectToDeviceByBluetoothAddress: false= device null");
        }
    }

    private void connectToDeviceByBluetoothAddress(String address) {

        updateViewByStage(UI_STAGE_SHOW_PROGRESS);

        updateViewByStage(UI_STAGE_CONNECTING_DEVICE);

        currBluetoothAddressConnected = address;
//        pos.connectBLE(address); // try to use BLE, but speed connection is slower than normal BL (10s for done)
        pos.connectBluetoothDevice(true, 25, address);
    }

    public void initPinpadStartTransaction() {

        initBluetoothAndPR();
    }

    private void initBluetoothAndPR() {
        BluetoothAdapter btAdapter = BluetoothAdapter.getDefaultAdapter();
        if (btAdapter != null) {
            boolean havePermissionBluetooth = true;
            // if >= android 12
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S && !UtilsSystem.checkHaveBluetoothPermission(context)) {
                havePermissionBluetooth = false;
                requestBluetoothPermission();
            }
            if (havePermissionBluetooth) {
                if (btAdapter.isEnabled()) {
                    checkAutoConnectReader();
                }
                else {
                    updateViewByStage(UI_STAGE_NO_ENABLE_BLUETOOTH);
                    UtilsSystem.enableBluetooth(context, REQUEST_ENABLE_BT);
                }
            }
        }
        else {
            showToast(context.getString(R.string.msg_bluetooth_is_not_supported));
            finish(STATUS_FINISH_PR_NOT_SUPPORT_BT);
        }
    }

    private void requestBluetoothPermission() {
        Toast.makeText(context, getString(R.string.msg_request_permission_bluetooth), Toast.LENGTH_LONG).show();
        appendLogAction("need bluetooth permission");
        if (cbRequestPermission != null) {
            cbRequestPermission.onRequestPermission(new String[]{Manifest.permission.BLUETOOTH_SCAN, Manifest.permission.BLUETOOTH_CONNECT});
        }
        else {
            appendLogAction("-> not set callback handle BLE permission");
        }
//        UtilsSystem.requestBluetoothPermission((Activity) context, REQUEST_CODE_PERMISSION_BLE);
    }

    public void checkAutoConnectReader() {
        updateViewByStage(UI_STAGE_NO_CONNECT_DEVICE);
        String bluetoothAddress = PrefLibTV.getInstance(context).getBluetoothAddress();
        String serialNumberCache = PrefLibTV.getInstance(context).getSerialNumber();
        Utils.LOGD(TAG, "----checkAutoConnectReader: Address=" + bluetoothAddress + " serialNumber=" + serialNumberCache);
        Utils.LOGD(TAG, "--> auto connect SN=" + (deviceAutoConnect == null ? "null" : deviceAutoConnect.getName()));
        if (deviceAutoConnect != null && !TextUtils.isEmpty(deviceAutoConnect.getName())) {
            if (serialNumberCache.equals(deviceAutoConnect.getName())
                    && !TextUtils.isEmpty(bluetoothAddress)) {
                connectToDevice(serialNumberCache, bluetoothAddress);
            }
            else {
                startActivitySelectReader(deviceAutoConnect.getName());
            }
        }
        if (TextUtils.isEmpty(bluetoothAddress) || TextUtils.isEmpty(serialNumberCache)) {
            startActivitySelectReader();
        }
        else {
            connectToDevice(serialNumberCache, bluetoothAddress);
        }
    }

    public void setCfrFilterNameReader(boolean cfrFilterNameReader) {
        this.cfrFilterNameReader = cfrFilterNameReader;
    }

    public void setCfrShowImgInfo(boolean cfrShowImgInfo) {
        this.cfrShowImgInfo = cfrShowImgInfo;
    }

    public void setCfrAutoScanReader(boolean cfrAutoScanReader) {
        this.cfrAutoScanReader = cfrAutoScanReader;
    }

    public void setCfrTitleDeviceActivity(String cfrTitleDeviceActivity) {
        this.cfrTitleDeviceActivity = cfrTitleDeviceActivity;
    }

    public void setCfrDisconnectWhenDestroy(boolean cfrDisconnectWhenDestroy) {
        this.cfrDisconnectWhenDestroy = cfrDisconnectWhenDestroy;
    }

    public void startActivitySelectReader() {
        startActivitySelectReader(null);
    }
    public void startActivitySelectReader(String serialNumber) {
        updateViewByStage(UI_STAGE_CONNECTING_DEVICE);

        Intent intent = new Intent(context, DeviceListActivity.class);

        String btAddress = PrefLibTV.getInstance(context).getBluetoothAddress();
        if (!TextUtils.isEmpty(btAddress)) {
            intent.putExtra("btAddress", btAddress);
        }
        if (!TextUtils.isEmpty(serialNumber) && serialNumber.startsWith(ConstantsPay.PREFIX_NAME_READER_PR02)) {
            intent.putExtra(DeviceListActivity.EXTRA_SN_AUTO_CONNECT, serialNumber);
        }
        intent.putExtra(DeviceListActivity.EXTRA_FILTER_NAME, cfrFilterNameReader);
        intent.putExtra(DeviceListActivity.EXTRA_SHOW_IMG_INFO, cfrShowImgInfo);
        intent.putExtra(DeviceListActivity.EXTRA_AUTO_CONNECT, cfrAutoScanReader);
        intent.putExtra(DeviceListActivity.EXTRA_DISCONNECT_WHEN_DESTROY, cfrDisconnectWhenDestroy);
        if (!TextUtils.isEmpty(cfrTitleDeviceActivity)) {
            intent.putExtra(Intents.EXTRA_V_TITLE, cfrTitleDeviceActivity);
        }

        intent.putExtra(DeviceListActivity.EXTRA_IS_CONNECT_DEVICE, false);

        intent.setFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT);
        ((Activity) context).startActivityForResult(intent, REQUEST_DEVICE_DSPREAD);
    }

    // Connect to specific bluetooth device.
    private void connectToDevice(final String btName, final String btAddress) {
        this.devicePair = new BluetoothReaderPair(btName, btAddress);
        if (openConnectionToPos()) {
            Utils.LOGD(TAG, "connectToDeviceByBluetoothAddress: 222------------");
            connectToDeviceByBluetoothAddress(btAddress);
        }
    }

    public void requestDeviceStartPayment() {
        Utils.LOGD(TAG, "requestDeviceStartPayment: typeProcessCard=" + typeProcessCard);
        if (isDestroy) {
            return;
        }
        // anhnt: trade card without request pin
        initVariableNewPayment();
        pos.setFormatId("0020");

        if (Constants.isTypeIntegrationMpos()) {
            if (cardTradeMode == null) {
                boolean restrictionNFC = checkRestrictionNfc();//PrefLibTV.getInstance(context).get(PrefLibTV.restrictionNFC, Boolean.class, false);
                if (restrictionNFC) {
                    cardTradeMode = QPOSService.CardTradeMode.SWIPE_INSERT_CARD;
                    appendLogAction("restrictionNFC");
                } else {
                    boolean readNfcNotUp = "true".equalsIgnoreCase(PrefLibTV.getInstance(context).get(PrefLibTV.pr02ReadNfcNotUp, String.class, ""));
                    Utils.LOGD(TAG, "requestDeviceStartPayment: readNfcNotUp=" + readNfcNotUp);
                    if (readNfcNotUp) {
                        cardTradeMode = QPOSService.CardTradeMode.SWIPE_TAP_INSERT_CARD_NOTUP;
                    }
                    else {
                        cardTradeMode = QPOSService.CardTradeMode.SWIPE_TAP_INSERT_CARD;
                    }
                }
            }
            appendLogAction("cardTradeMode="+ cardTradeMode);
            pos.setCardTradeMode(cardTradeMode);

        }

        pos.doCheckCard(TIME_PENDING_DO_CHECK_CARD, getKeyIndex());
    }

    private void updateWorkingKey() {
        wk = getWorkingKey();
        String lastWkUsed = PrefLibTV.getInstance(context).get(PrefLibTV.WORKING_KEY_LAST_OK, String.class, "");
        appendLogAction("check WK: new=" + wk + " lastUsed=" + lastWkUsed);
        Utils.LOGD(TAG, "updateWorkingKey: --->new=" + wk + " lastUsed=" + lastWkUsed);


        if (TextUtils.isEmpty(wk) || checkUpdatedWK(wk, lastWkUsed)) {
            isFirstRunUpdateWK = false;
            requestDeviceStartPayment();
        }
        else {
//            wk = MyTextUtils.removeSpace(wk);
            pos.updateWorkKey(wk, "****************", getKeyIndex(), TIME_OUT_BUILD_PIN_BLOCK);
        }
    }

    private boolean checkUpdatedWK(String newWK, String lastWkUsed) {
        if (!TextUtils.isEmpty(lastWkUsed)) {
            try {
                JSONObject jRoot = new JSONObject(lastWkUsed);
                String currSN = PrefLibTV.getInstance(context).getSerialNumber();
                if (jRoot.has(currSN)) {
                    return newWK.equals(jRoot.getString(currSN));
                }
            } catch (Exception e) {
                appendLogAction("checkUpdatedWK error=" + e.getMessage());
                PrefLibTV.getInstance(context).put(PrefLibTV.WORKING_KEY_LAST_OK, "");
            }
        }
        return false;
    }

    private void saveLastWkUsed(String wk) {
        String lastWkUsed = PrefLibTV.getInstance(context).get(PrefLibTV.WORKING_KEY_LAST_OK, String.class, "");
        try {
            JSONObject jRoot;
            if (TextUtils.isEmpty(lastWkUsed)) {
                jRoot = new JSONObject();
            }
            else {
                jRoot = new JSONObject(lastWkUsed);
            }
            String currSN = PrefLibTV.getInstance(context).getSerialNumber();
            jRoot.put(currSN, wk);
            appendLogAction("save wk:" + jRoot);
            PrefLibTV.getInstance(context).put(PrefLibTV.WORKING_KEY_LAST_OK, jRoot.toString());
        } catch (JSONException e) {
            appendLogAction("saveLastWkUsed error=" + e.getMessage());
            PrefLibTV.getInstance(context).put(PrefLibTV.WORKING_KEY_LAST_OK, "");
        }
    }

    private int numRunInitTag = 0;

    public void runInitTagConfigurationForQpos() {
        numRunInitTag = 2;
        try {
            JSONObject joInitTag = new JSONObject(PrefLibTV.getInstance(context).getTagConfig());

            StringBuilder stringBuilder = new StringBuilder();

            String tag9F1A = joInitTag.getString("tag9F1ATerminalCountryCode");
            stringBuilder.append(new TagLengthValue("9F1A", tag9F1A.length() / 2, tag9F1A).getValue());

            String tag9F35 = joInitTag.getString("tag9F35TerminalType");
            stringBuilder.append(new TagLengthValue("9F35", tag9F35.length() / 2, tag9F35).getValue());

            String tag9F33 = joInitTag.getString("tag9F33TerminalCapabilities");
            stringBuilder.append(new TagLengthValue("9F33", tag9F33.length() / 2, tag9F33).getValue());

            String tag9F40 = joInitTag.getString("tag9F40AdditionalTerminalCapabilities");
            stringBuilder.append(new TagLengthValue("9F40", tag9F40.length() / 2, tag9F40).getValue());

            String tag5F2A = joInitTag.getString("tag5F2ATransactionCurrCode");
            stringBuilder.append(new TagLengthValue("5F2A", tag5F2A.length() / 2, tag5F2A).getValue());

            String tagDF19 = joInitTag.getString("tagDF19FloorLimitCurrency");
            stringBuilder.append(new TagLengthValue("DF19", tagDF19.length() / 2, tagDF19).getValue());

            String tag9F1B = joInitTag.getString("tag9F1BTerminalFloorLimit");
            stringBuilder.append(new TagLengthValue("9F1B", tag9F1B.length() / 2, tag9F1B).getValue());

            String config = stringBuilder.toString();
            ArrayList<String> arrConfig = new ArrayList<>();
            arrConfig.add(config);

            Utils.LOGD(TAG, "runInitTagConfigurationForQpos: config=" + config);

            pos.updateEmvAPP(QPOSService.EMVDataOperation.Add, arrConfig);

        } catch (JSONException e1) {
            appendLogAction("- ERROR joInitTag--");
            e1.printStackTrace();
        }
    }

    private void runInitTagApplicationIdForQpos() {
        try {
            JSONObject joInitTag = new JSONObject(PrefLibTV.getInstance(context).getTagConfigMaster());

            StringBuilder stringBuilder = new StringBuilder();

            String tag9F06 = joInitTag.getString("AID");
            stringBuilder.append(new TagLengthValue("9F06", tag9F06.length() / 2, tag9F06).getValue());

            String tag9F09 = joInitTag.getString("tag9F09AppVersionNumber");
            stringBuilder.append(new TagLengthValue("9F09", tag9F09.length() / 2, tag9F09).getValue());

            String tagDF03 = joInitTag.getString("tagDF03TermActionDefault");
            stringBuilder.append(new TagLengthValue("DF11", tagDF03.length() / 2, tagDF03).getValue());

            String tagDF04 = joInitTag.getString("tagDF04TermActionDenial");
            stringBuilder.append(new TagLengthValue("DF13", tagDF04.length() / 2, tagDF04).getValue());

            String tagDF05 = joInitTag.getString("tagDF05TermActionOnline");
            stringBuilder.append(new TagLengthValue("DF12", tagDF05.length() / 2, tagDF05).getValue());

            String config = stringBuilder.toString();
            ArrayList<String> arrConfig = new ArrayList<>();
            arrConfig.add(config);

            Utils.LOGD(TAG, "runInitTagConfigurationForQpos: config=" + config);

            pos.updateEmvAPP(QPOSService.EMVDataOperation.Add, arrConfig);

        } catch (JSONException e1) {
            appendLogAction("- ERROR joInitTag--");
            e1.printStackTrace();
        }
    }

    public void closeReadCard() {
        destroyReader();
    }

    public void cancelReadCard() {
        pos.cancelTrade();
    }

    public void destroyReader() {
        isDestroy = true;
        if (pos != null) {
            try {
                String addressCurrConnect = devicePair == null ? "" : devicePair.addr;
                if (pos.getConnectedSocketList() != null && !TextUtils.isEmpty(addressCurrConnect)) {
                    Utils.LOGD(TAG, "destroyReader: with address=" + addressCurrConnect);
                    pos.disconnectBT(addressCurrConnect);
                }
                else {
                    Utils.LOGD(TAG, "destroyReader: with no address");
                    pos.disconnectBT();
                }
            } catch (Exception e) {
                Utils.LOGE(TAG, "destroyReader: "+e.getMessage());
            }
            pos = null;
            posListener = null;
        }
    }

    private int getKeyIndex() {
        int typeServer = getTypeServer();
        /*if (isRunMacq) {
            typeServer = ConstantsPay.getTypeServerByBanKName(preSaleConfig.getAcquirerMagstripe());
        }
        else {
            typeServer = PrefLibTV.getInstance(context).getFlagServer();
        }*/
        return getKeyIndexByFlagServer(typeServer);
    }

    /*public int getKeyIndexByFlagServer(int typeServer) {
        int keyDataPosition = -1;
        Utils.LOGD(TAG, "getKeyDataIndex: typeServer=" + typeServer);
        if (typeServer == ConstantsPay.SERVER_SCB) {
            keyDataPosition = POSITION_KEY_STB;
        }
        else if (typeServer == ConstantsPay.SERVER_BIDV) {
            keyDataPosition = POSITION_KEY_BIDV;
        }
        else if (typeServer == ConstantsPay.SERVER_VTB) {
            keyDataPosition = POSITION_KEY_VTB;
        }
        else if (typeServer == ConstantsPay.SERVER_OPEN99) {
            keyDataPosition = POSITION_KEY_OPEN99;
        }
//        else if (typeServer == ConstantsPay.SERVER_MAILINH) {
//            keyDataPosition = POSITION_KEY_MAILINH;
//        }
        else if (typeServer == ConstantsPay.SERVER_VCB) {
            keyDataPosition = POSITION_KEY_VCB;
        }
        else if (typeServer == ConstantsPay.SERVER_TCB) {
            keyDataPosition = POSITION_KEY_TCB;
        }
//        else if (typeServer == ConstantsPay.SERVER_MPOS_ACQUIRER) {
//            keyDataPosition = POSITION_KEY_MPOS;
//        }
        appendLogAction("isMA: " + isRunMacq + " flagS=" + typeServer + " index=" + keyDataPosition);
        Utils.LOGD(TAG, "getKeyDataIndex: index=" + keyDataPosition);
        return keyDataPosition;
    }*/

    public List<BluetoothDevice> getListDevice() {
        List<BluetoothDevice> lstDeviceScanned = new ArrayList<>();
        if (pos != null) {
            Utils.LOGD(TAG, "getListDevice: pos not null");
            lstDeviceScanned = pos.getDeviceList();
        }
        Utils.LOGD(TAG, "getListDevice: sizeDevice: " + (lstDeviceScanned == null ? "null" : lstDeviceScanned.size()));
        return lstDeviceScanned;
    }

    // ----------------- HANDLER VIEW --------------------------

    private void showDialogErrorByTypeAndMsg(int code, String msg) {
        showDialogErrorByTypeAndMsg(new DataError(code, msg));
    }

    private void showDialogErrorByTypeAndMsg(DataError dataError) {
        Utils.LOGD(TAG, "showDialogErrorByTypeAndMsg: type=" + dataError.getErrorCode() + " msg=" + dataError.getMsg());
        showDialogError(dataError);
//        returnFailurePayment(dataPayCache, dataError, TYPE_ERROR_DEFAULT, 0, false);
//        if (typeProcessCard == TYPE_PROCESS_GET_CARD) {
//            returnFailurePayment(dataPayCache, dataError, TYPE_ERROR_DEFAULT, 0, false);
//        }
//        else if (cbUpdateUI != null) {
//            cbUpdateUI.showDialogErrorWithMessage(dataError);
//        }
//        else {
//            returnFailurePayment(dataPayCache, dataError, TYPE_ERROR_DEFAULT, 0, false);
//        }
    }

    private void dismissPgdl() {
        updateViewByStage(UI_STAGE_HIDE_PROGRESS);
    }

    private void showDialogCaseFallBack() {
        showDialogCaseFallBack(getString(R.string.FALLBACK_NOTI_SWIPE_CARD));
    }

    private void showDialogCaseFallBack(String msg) {
        runOnUiThread(() -> {
//            if (cbUpdateUI != null) {
//                cbUpdateUI.showDialogFallbackDspread(msg);
//            }
            showDialogWarningRetryPayment(msg, this::startPayment);
            isFallBack = true;
        });
    }

    Handler handler = new Handler();
    int timeDelaySendToQpos = 1000;

    private void sendDelayToQpos(Runnable runnable) {
        Utils.LOGD(TAG, "sendDelayToQpos: delay=" + timeDelaySendToQpos);
        handler.postDelayed(runnable, timeDelaySendToQpos);
    }

    private void showMsgInReader(String customDisplayString) {
        Utils.LOGD(TAG, "showMsgInReader: msg=" + customDisplayString);
        if (TextUtils.isEmpty(customDisplayString)) {
            return;
        }
        try {
            byte[] paras = customDisplayString.getBytes("GBK");
            customDisplayString = QPOSUtil.byteArray2Hex(paras);
            pos.lcdShowCustomDisplay(QPOSService.LcdModeAlign.LCD_MODE_ALIGNCENTER, customDisplayString, 10);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            Utils.LOGD(TAG, "gbk error");
        }
    }

    // ----------------- REQUEST API --------------------------
    @Override
    protected void handleWorkingKeyFromServer(String ezpk) {
        this.mEZPK = ezpk;
        buildPinBlock();
    }

    private void preBuildPinBlock() {
        if (PrefLibTV.getInstance(context).getFlagServer() == ConstantsPay.SERVER_MAILINH) {
            mEZPK = PrefLibTV.getInstance(context).getWkkMaiLinh().replace(" ", "");
        }
        else {
            mEZPK = MyTextUtils.removeSpace(WK_O99);
        }
        if (TextUtils.isEmpty(mEZPK)) {
            showDialogError("Not found WKK");
        }
        else {
            buildPinBlock();
        }
    }

    private void buildPinBlock() {
        buildPinBlock(msgShowEnterPin);
    }

    private void buildPinBlock(String msgShow) {
        Utils.LOGD(TAG, "--buildPinBlock: magstripe ezpk=" + mEZPK + " maskedPan=" + maskedPan
//                                            +" POSITION_KEY_PIN="+getKeyIndex()
        );
//        currStageProcess = STAGE_PROCESS_WAIT_INPUT_PIN;
        // **************** -> KCV: not check
//        pos.buildPinBLock(mEZPK, "****************", 1, getKeyIndex(), 8, maskedPan, msgShow);
        buildPinBlock(msgShow, mEZPK, getKeyIndex());
    }

    private void buildPinBlock(String msgShow, String ezpk, int index) {
        Utils.LOGD(TAG, "--buildPinBlock: magstripe ezpk=" + ezpk + " maskedPan=" + maskedPan + " index=" + index
                + " msgshow=" + msgShow
        );
        currStageProcess = STAGE_PROCESS_WAIT_INPUT_PIN;
        // **************** -> KCV: not check
        pos.buildPinBLock(ezpk, "****************", 1, index, 8, maskedPan, msgShow);
    }

    private void emvSales() {
        if (isRunMacq) {
            sendEmvSalesMacq();
        }
        else {
            sendEmvSalesBank(mDataEmv);
        }
    }

    @Override
    void handleSuccessSaleMacq(DataSaleSend dataSend, DataSaleRes dataRes) {
        if (nameTypeCard.equals(ConstantsPay.CARD_MAGSTRIPE) || dataSend.isNFC()) {
            onSuccessSaleMacq(dataSaleSuccess);
        }
        else {
            emv_script = dataRes.getScriptField();
            sendDelayToQpos(() -> runScriptEMV(emv_script));
        }
    }

    @Override
    protected void sendDenialToTerminal() {
        sendErrorCodeToPos("Z3", null);
    }


    @Override
    protected void sendErrorCodeToPos(String errorCode, String issuerAuthenData) {
        Utils.LOGD(TAG, "sendErrorCodeToPos: " + errorCode);
        if (Utils.checkTypeBuildIsCertify() && (testAmex || testJCB || testNapas)) {
            if (errorCode.length() == 1) {
                errorCode = "0" + errorCode;
            }
            if (!TextUtils.isEmpty(issuerAuthenData)) {
                issuerAuthenData = parseIssuerScript(issuerAuthenData);
            }
            else {
                issuerAuthenData = "";
            }
            String hexErrorCode = HexUtil.asciiToHex(errorCode);
            String onlineProcessResult = "8A02" + hexErrorCode + issuerAuthenData;
            Utils.LOGD(TAG, "sendErrorCodeToPos=> onlineProcessResult=" + onlineProcessResult);
            if (!TextUtils.isEmpty(hexErrorCode)) {
                sendOnlineProcessResultToPos(onlineProcessResult);
            }
        }
    }

    private void sendOnlineProcessResultToPos(String result) {
        if (pos != null) {
            pos.sendOnlineProcessResult(result);
        }
    }

    @Override
    protected void runScriptEMV(String script) {
        sendDelayToQpos(() -> runScriptEMVPr02(emv_script));
    }

    protected void runScriptEMVPr02(String script) {
        // run script
        String result = "8A023030";
        try {
            //String emvTag = "{KEY_RAW_ARRAY_71=[710F860D8424000008EF7E4EEB687A31BC],
            // 91=C77F4C25003F0D960012,
            // KEY_VALUE_ARRAY_91=[C77F4C25003F0D960012],
            // KEY_VALUE_ARRAY_71=[860D8424000008EF7E4EEB687A31BC],
            // 71=860D8424000008EF7E4EEB687A31BC,
            // KEY_RAW_ARRAY_91=[910AC77F4C25003F0D960012]}";
            if (!TextUtils.isEmpty(script)) {
                currStageProcess = STAGE_PROCESS_EMV_RUN_SCRIPT;
                appendLogAction("- start parse emv script");
                result += parseIssuerScript(script);
            }
        } catch (Exception e) {
            appendLogAction(">>Error parse tag emv_script: " + (e.getMessage()));
            e.printStackTrace();
        }

        Utils.LOGD(TAG, "runScriptEMV: result=" + result);
        runEmvScript = true;
        sendOnlineProcessResultToPos(result);
    }

    @Override
    protected void processAlertRemoveCard() {
        if (!isDestroy) {
            showToast(getString(R.string.ALERT_REMOVE_CARD));
        }
        if (enableProcessRemoveCard && pos != null && pos.isCardExist(1)) {
            alertRemoveCard();
        }
    }

    private void alertRemoveCard() {
        if (pos != null) {
            pos.doSetBuzzerOperation(numBeepRemoveCard);
        }
    }

    private void showMsgOnReaderScreen(String msg) {
        if (pos != null) {
            try {
                byte[] paras = msg.getBytes("GBK");
                //            byte[] paras = "\nPLS REMOVE CARD".getBytes("GBK");
                String customDisplayString = QPOSUtil.byteArray2Hex(paras);
                pos.lcdShowCustomDisplay(QPOSService.LcdModeAlign.LCD_MODE_ALIGNCENTER, customDisplayString, 5);
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
                appendLogAction("error show msg:" + msg);
            }
        }
    }

    MposPr02Listener mposPr02Listener = new MposPr02Listener() {
        @Override
        public void onRequestWaitingUser() {
            Utils.LOGD(TAG, "onRequestWaitingUser()");
            updateViewByStage(UI_STAGE_WAIT_SWIPE_CARD);
        }

        @Override
        public void onDoTradeResult(QPOSService.DoTradeResult result, Hashtable<String, String> decodeData) {

            appendLogAction("onDoTradeResult:11-> " + result);

            if (result == QPOSService.DoTradeResult.NONE) {
                Utils.LOGD(TAG, "onDoTradeResult: no card detected");
            }
            // emv process
            else if (result == QPOSService.DoTradeResult.ICC) {
                onDetectIccCard();
            }
            else if (result == QPOSService.DoTradeResult.NOT_ICC) {
                Utils.LOGD(TAG, "onDoTradeResult: not_icc");
                if (testNapas && enableFallback) {
                    // certify napas
                    showDialogCaseFallBack();
                }
                else {
                    showDialogErrorByTypeAndMsg(new ConsErrorDspread(NOT_ICC).getErrCode(), getString(R.string.transaction_not_icc));
                }
            }
            else if (result == QPOSService.DoTradeResult.BAD_SWIPE) {
                Utils.LOGD(TAG, "onDoTradeResult: bad_swipe");
            }
            else if (result == QPOSService.DoTradeResult.MCR) {
                onDetectMagCard(decodeData);
            }
            else if ((result == QPOSService.DoTradeResult.NFC_ONLINE) || (result == QPOSService.DoTradeResult.NFC_OFFLINE)) {
                onDetectNfcCard(result, decodeData);
            }
            else if (result == QPOSService.DoTradeResult.NFC_DECLINED) {
                Utils.LOGD(TAG, "onDoTradeResult: nfc declined--->");
                showDialogErrorByTypeAndMsg(new ConsErrorDspread(DECLINED).getErrCode(), getString(R.string.transaction_declined));
            }
            else if (result == QPOSService.DoTradeResult.NO_RESPONSE) {
                Utils.LOGD(TAG, "onDoTradeResult: card no response");
            }
        }

        @Override
        public void onRequestTransactionResult(QPOSService.TransactionResult transactionResult) {
            Utils.LOGD(TAG, "----------------onRequestTransactionResult----------------");
            appendLogAction("onRequestTransactionResult: " + transactionResult + " reSaleAgain=" + reSaleAgain);

            if (transactionResult == QPOSService.TransactionResult.APPROVED) {
                Utils.LOGD(TAG, "TransactionResult.APPROVED");
            }
            else if (transactionResult == QPOSService.TransactionResult.TERMINATED) {
                Utils.LOGD(TAG, "onRequestTransactionResult: TransactionResult.TERMINATED");
                if (reSaleAgain) {
                    reSaleAgain = false;
                }
                else {
                    handleChipDeclined();
                }
            }
            else if (transactionResult == QPOSService.TransactionResult.DECLINED) {
                Utils.LOGD(TAG, "onRequestTransactionResult: TransactionResult.DECLINED");

                // chip declined -> send void to server
                handleChipDeclined();
            }
            else if (transactionResult == QPOSService.TransactionResult.CANCEL) {
                Utils.LOGD(TAG, "onRequestTransactionResult: TransactionResult.CANCEL");
                showDialogErrorByTypeAndMsg(new ConsErrorDspread(CANCEL).getErrCode(), getString(R.string.transaction_cancel));
            }
            else if (transactionResult == QPOSService.TransactionResult.CAPK_FAIL) {
                Utils.LOGD(TAG, "onRequestTransactionResult: CAPK_FAIL");
                showDialogErrorByTypeAndMsg(new ConsErrorDspread(CAPK_FAIL).getErrCode(), getString(R.string.transaction_capk_fail));
            }
            else if (transactionResult == QPOSService.TransactionResult.NOT_ICC) {
                Utils.LOGD(TAG, "onRequestTransactionResult: NOT_ICC");
                if (testNapas && enableFallback) {
                    showDialogCaseFallBack();
                }
                else {
                    showDialogErrorByTypeAndMsg(new ConsErrorDspread(NOT_ICC).getErrCode(), getString(R.string.transaction_not_icc));
                }
            }
            // napas return -> not allow fallback
            else if (transactionResult == QPOSService.TransactionResult.APP_BLOCKED) {
                showDialogErrorByTypeAndMsg(new ConsErrorDspread(NOT_ICC).getErrCode(), getString(R.string.transaction_not_icc));
            }
            else if (transactionResult == QPOSService.TransactionResult.SELECT_APP_FAIL) {
                Utils.LOGD(TAG, "onRequestTransactionResult: SELECT_APP_FAIL enableFallBack=" + enableFallback);

                if (enableFallback) {
                    showDialogCaseFallBack();
                }
                else {
                    String msgError = getString(testNapas ? R.string.transaction_app_fail_no_napas
                            : R.string.transaction_app_fail);
                    msgError += getLastUpdatedEmvConfig();
                    showDialogErrorByTypeAndMsg(new ConsErrorDspread(SELECT_APP_FAIL).getErrCode(),
                            msgError);
                    enableRetryUpdateEmvConfig();
                }
            }
            else if (transactionResult == QPOSService.TransactionResult.DEVICE_ERROR) {
                Utils.LOGD(TAG, "onRequestTransactionResult: DEVICE_ERROR");
                showDialogErrorByTypeAndMsg(new ConsErrorDspread(DEVICE_ERROR).getErrCode(), getString(R.string.transaction_device_error));
            }
            else if (transactionResult == QPOSService.TransactionResult.CARD_NOT_SUPPORTED) {
                Utils.LOGD(TAG, "onRequestTransactionResult: CARD_NOT_SUPPORTED");
                showDialogErrorByTypeAndMsg(new ConsErrorDspread(CARD_NOT_SUPPORTED).getErrCode(), getString(R.string.card_not_supported));
            }
            else if (transactionResult == QPOSService.TransactionResult.MISSING_MANDATORY_DATA) {
                Utils.LOGD(TAG, "onRequestTransactionResult: MISSING_MANDATORY_DATA");
                showDialogErrorByTypeAndMsg(new ConsErrorDspread(MISSING_MANDATORY_DATA).getErrCode(), getString(R.string.missing_mandatory_data));
            }
            else if (transactionResult == QPOSService.TransactionResult.CARD_BLOCKED_OR_NO_EMV_APPS) {
                Utils.LOGD(TAG, "onRequestTransactionResult: CARD_BLOCKED_OR_NO_EMV_APPS");
                showDialogErrorByTypeAndMsg(new ConsErrorDspread(CARD_BLOCKED_OR_NO_EMV_APPS).getErrCode(), getString(R.string.card_blocked_or_no_evm_apps));
            }
            else if (transactionResult == QPOSService.TransactionResult.INVALID_ICC_DATA) {
                Utils.LOGD(TAG, "onRequestTransactionResult: INVALID_ICC_DATA");
                showDialogErrorByTypeAndMsg(new ConsErrorDspread(INVALID_ICC_DATA).getErrCode(), getString(R.string.transaction_not_icc));
            }
            else if (transactionResult == QPOSService.TransactionResult.FALLBACK) {
                Utils.LOGD(TAG, "onRequestTransactionResult: FALLBACK");
                if (testNapas) {
                    showDialogErrorByTypeAndMsg(new ConsErrorDspread(SELECT_APP_FAIL).getErrCode(), getString(R.string.transaction_app_fail_no_napas));
                }
                else if (enableFallback) {
                    showDialogCaseFallBack(getString(R.string.FALLBACK_NOTI_SWIPE_CARD));
                }
                else {
                    showDialogErrorByTypeAndMsg(new ConsErrorDspread(SELECT_APP_FAIL).getErrCode(), getString(R.string.transaction_app_fail_no_napas));
                }
            }
            else if (transactionResult == QPOSService.TransactionResult.NFC_TERMINATED) {
                Utils.LOGD(TAG, "onRequestTransactionResult: NFC_TERMINATED");
                showDialogErrorByTypeAndMsg(new ConsErrorDspread(NFC_TERMINATED).getErrCode(), getString(R.string.transaction_not_icc));
            }
            else if (transactionResult == QPOSService.TransactionResult.CARD_BLOCKED) {
                Utils.LOGD(TAG, "onRequestTransactionResult: CARD_BLOCKED");
                showDialogErrorByTypeAndMsg(new ConsErrorDspread(CARD_BLOCKED_OR_NO_EMV_APPS).getErrCode(), getString(R.string.transaction_not_icc));
            }
            else {
                Utils.LOGD(TAG, "onRequestTransactionResult: not handle --> "+transactionResult);
                showDialogErrorByTypeAndMsg(new ConsErrorDspread(TERMINATED).getErrCode(), getString(R.string.transaction_not_icc));
            }
        }

        private void handleChipDeclined() {
            if (currStageProcess == STAGE_PROCESS_START) {
                showDialogErrorByTypeAndMsg(new DataError(ConstantsPay.ERROR_CODE_DEFAULT, getString(R.string.transaction_denied)));
            }
            else if (runEmvScript) {
                // chip terminated -> send void to server
                if (isRunMacq) {
                    if (dataSaleSuccess == null) {
                        showDialogErrorByTypeAndMsg(new DataError(ConstantsPay.ERROR_CODE_DEFAULT, getString(R.string.transaction_denied)));
                    }
                    else {
//                        sendConfirmEmvSales();
                        runVoidFailedTransaction(TYPE_ERROR_RUN_SCRIPT_FAILURE, txId, pan, holderName, false,
                                new DataError(ConstantsPay.ERROR_CODE_DEFAULT, getString(R.string.transaction_denied)));
                    }
                }
                else {
                    runVoidFailedTransaction(TYPE_ERROR_RUN_SCRIPT_FAILURE, txId, pan, holderName, false,
                                new DataError(ConstantsPay.ERROR_CODE_DEFAULT, getString(R.string.transaction_denied)));
                }
            }
            else {
                showDialogErrorByTypeAndMsg(new DataError(ConstantsPay.ERROR_CODE_DEFAULT, getString(R.string.transaction_denied)));
            }
        }

        @Override
        public void onRequestBatchData(String tlv) {
            Utils.LOGD(TAG, "ICC end----onRequestBatchData");
            appendLogAction("ICC end----onRequestBatchData");
            Utils.LOGD(TAG, "tlv:" + tlv);
            Hashtable<String, String> map = pos.getICCTag(0, 29, tlv);

            Set<String> keys = map.keySet();

            //Obtaining iterator over set entries

            //Displaying Key and value pairs
            for (String key : keys) {
                // Getting Key
                mPrivateTag = map.get(key);
                Utils.LOGD(TAG, "Key: " + key + " & Value: " + map.get(key));
            }

            // call confirm_emv
            sendConfirmEmvSales();
        }

        @Override
        public void onRequestTransactionLog(String tlv) {
            Utils.LOGD(TAG, "onRequestTransactionLog: tlv=" + tlv);
        }

        @Override
        public void onRequestDeviceScanFinished() {
            Utils.LOGD(TAG, "onRequestDeviceScanFinished: ");
            checkDeviceCanAutoConnect();
        }

        @Override
        public void onRequestSelectEmvApp(ArrayList<String> appList) {
            Utils.LOGD(TAG, "select App -- S auto=" + autoSelectApp);
            appendLogAction("show select App -- S auto=" + autoSelectApp
                    + " sizeApp:" + (appList == null ? "null" : appList.size()));

            if (autoSelectApp && appList != null && appList.size() > 0) {
                appendLogAction(">> auto select App pos=0");
                pos.selectEmvApp(0);
            }
            else {
                showDialogSelectApplication(appList, position -> {
                    if (position>=0) {
                        pos.selectEmvApp(position);
                    }
                    else {
                        pos.cancelSelectEmvApp();
                    }
                });
            }
        }

        @Override
        public void onRequestSetAmount() {
            Utils.LOGD(TAG, "enter amount -- S: amount=" + amount);
            if (pos == null) {
                return;
            }
            if (typeProcessCard == TYPE_PROCESS_CHANGE_PIN || typeProcessCard == TYPE_PROCESS_GET_CARD) {
                pos.setAmountIcon("WAITING CARD......");
                pos.setAmount("0", "", "", QPOSService.TransactionType.INQUIRY);
                return;
            }
            pos.setAmountIcon(currency);
            if (Utils.checkTypeBuildIsCertify()) {
                // fake for test vcb
                String currencyCertify = PrefLibTV.getInstance(context).getCurrencyCertify();
                if (TextUtils.isEmpty(currencyCertify)) {
                    currencyCertify = "840";
                }
                Utils.LOGD(TAG, "onRequestSetAmount: fakeAmount=" + fakeAmount + " currencyCertify=" + currencyCertify);
                pos.setAmount(amount + (fakeAmount ? "00" : ""), "", currencyCertify, QPOSService.TransactionType.GOODS);
//            pos.setAmount(amount + (currencyCode.equalsIgnoreCase("840") && fakeAmount ? "00" : ""), "", currencyCode, QPOSService.TransactionType.GOODS);
            }
            else {
                pos.setAmount(amount, "", currencyCode, QPOSService.TransactionType.GOODS);
            }
        }

        @Override
        public void onRequestIsServerConnected() {
            Utils.LOGD(TAG, "send request to server");

            pos.isServerConnected(true);
        }

        @Override
        public void onRequestOnlineProcess(String tlv) {
            Utils.LOGD(TAG, "onRequestOnlineProcess: mPrivateTag:" + mPrivateTag);
            appendLogAction("RequestOnlineProcess, needCheckBin: " + checkMaskPan+ " typeProcessCard="+getNameTypeProcessCard());
            // call emv_sale
            mDataEmv = tlv;
            isContactLess = false;
            PayUtils payUtils = new PayUtils();
            initMaskedPan(payUtils.getTagC4(tlv));
            ksn = payUtils.getTagC0(tlv);
            logMaskPan();
            setNameTypeCard(ConstantsPay.CARD_EMV);

            if (typeProcessCard == TYPE_PROCESS_GET_CARD) {
                callbackCardData();
            } else {
                if (checkCanContinuePay()) {
                    emvSales();
                }
            }

            Utils.LOGD(TAG, "onRequestOnlineProcess: end online process");
        }

        @Override
        public void onRequestTime() {
            Utils.LOGD(TAG, "onRequestTime: --");
            String terminalTime = new SimpleDateFormat("yyyyMMddHHmmss").format(Calendar.getInstance().getTime());
            pos.sendTime(terminalTime);
        }

        @Override
        public void onRequestDisplay(QPOSService.Display displayMsg) {

            String msg = "";
            if (displayMsg == QPOSService.Display.CLEAR_DISPLAY_MSG) {
                msg = "";
            }
            else if (displayMsg == QPOSService.Display.PLEASE_WAIT) {
                msg = "wait";
            }
            else if (displayMsg == QPOSService.Display.REMOVE_CARD) {
                msg = "remove_card";
            }
            else if (displayMsg == QPOSService.Display.TRY_ANOTHER_INTERFACE) {
                msg = "try_another_interface";
            }
            else if (displayMsg == QPOSService.Display.PROCESSING) {
                msg = "processing";
            }
            else if (displayMsg == QPOSService.Display.INPUT_PIN_ING) {
                updateViewByStage(UI_STAGE_ENTER_PIN);
                msg = "please input pin on pos";
            }
            else if (displayMsg == QPOSService.Display.MAG_TO_ICC_TRADE) {
                msg = "please insert chip card on pos";
            }
            appendLogAction("display: " + msg);
        }

        @Override
        public void onRequestFinalConfirm() {
            Utils.LOGD(TAG, "onRequestFinalConfirm: ");
        }

        @Override
        public void onRequestNoQposDetected() {
            appendLogAction("onRequestNoQposDetected:" + typeController);
            callbackFailConnectPos();
        }

        @Override
        public void onRequestQposConnected() {
            dismissPgdl();
            Utils.LOGD(TAG, "onRequestQposConnected: typeController=" + typeController);

            appendLogAction("onRequestQposConnected:" + typeController);

            // pos connected --> check present type
            checkPresentType();
        }

        @Override
        public void onRequestQposDisconnected() {
            Utils.LOGD(TAG, "onRequestQposDisconnected: ");
            appendLogAction("onRequestQposDisconnected:" + typeController);

            updateViewByStage(UI_STAGE_NO_CONNECT_DEVICE);
        }

        @Override
        public void onError(QPOSService.Error errorState) {
            if (isDestroy) {
                return;
            }
            String msg = "";
            if (pos != null && !pos.isQposPresent()) {
                msg = getString(R.string.error_qpos_turn_off_reader);
            }
            else if (errorState == QPOSService.Error.CMD_NOT_AVAILABLE) {
                msg = getString(R.string.error_qpos_command_not_available);
            }
            else if (errorState == QPOSService.Error.TIMEOUT) {
                msg = getString(R.string.error_qpos_device_no_response);
            }
            else if (errorState == QPOSService.Error.DEVICE_RESET) {
                msg = getString(R.string.error_qpos_device_reset);
            }
            else if (errorState == QPOSService.Error.UNKNOWN) {
                msg = getString(R.string.error_qpos_unknown_error);
            }
            else if (errorState == QPOSService.Error.DEVICE_BUSY) {
                msg = getString(R.string.error_qpos_device_busy);
            }
            else if (errorState == QPOSService.Error.INPUT_OUT_OF_RANGE) {
                msg = getString(R.string.error_qpos_out_of_range);
            }
            else if (errorState == QPOSService.Error.INPUT_INVALID_FORMAT) {
                msg = getString(R.string.error_qpos_invalid_format);
            }
            else if (errorState == QPOSService.Error.INPUT_ZERO_VALUES) {
                msg = getString(R.string.error_qpos_zero_values);
            }
            else if (errorState == QPOSService.Error.INPUT_INVALID) {
                msg = getString(R.string.error_qpos_input_invalid);
            }
            else if (errorState == QPOSService.Error.CASHBACK_NOT_SUPPORTED) {
                msg = getString(R.string.error_qpos_cashback_not_supported);
            }
            else if (errorState == QPOSService.Error.CRC_ERROR) {
                msg = getString(R.string.error_qpos_crc_error);
            }
            else if (errorState == QPOSService.Error.COMM_ERROR) {
                msg = getString(R.string.error_qpos_comm_error);
            }
            else if (errorState == QPOSService.Error.MAC_ERROR) {
                msg = getString(R.string.error_qpos_mac_error);
            }
            else if (errorState == QPOSService.Error.CMD_TIMEOUT) {
                msg = getString(R.string.error_qpos_cmd_timeout);
            }
            Utils.LOGD(TAG, "onError: " + errorState + " -->" + msg);
            appendLogAction("Error by device: " + errorState + " -->" + msg);
            if (cbGetPosInfo != null) {
                cbGetPosInfo.onErrorGetPosInfo(errorState);
                if (isGetPosInfoForFw) {
                    return;
                }
            }
            if (currStageProcess != STAGE_PROCESS_END && currStageProcess != STAGE_SIGNATURE) {
                if (errorState == QPOSService.Error.TIMEOUT && runEmvScript) {
                    processCheckWaitSign(null, CHECK_WAIT_SIGN_RUN_SCRIPT);
                }
                else {
                    showDialogError(msg);
                }
            }
            if (cbUpdateConfig != null) {
                cbUpdateConfig.onResultUpdateConfig(false, msg);
            }
            if (cbResultInject != null) {
                Utils.LOGD(TAG, "onError: --> have cbResultInject");
                cbResultInject.onErrorInject(errorState);
            }
        }

        @Override
        public void onReturnReversalData(String tlv) {
            Utils.LOGD(TAG, "onReturnReversalData: tlv:" + tlv);
            appendLogAction("onReturnReversalData:" + tlv);
        }

        @Override
        public void onReturnGetPinResult(Hashtable<String, String> result) {
            Utils.LOGD(TAG, "onReturnGetPinResult: typeProcessCard=" + typeProcessCard);
            String pinBlock = result.get("pinBlock");
            handlerPinResult(pinBlock);
        }

        @Override
        public void onRequestUpdateWorkKeyResult(QPOSService.UpdateInformationResult result) {
            Utils.LOGD(TAG, "onRequestUpdateWorkKeyResult: result=" + result + " isFirstRunUpdateWK=" + isFirstRunUpdateWK);

            boolean haveError = true;
            String msg = "";
            if (result == QPOSService.UpdateInformationResult.UPDATE_SUCCESS) {
                msg = "update work key success";
                haveError = false;

                saveLastWkUsed(wk);

                if (isFirstRunUpdateWK) {
                    requestDeviceStartPayment();
                }
                else {
                    updateViewByStage(UI_STAGE_ENTER_PIN);
                }
            }
            else if (result == QPOSService.UpdateInformationResult.UPDATE_FAIL) {
                msg = "update work key fail";
            }
            else if (result == QPOSService.UpdateInformationResult.UPDATE_PACKET_VEFIRY_ERROR) {
                msg = "update work key packet vefiry error";
            }
            else if (result == QPOSService.UpdateInformationResult.UPDATE_PACKET_LEN_ERROR) {
                msg = "update work key packet len error";
            }
            isFirstRunUpdateWK = false;
            appendLogAction("WorkKey: " + result + " - " + msg);

            if (haveError) {
                showDialogErrorByTypeAndMsg(new ConsErrorDspread(UPDATE_WK_FALSE).getErrCode(), getString(R.string.transaction_update_wk_false));
            }
        }

        @Override
        public void onReturnCustomConfigResult(boolean isSuccess, String result) {
            String reString = "Failed";
            if (isSuccess) {
                reString = "Success";
            }
            Utils.LOGD(TAG, "onReturnCustomConfigResult: reString=" + reString + " result=" + result);
//            statusEditText.setText("result: " + reString + "\ndata: " + result);
            if (cbUpdateConfig != null) {
                cbUpdateConfig.onResultUpdateConfig(isSuccess, result);
            }
        }

        @Override
        public void onReturnUpdateEMVResult(boolean result) {
            Utils.LOGD(TAG, "onReturnUpdateEMVResult: result=" + result);
            if (result) {
                numRunInitTag--;
                Utils.LOGD(TAG, "onReturnUpdateEMVResult: numRunInitTag=" + numRunInitTag);
                if (numRunInitTag == 1) {
                    runInitTagApplicationIdForQpos();
                }
                else {
                    requestDeviceStartPayment();
                }
            }
        }

        @Override
        public void onDeviceFound(BluetoothDevice bluetoothDevice) {
//            Utils.LOGD(TAG, "onDeviceFound: " + bluetoothDevice.getName());
            handlerDeviceFound(bluetoothDevice);
        }

        @Override
        public void onLcdShowCustomDisplay(boolean arg0) {
            Utils.LOGD(TAG, "onLcdShowCustomDisplay: value=" + arg0);
            processAlertRemoveCard();
        }


        @Override
        public void onEmvICCExceptionData(String arg0) {

            Utils.LOGD(TAG, "onEmvICCExceptionData: result="+arg0);
            appendLogAction("IccException:"+arg0);
        }

        @Override
        public void onSetBuzzerResult(boolean b) {
            Utils.LOGD(TAG, "onSetBuzzerResult: b="+b);
            showMsgOnReaderScreen("PLS REMOVE CARD");
        }

        @Override
        public void onGetPosInfo(Hashtable<String, String> posInfoData) {
            if (cbGetPosInfo != null) {
                cbGetPosInfo.onGetPosInfo(posInfoData);
            }
        }

        @Override
        public void onUpdatePosFirmwareResult(QPOSService.UpdateInformationResult arg0) {
            if (arg0.equals(QPOSService.UpdateInformationResult.UPDATE_SUCCESS)) {
                sendSuccessUpgradeFw();
            }
            else {
                sendErrorUpgradeFw(arg0);
            }
        }

        @Override
        public void onReturnSetMasterKeyResult(boolean isSuccess) {
            Utils.LOGD(TAG, "onReturnSetMasterKeyResult: "+isSuccess);
            if (cbResultInject != null) {
                cbResultInject.onReturnSetMasterKeyResult(isSuccess);
            }
        }

        @Override
        public void onGetKeyCheckValue(List<String> list) {
            Utils.LOGD(TAG, "onGetKeyCheckValue: ");
            if (list != null) {
                for (String item : list) {
                    Utils.LOGD(TAG, "onGetKeyCheckValue: " + item);
                }
            }
            if (cbResultInject != null) {
                cbResultInject.onGetKeyCheckValue(list);
            }
        }

        @Override
        public void onReturnUpdateEMVRIDResult(boolean result) {
            Utils.LOGD(TAG, "onReturnUpdateEMVRIDResult: "+result);
        }

        @Override
        public void onReturnUpdateIPEKResult(boolean result) {
            Utils.LOGD(TAG, "onReturnUpdateIPEKResult: " + result);
            if (cbResultInject != null) {
                cbResultInject.onReturnUpdateIPEKResult(result);
            }
        }

        @Override
        public void onReturnGetEMVListResult(String s) {
            Utils.LOGD(TAG, "onReturnGetEMVListResult: " + s);
        }
    };

    private void onDetectNfcCard(QPOSService.DoTradeResult result, Hashtable<String, String> decodeData) {
        Utils.LOGD(TAG, result + ", ------NFC card----- decodeData: " + decodeData);

        updateViewByStage(UI_STAGE_PROCESSING_CARD);
        setNameTypeCard(ConstantsPay.CARD_NFC);
        initMaskedPan(decodeData.get("maskedPAN"));
        if (Utils.checkTypeBuildIsCertify()) {
            encTrack1 = decodeData.get("encTrack1");
            encTrack2 = decodeData.get("encTrack2");
        }
//        ksn = decodeData.get("trackksn"); // not same with C0
        logMaskPan();

        mDataEmv = "";
        Hashtable<String, String> h = pos.getNFCBatchData();
        mDataEmv = h.get("tlv");
        isContactLess = true;

        PayUtils payUtils = new PayUtils();
        ksn = payUtils.getTagC0(mDataEmv);

        String pinBlock = decodeData.get("pinBlock");
        if (!TextUtils.isEmpty(pinBlock)) {
            appendLogAction(" has pBlock");
            String C7 = "C7" + PayUtils.calculatorLength(pinBlock.length()) + pinBlock;
            mDataEmv = C7 + mDataEmv;
        }
        if (typeProcessCard == TYPE_PROCESS_GET_CARD) {
            callbackCardData();
        }
        else if (checkCanContinuePay()) {
            emvSales();
        }
    }

    private void onDetectMagCard(Hashtable<String, String> decodeData) {
        updateViewByStage(UI_STAGE_PROCESSING_CARD);
        setNameTypeCard(ConstantsPay.CARD_MAGSTRIPE);

        Utils.LOGD(TAG, " typeProcessCard=" + typeProcessCard + " decodeData: " + decodeData);

        initMaskedPan(decodeData.get("maskedPAN"));
        encTrack1 = decodeData.get("encTrack1");
        encTrack2 = decodeData.get("encTrack2");

        encDataMag = encTrack1 + encTrack2;

        ksn = decodeData.get("trackksn");

        logMaskPan();

        if (typeProcessCard == TYPE_PROCESS_NORMAL) {
            if (isRunMacq) {
                // check show enter pinblock if need
                CardUtils cardUtils = new CardUtils();
                if (!TextUtils.isEmpty(maskedPan) && cardUtils.checkBinExitInList(maskedPan, preSaleConfig.getRequirePinPrefixes())) {

                    String wk = getWorkingKey();//preSaleConfig.geteZPKDomestic();
                    int newFlagServer = ConstantsPay.getTypeServerByBanKName(preSaleConfig.getAcquirerMagstripe());
                    buildPinBlock(msgShowEnterPin, wk, getKeyIndexByFlagServer(newFlagServer));
                }
                else {
                    sendMagstripeSaleMacq(null);
                }
            }
            else {
                if (checkCanContinuePay()) {
                    sendMagstripeSalesBank("");
                }
            }
        }
        else {
            if (typeProcessCard == TYPE_PROCESS_GET_CARD_PIN) {
                preBuildPinBlock();
            }
            else if (typeProcessCard == TYPE_PROCESS_CHANGE_PIN) {
                counterInputPin = 1;
                preBuildPinBlock();
            }
            else if (typeProcessCard == TYPE_PROCESS_GET_CARD) {
                callbackCardData();
            }
        }
    }

    private void onDetectIccCard() {
        Utils.LOGD(TAG, "EMV ICC Start");
        setNameTypeCard(ConstantsPay.CARD_MAGSTRIPE);
        updateViewByStage(UI_STAGE_PROCESSING_CARD);
        currStageProcess = STAGE_PROCESS_START_EMV;
        pos.doEmvApp(QPOSService.EmvOption.START);
    }

    protected void startReadCard() {
        Utils.LOGD(TAG, "checkCanStartPayment: stagePresent=" + (pos == null ? "null" : pos.isQposPresent()));
        if (pos != null && pos.isQposPresent()) {
            boolean canStartPayment = false;
            if (reSaleAgain) {
                canStartPayment = true;
            }
            else {
                int updateProgress = pos.getUpdateProgress();
                appendLogAction("updateProgress: " + updateProgress);
                if (updateProgress > 0 && updateProgress < 100) {
                    showDialogError(getString(R.string.warning_not_exit_while_upgrading_fw));
                }
                else {
                    cancelProcessUpgradeFw();
                    canStartPayment = true;
                }
            }
            if (canStartPayment) {
                checkPresentType();
            }
        }
        else {
            startAutoConnectReader();
        }
    }

    public void checkPresentType() {
        if (typeController == TYPE_CONTROLLER_GET_SERIAL_NUMBER) {
            if (callBackConnectDevice != null) {
                callBackConnectDevice.onSuccessConnectDevice(devicePair);
            }
        } else if (typeController == TYPE_CONTROLLER_SWIPE_CARD) {
            initPresale();
            if (typeProcessCard == TYPE_PROCESS_GET_CARD) {
                requestDeviceStartPayment();
            }
            else {
                isFirstRunUpdateWK = true;
                updateWorkingKey();
            }
        }
    }

    private void logMaskPan() {
        appendLogAction("--MaskP:"+maskedPan);
    }

    private void handlerPinResult(String pinBlock) {
        if (typeProcessCard == TYPE_PROCESS_NORMAL) {
            if (isRunMacq) {
                sendMagstripeSaleMacq(pinBlock);
            }
            else {
                sendMagstripeSalesBank(pinBlock);
            }
        }
        else if (typeProcessCard == TYPE_PROCESS_GET_CARD_PIN){
            if (cbProcessCard != null) {
                DataProcessCard dataCard = new DataProcessCard(typeProcessCard, encDataMag, encTrack1, encTrack2, ksn, pinBlock);
                dataCard.setReaderType(getReaderType());
                cbProcessCard.onSuccessReadCard(dataCard);
            }
        }
        else if (typeProcessCard == TYPE_PROCESS_CHANGE_PIN){
            if (dataProcessCard == null) {
                dataProcessCard = new DataProcessCard(typeProcessCard, encDataMag, encTrack1, encTrack2, ksn, pinBlock);
            }
            Utils.LOGD(TAG, "onReturnGetPinResult: counter="+counterInputPin+" pinBlock="+pinBlock);
            dataProcessCard.setPinByCounter(counterInputPin, pinBlock);
            dataProcessCard.setReaderType(getReaderType());
            if (counterInputPin < maxInputPin) {
                counterInputPin++;
                String msgShow = counterInputPin==2?"Please enter new PIN":"Please re-enter new PIN";
                buildPinBlock(msgShow);
            } else if (cbProcessCard != null) {
                cbProcessCard.onSuccessReadCard(dataProcessCard);
            }
        }
    }

    /**
     * ----------*----------*---- upgrade fw ------*----------*----------*
     * <p>
     * ----------*----------*----------*----------*----------
     */
    private AsyncUpgradeFw asyncUpgradeFw;
    private ItfResultUpgradeFw cbUpdateProgress;
    private ItfGetPosInfo cbGetPosInfo;
    private boolean isGetPosInfoForFw = false;

    public void processUpgradeFw(String pathFile) {
        currStageProcess = STAGE_PROCESS_UPGRADING_FW_PR02;
        cancelProcessUpgradeFw();
        asyncUpgradeFw = new AsyncUpgradeFw(pathFile, currBluetoothAddressConnected, this);
        asyncUpgradeFw.execute();
        updateViewByStage(UI_STAGE_START_UPDATE_FW);
    }

    private void cancelProcessUpgradeFw() {
        if (asyncUpgradeFw != null) {
            asyncUpgradeFw.cancel(true);
            asyncUpgradeFw = null;
        }
    }

    public void setCbUpdateProgress(ItfResultUpgradeFw cbUpdateProgress) {
        this.cbUpdateProgress = cbUpdateProgress;
    }

    public void setGetPosInfoForFw(boolean getPosInfoForFw) {
        isGetPosInfoForFw = getPosInfoForFw;
    }

    public void setCbGetPosInfo(ItfGetPosInfo cbGetPosInfo) {
        this.cbGetPosInfo = cbGetPosInfo;
    }

    private void showProgress(int progress) {
        if (progress != 0 && progress % 10 == 0) {
            appendLogAction("->" + progress);
        }
        if (cbUpdateProgress != null) {
            cbUpdateProgress.showProgressUpgradeFw(progress);
        }
    }
    private void showAlertUpgradeFw(int type) {
        if (cbUpdateProgress != null) {
            runOnUiThread(()->cbUpdateProgress.showAlertUpgradeFw(type));
        }
    }

    private void sendSuccessUpgradeFw() {
        isGetPosInfoForFw = false;
        if (cbUpdateProgress != null) {
            cbUpdateProgress.onSuccessUpgradeFw();
        }
    }

    private void sendErrorUpgradeFw(QPOSService.UpdateInformationResult arg0) {
        cancelProcessUpgradeFw();
        if (cbUpdateProgress != null) {
            cbUpdateProgress.onErrorUpgradeFw(arg0);
        }
    }

    private static class AsyncUpgradeFw extends AsyncTask<Void, Void, Void> {

        private final String TAG = this.getClass().getSimpleName();

        WeakReference<LibDspreadReader> wr;
        String fileName;
        String blueToothAddress;

        AsyncUpgradeFw(String fileName, String blueToothAddress, LibDspreadReader helper) {
            this.wr = new WeakReference<>(helper);
            this.fileName = fileName;
            this.blueToothAddress = blueToothAddress;
        }

        @Override
        protected Void doInBackground(Void... voids) {
            Utils.LOGD(TAG, "doInBackground: ====>>" + blueToothAddress);
            if (wr != null && wr.get().context != null && wr.get().pos!=null) {
                byte[] data = readLine(fileName);
                Utils.LOGD(TAG, "doInBackground: size:"+data.length);
                try {
                    int result = wr.get().pos.updatePosFirmware(data, blueToothAddress);
                    Utils.LOGD(TAG, "testUpdateFirmware: result = "+result);
                    if (result == -1) {
                        wr.get().showAlertUpgradeFw(result);
                    }
                    else {
                        while (true) {
                            try {
                                Thread.sleep(1000);
                            } catch (InterruptedException e) {
                                e.printStackTrace();
                            }
                            if (wr.get().pos == null) {
                                break;
                            }
                            int progress = wr.get().pos.getUpdateProgress();
                            if (progress < 100) {
                                wr.get().showProgress(progress);
                            }
                            else {
                                break;
                            }
                        }
                    }

                } catch (Exception e) {
                    e.printStackTrace();

                    wr.get().appendLogAction("can not read file downloaded");
                    wr.get().sendErrorUpgradeFw(QPOSService.UpdateInformationResult.UPDATE_FAIL);
                }
            }
            return null;
        }

        private byte[] readLine(String fileName) {

            ByteArrayBuffer buffer = new ByteArrayBuffer(0);
            InputStream inputStream = null;
            try {
                File file = new File(fileName);
                Utils.LOGD(TAG, "readLinefw: -1- "+file.exists() + " length="+file.length());
                if (file.exists() && file.length() == 0) {
                    // delay 1s: because of cannot read file immediately
                    Thread.sleep(1000);
                }
                Utils.LOGD(TAG, "readLinefw: -2- "+file.exists() + " length="+file.length());
                inputStream = new FileInputStream(file);
                // BufferedReader br = new BufferedReader(new
                // InputStreamReader(inputStream));
                // str = br.readLine();
                int b = inputStream.read();
                while (b != -1) {
                    buffer.append((byte) b);
                    b = inputStream.read();
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                if (inputStream != null) {
                    try {
                        inputStream.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
            return buffer.toByteArray();
        }
    }



    private void callbackFailConnectPos() {
        dismissPgdl();
        Utils.LOGD(TAG, "onRequestNoQposDetected: ");
        if (typeController == TYPE_CONTROLLER_GET_SERIAL_NUMBER) {
            if (callBackConnectDevice != null) {
                callBackConnectDevice.onFailConnectDevice(devicePair);
            }
        } else if (typeController == TYPE_CONTROLLER_SWIPE_CARD) {
            updateViewByStage(UI_STAGE_FALSE_CONNECT_DEVICE);
        }
    }
    // ----------------- INTERFACE --------------------------
    public interface ItfUpdateViewDspread{
        void showViewDspreadByStage(int stage);
        void showDialogErrorWithMessage(DataError dataError);
        void showNotify(DataNotifyPay dataNotifyPay);
        void showDialogFallbackDspread(String msg);
        void onSuccessConnectDeviceDspread(String serialnumber, String mFWVersion);
    }
    public interface ResultConnectDspread{
        void onSuccessConnectDevice(BluetoothReaderPair devicePair);
        void onFailConnectDevice(BluetoothReaderPair devicePair);
        void foundDevices(ArrayList<BluetoothDevice> arrDevices);
    }

    public interface ResultUpdateConfig{
        void onResultUpdateConfig(boolean isSuccess, String result);
    }
    public interface ResultInject{
        void onReturnUpdateIPEKResult(boolean result);
        void onReturnSetMasterKeyResult(boolean isSuccess);
        void onGetKeyCheckValue(List<String> list);
        void onErrorInject(QPOSService.Error errorState);
    }
    public interface ItfResultUpgradeFw {
        void showProgressUpgradeFw(int progress);
        void showAlertUpgradeFw(int type);
        void onSuccessUpgradeFw();
        void onErrorUpgradeFw(QPOSService.UpdateInformationResult arg0);
    }
    public interface ItfGetPosInfo{
        void onGetPosInfo(Hashtable<String, String> posInfoData);
        void onErrorGetPosInfo(QPOSService.Error typeError);
    }
    public interface ItfNPRequestPermission{
        void onRequestPermission(String[] permissions);
    }
}
