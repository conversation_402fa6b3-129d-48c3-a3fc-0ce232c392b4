package com.mpos.sdk.core.control;

import android.app.Dialog;
import android.graphics.Color;
import android.text.TextUtils;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.mpos.sdk.core.model.DataError;
import com.mpos.sdk.core.model.DataPay;
import com.mpos.sdk.core.model.DataPrePay;
import com.mpos.sdk.core.model.MposCustom;
import com.mpos.sdk.core.model.ResultPay;
import com.mpos.sdk.core.model.ResultPayWrapper;
import com.mpos.sdk.core.model.TransItem;
import com.mpos.sdk.core.model.UserCard;
import com.mpos.sdk.util.Constants;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.HiddenAnnotationExclusionStrategy;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Collection;


public class MposIntegrationHelper {

    // status là trạng thái của gd đó:
    public static final String TRANS_STATUS_APPROVED 	= "APPROVED";
    public static final String TRANS_STATUS_REVERSED 	= "REVERSED";
    public static final String TRANS_STATUS_REVERSAL 	= "REVERSAL";   // macq
    public static final String TRANS_STATUS_VOID 		= "VOID";
    public static final String TRANS_STATUS_CANCEL 	    = "CANCEL";
    public static final String TRANS_STATUS_NOT_FOUND 	= "NOT_FOUND";
    public static final String TRANS_STATUS_ERROR	 	= "TRANS_ERROR";
    public static final String TRANS_STATUS_VOIDED	 	= "VOIDED";
    public static final String TRANS_STATUS_SETTLE	 	= "SETTLE";
    public static final String TRANS_STATUS_SETTLED	 	= "SETTLED";    // macq
    public static final String TRANS_STATUS_REFUND	 	= "REFUND";
    public static final String TRANS_STATUS_UNSIGN 	    = "UNSIGN";
    public static final String TRANS_STATUS_PENDING_SIGN= "PENDING_SIGNATURE";  // macq
    public static final String TRANS_STATUS_PENDING_TC  = "PENDING_TC";  // macq


    public static final String STATUS_UPDATED	 	    = "UPDATED";

    // method
    public static final String TRANS_METHOD_RESIGN	 	= "RESIGN";


    // action
    public static final String A_CONTINUE_TRANS             = "CONTINUE_TRANS";
    public static final String A_CHECK_UNSIGNATURE_TRANS    = "UNSIGNATURE_TRANS";
    public static final String A_VOID_TRANS                 = "VOID_TRANS";
    public static final String A_GET_STATUS                 = "GET_STATUS";
    public static final String A_GET_LIST_WAIT_SIGN         = "GET_LIST_WAIT_SIGN";
    public static final String A_UPDATE_POS_CONFIG          = "UPDATE_POS_CONFIG";


    public String buildDataSuccess(DataPrePay dataPrePay, DataPay dataPay) {

        String method = dataPay.isResign() ? TRANS_METHOD_RESIGN : null;
        ResultPay resultObj = new ResultPay(dataPay.getUdid(), TRANS_STATUS_APPROVED, dataPay.getTxId(), dataPay);
        resultObj.setMethod(method);

        ResultPayWrapper resultWrapper = new ResultPayWrapper(new UserCard(dataPay), resultObj);
        putUpdateConfigToCallback(resultWrapper, dataPrePay);

        if (Constants.isTypeIntegrationMpos()) {
            resultWrapper.setWfInfo(dataPay.getWfDetailRes());
            resultWrapper.setTransactionDetail(dataPay.getTransactionDetail());
        }

        return getGson().toJson(resultWrapper);
    }

    private Gson getGson() {
        return new GsonBuilder()
                .setExclusionStrategies(new HiddenAnnotationExclusionStrategy())
                .setPrettyPrinting()
                .create();
    }

    private void putUpdateConfigToCallback(ResultPayWrapper resultWrapper, DataPrePay dataPrePay) {
        if (dataPrePay!=null && dataPrePay.isNeedUpdateConfig()) {
            resultWrapper.setNeedUpdateConfig(dataPrePay.isNeedUpdateConfig());
            resultWrapper.setNumberSaleRemain(dataPrePay.getNumberSaleRemain());
        }
    }

    public String buildDataVoided(DataPrePay dataPrePay, DataPay dataPay) {

        ResultPay resultObj = new ResultPay(dataPrePay.getUdid(), TRANS_STATUS_VOIDED, dataPrePay.getTxId(), dataPay);
        ResultPayWrapper resultWrapper = new ResultPayWrapper(null, resultObj);

        return getGson().toJson(resultWrapper);
    }

    public String buildDataUnsign(DataPrePay dataPrePay, DataPay dataPay) {

        ResultPay resultObj = new ResultPay(dataPay.getUdid(), TRANS_STATUS_UNSIGN, dataPay.getTxId(), dataPay);
        ResultPayWrapper resultWrapper = new ResultPayWrapper(new UserCard(dataPay), resultObj);
        Gson gson = getGson();

        return gson.toJson(resultWrapper);
    }

    public String buildDataNotFoundUnsign(DataPrePay dataPrePay, DataPay dataPay) {

        ResultPay resultObj = new ResultPay(dataPrePay.getUdid(), TRANS_STATUS_NOT_FOUND, dataPay == null ? "" : dataPay.getTxId());
        ResultPayWrapper resultWrapper;// = new ResultPayWrapper(new UserCard(dataPay), resultObj);
        if (dataPay == null) {
            resultWrapper = new ResultPayWrapper(resultObj);
        }
        else {
            resultWrapper = new ResultPayWrapper(new UserCard(dataPay), resultObj);
        }

        return getGson().toJson(resultWrapper);
    }

    public String buildDataTransItem(TransItem transItem) {
        ResultPay resultObj = new ResultPay(getTransStatusByTransType(transItem), transItem);
        UserCard userCard = new UserCard(transItem);
        ResultPayWrapper resultWrapper = new ResultPayWrapper(userCard, resultObj);

        return getGson().toJson(resultWrapper);
    }

    public String buildDataListPendingTrans(TransItem transItem) {
        ArrayList<ResultPayWrapper> arrData = new ArrayList<>();
        if (transItem != null && transItem.getListPendingSig() != null && transItem.getListPendingSig().size() > 0) {
            for (TransItem.TransactionDetail transDetail : transItem.getListPendingSig()) {
                ResultPay resultPay = new ResultPay(transDetail);
                UserCard userCard = new UserCard(transDetail);
                ResultPayWrapper resultWrapper = new ResultPayWrapper(userCard, resultPay);
                arrData.add(resultWrapper);
            }
        }
        Type collectionType = new TypeToken<Collection<ResultPayWrapper>>(){}.getType();
        return getGson().toJson(arrData, collectionType);
    }

    public String getTransStatusByTransType(TransItem transItem) {
        if (transItem == null || transItem.getTransactionDetail() == null) {
            return TRANS_STATUS_NOT_FOUND;
        }
        return convertTransStatusFromInt(transItem.getTransactionDetail().getTransactionStatus());
    }

    public static String convertTransStatusFromInt(int transType) {
        switch (transType) {
            case ConstantsPay.TRANS_TYPE_SUCCESS:
            case ConstantsPay.TRANS_TYPE_PENDING_TC:
                return TRANS_STATUS_APPROVED;
            case ConstantsPay.TRANS_TYPE_REVERSAL:
                return TRANS_STATUS_REVERSED;
            case ConstantsPay.TRANS_TYPE_VOID:
                return TRANS_STATUS_VOIDED;
            case ConstantsPay.TRANS_TYPE_PENDING_SIGNATURE:
                return TRANS_STATUS_UNSIGN;
            case ConstantsPay.TRANS_TYPE_SETTLE :
                return TRANS_STATUS_SETTLE;
            case ConstantsPay.TRANS_TYPE_REFUND:
                return TRANS_STATUS_REFUND;
            default:
                return TRANS_STATUS_NOT_FOUND;
        }
    }

    public static int getTransTypeByTransStatus(String transStatus) {
        switch (transStatus) {
            case TRANS_STATUS_APPROVED:
                return ConstantsPay.TRANS_TYPE_SUCCESS;

            case TRANS_STATUS_REVERSED:
            case TRANS_STATUS_REVERSAL:
                return ConstantsPay.TRANS_TYPE_REVERSAL;

            case TRANS_STATUS_VOIDED:
                return ConstantsPay.TRANS_TYPE_VOID;

            case TRANS_STATUS_UNSIGN:
            case TRANS_STATUS_PENDING_SIGN:
                return ConstantsPay.TRANS_TYPE_PENDING_SIGNATURE;

            case TRANS_STATUS_SETTLE:
            case TRANS_STATUS_SETTLED:
                return ConstantsPay.TRANS_TYPE_SETTLE ;

            case TRANS_STATUS_REFUND:
                return ConstantsPay.TRANS_TYPE_REFUND;
            case TRANS_STATUS_PENDING_TC:
                return ConstantsPay.TRANS_TYPE_PENDING_TC;
            default:
                return 0;
        }
    }

    public String buildDataFail(DataPrePay dataPrePay, DataPay dataPay, DataError error) {
        ResultPay resultObj = new ResultPay(getUdid(dataPrePay, dataPay), TRANS_STATUS_ERROR, getTxId(dataPrePay, dataPay), error);
        ResultPayWrapper resultWrapper;// = new ResultPayWrapper(new UserCard(dataPay, dataPrePay), resultObj);
        if (dataPay != null || dataPrePay.getAmount()>0) {
            resultWrapper = new ResultPayWrapper(new UserCard(dataPay, dataPrePay), resultObj);
        }
        else {
            resultWrapper = new ResultPayWrapper(resultObj);
        }
        putUpdateConfigToCallback(resultWrapper, dataPrePay);
        Gson gson = getGson();

        return gson.toJson(resultWrapper);
    }

    public String buildDataCancel(DataPrePay dataPrePay, DataPay dataPay) {
        ResultPay resultObj = new ResultPay(getUdid(dataPrePay, dataPay), TRANS_STATUS_CANCEL, "");
        ResultPayWrapper resultWrapper = new ResultPayWrapper(new UserCard(dataPay, dataPrePay), resultObj);
        putUpdateConfigToCallback(resultWrapper, dataPrePay);

        return getGson().toJson(resultWrapper);
    }

    public String buildDataReaderUpdated() {
        ResultPay resultObj = new ResultPay("", STATUS_UPDATED, "");
        ResultPayWrapper resultWrapper = new ResultPayWrapper(resultObj);

        return getGson().toJson(resultWrapper);
    }

    private String getUdid(DataPrePay dataPrePay, DataPay dataPay) {
        return dataPay == null ? (dataPrePay == null ? "" : dataPrePay.getUdid()) : dataPay.getUdid();
    }

    private String getTxId(DataPrePay dataPrePay, DataPay dataPay) {
        return dataPay == null ? (dataPrePay == null ? "" : dataPrePay.getTxId()) : dataPay.getTxId();
    }

    public void changeBackgroundColor(View viewRoot, MposCustom sdkCustom) {
        if (viewRoot!=null && sdkCustom!=null && !TextUtils.isEmpty(sdkCustom.getColorBackground())) {
            try {
                viewRoot.setBackgroundColor(Color.parseColor(sdkCustom.getColorBackground()));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public void changeNavigationColor(Window window, MposCustom sdkCustom) {
        if (window != null && sdkCustom != null && !TextUtils.isEmpty(sdkCustom.getColorStatusBar())) {
            try {
                window.setStatusBarColor(Color.parseColor(sdkCustom.getColorStatusBar()));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public void setStatusBarColorIfPossible(Dialog dialog, MposCustom sdkCustom) {
        if (dialog != null && dialog.getWindow() != null && sdkCustom != null
                && !TextUtils.isEmpty(sdkCustom.getColorStatusBar())) {
            dialog.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            try {
                dialog.getWindow().setStatusBarColor(Color.parseColor(sdkCustom.getColorStatusBar()));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }



}
