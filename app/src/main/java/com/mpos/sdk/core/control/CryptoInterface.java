package com.mpos.sdk.core.control;

import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;
import android.util.Base64;

import androidx.annotation.NonNull;

import com.google.crypto.tink.Aead;
import com.google.crypto.tink.CleartextKeysetHandle;
import com.google.crypto.tink.JsonKeysetReader;
import com.google.crypto.tink.JsonKeysetWriter;
import com.google.crypto.tink.KeysetHandle;
import com.google.crypto.tink.aead.AeadConfig;
import com.google.crypto.tink.aead.AesGcmKeyManager;
import com.google.crypto.tink.integration.android.SharedPrefKeysetReader;
import com.google.crypto.tink.integration.android.SharedPrefKeysetWriter;
import com.google.crypto.tink.proto.Keyset;
import com.mpos.sdk.core.common.MyGson;
import com.mpos.sdk.core.modelma.DataBaseObj;
import com.mpos.sdk.util.Utils;
import com.payneteasy.tlv.HexUtil;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.MGF1ParameterSpec;
import java.security.spec.X509EncodedKeySpec;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.OAEPParameterSpec;
import javax.crypto.spec.PSource;

/**
 * Create by anhnguyen on 8/31/20
 */
public class CryptoInterface {

    private static final String TAG = "ApiInterface";

    private static final String PREF_FILE_NAME = "mpos_sdk_pref";
    private static final String TINK_KEYSET_NAME = "mpos_sdk_keyset";
    private static final String MASTER_KEY_URI = "android-keystore://sdk.mpos.vn";
    private static final String PK = "LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUF1SmRGa3ZtRjBqVWYzVzdLNFI4eApRYng0a2pnalFqbDlnc09QRjBjTFVZK1RIeUN6TFpiaWxVT1RHREV0Y2IzYS9RK0F0NXovREE3ZjlvaklpS2FuCnZhS0NWbC8yKytVOHNxMFhxNjl5b2RnY2ZSQWt3TyttamZSTkdmVGdjN1o3L0RLQ0Fjc0ZzSmxTbG5KbEdyQUMKZ2RpWkNRUDN0eFhqaFl2K0pPQ2FLTVNzUEJiMU5sN045MjNwRXdwSlBQQS9jcTkxdVlXL2Y1eEc0Q0hqRGpvNgpwczYyMldSelp4dTErbGV6MU42R2pmUy8wajROdjRTcUJsMm1hTE1ucWt1ekhmekkzVCtSOVJ4RDJGVzh4cjJPCkpCSnhRWnBOenRmZmxtL25sZ1pTeHpvSzMzNEhXdUxzNGFaczFvVDlKREQ1NHFwWjQ3eTlnRnVGRUJNZGtIV2UKMHdJREFRQUIKLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0tCg==";

    private static final String keysetName = "ksHandle";
    private static final String prefFileName = "prefMposTink";
    private static CryptoInterface cryptoInterface;
    private KeysetHandle keysetHandle;

//    Application application;
    private Aead aead;

    private CryptoInterface() {
        try {
            AeadConfig.register();
            initAead();
        } catch (GeneralSecurityException | IOException e) {
            e.printStackTrace();
        }
    }

    public static synchronized CryptoInterface getInstance(){
        if (cryptoInterface == null) {
            cryptoInterface = new CryptoInterface();
        }
        return cryptoInterface;
    }


    private KeysetHandle getOrGenerateNewKeysetHandle() throws GeneralSecurityException {

        Utils.LOGD(TAG, "getOrGenerateNewKeysetHandle: -->");
        if (keysetHandle == null) {
            Utils.LOGD(TAG, "create new KeysetHandle ");
            keysetHandle = KeysetHandle.generateNew(AesGcmKeyManager.aes256GcmTemplate());
        }
        return keysetHandle;
    }

    public void resetKeyUsed() {
        Utils.LOGD(TAG, "resetKeyUsed: ===>");
        keysetHandle = null;
        try {
            initAead();
        } catch (GeneralSecurityException | IOException e) {
            e.printStackTrace();
        }
    }

    private void initAead() throws GeneralSecurityException, IOException {
        Utils.LOGD(TAG, "initAead: ");
        aead = getOrGenerateNewKeysetHandle().getPrimitive(Aead.class);
    }

    public String getKeyUsed() {
        try {
            return parsePublicKey(getKeyset(), PK);
        } catch (IOException | GeneralSecurityException e) {
            e.printStackTrace();
        }
        return "";
    }

    private String parsePublicKey(byte[] plainText, String base64PublicKey) {
        byte[] decode = Base64.decode(base64PublicKey, Base64.DEFAULT);
        String pubKeyPEM = new String(decode, StandardCharsets.UTF_8)
                .replace("-----BEGIN PUBLIC KEY-----\n", "")
                .replace("-----END PUBLIC KEY-----", "");
        String cipherText = "";
        try {
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(Base64.decode(pubKeyPEM, Base64.DEFAULT));
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PublicKey publicKey = keyFactory.generatePublic(keySpec);

            Cipher cipher = Cipher.getInstance("RSA/ECB/OAEPPadding");
            OAEPParameterSpec oaepParams = new OAEPParameterSpec("SHA-256", "MGF1", new MGF1ParameterSpec("SHA-256"), PSource.PSpecified.DEFAULT);
            cipher.init(Cipher.ENCRYPT_MODE, publicKey, oaepParams);

            Utils.LOGD(TAG, "size plainText="+plainText.length);
            byte[] encryptedBytes = cipher.doFinal(plainText);
            cipherText = Base64.encodeToString(encryptedBytes, Base64.DEFAULT);
            Utils.LOGD(TAG, "cipher text: "+ cipherText.replace("\n", ""));


        } catch (NoSuchAlgorithmException | NoSuchPaddingException | InvalidKeySpecException | InvalidKeyException | BadPaddingException | IllegalBlockSizeException | InvalidAlgorithmParameterException e) {
            Utils.LOGD(TAG, e.getMessage());
        }
        return cipherText;
    }

    private byte[] getKeyset() throws IOException, GeneralSecurityException {
        KeysetHandle keysetHandle = getOrGenerateNewKeysetHandle();

        Keyset keyset = CleartextKeysetHandle.getKeyset(keysetHandle);
        Utils.LOGD(TAG, "getKey: sizeKey=" + keyset.getKeyCount());
        Utils.LOGD(TAG, "getKey: primaryKeyId=" + keyset.getPrimaryKeyId());


        if (keyset.getKeyCount() > 0) {

            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            keyset.writeTo(byteArrayOutputStream);

            Utils.LOGD(TAG, "getKey: ---->" + Base64.encodeToString(byteArrayOutputStream.toByteArray(), Base64.DEFAULT));

            return byteArrayOutputStream.toByteArray();
        }

        return null;
    }

    public void saveKeyToCache(@NonNull Context context, @NonNull SharedPreferences sharedPreferencesEncrypted) {
        if (keysetHandle != null) {
            try {
                SharedPrefKeysetWriter keysetWriter = new SharedPrefKeysetWriter(context, keysetName, prefFileName);
                CleartextKeysetHandle.write(keysetHandle, keysetWriter);

                String keySaved = getKeySavedInSharedPref(context);
                Utils.LOGD(TAG, "saveKeyToCache: after save key------keySaved=" + keySaved);
                sharedPreferencesEncrypted.edit().putString(keysetName, keySaved).apply();
                resetKeySavedInSharedPref(context);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

    public boolean loadKeySavedToKeySet(Context context, SharedPreferences sharedPreferencesEncrypted) {
        String keySaved = sharedPreferencesEncrypted.getString(keysetName, "");
        Utils.LOGD(TAG, "loadKeySavedToKeySet: "+keySaved);
        if (!TextUtils.isEmpty(keySaved)) {
            try {
                saveKeyToSharedPref(context, keySaved);
                SharedPrefKeysetReader keysetReader = new SharedPrefKeysetReader(context, keysetName, prefFileName);
                keysetHandle = CleartextKeysetHandle.read(keysetReader);

                initAead();

                resetKeySavedInSharedPref(context);

                return true;
            } catch (IOException | GeneralSecurityException e) {
                throw new RuntimeException(e);
            }
        }
        return false;
    }

    private String getKeySavedInSharedPref(Context context) {
        SharedPreferences sharedPreferences = context.getApplicationContext().getSharedPreferences(prefFileName, Context.MODE_PRIVATE);
        String keySaved = sharedPreferences.getString(keysetName, "");
        Utils.LOGD(TAG, "showKeySavedInSharedPref: keySaved=" + keySaved);
        return keySaved;
    }

    private void resetKeySavedInSharedPref(Context context) {
        context.getApplicationContext()
                .getSharedPreferences(prefFileName, Context.MODE_PRIVATE)
                .edit().remove(keysetName).apply();
    }

    private void saveKeyToSharedPref(Context context, String keySaved) {
        context.getApplicationContext()
                .getSharedPreferences(prefFileName, Context.MODE_PRIVATE)
                .edit().putString(keysetName, keySaved).apply();
    }

    public void clearCacheTinkKey(Context context, SharedPreferences sharedPreferencesEncrypted) {
        resetKeySavedInSharedPref(context);
        sharedPreferencesEncrypted.edit().putString(keysetName, "").apply();
    }
//    private void showCurrKeySetValue(Keyset keyset) {
//        Keyset.Key key = keyset.getKey(0);
//        Utils.LOGD(TAG, "showCurrKeySetValue: " + key.getKeyData().getValue().toStringUtf8());
//    }

    public String encryptData(@NonNull String data) {
        return encryptData(data.getBytes(StandardCharsets.UTF_8));
    }

    public String encryptData(@NonNull byte[] data) {
        try {
            return base64Encode(aead.encrypt(data, null));
        } catch (GeneralSecurityException e) {
            e.printStackTrace();
        }
        return "";
    }

    public String decryptRawData(String rawData) {
        DataBaseObj data = MyGson.parseJson(rawData, DataBaseObj.class);
        return decryptData(data);
    }

    public String decryptData(@NonNull DataBaseObj dataBaseObj) {
        if (dataBaseObj == null || TextUtils.isEmpty(dataBaseObj.getData())) {
            return "";
        }
        return decryptData(base64Decode(dataBaseObj.getData()));
    }

    public String decryptData(@NonNull String data) {
        return decryptData(base64Decode(data));
    }

    public String decryptData(@NonNull byte[] data) {
        try {
            return new String(aead.decrypt(data, null));
        } catch (GeneralSecurityException e) {
            e.printStackTrace();
        }
        return "";
    }

    private static String base64Encode(final byte[] input) {
        return Base64.encodeToString(input, Base64.DEFAULT);
    }

    private static byte[] base64Decode(String input) {
        if (TextUtils.isEmpty(input)) {
            return new byte[]{};
        }
        return Base64.decode(input, Base64.DEFAULT);
    }

    public String buildStringSend(String plainText) {
        try {
            Utils.LOGD(TAG, "buildCipherData plainText: " + plainText);

            String cipherText = encryptData(plainText);
//            String cipherText = CryptoInterface.getInstance().encryptData(plainText);
            DataBaseObj dataBaseObj = new DataBaseObj(cipherText);

            Utils.LOGD(TAG, "buildCipherData DataSend: " + MyGson.getGson().toJson(dataBaseObj));
            return MyGson.getGson().toJson(dataBaseObj);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }
}
