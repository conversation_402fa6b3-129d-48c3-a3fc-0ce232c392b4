package com.mpos.sdk.core.control;

//import com.mpos.sdk.util.HexUtil;

import com.mpos.sdk.util.HexUtil;

import java.math.BigInteger;

/**
 * Create by anhnguyen on 1/5/21
 */
public class Tr31Utils {

    String data = "00000000000000000000000000000000";
    String r128 = "00000000000000000000000000000087";

    // demo TR31
//    String header       = "D0112P0AE00E0000";
//    String keyInjected  = "00803F419E1CB7079442AA37474C2EFBF8B81C2965473CE206bb855b01533782";
//    String kbpk         = "88E1AB2A2E3DD38C1FA039A536500CC8A87AB9D62DC92C01058FA79F44657DE6";

    // IMEAK
//    String header       = "D0172B1AX00E0100KS1C00604B120F92928000000000";
//    String kbpk         = "5555555555555555555555555555555555555555555555555555555555555555";
//    String keyInjected  = "010056565656565656565656565656565656565656565656565656565656565656560000000000000000000000000000";

    // IMEAK - inject success
//    String header       = "D0176B1AX00E0200KS1C00604B120F92928000000000PB04";
//    String kbpk         = "5555555555555555555555555555555555555555555555555555555555555555";
//    String keyInjected  = "010056565656565656565656565656565656565656565656565656565656565656560000000000000000000000000000";


    // IMEAK try edit
    String header       = "D0176B1AX00E0200KS1CF2F123062801810000000000PB04";
    String kbpk         = "5555555555555555555555555555555555555555555555555555555555555555";
    String keyInjected  = "01003743489dd6444d8e31353f1a3c092028e23b891bf2f464d484f43bd11717fde60000000000000000000000000000";


    // IMEAK-new F2F12306086789000000
//    String header       = "D0172B1AX00E0100KS1CF2F123060867890000000000";
////    String header       = "D0172B1AX00E0100KS1CF2F123060867890000000000";
//    String kbpk         = "5555555555555555555555555555555555555555555555555555555555555555";
//    String keyInjected  = "010056565656565656565656565656565656565656565656565656565656565656560000000000000000000000000000";

//    String header       = "D0176B1AX00E0100KS1CF2F123060867890000000000PB04";
////    String header       = "D0172B1AX00E0100KS1CF2F123060867890000000000";
//    String kbpk         = "690751577EB883B411064C6F15DA4BB267E921D603D34F7C9C376A6158A29437";
//    String keyInjected  = "010031623765666534363866373062643830323639633138326531333230633035300000000000000000000000000000";

    // IPEK
//    String header       = "D0172B1AX00E0100KS1C00604B120F92928000000000";
//    String kbpk         = "6666666666666666666666666666666666666666666666666666666666666666";
//    String keyInjected  = "010056565656565656565656565656565656565656565656565656565656565656560000000000000000000000000000";

    // test CMAC
//    String kbpk         = "603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4";
//    String dataTestCmac = "6BC1BEE22E409F96E93D7E117393172AAE2D8A571E03AC9C9EB76FAC45AF8E5130C81C46A35CE411E5FBC1191A0A52EFF69F2445DF4F9B17AD2B417BE66C3710";

    public void generateKey() {
        System.out.println("--headerHex: "+ HexUtil.asciiToHex(header));
//        header = validateHeader(header);
//        System.out.println("--header after validate->"+header);
        String headerHex = HexUtil.asciiToHex(header);
        System.out.println("headerHex: " + headerHex);

//        String keyProtect = this.dataTestCmac.replace(" ", ""); // binary key data
//        String keyProtect = this.keyInjected.replace(" ", ""); // binary key data
//        String firstKeyProtect = keyProtect.substring(0, keyProtect.length()/2);
//        String secondKeyProtect = keyProtect.substring(keyProtect.length()/2);

        String s = encryptAesEcb256(data, kbpk);

        String shiftS = shiftBit(s);
        String k1;
        if (checkLeftMostBitIs1(s)) {
            System.out.println("s: "+ s+" ->need xor");
            k1 = xorByte(shiftS, r128);
        } else {
            System.out.println("s: "+ s+" -> NOT need xor");
            k1 = shiftS;
        }
        System.out.println("k1: " + k1);

        String shiftBitK1 = shiftBit(k1);
        String k2;
        if (checkLeftMostBitIs1(k1)) {
            System.out.println("k1: "+k1+ " ->need xor");
            k2 = xorByte(shiftBitK1, r128);
        }
        else {
            System.out.println("k1: "+k1+ " -> NOT need xor");
            k2 = shiftBitK1;
        }
        System.out.println("k2: " + k2);

        System.out.println("--KBEK--");
        String kbek = generateKBEK(kbpk, k2);
        System.out.println("kbek: " + kbek);

        System.out.println("--KBAK--");
        String kbak = generateKBAK(kbpk, k2);
        System.out.println("kbak: " + kbak);


        // ** Calculator mac **
        System.out.println("--KM1--");
        String km1 = generateKm1(data, kbak);
        System.out.println("km1: " + km1);

        System.out.println("\n--KeyBlockMac--");

        String dataNeedMac = headerHex + keyInjected;
        String keyBlockMAC;
        String encryptedKeyBlock = "";

        int surplus = (dataNeedMac.length()/2) % 16;    // 16 byte
        if (surplus == 0) {
            System.out.println("MAC Generation CASE A");
            keyBlockMAC = calculatorMacHeaderAndBinaryKeyData(kbak, km1, dataNeedMac);
            System.out.println("keyBlockMAC: me-" + keyBlockMAC);

//            System.out.println("\n--encrypting data--");
//            encryptedKeyBlock = encryptData(kbek, keyBlockMAC, firstKeyProtect, secondKeyProtect);
//            System.out.println("encryptedKeyBlock: " + encryptedKeyBlock);

        }
        else {
            String km2 = generateKm2(km1);

            System.out.println("MAC Generation CASE B");
            int missCharHex = (16 - surplus)*2;
            String padding = String.format("%1$-" + missCharHex + "s", "8").replace(" ", "0");
            System.out.println("surplus: " + missCharHex + " padding: " + padding);
            dataNeedMac += padding;
            keyBlockMAC = calculatorMacHeaderAndBinaryKeyData(kbak, km2, dataNeedMac);
            System.out.println("keyBlockMAC: me-" + keyBlockMAC);

        }

        System.out.println("\n--encrypting data--");

        try {
            // NOTE: Can use AES 256 CBC
            encryptedKeyBlock = encryptData(kbek, keyBlockMAC, keyInjected);
            System.out.println("encryptedKeyBlock: " + encryptedKeyBlock);

            String test = EncodeDecode.encryptAesCbc(kbek, keyBlockMAC, keyInjected);
            test = convertBase64ToHex(test).replace(" ", "");
            System.out.println("test: "+test);
        } catch (Exception e) {
            e.printStackTrace();
        }

        System.out.println("\noutput: " +
                " \nheader: " + header +
                " \nencryptedKeyBlock: " + encryptedKeyBlock +
                " \nkeyBlockMAC: " + keyBlockMAC +
                " \n" + (header + encryptedKeyBlock + keyBlockMAC));

        System.out.println("output: " + header + encryptedKeyBlock + keyBlockMAC);
    }

    public String validateHeader(String header) {
        if (header != null) {
            int num = header.length() % 16;
            System.out.println(num);
            if (num > 0) {
                num = 16 - num;
                String hexNum = Integer.toHexString(num);
                if (hexNum.length() % 2 != 0) {
                    hexNum = "0" + hexNum;
                }
                header += "PB" + hexNum;
                if (num > 4) {
                    header+= String.format("%1$-" + num + "s", "0").replace(" ", "0");
                }
            }
        }
        return header;
    }


    public String encryptAesEcb256(String data, String key) {
        String result = "";
        try {
            result = EncodeDecode.encryptAesEcb(key, data);
            result = convertBase64ToHex(result).replace(" ", "");
            System.out.println("Aes->"+result);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    public String decryptAesEcb256(String data, String key) {
        String result = "";
        try {
            result = EncodeDecode.decryptAesEcb(key, data);
            result = convertBase64ToHex(result).replace(" ", "");
            System.out.println("decrypt: " + result);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    public String shiftBit(String input) {
        BigInteger bigInteger = new BigInteger(input, 16);

//        System.out.println("---> shift left");
        BigInteger shiftLeftBi = bigInteger.shiftLeft(1);
        String shiftLeft = shiftLeftBi.toString(16).toUpperCase();
        if (shiftLeft.length() > 32) {
            shiftLeft = shiftLeft.substring(1);
        }
        else if (shiftLeft.length() < 32) {
//            shiftLeft = "0" + shiftLeft;
            shiftLeft = String.format("%1$"+32+"s", shiftLeft).replace(" ", "0");
        }
        System.out.println("SL: " + shiftLeft);
        return shiftLeft;
    }

    public String xorByte(String input, String input2) {
//        System.out.println("---> xor");
//        System.out.println("1-"+input);
//        System.out.println("2-"+input2);

        BigInteger bigInteger = new BigInteger(input, 16);

        BigInteger xorBi = bigInteger.xor(new BigInteger(input2, 16));
        String hexResult = convertBI2Hex(xorBi);
        if (hexResult.length() % 2 == 0) {
            return hexResult;
        }
        else {
            return "0" + hexResult;
        }
//        return hexResult;
    }

    public boolean checkLeftMostBitIs1(String hex) {
        if (hex.length() > 0) {
            String lastCharHex = hex.substring(0, 1);
            int i = Integer.parseInt(lastCharHex, 16);
            return (i & 8) == 8;
        }
        return false;
    }
    /*public boolean checkLeftMostBitIs1(String hex) {
        BigInteger bigInteger = new BigInteger(hex, 16);
        if (bigInteger.bitLength() > 0) {
            return bigInteger.testBit(bigInteger.bitLength() - 1);
        }
        else {
            return false;
        }
    }*/

    public String convertBI2Hex(BigInteger bigInteger) {
        return bigInteger.toString(16).toUpperCase();
    }

    public String convertBase64ToHex(String base64) {
        String hexResult = "";
        try {
            hexResult = HexUtil.byteArrayToHexString(com.mpos.sdk.core.control.Base64.decode(base64));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return hexResult;
    }

    public String generateKBEK(String kbpk, String k2) {
        // key derivation data
        String kdd  =   "0100000000040100" +
                        "8000000000000000";
        String kdd2 =   "0200000000040100" +
                        "8000000000000000";

        String first = calculatorHalfKey(kbpk, k2, kdd);
        String second = calculatorHalfKey(kbpk, k2, kdd2);

        return first + second;
    }

    public String generateKBAK(String kbpk, String k2) {
        // key derivation data
        String kdd  =   "0100010000040100" +
                        "8000000000000000";
        String kdd2 =   "0200010000040100" +
                        "8000000000000000";

        String first = calculatorHalfKey(kbpk, k2, kdd);
        String second = calculatorHalfKey(kbpk, k2, kdd2);

        return first + second;
    }

    public String calculatorHalfKey(String kbpk, String k2, String kdd) {
        String result = xorByte(k2, kdd);
        try {
            System.out.println("result Xor: "+result);
            result = EncodeDecode.encryptAesEcb(kbpk, result);
            result = convertBase64ToHex(result).replace(" ", "");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }


    //** calculator mac **
    public String generateKm1(String data, String kbak) {
        String s = encryptAesEcb256(data, kbak);
        boolean needXor;
        if (checkLeftMostBitIs1(s)) {
            System.out.println("s: " + s + " --> need xor");
            needXor = true;
        }
        else {
            System.out.println("s: "+s+" --> NOT need xor");
            needXor = false;
        }
        String shiftBitS = shiftBit(s);
        return needXor ? xorByte(shiftBitS, r128) : shiftBitS;
    }

    public String generateKm2(String km1) {
        String shiftBitKm1 = shiftBit(km1);
        if (checkLeftMostBitIs1(km1)) {
            System.out.println("km1: "+km1+" --> need xor");
            return xorByte(shiftBitKm1, r128);
        }
        else {
            System.out.println("km1: "+km1+" --> NOT need xor");
            return shiftBitKm1;
        }
    }

    public String calculatorMacHeaderAndBinaryKeyData(String kbak, String km1, String data) {
        String IV = "00000000000000000000000000000000";

        System.out.println("** calculatorMacHeaderAndBinaryKeyData() called with: " +
                "\nkbak = [" + kbak + "], " +
                "\nkm1 = [" + km1 + "], " +
                "\ndata = [" + data + "],\n");

        int sizeBlock = 16*2;
        int numBlock16byte = data.length() / sizeBlock;

        String previewResult = "";
        for (int i = 0; i < numBlock16byte; i++) {
            String dataBlock = data.substring(i * sizeBlock, (i + 1) * sizeBlock);

            if (i == 0) {
                dataBlock = xorByte(dataBlock, IV);
            }
            else if (i < numBlock16byte - 1) {
                dataBlock = xorByte(dataBlock, previewResult);
            }
            else {
                dataBlock = xorByte(dataBlock, km1);
                dataBlock = xorByte(dataBlock, previewResult);
            }
            previewResult = encryptAesEcb256(dataBlock, kbak);
        }

        return previewResult;
    }

    public String calculatorMacHeaderAndBinaryKeyData(String kbak, String km1,
                                                      String header, String firstBinaryKeyData, String secondBinaryKeyData) {
        String IV = "00000000000000000000000000000000";

        System.out.println("** calculatorMacHeaderAndBinaryKeyData() called with: " +
                "\nkbak = [" + kbak + "], " +
                "\nkm1 = [" + km1 + "], " +
                "\nheader = [" + header + "], " +
                "\nfirstBinaryKeyData = [" + firstBinaryKeyData + "], " +
                "\nsecondBinaryKeyData = [" + secondBinaryKeyData + "]\n");

        String xorHeader = xorByte(header, IV);
        String encryptedHeader = encryptAesEcb256(xorHeader, kbak);
        System.out.println("1->"+encryptedHeader);

        String xorFirstBKD = xorByte(encryptedHeader, firstBinaryKeyData);
        System.out.println("2->"+xorFirstBKD);

        String encryptedKey = encryptAesEcb256(xorFirstBKD, kbak);
        System.out.println("3->"+encryptedKey);

        String xorK1 = xorByte(km1, encryptedKey);
        String xorSecond = xorByte(secondBinaryKeyData, xorK1);
        System.out.println("4->"+xorSecond);

        String result = encryptAesEcb256(xorSecond, kbak);
        return result;
    }

    //** encrypt data **
    //kbek, keyBlockMAC, firstKeyProtect, secondKeyProtect
    public String encryptData(String kbek, String iv, String firstBlock, String secondBlock) {
        String xorFirstBlock = xorByte(iv, firstBlock);

        String encryptFirst = encryptAesEcb256(xorFirstBlock, kbek);

        String xorSecond = xorByte(secondBlock, encryptFirst);

        String encryptSecond = encryptAesEcb256(xorSecond, kbek);

        return encryptFirst + encryptSecond;
    }

    public String encryptData(String kbek, String iv, String data) {
        System.out.println("encryptData() called with: " +
                "\nkbek = [" + kbek + "], " +
                "\niv = [" + iv + "], " +
                "\ndata = [" + data + "]");

        int sizeBlock = 16*2;
        int numBlock = data.length() / sizeBlock;

        StringBuilder result = new StringBuilder();
        for (int i = 0; i < numBlock; i++) {
            String dataBlock = data.substring(i * sizeBlock, (i + 1) * sizeBlock);
            dataBlock = xorByte(dataBlock, iv);

            String encryptedBlock = encryptAesEcb256(dataBlock, kbek);
            result.append(encryptedBlock);

            iv = encryptedBlock;
        }

        return result.toString();
    }

}
