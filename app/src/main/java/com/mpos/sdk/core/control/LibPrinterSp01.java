package com.mpos.sdk.core.control;

import android.content.Context;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.mpos.sdk.util.DevicesUtil;
import com.mpos.sdk.util.Utils;
import com.whty.smartpos.tysmartposapi.ITYSmartPosApi;
import com.whty.smartpos.tysmartposapi.printer.PrintElement;
import com.whty.smartpos.tysmartposapi.printer.PrinterConfig;
import com.whty.smartpos.tysmartposapi.printer.PrinterConstrants;
import com.whty.smartpos.tysmartposapi.printer.PrinterListener;

import java.util.ArrayList;

/**
 * Create by anhnguyen on 2019-07-19
 */
public class LibPrinterSp01 {

    private static final String TAG = "LibPrinter";


    //    Printing a line is 384 points,with FONT_SIZE_SMALL  ,a character is 8 points
    final static int NUM_CHAR_SIZE_HUGE     = 16;
    final static int NUM_CHAR_SIZE_LARGE 	= 24;
    final static int NUM_CHAR_SIZE_MIDDLE	= 32;
    final static int NUM_CHAR_SIZE_SMALL 	= 48;

    ITYSmartPosApi smartPosApi;

    int RESPONSE_CODE_SUCCESS = 0;
    boolean initTextFormat = false;

    public LibPrinterSp01(@NonNull Context c, @NonNull PrinterListener myPrinterListener) throws Exception{
        if (!DevicesUtil.isP20L()) {
            throw new Exception("Library is only use for reader SP01.");
        }
        else {
            initSmartPos(c);
            initPrinter(myPrinterListener);
        }
    }

    private void initSmartPos(@NonNull Context c) {
        smartPosApi = ITYSmartPosApi.get(c.getApplicationContext());
    }

    private boolean initPrinter(PrinterListener myPrinterListener) {
        boolean res = smartPosApi.initPrinter();
        smartPosApi.setPrinterListener(myPrinterListener);
        Utils.LOGD(TAG, "initPrinter: res=" + res);
        return res;
    }

    public boolean printText(@NonNull String text) {
        if (TextUtils.isEmpty(text) || smartPosApi == null) {
            return false;
        }
        if (!initTextFormat) {
            setTextFormat(PrinterConstrants.FONT_SIZE_MIDDLE, PrinterConstrants.ALIGN_LEFT);
        }
        int res = smartPosApi.printText(text);
        return res == RESPONSE_CODE_SUCCESS;
    }

    public boolean printBitmap(@NonNull Bitmap bitmap) {
        if (smartPosApi == null) {
            return false;
        }
        int res = smartPosApi.printBitmap(bitmap);
        Utils.LOGD(TAG, "printBitmap: res=" + res);
        return res == RESPONSE_CODE_SUCCESS;
    }

    /**
     * print array element
     * new PrintElement("abc xyz", PrinterConstrants.ALIGN_CENTER)
     *
     * @param arrElement
     * @return
     */
    public boolean printArrElement(ArrayList<PrintElement> arrElement){
        if (smartPosApi == null || arrElement == null || arrElement.size() == 0) {
            return false;
        }
        for (PrintElement printElement : arrElement) {
            smartPosApi.appendPrintElement(printElement);
        }
        smartPosApi.startPrintElement();

        return true;
    }

    public void pushPage(int number) {
        if (smartPosApi == null) {
            return;
        }
        for (int i = 0; i < number; i++) {
            smartPosApi.feedPaper(5000);
        }
    }

    public void setTextFormat(int size, int align) {
        setTextFormat(size, align, false);
    }
    public void setTextFormat(int size, int align, boolean isBold ) {
        if (smartPosApi == null) {
            return;
        }
        Bundle bundle = new Bundle();
        bundle.putInt(PrinterConfig.FONT_SIZE, size);
        bundle.putInt(PrinterConfig.ALIGN, align);
        bundle.putInt(PrinterConfig.CN_FONT, PrinterConstrants.KAITI);
        bundle.putBoolean(PrinterConfig.BOLD, isBold);
        boolean res = smartPosApi.setPrintParameters(bundle);
        Utils.LOGD(TAG, "setTextFormat() called with: size = [" + size + "], align = [" + align + "]" + "], isBold = [" + isBold+" res=" + res);
        initTextFormat = true;
    }

    public void disconnectPrinter() {
        if (smartPosApi != null) {
            smartPosApi.releasePrinter();
            smartPosApi.setPrinterListener(null);
        }
    }

}
