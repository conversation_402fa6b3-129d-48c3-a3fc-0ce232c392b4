package com.mpos.sdk.core.modelma;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
/**
 * Create by anhnguyen on 9/7/20
 */
public class DataSaleRes {
    @SerializedName("wfId")
    @Expose
    private String wfId;
    @SerializedName("merchantId")
    @Expose
    private String merchantId;
//    @SerializedName("flagPassPinRequired")
//    @Expose
//    private boolean flagPassPinRequired;
//    @SerializedName("flagConfirmPayment")
//    @Expose
//    private boolean flagConfirmPayment;
//    @SerializedName("flagTCCommand")
//    @Expose
//    private boolean flagTCCommand;
//    @SerializedName("currStateId")
//    @Expose
//    private String currStateId;
//    @SerializedName("currStateStatus")
//    @Expose
//    private String currStateStatus;
    @SerializedName("saleResCode")
    @Expose
    private String saleResCode;
    @SerializedName("tid")
    @Expose
    private String tid;
    @SerializedName("mid")
    @Expose
    private String mid;
    @SerializedName("acquirer")
    @Expose
    private String acquirer;
    @SerializedName("amount")
    @Expose
    private String amount;
    @SerializedName("pan")
    @Expose
    private String pan;
    @SerializedName("cardHolderName")
    @Expose
    private String cardHolderName;
    @SerializedName("wfStatus")
    @Expose
    private String wfStatus;
    @SerializedName("txid")
    @Expose
    private String txid;
    @SerializedName("status")
    @Expose
    private String status;
    @SerializedName("authCode")
    @Expose
    private String authCode;
    @SerializedName("rrn")
    @Expose
    private String rrn;
    @SerializedName("mposTID")
    @Expose
    private String mposTID;
    @SerializedName("mposMID")
    @Expose
    private String mposMID;
    @SerializedName("udid")
    @Expose
    private String udid;
    @SerializedName("issuerCode")
    @Expose
    private String issuerCode;
    @SerializedName("transactionDate")
    @Expose
    private String transactionDate;
    @SerializedName("AID")
    @Expose private String aid;
    @SerializedName("APPL")
    @Expose
    private String appl;
    @SerializedName("ARQC")
    @Expose
    private String arqc;
    @SerializedName("muid")
    @Expose
    private String muid;
    @SerializedName("readerSerial")
    @Expose
    private String readerSerial;
    @SerializedName("scriptField")
    @Expose
    private String scriptField;
    @SerializedName("trxType")
    @Expose
    private String trxType;
    @SerializedName("batchNo")
    @Expose
    private String batchNo;
    @SerializedName("invoiceNo")
    @Expose
    private String invoiceNo;
    @SerializedName("latitude")
    @Expose
    private String latitude;
    @SerializedName("longitude")
    @Expose
    private String longitude;
    @SerializedName("flagNoSignature")
    @Expose
    private boolean flagNoSignature;
    @SerializedName("merchantName")
    @Expose
    private String merchantName;
    @SerializedName("merchantAddress")
    @Expose
    private String merchantAddress;
    @SerializedName("depositRefCode")
    @Expose
    private String depositRefCode;
    @SerializedName("amountDeposit")
    @Expose
    private String amountDeposit;
    @SerializedName("description")
    @Expose
    private String description;

    public String getWfId() {
        return wfId;
    }

    public void setWfId(String wfId) {
        this.wfId = wfId;
    }

//    public boolean isFlagPassPinRequired() {
//        return flagPassPinRequired;
//    }
//
//    public void setFlagPassPinRequired(boolean flagPassPinRequired) {
//        this.flagPassPinRequired = flagPassPinRequired;
//    }

//    public String getCurrStateId() {
//        return currStateId;
//    }
//
//    public void setCurrStateId(String currStateId) {
//        this.currStateId = currStateId;
//    }
//
//    public String getCurrStateStatus() {
//        return currStateStatus;
//    }
//
//    public void setCurrStateStatus(String currStateStatus) {
//        this.currStateStatus = currStateStatus;
//    }

    public String getSaleResCode() {
        return saleResCode;
    }

    public void setSaleResCode(String saleResCode) {
        this.saleResCode = saleResCode;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    public String getAcquirer() {
        return acquirer;
    }

    public void setAcquirer(String acquirer) {
        this.acquirer = acquirer;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getPan() {
        return pan;
    }

    public void setPan(String pan) {
        this.pan = pan;
    }

    public String getCardHolderName() {
        return cardHolderName;
    }

    public void setCardHolderName(String cardHolderName) {
        this.cardHolderName = cardHolderName;
    }

    public String getWfStatus() {
        return wfStatus;
    }

    public void setWfStatus(String wfStatus) {
        this.wfStatus = wfStatus;
    }

    public String getTxid() {
        return txid;
    }

    public void setTxid(String txid) {
        this.txid = txid;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getAuthCode() {
        return authCode;
    }

    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }

    public String getRrn() {
        return rrn;
    }

    public void setRrn(String rrn) {
        this.rrn = rrn;
    }

    public String getMposTID() {
        return mposTID;
    }

    public void setMposTID(String mposTID) {
        this.mposTID = mposTID;
    }

    public String getMposMID() {
        return mposMID;
    }

    public void setMposMID(String mposMID) {
        this.mposMID = mposMID;
    }

    public String getUdid() {
        return udid;
    }

    public void setUdid(String udid) {
        this.udid = udid;
    }

    public String getIssuerCode() {
        return issuerCode;
    }

    public void setIssuerCode(String issuerCode) {
        this.issuerCode = issuerCode;
    }

    public String getScriptField() {
        return scriptField;
    }

    public void setScriptField(String scriptField) {
        this.scriptField = scriptField;
    }

    public String getTrxType() {
        return trxType;
    }

    public void setTrxType(String trxType) {
        this.trxType = trxType;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(String transactionDate) {
        this.transactionDate = transactionDate;
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public String getAppl() {
        return appl;
    }

    public void setAppl(String appl) {
        this.appl = appl;
    }

    public String getArqc() {
        return arqc;
    }

    public void setArqc(String arqc) {
        this.arqc = arqc;
    }

    public String getMuid() {
        return muid;
    }

    public void setMuid(String muid) {
        this.muid = muid;
    }

    public String getReaderSerial() {
        return readerSerial;
    }

    public void setReaderSerial(String readerSerial) {
        this.readerSerial = readerSerial;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public boolean isFlagNoSignature() {
        return flagNoSignature;
    }

    public void setFlagNoSignature(boolean flagNoSignature) {
        this.flagNoSignature = flagNoSignature;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public String getMerchantAddress() {
        return merchantAddress;
    }

    public void setMerchantAddress(String merchantAddress) {
        this.merchantAddress = merchantAddress;
    }

    public String getDepositRefCode() {
        return depositRefCode;
    }

    public String getAmountDeposit() {
        return amountDeposit;
    }

    public void setDepositRefCode(String depositRefCode) {
        this.depositRefCode = depositRefCode;
    }

    public void setAmountDeposit(String amountDeposit) {
        this.amountDeposit = amountDeposit;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
