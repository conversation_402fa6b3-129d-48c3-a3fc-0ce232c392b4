package com.mpos.sdk.core.model;

import android.text.TextUtils;

import com.mpos.sdk.core.common.MyGson;
import com.mpos.sdk.util.Constants;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.DevicesUtil;
import com.mpos.sdk.util.Utils;

/**
 * Create by anhnguyen on 12/29/20
 */
public class DataCache {

    private static final String TAG = "DataCache";

    private String bankName;
    private String serialNumber;
    private String acc;
    private String pw;
    private String dataJson;
    private long lastTimeCache;
    private static DataCache dataCache;

    public static synchronized DataCache getInstance() {
        if (dataCache == null) {
            dataCache = new DataCache();
        }
        return dataCache;
    }

    public void putDataCache(String bankName, String serialNumber, String acc, String pw, String dataJson) {
        if (Constants.isTypeIntegrationMpos()) {
            this.bankName = bankName;
            this.serialNumber = serialNumber;
            this.acc = acc;
            this.pw = pw;
            this.dataJson = dataJson;
            this.lastTimeCache = System.currentTimeMillis();
        }
    }

    public void putDataCacheByJson(String dataJson) {
        if (Constants.isTypeIntegrationMpos() && !TextUtils.isEmpty(dataJson)) {
            dataCache = MyGson.getGson().fromJson(dataJson, DataCache.class);
        }
    }

    public void clearCache() {
        this.bankName = "";
        this.serialNumber = "";
        this.acc = "";
        this.pw = "";
        this.dataJson = "";
        this.lastTimeCache = 0;
    }

    public boolean compareData(String serialNumber, String acc, String pin) {
        Utils.LOGD(TAG, "compareData() called with: serialNumber = [" + serialNumber + "], acc = [" + acc + "], pin = [" + pin + "]");
        if (TextUtils.isEmpty(serialNumber) || TextUtils.isEmpty(acc) || TextUtils.isEmpty(pin)) {
            return false;
        }
        showCurrCache();
        return serialNumber.equals(this.serialNumber) && acc.equals(this.acc) && pin.equals(this.pw);
    }

    public boolean compareDataWithoutPw(String serialNumber, String acc) {
        Utils.LOGD(TAG, "compareDataWithoutPw() called with: serialNumber = [" + serialNumber + "], acc = [" + acc + "]");
        if (!DevicesUtil.isSP02P12() || TextUtils.isEmpty(serialNumber) || TextUtils.isEmpty(acc)) {
            return false;
        }
        showCurrCache();
        return serialNumber.equals(this.serialNumber) && acc.equals(this.acc);
    }


    /**
     * used for case: login new UI in PR02 (don't connect to device)
     */
    public boolean compareDataWithoutSN(String acc, String pin) {
        Utils.LOGD(TAG, "compareData() called with: acc = [" + acc + "], pin = [" + pin + "]");
        if (TextUtils.isEmpty(acc) || TextUtils.isEmpty(pin)) {
            return false;
        }
        showCurrCache();
        return acc.equals(this.acc) && pin.equals(this.pw);
    }

    public boolean hasData() {
        showCurrCache();
        if (TextUtils.isEmpty(bankName) || TextUtils.isEmpty(serialNumber)
                || TextUtils.isEmpty(acc) || TextUtils.isEmpty(pw)
                || TextUtils.isEmpty(dataJson)
        ) {
            return false;
        }
        return true;
    }

    public boolean hasDataWithoutPw() {
        showCurrCache();
        if (!DevicesUtil.isSP02P12() || TextUtils.isEmpty(bankName) || TextUtils.isEmpty(serialNumber)
                || TextUtils.isEmpty(acc) || TextUtils.isEmpty(dataJson)
        ) {
            return false;
        }
        return true;
    }

    /**
     * used for case: login new UI in PR02 (don't connect to device)
     */
    public boolean hasDataWithoutSN() {
        showCurrCache();
        if (TextUtils.isEmpty(bankName)
                || TextUtils.isEmpty(acc) || TextUtils.isEmpty(pw)
                || TextUtils.isEmpty(dataJson)
        ) {
            return false;
        }
        return true;
    }

    public boolean checkInSessionRangeTime(int minutes) {
        Utils.LOGD(TAG, "checkInSessionRangeTime: lastTimeCache=" + Utils.convertTimestamp(lastTimeCache, 4));
        // minutes * 60*1000
        return System.currentTimeMillis() - lastTimeCache <= minutes*60000L;
    }

    /**
     * check time: lastCache and now is same the day
     *
     * @return true: lastCache same in the day
     */
    public boolean checkSessionInDay() {
        Utils.LOGD(TAG, "checkSessionInDay: lastTimeCache=" + Utils.convertTimestamp(lastTimeCache, 4));

        String lastDayCache = Utils.convertTimestamp(lastTimeCache, 2);

        String currDay = Utils.convertTimestamp(System.currentTimeMillis(), 2);
        return currDay.equals(lastDayCache);
    }

    public String getBankName() {
        return bankName;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public String getAcc() {
        return acc;
    }

    public String getPw() {
        return pw;
    }

    public String getDataJson() {
        return dataJson;
    }

    public long getLastTimeCache() {
        return lastTimeCache;
    }

    private void showCurrCache() {
        Utils.LOGD(TAG, "showCurrCache: " + toString());
    }

    @Override
    public String toString() {
        if (Constants.isTypeIntegrationMpos() && Utils.mDEBUG) {
            return "DataCache{" +
                    "bankName='" + bankName + '\'' +
                    ", serialNumber='" + serialNumber + '\'' +
                    ", acc='" + acc + '\'' +
                    ", pw='" + pw + '\'' +
                    ", lastTimeCache=" + lastTimeCache +
                    ", dataJson='" + dataJson + '\'' +
                    '}';
        }
        else {
            return "";
        }
    }
}
