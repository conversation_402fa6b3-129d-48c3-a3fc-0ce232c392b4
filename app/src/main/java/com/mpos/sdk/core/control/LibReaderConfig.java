package com.mpos.sdk.core.control;

import com.mpos.sdk.BuildConfig;

/**
 * Create by anhnguyen on 6/8/21
 */
public class LibReaderConfig {


    public static final int REQUEST_ENABLE_BT       = 1;
    public static final int REQUEST_DEVICE          = 2;  // PR
    public static final int REQUEST_DEVICE_DSPREAD  = 3;
//    public static final int REQUEST_INPUT_PIN = 3;

    /* stage proccess payment */
    public static final int STAGE_PROCESS_END                	= 20;
    public static final int STAGE_PROCESS_NONE               	= 0;    // AS AFTER SWIPE CARD --> start counter percent 0%
    public static final int STAGE_PROCESS_START             	= 1;    // AS AFTER SWIPE CARD --> start counter percent 0%
    public static final int STAGE_PROCESS_UPGRADING_FW_PR02     = 2;    //
    public static final int STAGE_PROCESS_PROCESS_CARD          = 3;    // AS PROCESS_CARD

    public static final int STAGE_PROCESS_START_MAGSTRIPE    	= 4;    // ( start in 20% ) start call payment_magstripe
    public static final int STAGE_PROCESS_MAGSTRIPE_SALE    	= 5;    // ( start in 20% ) start call payment_magstripe
    public static final int STAGE_PROCESS_WAIT_INPUT_PIN     	= 7;    // ( stop in 50% ) payment_magstripe -> success + request pin => wait input pin
    public static final int STAGE_PROCESS_MAGSTRIPE_PIN     	= 9;    // ( max 80% ) start call payment_magstripe with pin

    public static final int STAGE_PROCESS_START_EMV            	= 11;    // ( start in  20% ) start call payment_emv
    public static final int STAGE_PROCESS_EMV_SALE            	= 13;    // ( start in  20% ) start call payment_emv
    public static final int STAGE_PROCESS_EMV_RUN_SCRIPT        = 15;    // ( start in  50% ) payment_emv -> success + run script
    public static final int STAGE_PROCESS_EMV_CONFIRM           = 17;    // ( max 80% ) after run script -> success => start call confirm_emv
    public static final int STAGE_SIGNATURE                     = 19;    // ( max 80% ) after run script -> success => start call confirm_emv


    /* type error */
    public static final int TYPE_ERROR_DEFAULT                      = -2;        // Default: start send msg sale -> reset to default
    public static final int TYPE_ERROR_NONE                         = -1;        // NONE error
    public static final int TYPE_ERROR_SALE_TIMEOUT_SESSION         = 0;        // time out session
    public static final int TYPE_ERROR_SALE_TIMEOUT_REQUEST         = 1;        // time out by onFail
    public static final int TYPE_ERROR_SALE_FAILURE                 = 2;
    public static final int TYPE_ERROR_CHIP_AID_FAILURE             = 3;
    public static final int TYPE_ERROR_RUN_SCRIPT_FAILURE           = 4;
    public static final int TYPE_ERROR_NO_CVM_CHIP_DENIAL           = 5;        // NAPAS chip: result no-cvm and chip denial
    public static final int TYPE_ERROR_RUN_REVERSE_ONLINE           = 6;
    public static final int TYPE_ERROR_CONFIRM_PAYMENT_FAILURE      = 8;    // case: session timeout, request timeout, error http
    public static final int TYPE_ERROR_CONFIRM_PAYMENT_FAIL_SERVER  = 10;    // case: error return by server
    public static final int TYPE_ERROR_TC_ADVICE_FAILURE            = 12;    // case: session timeout, request timeout, error http
    public static final int TYPE_ERROR_TC_ADVICE_FAIL_SERVER        = 14;    // case: error return by server

    /* type void fail */
    public static final int STATUS_VOID_FAIL_RUN_SUCCESS        = 1;
    public static final int STATUS_VOID_FAIL_NO_RUN             = 0;
    public static final int STATUS_VOID_FAIL_RUN_FAILURE        = -1;

    /* status error finish activity*/
    public static final int STATUS_FINISH_AR_CANCEL_PIN         = 1;
    public static final int STATUS_FINISH_PR_NOT_SUPPORT_BT     = 2;    // not support bluetooth
    public static final int STATUS_FINISH_PR_ABORTED_EXP        = 3;
    public static final int STATUS_FINISH_PR_CANCELED_EXP       = 4;
    public static final int STATUS_FINISH_PR_DENIED_EXP         = 5;
    public static final int STATUS_FINISH_PR_EXCEPTION          = 6;
    public static final int STATUS_FINISH_PR_PIN_LENGTH         = 7;

    /* ui stage */
    public final static int UI_STAGE_SHOW_PROGRESS 	        = -3;   // hiển thị loading
    public final static int UI_STAGE_HIDE_PROGRESS          = -2;   // ấn loading
    public final static int UI_STAGE_FALSE_CONNECT_DEVICE   = -1;   // kết nối với device không thành công
    public final static int UI_STAGE_NO_ENABLE_BLUETOOTH 	= 0;    // điện thoại chưa bật bluetooth
    public final static int UI_STAGE_NO_CONNECT_DEVICE 	    = 1;    // ko có kết nối tới device
    public final static int UI_STAGE_CONNECTING_DEVICE 	    = 2;    // đang kết nối tới device
    public final static int UI_STAGE_CONNECTED_DEVICE 	    = 3;    // đã kết nối device
    public final static int UI_STAGE_WAIT_SWIPE_CARD		= 4;    // chờ quẹt thẻ
    public final static int UI_STAGE_PROCESSING_CARD        = 5;    // đang xử lý thẻ
    public final static int UI_STAGE_ENTER_PIN			    = 6;    // chờ nhập pin
    public final static int UI_STAGE_SCANNING_DEVICE        = 7;    // quét thiết bị
    public final static int UI_STAGE_SELECT_APPLICATION     = 8;    // chọn ứng dụng trên chip
    public final static int UI_STAGE_SENDING_DATA           = 9;    // đang gửi dữ liệu
    public final static int UI_STAGE_GET_MERCHANT_INFO      = 10;    // get merchant info from mpos
    public final static int UI_STAGE_END_SCAN_DEVICE        = 11;   // kết thúc quét thiết bị
    public final static int UI_STAGE_CANCEL_SCAN_DEVICE     = 12;   // cancel bật bluetooth, close popup chọn thiết bị
    public final static int UI_STAGE_START_UPDATE_FW        = 13;
    public final static int UI_STATE_SELECT_PROMOTION       = 14;


    // custom style dialog signature
    protected int TITLE_BG_COLOR      = 0;
    protected int TITLE_TEXT_COLOR    = 0;
    protected int CLEAR_TEXT_COLOR    = 0;
    protected int INFO_BG_COLOR       = 0;
    protected int INFO_TEXT_COLOR     = 0;
    protected int CONTINUE_BG_ICON    = 0;

    protected int SIGNATURE_WIDTH       = 0;
    protected int SIGNATURE_HEIGHT      = 0;


    protected boolean fakeAmount = false;
    protected boolean fakeDate = false;
    protected boolean testJCB = false;
    protected boolean testAmex = false;
    protected boolean testNapas = false;
    protected boolean testZ3 = false;
    protected boolean isFallBack = false;
    protected boolean enableFallback = false;
    protected boolean enableInjectKey = false;





    public void setTITLE_BG_COLOR(int TITLE_BG_COLOR) {
        this.TITLE_BG_COLOR = TITLE_BG_COLOR;
    }

    public void setTITLE_TEXT_COLOR(int TITLE_TEXT_COLOR) {
        this.TITLE_TEXT_COLOR = TITLE_TEXT_COLOR;
    }

    public void setINFO_BG_COLOR(int INFO_BG_COLOR) {
        this.INFO_BG_COLOR = INFO_BG_COLOR;
    }

    public void setINFO_TEXT_COLOR(int INFO_TEXT_COLOR) {
        this.INFO_TEXT_COLOR = INFO_TEXT_COLOR;
    }

    public void setCONTINUE_BG_ICON(int CONTINUE_BG_ICON) {
        this.CONTINUE_BG_ICON = CONTINUE_BG_ICON;
    }

    public int getSIGNATURE_WIDTH() {
        return SIGNATURE_WIDTH;
    }

    public void setSIGNATURE_WIDTH(int SIGNATURE_WIDTH) {
        this.SIGNATURE_WIDTH = SIGNATURE_WIDTH;
    }

    public int getSIGNATURE_HEIGHT() {
        return SIGNATURE_HEIGHT;
    }

    public void setSIGNATURE_HEIGHT(int SIGNATURE_HEIGHT) {
        this.SIGNATURE_HEIGHT = SIGNATURE_HEIGHT;
    }

    public void setCLEAR_TEXT_COLOR(int CLEAR_TEXT_COLOR) {
        this.CLEAR_TEXT_COLOR = CLEAR_TEXT_COLOR;
    }

    //    public void setRunMposMultiAcquirer(boolean runMposMultiAcquirer) {
//        this.runMposMultiAcquirer = runMposMultiAcquirer;
//    }
    /**
     * for certify
     * @param fakeAmount
     */
    public void setFakeAmount(boolean fakeAmount) {
        if (BuildConfig.DEBUG) {
            this.fakeAmount = fakeAmount;
        }
    }

    public void setFakeDate(boolean fakeDate) {
        if (BuildConfig.DEBUG) {
            this.fakeDate = fakeDate;
        }
    }

    public void setTestJCB(boolean testJCB) {
        if (BuildConfig.DEBUG) {
            this.testJCB = testJCB;
        }
    }

    public void setTestAmex(boolean testAmex) {
        if (BuildConfig.DEBUG) {
            this.testAmex = testAmex;
        }
    }

    public void setTestNapas(boolean testNapas) {
        if (BuildConfig.DEBUG) {
            this.testNapas = testNapas;
        }
    }
    public void setTestZ3(boolean testZ3) {
        if (BuildConfig.DEBUG) {
            this.testZ3 = testZ3;
        }
    }

    public void setEnableFallback(boolean enableFallback) {
        this.enableFallback = enableFallback;
    }


}
