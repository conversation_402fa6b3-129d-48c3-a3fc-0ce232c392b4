package com.mpos.sdk.core.control;

import android.content.Context;

import androidx.annotation.NonNull;

import com.loopj.android.http.AsyncHttpResponseHandler;
import com.mpos.sdk.R;
import com.mpos.sdk.core.common.MyGson;
import com.mpos.sdk.core.common.MyTextHttpResponseHandler;
import com.mpos.sdk.core.model.DataError;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.core.modelma.WorkFlow;
import com.mpos.sdk.core.mposinterface.ItfAppendLog;
import com.mpos.sdk.core.network.ApiMultiAcquirerInterface;
import com.mpos.sdk.core.network.MposRestClient;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.MacqUtil;
import com.mpos.sdk.util.Utils;

import org.json.JSONObject;

import java.util.Arrays;

import cz.msebera.android.httpclient.Header;
import cz.msebera.android.httpclient.HttpStatus;
import cz.msebera.android.httpclient.entity.StringEntity;

public final class LibVoidFail {

    private static final String TAG = "LibVoidFail";

	Context context;
	String amount, udid;

    ItfResultVoidFail cbVoidFail;
    ItfAppendLog appendLog;

    public LibVoidFail(@NonNull Context c) {
        this.context = c;
    }

	public LibVoidFail(@NonNull Context c, String amount, String udid, ItfResultVoidFail cb){
		this.context = c;
		this.amount = amount;
		this.udid = udid;
        this.cbVoidFail = cb;
	}

    public void setAppendLog(ItfAppendLog appendLog) {
        this.appendLog = appendLog;
    }

    public void runVoidFailedTransaction(boolean isTimeOut, final DataError errorParent){
		Utils.LOGD(TAG, " udid=" + udid + " errorParent=" + (errorParent == null ? "" : errorParent.getMsg())
				+ " isTimeOut="+isTimeOut);
		StringEntity entity = null;
        appendLog("VOID_FAILED_TRANSACTION " + " udid=" + udid + " errorParent=" + (errorParent == null ? "" : errorParent.getMsg())
                + " isTimeOut=" + isTimeOut);
		String sessionKey;
		String urlMore = "";
		if(isTimeOut){
			sessionKey = PrefLibTV.getInstance(context).getMpK();
			urlMore = ConstantsPay.URL_VOID_FAILD;
		}
		else {
			sessionKey = PrefLibTV.getInstance(context).getSessionKey();
		}
		try {
			JSONObject jo = new JSONObject();
			jo.put("serviceName", ConstantsPay.VOID_FAILED_TRANSACTION);
			jo.put("readerSerialNo", PrefLibTV.getInstance(context).getSerialNumber());
			jo.put("versionNo", android.os.Build.VERSION.RELEASE);
			jo.put("platform", ConstantsPay.PLATFORM);
			jo.put("userID", PrefLibTV.getInstance(context).getUserId());
			
			jo.put("sessionKey", sessionKey);

			jo.put("udid", udid);
			jo.put("amount", amount);
			Utils.LOGD("Data: ", jo.toString());
			entity = new StringEntity(EncodeDecode.doAESEncrypt(jo.toString(), sessionKey));
		} catch (Exception e1) {
			e1.printStackTrace();
		}
        Utils.LOGD(TAG, "runVoidFailedTransaction: send ssK="+sessionKey);
		MposRestClient.getInstance(context).setPaymentTimeout().post(context, ConstantsPay.getUrlServer(context)+urlMore, entity, ConstantsPay.CONTENT_TYPE, new AsyncHttpResponseHandler() {
			@Override
			public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                Utils.LOGD(TAG, "runVoidFailedTransaction isTimeOut="+isTimeOut+" success:" + new String(arg2));
                appendLog("VOID_FAILED_TRANSACTION success");
//				pushNotify("", "", amount);
//				showDialogReversalTrans();
                if (!isTimeOut) {
                    try {
                        JSONObject jRoot = new JSONObject(EncodeDecode.doAESDecrypt(new String(arg2), PrefLibTV.getInstance(context).getSessionKey()));
                        Utils.LOGD(TAG, "onSuccess: " + jRoot.toString());
                        PrefLibTV.getInstance(context).setSessionKey(jRoot.getString("sessionKey"));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                returnResult(true);
			}

			@Override
			public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
//				showDialogError(errorParent);
				appendLog("VOID_FAILED_TRANSACTION onFailure");
//				showDialogReversalTrans();
				Utils.LOGE("runVoidFailedTransaction Error: ", arg3.getMessage());
                returnResult(false);
			}
		});
	}

    public void runVoidFailTransactionMacq() {
        appendLog("call DO_REVERSAL udid=" + udid);
        WorkFlow workFlow = new WorkFlow(null);
        workFlow.setUdid(udid);

        MposRestClient.getInstance(context).postMacq(context, ApiMultiAcquirerInterface.URL_DO_REVERSAL, workFlow, new MyTextHttpResponseHandler(context) {

            @Override
            public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                Utils.LOGD(TAG, "do_reversal onFailure() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");
                DataError dataError = new DataError();
                dataError.build(statusCode, rawJsonData, context.getString(R.string.error_ma_default, String.valueOf(statusCode)));

                appendLog("do_reversal onFailure httpStatus=" + statusCode + dataError.getMsg());
                returnResult(false);
            }

            @Override
            public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                Utils.LOGD(TAG, "do_reversal onSuccess() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonResponse = [" + rawJsonResponse + "]");

                String clearData = MacqUtil.getInstance().parseAndDecryptData(rawJsonResponse);

                Utils.LOGD(TAG, "do_reversal onSuccess: " + clearData);
                boolean result = statusCode == HttpStatus.SC_OK;
                appendLog("do_reversal onSuccess: HttpCode=" + statusCode + " ->" + clearData);
                returnResult(result);
            }
        });
    }

    private void appendLog(String s) {
        if (appendLog != null) {
            appendLog.appendLog(ItfAppendLog.TypeLog.action, s);
        }
    }

    void returnResult(boolean flag) {
        if (cbVoidFail != null) {
            cbVoidFail.onResultVoidFail(flag);
        }
    }

    public interface ItfResultVoidFail{
        void onResultVoidFail(boolean result);
    }

}
