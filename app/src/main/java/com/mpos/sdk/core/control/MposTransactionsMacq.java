package com.mpos.sdk.core.control;

import android.content.Context;
import android.content.res.Configuration;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.mpos.sdk.R;
import com.mpos.sdk.core.common.MyGson;
import com.mpos.sdk.core.common.MyTextHttpResponseHandler;
import com.mpos.sdk.core.model.DataCache;
import com.mpos.sdk.core.model.DataError;
import com.mpos.sdk.core.model.LibError;
import com.mpos.sdk.core.model.PaymentItem;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.core.model.TransItem;
import com.mpos.sdk.core.modelma.DownloadReceiptRes;
import com.mpos.sdk.core.modelma.HistoryRes;
import com.mpos.sdk.core.modelma.HistorySend;
import com.mpos.sdk.core.modelma.Paging;
import com.mpos.sdk.core.modelma.ReceiptSend;
import com.mpos.sdk.core.modelma.SettleSend;
import com.mpos.sdk.core.modelma.TransItemMacq;
import com.mpos.sdk.core.modelma.WfDetailRes;
import com.mpos.sdk.core.modelma.WorkFlow;
import com.mpos.sdk.core.modelma.WorkFlowTxId;
import com.mpos.sdk.core.modelma.WorkFlowUdid;
import com.mpos.sdk.core.network.ApiMultiAcquirerInterface;
import com.mpos.sdk.core.network.MposRestClient;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.MacqUtil;
import com.mpos.sdk.util.Utils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Locale;

import cz.msebera.android.httpclient.Header;
import cz.msebera.android.httpclient.HttpStatus;
import cz.msebera.android.httpclient.entity.StringEntity;

import static com.mpos.sdk.util.ConstantsPay.ERROR_CODE_TIMEOUT_REQ;

/**
 * Create by anhnguyen on 6/10/21
 */
public class MposTransactionsMacq {

    private static final String TAG = "MposTransactionsMacq";

    public enum ActionTrans {
        LIST_TRANS,
        TRANS_DETAIL,
        TRANS_STATUS,
        VOID_TRANS,
        GET_RECEIPT,
        GET_RECEIPT_SETTLE,
        SETTLE,
        SEND_RECEIPT,
        LIST_TRANS_PENDING_SIG,
    }

    protected Context context;
    protected Context contextRes;
    protected SaveLogController sv;

    protected String serialNumber;
    protected String userId;

    protected boolean runMacq = false;

    protected MposTransactionsMacq(@NonNull Context context) {
//        if (Constants.isTypeIntegrationMpos()) {
            this.context = context;
            sv = SaveLogController.getInstance(context);

            checkCustomLanguage();
//        }
        checkRunMacq();
    }

    private void checkRunMacq() {
        String currBankName = PrefLibTV.getInstance(context).getBankName();
        if (ConstantsPay.MPOS_MULTI_ACQUIRER.equals(currBankName)) {
            runMacq = true;
        }
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public void setRunMacq(boolean runMacq) {
        this.runMacq = runMacq;
    }

    public boolean isRunMacq() {
        return runMacq;
    }

    protected String getCurrSerialNumber() {
        if (TextUtils.isEmpty(serialNumber)) {
            serialNumber = PrefLibTV.getInstance(context).getSerialNumber();
        }
        return serialNumber;
    }

    protected String getCurrUserId() {
        if (TextUtils.isEmpty(userId)) {
            userId = PrefLibTV.getInstance(context).getUserId();
        }
        return userId;
    }


    private void checkCustomLanguage() {
        String language = PrefLibTV.getInstance(context).getCustomLanguage();
        Utils.LOGD(TAG, "checkCustomLanguage: " + language);
        if (!TextUtils.isEmpty(language)) {
            Configuration config = new Configuration(context.getResources().getConfiguration());
            config.setLocale(new Locale(language));
            contextRes = context.createConfigurationContext(config);
        }
    }

    protected void getListTransactionCompleteMacq(@NonNull ItfHandlerActionTrans<ArrayList<PaymentItem>> callback) {
        Paging paging = new Paging();
        paging.setPageIndex(0);
        paging.setPageSize(30);

        HistorySend historySend = new HistorySend();
        historySend.setMuid(getCurrUserId());
        historySend.setMposMid(PrefLibTV.getInstance(context).get(PrefLibTV.MPOS_MID, String.class));
//        historySend.setSerialNumber(PrefLibTV.getInstance(context).getSerialNumber());
        historySend.setPaging(paging);

        Utils.LOGD(TAG, "getHistoryMA: " + MyGson.getGson().toJson(historySend));

//        StringEntity entity = ApiMultiAcquirerInterface.getInstance()
//                .buildStringEntity(historySend);
        MposRestClient.getInstance(context).postMacq(context, ApiMultiAcquirerInterface.URL_GET_HISTORY_V2, historySend, new MyTextHttpResponseHandler(context) {

            @Override
            public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                Utils.LOGD(TAG, "GET_HISTORY onFailure() called with: statusCode = [" + statusCode + "], headers = [" + headers + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");
                DataError dataError = new DataError();
                dataError.build(statusCode, rawJsonData, getString(R.string.error_ma_default, String.valueOf(statusCode)));
                processCallbackFail(callback, new DataError(ConstantsPay.ERROR_CODE_TIMEOUT_REQ,
                        dataError.getMsg()), ActionTrans.LIST_TRANS);
            }

            @Override
            public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                Utils.LOGD(TAG, "GET_HISTORY onSuccess() called with: statusCode = [" + statusCode + "], headers = [" + headers + "], rawJsonResponse = [" + rawJsonResponse + "]");
//                DataBaseObj data = MyGson.getGson().fromJson(rawJsonResponse, DataBaseObj.class);
//                String clearData = CryptoInterface.getInstance().decryptData(data.getData());
                String clearData = MacqUtil.getInstance().parseAndDecryptData(rawJsonResponse);

                Utils.LOGD(TAG, "GET_HISTORY onSuccess: " + clearData);

                HistoryRes historyRes = MyGson.parseJson(clearData, HistoryRes.class);
                ArrayList<PaymentItem> arr = new ArrayList<>();
                if (historyRes.getData() != null && historyRes.getData().size() > 0) {
                    for (TransItemMacq item: historyRes.getData()) {
                        arr.add(new PaymentItem(item));
                    }
                }
                callback.onSuccessActionTrans(arr, ActionTrans.LIST_TRANS);
            }
        });
    }

    protected void getTransactionDetailMacq(String transId, @NonNull MposTransactions.ItfHandlerActionTrans<TransItem> callback) {
        WorkFlowTxId workFlowTxId = new WorkFlowTxId(transId);

//        StringEntity entity = ApiMultiAcquirerInterface.getInstance().buildStringEntity(workFlowTxId);
        MposRestClient.getInstance(context).postMacq(context, ApiMultiAcquirerInterface.URL_GET_TRANS_BY_TXID, workFlowTxId, new MyTextHttpResponseHandler(context) {

            @Override
            public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                Utils.LOGD(TAG, "TransDetails onFailure() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");
                DataError dataError = new DataError();
                dataError.build(statusCode, rawJsonData, getString(R.string.error_ma_default, String.valueOf(statusCode)));

                saveLRs(ConstantsPay.SALES_HISTORY_DETAIL + " onFailure httpStatus=" + statusCode);
                processCallbackFail(callback, dataError , ActionTrans.TRANS_DETAIL);

            }

            @Override
            public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                Utils.LOGD(TAG, "TransDetails onSuccess() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonResponse = [" + rawJsonResponse + "]");
                
                String clearData = CryptoInterface.getInstance().decryptRawData(rawJsonResponse);

                Utils.LOGD(TAG, "TransDetails onSuccess: " + clearData);
                saveLRs("TransDetails onSuccess: " + clearData);

                WfDetailRes detailRes = MyGson.parseJson(clearData, WfDetailRes.class);
                TransItem transItem = new TransItem(detailRes);
                callback.onSuccessActionTrans(transItem, ActionTrans.TRANS_DETAIL);
            }
        });
    }

    protected void getTransactionStatusMacqByTxId(String transId, @NonNull ItfHandlerActionTrans<TransItem> callback) {
        WorkFlowTxId workFlowTxId = new WorkFlowTxId(transId);

//        StringEntity entity = ApiMultiAcquirerInterface.getInstance().buildStringEntity(workFlowTxId);
        MposRestClient.getInstance(context).postMacq(context, ApiMultiAcquirerInterface.URL_GET_TRANS_BY_TXID, workFlowTxId, new MyTextHttpResponseHandler(context) {

            @Override
            public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                Utils.LOGD(TAG, "TransDetailsByTxid onFailure() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");
                DataError dataError = new DataError();
                dataError.build(statusCode, rawJsonData, getString(R.string.error_ma_default, String.valueOf(statusCode)));

                saveLRs(ConstantsPay.SALES_HISTORY_DETAIL + " onFailure httpStatus=" + statusCode);
                processCallbackFail(callback, dataError , ActionTrans.TRANS_STATUS);

            }

            @Override
            public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                Utils.LOGD(TAG, "TransDetailsByTxid onSuccess() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonResponse = [" + rawJsonResponse + "]");

                String clearData = CryptoInterface.getInstance().decryptRawData(rawJsonResponse);

                Utils.LOGD(TAG, "TransDetailsByTxid onSuccess: " + clearData);

                WfDetailRes detailRes = MyGson.parseJson(clearData, WfDetailRes.class);
                TransItem transItem = new TransItem(detailRes);
                callback.onSuccessActionTrans(transItem, ActionTrans.TRANS_STATUS);
            }
        });
    }
    protected <T> void getTransactionStatusMacqByUdid(String udid, @NonNull ItfHandlerActionTrans<T> callback, Class<T> anonymousClass) {
        WorkFlowUdid workFlowUdid = new WorkFlowUdid(udid);

//        StringEntity entity = ApiMultiAcquirerInterface.getInstance().buildStringEntity(workFlowUdid);
        MposRestClient.getInstance(context).postMacq(context, ApiMultiAcquirerInterface.URL_GET_TRANS_BY_UDID, workFlowUdid, new MyTextHttpResponseHandler(context) {

            @Override
            public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                Utils.LOGD(TAG, "TransDetailsByUdid onFailure() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");
                DataError dataError = new DataError();
                dataError.build(statusCode, rawJsonData, getString(R.string.error_ma_default, String.valueOf(statusCode)));

                saveLRs("GET_TRANS_BY_UDID onFailure httpStatus=" + statusCode);
                processCallbackFail(callback, dataError , ActionTrans.TRANS_STATUS);
            }

            @Override
            public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                Utils.LOGD(TAG, "TransDetailsByUdid onSuccess() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonResponse = [" + rawJsonResponse + "]");

                String clearData = CryptoInterface.getInstance().decryptRawData(rawJsonResponse);

                Utils.LOGD(TAG, "TransDetailsByUdid onSuccess: " + clearData);

                WfDetailRes detailRes = MyGson.parseJson(clearData, WfDetailRes.class);
                Utils.LOGD(TAG, "onSuccessApi: stauts=" + detailRes.getStatus());
                try {
                    if (anonymousClass == TransItem.class) {
                        TransItem transItem = new TransItem(detailRes);
                        callback.onSuccessActionTrans((T) transItem, ActionTrans.TRANS_STATUS);
                    }
                    else {
                        callback.onSuccessActionTrans((T) detailRes, ActionTrans.TRANS_STATUS);
                    }
                } catch (Exception e) {
                    saveLRs("GET_TRANS_BY_UDID onFailure httpStatus=" + statusCode);

                    callback.onSuccessActionTrans(null, ActionTrans.TRANS_STATUS);
                }
            }
        });
    }

    protected void voidTransactionMacq(String wfId, @NonNull ItfHandlerActionTrans<Boolean> callback) {
        WorkFlow workFlow = new WorkFlow(wfId);

//        StringEntity entity = ApiMultiAcquirerInterface.getInstance().buildStringEntity(workFlow);

        MposRestClient.getInstance(context).postMacq(context, ApiMultiAcquirerInterface.URL_DO_VOID, workFlow, new MyTextHttpResponseHandler(context) {

            @Override
            public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                Utils.LOGD(TAG, "voidTransactionMacq onFailure() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");
                DataError dataError = new DataError();
                dataError.build(statusCode, rawJsonData, getString(R.string.error_ma_default, String.valueOf(statusCode)));

                saveLRs("URL_DO_VOID onFailure httpStatus=" + statusCode);
                processCallbackFail(callback, dataError , ActionTrans.VOID_TRANS);
            }

            @Override
            public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                Utils.LOGD(TAG, "voidTransactionMacq onSuccess() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonResponse = [" + rawJsonResponse + "]");

                String clearData = MacqUtil.getInstance().parseAndDecryptData(rawJsonResponse);

                Utils.LOGD(TAG, "voidMultiAcquirer onSuccess: " + clearData);
                boolean result = statusCode == HttpStatus.SC_OK;
                callback.onSuccessActionTrans(result, ActionTrans.VOID_TRANS);
            }
        });
    }

    protected void getListTransactionWaitSignatureMacq(@NonNull ItfHandlerActionTrans<TransItem> callback) {

    }

    protected void getTransactionReceiptMacq(String txid, ItfHandlerActionTrans<String> callback) {
        ReceiptSend receiptSend = new ReceiptSend(txid, null);
//        receiptSend.setWidthSize(Constants.WIDTH_IMAGE_RECEIPT_MACQ);

//        StringEntity entity = ApiMultiAcquirerInterface.getInstance().buildStringEntity(receiptSend);
        MposRestClient.getInstance(context).postMacq(context, ApiMultiAcquirerInterface.URL_GET_RECEIPT, receiptSend, new MyTextHttpResponseHandler(context) {

            @Override
            public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                Utils.LOGD(TAG,  "downloadReceiptMA onFailure() called with: statusCode = [" + statusCode + "], headers = [" + headers + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");
                DataError dataError = new DataError();
                dataError.build(statusCode, rawJsonData, getString(R.string.error_ma_default, String.valueOf(statusCode)));
                processCallbackFail(callback, dataError , ActionTrans.GET_RECEIPT);
            }

            @Override
            public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                Utils.LOGD(TAG,  "downloadReceiptMA onSuccess() called with: statusCode = [" + statusCode + "], headers = [" + headers + "], rawJsonResponse = [" + rawJsonResponse + "]");
//                DataBaseObj data = MyGson.getGson().fromJson(rawJsonResponse, DataBaseObj.class);
//                String clearData = CryptoInterface.getInstance().decryptData(data.getData());
                String clearData = MacqUtil.getInstance().parseAndDecryptData(rawJsonResponse);

                DownloadReceiptRes receiptRes = MyGson.parseJson(clearData, DownloadReceiptRes.class);
                callback.onSuccessActionTrans(receiptRes.getReceiptBase64(), ActionTrans.GET_RECEIPT);
            }
        });
    }

    protected void settleAllTransactionMacq(@NonNull ItfHandlerActionTrans<Boolean> callback) {
        SettleSend workFlow = new SettleSend(PrefLibTV.getInstance(context).get(PrefLibTV.MPOS_TID, String.class));
//        StringEntity entity = ApiMultiAcquirerInterface.getInstance().buildStringEntity(workFlow);
        MposRestClient.getInstance(context).postMacq(context, ApiMultiAcquirerInterface.URL_DO_SETTLEMENT, workFlow, new MyTextHttpResponseHandler(context) {

            @Override
            public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                Utils.LOGD(TAG, "doSettle onFailure() called with: statusCode = [" + statusCode + "], headers = [" + headers + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");
                DataError dataError = new DataError();
                dataError.build(statusCode, rawJsonData, getString(R.string.error_ma_default, String.valueOf(statusCode)));
                processCallbackFail(callback, dataError , ActionTrans.SETTLE);
            }

            @Override
            public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                Utils.LOGD(TAG, "doSettle onSuccess() called with: statusCode = [" + statusCode + "], headers = [" + headers + "], rawJsonResponse = [" + rawJsonResponse + "]");
//                DataBaseObj data = MyGson.getGson().fromJson(rawJsonResponse, DataBaseObj.class);
//                String clearData = CryptoInterface.getInstance().decryptData(data.getData());
                String clearData = MacqUtil.getInstance().parseAndDecryptData(rawJsonResponse);

                Utils.LOGD(TAG, "doSettle onSuccess: " + clearData);
                boolean result = statusCode == HttpStatus.SC_OK;
                callback.onSuccessActionTrans(result, ActionTrans.SETTLE);
            }
        });
    }

    protected void sendReceiptWithTransactionMacq(String transId, String email, @NonNull MposTransactions.ItfHandlerActionTrans<Boolean> callback) {
        ReceiptSend receiptSend = new ReceiptSend(transId, email);
//        StringEntity entity = ApiMultiAcquirerInterface.getInstance().buildStringEntity(receiptSend);
        MposRestClient.getInstance(context).postMacq(context, ApiMultiAcquirerInterface.URL_SEND_RECEIPT, receiptSend, new MyTextHttpResponseHandler(context) {

            @Override
            public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                Utils.LOGD(TAG, "sendReceiptToEmailMA onFailure() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");
                saveLRs(ConstantsPay.SEND_RECEIPT + " MA--onFailure ");

                DataError dataError = new DataError();
                dataError.build(statusCode, rawJsonData, getString(R.string.error_ma_default, statusCode));
                processCallbackFail(callback, dataError, ActionTrans.SEND_RECEIPT);
            }

            @Override
            public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                Utils.LOGD(TAG, "sendReceiptToEmailMA onSuccess() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonResponse = [" + rawJsonResponse + "]");

                String clearData = CryptoInterface.getInstance().decryptRawData(rawJsonResponse);

                Utils.LOGD(TAG, "voidMultiAcquirer onSuccess: " + clearData);
                saveLRs(ConstantsPay.SEND_RECEIPT + " MA--success statusHttp="+statusCode);
                if (statusCode == HttpStatus.SC_OK) {
                    callback.onSuccessActionTrans(true, ActionTrans.SEND_RECEIPT);
                }
                else {
                    DataError dataError = new DataError();
                    dataError.build(statusCode, clearData, getString(R.string.error_ma_default));
                    processCallbackFail(callback, dataError, ActionTrans.SEND_RECEIPT);
                }
            }
        });
    }


    //--------------------------------------- CALLBACK ----------------------------------------------------
    protected void processCallbackFail(ItfHandlerActionTrans callback, DataError dataError, ActionTrans typeAction) {
        if (dataError!=null && (LibError.isSessionExpired(dataError.getErrorCode())
                || dataError.getErrorCode() == ERROR_CODE_TIMEOUT_REQ)) {
            PrefLibTV.getInstance(context).setTKL2("");
            DataCache.getInstance().clearCache();
        }
        sl();
        callback.onFailureActionTrans(dataError, typeAction);
    }

    protected String getString(int stringId) {
        if (contextRes == null) {
            return context.getString(stringId);
        }
        else {
            return contextRes.getString(stringId);
        }
    }
    protected String getString(int stringId, Object... obj) {
        if (contextRes == null) {
            return context.getString(stringId, obj);
        }
        else {
            return contextRes.getString(stringId, obj);
        }
    }

    //--------------------------------------- LOG ----------------------------------------------------
//    private void saveLAct(String text) {
//        sv.appendLogAction(text);
//    }

    protected void saveLRq(String text) {
        sv.appendLogRequestApi(text);
    }

    protected void saveLRs(String text) {
        sv.appendLogResponse(text);
    }

    protected void sl() {
        if (sv != null) {
            sv.saveLog();
        }
    }

    public interface ItfHandlerActionTrans<T>{
        void onFailureActionTrans(@NonNull DataError dataError, ActionTrans typeAction);
        void onSuccessActionTrans(T obj, ActionTrans typeAction);
    }

}