package com.mpos.sdk.core.control;

import android.annotation.SuppressLint;
import android.app.AlarmManager;
import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;

import com.mpos.sdk.BuildConfig;
import com.mpos.sdk.R;
import com.mpos.sdk.core.model.DataError;
import com.mpos.sdk.core.model.DataPrePay;
import com.mpos.sdk.core.model.EmvConfigSp01;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.core.modelma.DataSaleRes;
import com.mpos.sdk.core.modelma.DataSaleSend;
import com.mpos.sdk.core.mposinterface.ItfHandlerSwipeIndustryCard;
import com.mpos.sdk.core.mposinterface.MposP20lListener;
import com.mpos.sdk.util.CardUtils;
import com.mpos.sdk.util.Constants;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.HexUtil;
import com.mpos.sdk.util.MyTextUtils;
import com.mpos.sdk.util.PayUtils;
import com.mpos.sdk.util.Utils;
import com.whty.smartpos.service.EMVTermConfig;
import com.whty.smartpos.service.EMVTransData;
import com.whty.smartpos.service.IPosService;
import com.whty.smartpos.service.OnlineResult;
import com.whty.smartpos.service.PINResult;
import com.whty.smartpos.service.SmartPosService;
import com.whty.smartpos.tysmartposapi.ITYSmartPosApi;
import com.whty.smartpos.tysmartposapi.OperationResult;
import com.whty.smartpos.tysmartposapi.ResultCode;
import com.whty.smartpos.tysmartposapi.cardreader.CardInfo;
import com.whty.smartpos.tysmartposapi.cardreader.CardReaderConstrants;
import com.whty.smartpos.tysmartposapi.emv.EMVConstrants;
import com.whty.smartpos.tysmartposapi.emv.EMVTransListener;
import com.whty.smartpos.tysmartposapi.pinpad.PinPadConfig;
import com.whty.smartpos.tysmartposapi.pinpad.PinPadConstrants;
import com.whty.smartpos.tysmartposapi.pinpad.PinResult;
import com.whty.smartpos.utils.GPMethods;

import java.util.HashMap;
import java.util.List;
import java.util.TimeZone;

import static com.mpos.sdk.util.ConstantsPay.ERROR_CODE_SP01_NO_CARD_DATA;

public final class LibP20L extends LibReaderController implements MposP20lListener {

    private static final String TAG = "LibP20L";

    public final String PREFIX_P20L = "SP01";    // SMART-POS-01

    private ITYSmartPosApi smartPosApi;

    int SWIPE_TIMEOUT = 40; // SECOND

    private String field_55_cache;
    private byte[] TVR;

    private boolean soundEnterPin = true;
    private boolean errorEnterPin = false;

    private int keyDataPosition = 0;

    private PinResult pinResult;

    ItfHandlerSwipeIndustryCard callbackSwipeIndustryCard;

    HashMap<String, String> mapCharIndustryCard;

    public LibP20L(Context c){
        super(c);
        initSmartPos(c);
    }

    public LibP20L(Context c, DataPrePay dataPrePay, LibDspreadReader.ItfUpdateViewDspread updateUi, ItfResultPay callbackResult) {
        super(c, dataPrePay);

        this.cbUpdateUI = updateUi;
        initVariable(callbackResult);

        initSmartPos(c);

        checkNeedInitCertify();
    }


    public void setSoundEnterPin(boolean soundEnterPin) {
        this.soundEnterPin = soundEnterPin;
    }

    public void setCallbackSwipeIndustryCard(ItfHandlerSwipeIndustryCard callbackSwipeIndustryCard) {
        if (Constants.isTypeIntegrationMpos() || Constants.isTypeIntegrationMpos3rd()) {
            this.callbackSwipeIndustryCard = callbackSwipeIndustryCard;
        }
    }

    /**
     * set timeout wait search card
     * @param swipeTimeout is second and in range: [30-300] second
     */
    public void setSWIPE_TIMEOUT(int swipeTimeout) {
        if (Constants.isTypeIntegrationMpos() || Constants.isTypeIntegrationMpos3rd()) {
            if (30 <= swipeTimeout && swipeTimeout <= 300) {
                this.SWIPE_TIMEOUT = swipeTimeout;
            }
        }
    }

    public void setMapCharIndustryCard(HashMap<String, String> mapCharIndustryCard) {
        this.mapCharIndustryCard = mapCharIndustryCard;
    }

    private void initSmartPos(Context c) {
        Utils.LOGD(TAG, "initSmartPos: -->");
        smartPosApi = ITYSmartPosApi.get(c.getApplicationContext());
        smartPosApi.setListener(new MyP20LListener(this));
        // todo test
//        if (BuildConfig.DEBUG && Constants.isTypeIntegrationMpos()) {
//            smartPosApi.setLogLevel(5);
//        }
    }

    private void checkNeedInitCertify() {
        if (testNapas) {
            itfCertifyMacq = new ItfCertifyMacq() {
                @Override
                public void showDialogUseChip() {
                    udid = udid + "-fb";
                    showDialogWarningUseChip();
                }

                @Override
                public void sendErrorCodeToPosMacq(int codeError, String emvScript) {
                    sendErrorCodeToPos(String.valueOf(codeError), emvScript);
                }

                @Override
                public void sendDenialMacq() {
                    sendDenialToTerminal();
                }
            };
        }
    }

    public String getSerialNumber() {
        String originSN = smartPosApi.getDeviceSN();
        appendLogAction("getSN=" + originSN);
        if (!TextUtils.isEmpty(originSN) && originSN.startsWith(PREFIX_P20L)) {
            return originSN;
        } else if (!TextUtils.isEmpty(originSN) && originSN.length() > 13) {
            return PREFIX_P20L + originSN.substring(originSN.length() - (14 - PREFIX_P20L.length()));   // 14: ~ 7byte KSN
        } else {
            String cacheSN = PrefLibTV.getInstance(context).getSerialNumber();
            appendLogAction("getSN cache="+cacheSN);
            if (!TextUtils.isEmpty(cacheSN) && cacheSN.startsWith(PREFIX_P20L)) {
                return cacheSN;
            }
            return "";
        }
    }

    public ITYSmartPosApi getSmartPosApi() {
        return smartPosApi;
    }

    public String  getPosKcvP20LIpek(int keyPosition) {
        OperationResult operationResultIpek = smartPosApi.getCheckValue(keyPosition, PinPadConstrants.KEY_ID_FIRST_TDKEY);
        return operationResultIpek.getData();
    }
    public String  getPosKcvP20LTMK(int keyPosition) {
        OperationResult operationResult = smartPosApi.getCheckValue(keyPosition, PinPadConstrants.KEY_ID_FIRST_MAINKEY);
        return operationResult.getData();
    }

    public boolean isSupportUpgradeFw() {
        String data = smartPosApi.getDevicePN().getData();
        appendLogAction("DevicePN=" + data);
        return "P20L".equalsIgnoreCase(data) || "P20LKH".equalsIgnoreCase(data) || "P20LKHG".equalsIgnoreCase(data);
    }

    public boolean canUpgradeFw(String versionNew) {
        if (isSupportUpgradeFw()) {
            String versionOld = smartPosApi.getDeviceVersion().getSoftVersion();
            appendLogAction("vNew=" + versionNew + " vOld=" + versionOld);
            if (!versionOld.equals(versionNew)) {
                return true;
            }
        }
        return false;
    }

    @Override
    protected String getReaderType() {
        return ConstantsPay.READER_TYPE_P20L;
    }
/*
    private AsyncStartPayment asyncStartPayment;
    public void startPaymentWitAsyncTask() {
        cancelAsyncPayment();
        Utils.LOGD(TAG, "startPaymentWitAsyncTask: -->");
        asyncStartPayment = new AsyncStartPayment(this);
        asyncStartPayment.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR);
    }

    private void cancelAsyncPayment() {
        if (asyncStartPayment != null) {
            asyncStartPayment.cancel(true);
            asyncStartPayment = null;
        }
    }

    private static class AsyncStartPayment extends AsyncTask<Void, Void, Void> {

        WeakReference<LibP20L> weakReference;

        AsyncStartPayment(LibP20L helper) {
            this.weakReference = new WeakReference<>(helper);
        }


        @Override
        protected Void doInBackground(Void... voids) {
            Utils.LOGD(TAG, "doInBackground: ====>>");
            if (weakReference != null && !isCancelled() && weakReference.get().context != null) {
                weakReference.get().startReadCard();
            }
            return null;
        }
    }*/

    private void initVariable() {
        initVariableNewPayment();
        encDataMag = "";
        encTrack1 = "";
        encTrack2 = "";
        ksn = "";
        pinResult = null;
        isContactLess = false;
        TVR = null;
    }

    protected void startReadCard() {
        Utils.LOGD(TAG, "startPayment: ==>");
        initVariable();
        updateViewByStage(UI_STAGE_WAIT_SWIPE_CARD);
        //tartSearchCard(boolean msr, boolean ic, boolean contactlessIc, int timeout);
        if (Utils.checkTypeBuildIsRelease()) {
            try {
                checkStatusPosService();
                checkResetTermConfigSP01();
            } catch (Exception e) {
                appendLogAction("error: " + e.getMessage());
                e.printStackTrace();
            }
        }

        initPresale();

        initKeyPosition();

        if ((Constants.isTypeIntegrationMpos() || Constants.isTypeIntegrationMpos3rd())
                && callbackSwipeIndustryCard != null) {
            processIndustryCard();
            return;
        }

        int result = smartPosApi.startSearchCard(true, true, true, SWIPE_TIMEOUT);
        Utils.LOGD(TAG, "startPayment: typeSwipe="+result);
        appendLogAction("resultSearchCard=" + result+" kcvIpek="+getPosKcvP20LIpek(keyDataPosition));
        switch (result) {
            case CardReaderConstrants.SEARCH_CARD_MAG:
                processMagCard();
                break;
            case CardReaderConstrants.SEARCH_CARD_MAG_ERROR:
                break;
            case CardReaderConstrants.SEARCH_CARD_IC_IN:
                processEmv(true);
                break;
            case CardReaderConstrants.SEARCH_CARD_IC_OUT:

                break;
            case CardReaderConstrants.SEARCH_CARD_IC_ERROR:
                if (enableFallback) {
                    runProcessOnUiThread(this::showDialogFallBack);
                }
                else {
                    showDialogErrorOnUiThread(getString(R.string.transaction_not_icc));
                }
                break;
            case CardReaderConstrants.SEARCH_CARD_RF_IN:
//                initSoundEffect();
                showToast(getString(R.string.msg_processing_NFC_card));
                processEmv(false);
                break;
            case CardReaderConstrants.SEARCH_CARD_RF_OUT:

                break;
            case CardReaderConstrants.SEARCH_CARD_TIME_OUT:
                showDialogErrorOnUiThread(new DataError(ConstantsPay.ERROR_CODE_TIMEOUT_SWIPE_CARD, getString(R.string.error_timeout_wait_card)));
                break;
            case CardReaderConstrants.SEARCH_CARD_CANCEL:

                break;
        }
    }

    private void processIndustryCard() {
        try {
            String track1 = "";
            Utils.LOGD(TAG, "00--owner card: start--->");
            OperationResult o = smartPosApi.transceive("FD3F010000"); //①Switch to a non-bank card mode
            Utils.LOGD(TAG, "11--owner card: " + o.getStatusCode());
            appendLogAction("SN="+ getSerialNumber() + " industry card: 1-> status=" + o.getStatusCode());
            int cardStatus = -1;
            if (o.getStatusCode() == ResultCode.SUCCESS) {
                cardStatus = smartPosApi.startSearchCard(true, false, false, SWIPE_TIMEOUT);//②Start searching for magnetic stripe card
                Utils.LOGD(TAG, "22--owner card: " + cardStatus);
                appendLogAction("industry card: 2-> cardStatus=" + cardStatus);
                if (cardStatus == CardReaderConstrants.SEARCH_CARD_MAG) {
                    o = smartPosApi.transceive("F0CF000000");//③Get track 1 data
                    Utils.LOGD(TAG, "33--owner card: " + o.getStatusCode());
                    appendLogAction("industry card: 3-> StatusCode=" + o.getStatusCode());
                    if (o.getStatusCode() == ResultCode.SUCCESS) {
                        track1 = o.getData();//Get track 1 data
                        Utils.LOGD(TAG, "44--owner card: track1=" + track1);
                        appendLogAction("industry card: 4-> reader return track1=" + track1);
                    }
                }
                smartPosApi.transceive("FD3F000000");//④Switch to bank card mode
            }
            Utils.LOGD(TAG, "55--owner card: end--->cardStatus=" + cardStatus);

            CardUtils cardUtils = new CardUtils();
            appendLogAction("mapChar= " + (mapCharIndustryCard == null ? "useDefault" : "had set, length=" + mapCharIndustryCard.size()));
            track1 = cardUtils.parseInfoFromTrack1(track1, mapCharIndustryCard);
            appendLogAction("industry card: 5-> after parse track1=" + track1);
            callbackIndustryCardSuccess(cardStatus, track1);
        } catch (Exception e) {
            e.printStackTrace();
            callbackIndustryCardFail();
        }
    }

    private void callbackIndustryCardSuccess(int cardStatus, String track1) {
        callbackSwipeIndustryCard.onHandlerSwipeIndustryCard(cardStatus, track1, null, null);
    }

    private void callbackIndustryCardFail() {
        callbackSwipeIndustryCard.onHandlerSwipeIndustryCard(-2, "", null, null);
    }

    public boolean updateEmvTermConfig(String currency, String terminalCap){
        if (!BuildConfig.DEBUG) {
            return false;
        }
        EMVTermConfig emvTermConfig = new EMVTermConfig();
        emvTermConfig.setReferCurrCon(0);
        emvTermConfig.setMerchName("MPOS");
        emvTermConfig.setMerchCateCode(null);
//                emvTermConfig.setMerchId("301310070118940");
//                emvTermConfig.setTermId("37677022");
        emvTermConfig.setTermType(34);              // 34 is hex of 22
//                emvTermConfig.setCapability("E008C8");
        if (TextUtils.isEmpty(terminalCap) || terminalCap.length() != 6) {
            emvTermConfig.setCapability("E0B8C8");
        }
        else {
            emvTermConfig.setCapability(terminalCap);
        }
//                emvTermConfig.setExtCapability("6000D01001");
//                emvTermConfig.setTransCurrCode("0704");   // vnd
        emvTermConfig.setTransCurrCode(currency);     // usd
//        emvTermConfig.setTransCurrCode("0840");     // usd
        emvTermConfig.setTransCurrExp(0);           // 9f36
//                emvTermConfig.setReferCurrCode(null);
//                emvTermConfig.setReferCurrExp(0);
        emvTermConfig.setCountryCode("0704");
        emvTermConfig.setExtField(null);
        appendLogAction("param emvTermConfig : " + emvTermConfig + "\n");
        return smartPosApi.setTermConfig(emvTermConfig);
    }

    private void checkStatusPosService() {
        IPosService posService = SmartPosService.get();
        appendLogAction("initSp01: posService=" + (posService == null ? "null" : "ok"));
    }

    private void checkResetTermConfigSP01() throws Exception{
        String terminalCap = "E0B8C8";
        String currency = "0704";
        EMVTermConfig emvTermConfigOld = smartPosApi.getTermConfig();
        if (emvTermConfigOld == null) {
            appendLogAction("TermConfigSP01 old=null");
            return;
        }
        String configOld = emvTermConfigOld.toString();

        appendLogAction("TermConfigSP01 old=" + configOld+" SN="+getSerialNumber());
        Utils.LOGD(TAG, "checkResetTermConfigSP01: config old=" + configOld);

        if (!emvTermConfigOld.getCapability().equals(terminalCap)
                || !emvTermConfigOld.getTransCurrCode().equals(currency)) {

            EMVTermConfig emvTermConfig = new EMVTermConfig();
            emvTermConfig.setReferCurrCon(0);
            emvTermConfig.setMerchName("MPOS");
            emvTermConfig.setMerchCateCode(null);
            emvTermConfig.setTermType(34);              // 34 is hex of 22
            emvTermConfig.setCapability(terminalCap);

            emvTermConfig.setTransCurrCode(currency);
            emvTermConfig.setTransCurrExp(0);
            emvTermConfig.setCountryCode(currency);
            emvTermConfig.setExtField(null);

            smartPosApi.setTermConfig(emvTermConfig);
            EMVTermConfig emvTermConfigNew = smartPosApi.getTermConfig();
            String configNew = emvTermConfigNew.toString();

            appendLogAction("TermConfigSP01 new=" + configNew);
            Utils.LOGD(TAG, "resetTermConfigSP01: new=" + configNew);
        }
    }

    private void initKeyPosition() {
        int typeServer = getTypeServer();
        /*if (isRunMacq) {
            typeServer = ConstantsPay.getTypeServerByBanKName(preSaleConfig.getAcquirerMagstripe());
        }
        else {
            typeServer = PrefLibTV.getInstance(context).getFlagServer();
        }*/
        /*keyDataPosition = getKeyIndexByFlagServer(typeServer);
        smartPosApi.selectKeyGroup(keyDataPosition);
        Utils.LOGD(TAG, "typeServer=" + typeServer + " initKeyPosition= " + keyDataPosition);
        appendLogAction("flagS=" + typeServer + " index=" + keyDataPosition);*/
        changeKeyPosition(typeServer);
    }

    private void changeKeyPosition() {
        changeKeyPosition(ConstantsPay.getTypeServerByBanKName(preSaleConfig.getAcquirerMagstripe()));
    }
    private void changeKeyPosition(int typeServer) {
        keyDataPosition = getKeyIndexByFlagServer(typeServer);
        smartPosApi.selectKeyGroup(keyDataPosition);
        Utils.LOGD(TAG, "changeKeyPosition typeServer=" + typeServer + " initKeyPosition= " + keyDataPosition);
        appendLogAction("flagS=" + typeServer + " index=" + keyDataPosition);
    }

    /*public int getKeyIndexByFlagServer(int typeServer) {
        int index = -1;
        if (typeServer == ConstantsPay.SERVER_SCB) {
            index = POSITION_KEY_STB;
        }
        else if (typeServer == ConstantsPay.SERVER_BIDV) {
            index = POSITION_KEY_BIDV;
        }
        else if (typeServer == ConstantsPay.SERVER_VTB) {
            index = POSITION_KEY_VTB;
        }
        else if (typeServer == ConstantsPay.SERVER_VCB) {
            index = POSITION_KEY_VCB;
        }
        else if (typeServer == ConstantsPay.SERVER_TCB) {
            index = POSITION_KEY_TCB;
        }
//        else if (typeServer == ConstantsPay.SERVER_MPOS_ACQUIRER) {
//            index = LibDspreadReader.POSITION_KEY_MPOS;
//        }
        return index;
    }*/

//    private void initSoundEffect() {
//        SoundEffects.getInstance().init(context);
//    }

    private void playSoundBeep() {
        appendLogAction("playSoundBeep");
        SoundEffects.getInstance().playSoundBeep();
    }

    private void processMagCard() {
        updateViewByStage(UI_STAGE_PROCESSING_CARD);
        currStageProcess = STAGE_PROCESS_START_MAGSTRIPE;
        setNameTypeCard(ConstantsPay.CARD_MAGSTRIPE);

        CardInfo cardInfo = smartPosApi.getMagCardData();
        encTrack1 = cardInfo.getEncTrack1();
        encTrack2 = cardInfo.getEncTrack2();

        encDataMag = (TextUtils.isEmpty(encTrack1) ? "" : encTrack1) + (TextUtils.isEmpty(encTrack2) ? "" : encTrack2);
        ksn = smartPosApi.dukptUpdateKSN(PinPadConstrants.GET_KSN).getData();

        initMaskedPan(smartPosApi.getMaskPan());

        appendLogAction("1-encT1=" + (TextUtils.isEmpty(encTrack1) ? "empty" : "exit-" + encTrack1.length())
                + " encT2=" + (TextUtils.isEmpty(encTrack2) ? "empty" : "exit-" + encTrack2.length()));

        CardUtils cardUtils = new CardUtils();
        if (typeProcessCard == TYPE_PROCESS_GET_CARD) {
            callbackCardData();
        }
        else if (isRunMacq && preSaleConfig != null && !TextUtils.isEmpty(maskedPan)
                && cardUtils.checkBinExitInList(maskedPan, preSaleConfig.getRequirePinPrefixes())
        ) {
            if (testNapas && amount.equals("10000")) {
                sendMagSales();
            } else {
                changeKeyPosition();

                String wk = getWorkingKey();//preSaleConfig.geteZPKDomestic();
                processAtmPin(wk);
            }
        }
        else {
            sendMagSales();
        }
    }

    private void processEmv(boolean isContact) {
        updateViewByStage(UI_STAGE_PROCESSING_CARD);
        currStageProcess = STAGE_PROCESS_START_EMV;

        EMVTransData emvTransData = new EMVTransData();
        emvTransData.setAmount(Long.parseLong(amount + (fakeAmount ? "00" : "")));

        emvTransData.setTransType((byte) 0x00);
        if (Utils.checkTypeBuildIsCertify() && fakeDate) {
            String currDate = "2020" + Utils.convertTimestamp(System.currentTimeMillis(), "MMdd");
            emvTransData.setTransDate(currDate);
        } else {
            emvTransData.setTransDate(Utils.convertTimestamp(System.currentTimeMillis(), "yyyyMMdd"));
        }
        emvTransData.setTransTime(Utils.convertTimestamp(System.currentTimeMillis(), "HHmmss"));
        emvTransData.setSupportSM(false);
//        emvTransData.setCardAuth(false);
//        emvTransData.setForceOnline(true);
        emvTransData.setCardAuth(true);
        emvTransData.setForceOnline(false);
        emvTransData.setSupportEC(false);
        emvTransData.setSupportCVM(true);
        if (isContact) {
            setNameTypeCard(ConstantsPay.CARD_EMV);
            isContactLess = false;
            emvTransData.setFlow(EMVConstrants.STANDARD_PBOC);
            emvTransData.setChannelType(EMVConstrants.CONTACT);
        }
        else {
            setNameTypeCard(ConstantsPay.CARD_NFC);
            isContactLess = true;
            emvTransData.setFlow(EMVConstrants.QUICK_PBOC);
//            emvTransData.setChannelType(1);
            emvTransData.setChannelType(EMVConstrants.CONTACTLESS);
        }
        Utils.LOGD(TAG, "processEmv: param emvTransData : " + emvTransData);
        appendLogAction("param emvTransData : " + emvTransData);
        runOnUiThread(this::startHandleCheckResponsePosService);

        smartPosApi.emvProcess(emvTransData, new MyEmvListener());
    }

    public void cancelReadCard() {}
    public void closeReadCard(){
        cancelHandleCheckResponsePosService();
        cancelAsyncPayment();
        if (smartPosApi != null) {
            smartPosApi.stopSearchCard();
        }
    }

    @SuppressLint("MissingPermission")
    public void processUpdateTimeZoneP20L() {
        try {
            TimeZone timeZone = TimeZone.getDefault();
            String currIdTimeZone = timeZone.getID();
            Log.d(TAG, "processUpdateTimeZoneP20L: " + timeZone.getDisplayName() + " " + timeZone.getID());
            appendLogAction("timezone=" + timeZone.getDisplayName() + " " + timeZone.getID());
            //GMT+07:00
            if (!"GMT+07:00".equals(currIdTimeZone)) {
                AlarmManager am = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
                am.setTimeZone("Asia/Ho_Chi_Minh");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // ----------------- HANDLER wait PosService reponse --------------------------
    Handler handler;

    private void startHandleCheckResponsePosService() {
        Utils.LOGD(TAG, "startHandleCheckResponsePosService: --->");
//        Looper.prepare();
        // 70s
        int TIMEOUT_POSSERVICE_RESPONSE = 70000;
        handler = new Handler();
        handler.postDelayed(() -> {
            Utils.LOGD(TAG, "startHandleCheckResponsePosService: show dialog error==>");
            DataError dataError = new DataError(ConstantsPay.ERROR_CODE_NO_RESPONSE_SP01, getString(R.string.error_pos_service_no_response));
            showDialogErrorOnUiThread(dataError);
        }, TIMEOUT_POSSERVICE_RESPONSE);
//        Looper.loop();
    }

//    public AsyncStartPayment getAsyncStartPayment() {
//        return asyncStartPayment;
//    }

    private void cancelHandleCheckResponsePosService() {
        Utils.LOGD(TAG, "cancelHandleCheckResponsePosService: ---->");
        if (handler != null) {
            handler.removeCallbacksAndMessages(null);
            handler = null;
        }
    }

    // ----------------- HANDLER wait confirm --------------------------
    private final String STATUS_OK 				= "OK";
//    private final String STATUS_FAIL 			= "FAIL";
//    private final String STATUS_SALE_TIMEOUT    = "TIMEOUT";

    private final int[] waitSelectApp= new int[]{-1};
    private final String[] waitInputPin = new String[]{STATUS_OK};

    private void waitSelectApp() {
        synchronized (waitSelectApp) {
            try {
                waitSelectApp.wait();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
    private void waitEnterPin() {
        Utils.LOGD(TAG, "waitEnterPin: -->");
        synchronized (waitInputPin) {
            try {
                waitInputPin.wait();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        Utils.LOGD(TAG, "waitEnterPin: <--");
    }

    private void responseEnterPin() {
        appendLogAction("responseEnterPin");
        Utils.LOGD(TAG, "responseEnterPin");
        synchronized (waitInputPin) {
            waitInputPin.notify();
        }
    }

    /**
     * response select application index
     *
     * @param index int -return  >=0 selected application index；-1: cancel； -2: failure  Instruction
     */
    private void responseSelectApp(int index) {
        Utils.LOGD(TAG, "responseSelectApp: index=" + index);
        waitSelectApp[0] = index;
        synchronized (waitSelectApp) {
            waitSelectApp.notify();
        }
    }

    // ----------------- END HANDLER wait confirm --------------------------


    /*private void showDialogSelectApplication(final List<String> appList) {
        runProcessOnUiThread(() -> {
            final Dialog dialog = new Dialog(context);
            dialog.setContentView(R.layout.dsp_emv_app_dialog);

            ((TextView) dialog.findViewById(R.id.tv_title)).setText(R.string.please_select_app);

            String[] appNameList = null;
            if (appList != null) {
                Object[] objArr = appList.toArray();

                appNameList = Arrays.copyOf(objArr, objArr.length, String[].class);
            }
            if (appNameList == null){
                appNameList = new String[0];
            }
            if (autoSelectAppNapas) {
                int numAppNapas = 0;
                int indexAppNapas = -1;
                for (int i = 0; i < appNameList.length; ++i) {
                    Utils.LOGD(TAG, "i=" + i + "," + appList.get(i));
                    appNameList[i] = appList.get(i);
                    if (appList.get(i).toUpperCase().contains(defaultAppNameNapasSelected.toUpperCase())) {
                        numAppNapas++;
                        indexAppNapas = i;
                    }
                }
                if (numAppNapas == 1) {
                    responseSelectApp(indexAppNapas);
                    return;
                }
            }

            Utils.LOGD(TAG, "showDialogSelectApplication: ->" + Arrays.toString(appNameList) + " autoSelectAppNapas=" + autoSelectAppNapas);
            appendLogAction("showDialogSelectApplication: ->" + Arrays.toString(appNameList) + " autoSelectAppNapas=" + autoSelectAppNapas);
            ListView appListView = dialog.findViewById(R.id.appList);
            appListView.setAdapter(new ArrayAdapter<>(context, android.R.layout.simple_list_item_1, appNameList));
            appListView.setOnItemClickListener((parent, view, position, id) -> {
                dialog.dismiss();
                appendLogAction(">> select App pos=" + position);
                Utils.LOGD(TAG, "select App -- end position = " + position);
                responseSelectApp(position);
            });
            dialog.findViewById(R.id.cancelButton).setOnClickListener(v -> {
                dialog.dismiss();
                appendLogAction(">>cancel select App");
                responseSelectApp(-1);
            });
            dialog.show();
        });
    }*/

    private void runProcessOnUiThread(Runnable runnable) {
        runOnUiThread(runnable);
    }

    @Override
    public void onTradeResponse(int result) {
        // result: 0 means approve,-1 means refuse
        Utils.LOGD(TAG, "onTradeResponse: "+result+" joRootEmvSale="+(joRootEmvSale==null?"null":"not null"));
        appendLogAction("onTradeResponse: " + result + " ->" + (result == 0 ? " success" : "declined") + " isErrorCodeToPos=" + isErrorCodeToPos);
        smartPosApi.stopSearchCard();
        if (isErrorCodeToPos) {
            return;
        }
        if (result == 0) {
            sendConfirmEmvSales();
        }
        else {
            // todo note need change: wait macq support reversal (temporary: flow success)
//            if (dataSaleSuccess != null && isRunMacq) {
//                sendConfirmEmvSales();
//            }
//            else {
//                runOnUiThread(() -> runVoidFailedTransaction(false, getString(R.string.transaction_declined), TYPE_ERROR_RUN_SCRIPT_FAILURE));
//            }
            runOnUiThread(() -> runVoidFailedTransaction(false, getString(R.string.transaction_declined), TYPE_ERROR_RUN_SCRIPT_FAILURE));

            if (BuildConfig.DEBUG && (testJCB||testNapas)) {
                getCardData();
            }
        }
    }

    @Override
    public void onGetPinResult(PinResult pinResult) {
        this.pinResult = pinResult;
        responseEnterPin();
    }

    // ----------------- listener emv transaction --------------------------
    class MyEmvListener implements EMVTransListener {

//        private String cardNum;

        @Override
        public int onWaitAppSelect(List<String> appNameList, boolean isFirstSelect) {
            Utils.LOGD(TAG, "onWaitAppSelect: ");
            cancelHandleCheckResponsePosService();
            showDialogSelectApplication(appNameList, LibP20L.this::responseSelectApp);
            waitSelectApp();

            int result = waitSelectApp[0];
            Utils.LOGD(TAG, "select result="+result);
            return result;
        }

        @Override
        public int onConfirmEC() {
            Utils.LOGD(TAG, "onConfirmEC: ");
            cancelHandleCheckResponsePosService();
            return 0;
        }

        @Override
        public int onConfirmCardNo(String cardNO) {
            Utils.LOGD(TAG, "onConfirmCardNo: "+cardNO);
            cancelHandleCheckResponsePosService();
//            cardNum = cardNO;
            return 0;
        }

        @Override
        public int onCertVerfiy(String certType, String certValue) {
            Utils.LOGD(TAG, "onCertVerfiy: ");
            cancelHandleCheckResponsePosService();
            return 0;
        }

        @Override
        public PINResult onCardHolderPwd(boolean isOnlinePin, int offlinePinLeftTimes) {
            Utils.LOGD(TAG, "isOnlinePin : " + isOnlinePin + " offlinePinLeftTimes : " + offlinePinLeftTimes);

            appendLogAction("onCardHolderPwd: isOnlinePin=" + isOnlinePin + " offlinePinLeftTimes=" + offlinePinLeftTimes
                    + " isMA=" + isRunMacq + " typeProcessCard=" + getNameTypeProcessCard());
            cancelHandleCheckResponsePosService();
            PINResult newPinResult = new PINResult();

            if (typeProcessCard == TYPE_PROCESS_NORMAL) {
                int resultUpdateWK = 0;
                String wk = getWorkingKey();
    //            String wk = preSaleConfig.geteZPKInternational();
                if (isOnlinePin && !TextUtils.isEmpty(wk)) {
                    if (isRunMacq) {
                        changeKeyPosition();
                    }
                    resultUpdateWK = updateWorkingKey(wk);
                }
                if (resultUpdateWK == 0) {
                    requestEnterPin();

                    if (pinResult == null) {
                        errorEnterPin = true;
                        showDialogErrorOnUiThread(getString(R.string.error_pin_p20l));
                        newPinResult.setResultCode(-12);
                    }
                    else if (pinResult.getResultCode() == 0) {
                        isEnterPinOk = true;
                        newPinResult.setResultCode(pinResult.getResultCode());
                        newPinResult.setPindata(GPMethods.str2bytes(pinResult.getPinblock()));
                    } else {
                        errorEnterPin = true;
                        runProcessOnUiThread(LibP20L.this::handlerErrorEnterPin);
                    }
                }
                else {
                    showDialogErrorOnUiThread(getString(R.string.error_update_wk_fail));
                    newPinResult.setResultCode(-11);
                }
                Utils.LOGD(TAG, "result resultCode 11: " + pinResult.getResultCode());
                Utils.LOGD(TAG, "result pinblock 11: " + pinResult.getPinblock());
            } else {
                newPinResult.setResultCode(0);
                newPinResult.setPindata(GPMethods.str2bytes(""));
            }

            return newPinResult;
        }

        @Override
        public OnlineResult onOnlineProc() {
            // todo note: online process
            Utils.LOGD(TAG, "onOnlineProc: ");
            appendLogAction("onOnlineProc");
            cancelHandleCheckResponsePosService();
//            getCardAndSale();
//
//            waitSaleResponse();

            OnlineResult onlineResult = new OnlineResult();
//            if (waitSale[0].equals(STATUS_OK)) {
//                onlineResult.setResultCode(0);
//                onlineResult.setField39(new byte[]{0x30, 0x30});
//
//                onlineResult.setField55(HexUtil.hexStringToByteArray(parseScriptEmv(emvScript)));
//            }
//            else {
//                onlineResult.setResultCode(-1);
//                onlineResult.setField39(new byte[]{0x5A, 0x33});
////                onlineResult.setField39(new byte[]{0x30, 0x31});
//
//                onlineResult.setField55(new byte[]{});
//            }
            return onlineResult;
        }

        @Override
        public void onTransResult(int code, final String desc) {
            Utils.LOGD(TAG, "onTransResult() called with: code = [" + code + "], desc = [" + desc + "] errorEnterPin=" + errorEnterPin);
            appendLogAction("transResult: code=" + code + " desc=" + desc
                    + " errorEnterPin=" + errorEnterPin + " typeProcessCard="+getNameTypeProcessCard());
            cancelHandleCheckResponsePosService();
            if (errorEnterPin) {
                return;
            }
            switch (code) {
                case 32:
                    appendLogAction("--->refused");
                    break;
                case 64:
                    appendLogAction("--->select aid failed");
                    break;
            }
//            smartPosApi.stopSearchCard();
//            Toast.makeText(activity, desc, Toast.LENGTH_LONG).show();
            // 32 is refused;
            // 48 is online request
            // 64 aid selection failed
            // new PosService_v1.00.55_20200717
            // 0: approve; 1: online request; -1: trade decline
            if (code == 0 || code == 30 || code == 48 || code == 1) {
//                runScriptEMV(emv_script);
//                sendConfirmEmvSales();
                Utils.LOGD(TAG, "onTransResult: --> send tradeResponse");
                if (BuildConfig.DEBUG && testJCB) {
                    getCardData();
                    sendDenialToTerminal();
//                    smartPosApi.tradeResponse("Z3", "");
                }
                else {
                    getCardAndSale();
                }
            }
            else {
                if (typeProcessCard == TYPE_PROCESS_GET_CARD) {
                    field_55_cache = getCardData();
                    if (!TextUtils.isEmpty(field_55_cache)) {
                        mDataEmv = buildEmvData(field_55_cache);

                        callbackCardData();
                        return;
                    }
                }

                String msgError;//getString(R.string.invalid_transaction) +" ("+code+")";
                if (code == 64 && !Utils.checkTypeBuildIsCertify()) {
                    msgError = getString(isContactLess ? R.string.transaction_app_fail_p20l_nfc
                            : R.string.transaction_app_fail_p20l);
                    msgError += getLastUpdatedEmvConfig();
                    enableRetryUpdateEmvConfig();
                }
                else {
                    msgError = getString(R.string.invalid_transaction_sp01);
                }

                DataError dataError = new DataError(msgError);
                dataError.setDetailError("("+code+"|"+desc+")");
                if (code == 64) {
                    if (Utils.checkTypeBuildIsCertify() && desc.equals("(-9){ErrL2:10,Sw1Sw2:0}")
                        && amount.equals("260000")) {
                        runOnUiThread(() -> showDialogWarningRetryPayment(getString(R.string.FALLBACK_NOTI_SWIPE_CARD_NAPAS)));
                    }
                    else {
                        dataError.setErrorCode(isContactLess ? ConstantsPay.ERROR_CODE_SP01_APPLICATION_NFC_ERROR : ConstantsPay.ERROR_CODE_SP01_APPLICATION_ERROR);
                        showDialogErrorOnUiThread(dataError);
//                    showListAidInDevice();
                    }
                }
                else {
                    dataError.setErrorCode(ConstantsPay.ERROR_CODE_SP01_TERMINAL_DENIAL);
                    showDialogErrorOnUiThread(dataError);
//                    showDialogErrorOnUiThread(msgError);
                }
                if (BuildConfig.DEBUG && testAmex) {
                    getCardData();
                    smartPosApi.stopSearchCard();
                }
            }
        }

        @Override
        public void onOffLinePinVerify(boolean isPinOK) {
            cancelHandleCheckResponsePosService();
            appendLogAction("onOffLinePinVerify() called with: isPinOK = [" + isPinOK + "]");
            Utils.LOGD(TAG, "onOffLinePinVerify() called with: isPinOK = [" + isPinOK + "]");
        }

        @Override
        public void onOffLinePinLastTimeVerify(int number) {
            cancelHandleCheckResponsePosService();
            appendLogAction("onOffLinePinLastTimeVerify() called with: number = [" + number + "]");
            Utils.LOGD(TAG, "onOffLinePinLastTimeVerify() called with: number = [" + number + "]");
        }
    }

    /*private String getWk() {
        String wk;
        if (isRunMacq) {
            wk = preSaleConfig == null ? "" : preSaleConfig.geteZPKDomestic();
        }
        else {
            wk = PrefLibTV.getInstance(context).getWorkingKey();
        }
        return wk;
    }*/

    private void showListAidInDevice() {
        try {
            appendLogAction("showListAidInDevice");
            List<byte[]> arrAidContact = smartPosApi.getAID(EMVConstrants.CONTACT);
            String aidItem;
            if (arrAidContact != null) {
                appendLogAction("aid contact");

                for (byte[] item : arrAidContact) {
                    aidItem = MyTextUtils.removeSpace(HexUtil.byteArrayToHexString(item));
                    Utils.LOGD(TAG, "aid=" + aidItem);
                    appendLogAction("aid="+aidItem);
                }
            }
            List<byte[]> arrAidContactless = smartPosApi.getAID(EMVConstrants.CONTACTLESS);
            if (arrAidContactless != null) {
                appendLogAction("aid contactless");
                for (byte[] item : arrAidContactless) {
                    aidItem = MyTextUtils.removeSpace(HexUtil.byteArrayToHexString(item));
                    Utils.LOGD(TAG, "aid=" + aidItem);
                    appendLogAction("aid="+aidItem);
                }
            }
        } catch (Exception e) {
            appendLogAction("showListAidInDevice -> error");
            e.printStackTrace();
        }
    }

    @Override
    protected void sendDenialToTerminal() {
        appendLogAction("send Z3 to terminal");
        smartPosApi.tradeResponse("Z3", "");
    }

    /*private void sendErrorCodeToPos(int errorCode, String issuerAuthenData) {
        sendErrorCodeToPos(String.valueOf(errorCode), issuerAuthenData);
    }*/

    boolean isErrorCodeToPos = false;
    @Override
    protected void sendErrorCodeToPos(String errorCode, String issuerAuthenData) {
        Utils.LOGD(TAG, "sendErrorCodeToPos: " + errorCode);
        isErrorCodeToPos = true;
        if (BuildConfig.DEBUG && (testAmex || testJCB || testNapas)) {
            if (errorCode.length() == 1) {
                errorCode = "0" + errorCode;
            }
            String issuerScript = "";
            if (!TextUtils.isEmpty(issuerAuthenData)) {
                issuerScript = parseIssuerScript(issuerAuthenData);
            }
            String hexErrorCode = HexUtil.asciiToHex(errorCode);
            String onlineProcessResult = "8A02" + hexErrorCode + issuerScript;
            Utils.LOGD(TAG, "sendErrorCodeToPos=> onlineProcessResult=" + onlineProcessResult);
            if (!TextUtils.isEmpty(hexErrorCode)) {
                smartPosApi.tradeResponse(errorCode, issuerScript);
            }
        }
    }

    private void showDialogErrorOnUiThread(String msgError) {
        showDialogErrorOnUiThread(new DataError(ConstantsPay.ERROR_CODE_DEFAULT, msgError));
    }
    private void showDialogErrorOnUiThread(DataError dataError) {
        cancelHandleCheckResponsePosService();
        runProcessOnUiThread(() -> showDialogError(dataError));
    }

    /*private void showToast(String msg) {
        runProcessOnUiThread(() -> Utils.mToast(context, msg));
    }*/

    private void getCardAndSale() {

        field_55_cache = getCardData();


        initMaskedPan(smartPosApi.getMaskPan());
        Utils.LOGD(TAG, "maskPan= "+maskedPan);

        runProcessOnUiThread(() -> {
            if (typeProcessCard == TYPE_PROCESS_GET_CARD) {
                mDataEmv = buildEmvData(field_55_cache);

                callbackCardData();
            }
            else if (checkCanContinuePay()) {
                if (isContactLess && maskedPan.startsWith("9704") && checkNapasNFCNeedEnterPin()) {
                    processNapasNfcPin();
                }
                else {
                    sendEmvSales(field_55_cache);
                }
            }
        });
    }

    private String getCardData() {
        appendLogAction("----getCardData----");
        String field_55 = smartPosApi.getTlvListEncrypted(new int[]{
                0x9f26, 0x9f37, 0x9f36, 0x0082, 0x9f10, 0x0095, 0x9f33, 0x9f02, 0x009a, 0x009c,
                0x5f2a, 0x9f27, 0x9f1a, 0x9f03, 0x9F16, 0x5F24, 0x004F, 0x9F34, 0x9F06, 0x9F21,
                0x9F12, 0x005A, 0x0057, 0x9F4E, 0x0082, 0x008E, 0x5F25, 0x9F07, 0x9F0D, 0x9F0E,
                0x9F0F, 0x9F39, 0x9F40, 0x009B, 0x0084, 0x5F34, 0x9F09, 0x9F1E, 0x9F35, 0x9F41,
                0x9F53, 0x5F20, 0x5F30, 0x5F28, 0x9F4C, 0x0050, 0x9F08, 0x9F01, 0x9F15, 0x9F1C, 0x5f25, 0x9f6e
        }, PinPadConstrants.DUKPT_DAT_KEY_REQ, new byte[]{
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
        });
        TVR = smartPosApi.getTlvValue(0x0095);
        if (BuildConfig.DEBUG) {

            String track2 = smartPosApi.getTlvValueEncrypted(0x0057, PinPadConstrants.DUKPT_DAT_KEY_REQ, new byte[]{
                    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
            });
            byte[] cardSequence = smartPosApi.getTlvValue(0x5f34);
            byte[] cardNo = smartPosApi.getTlvValue(0x005A);
            byte[] name = smartPosApi.getTlvValue(0x5F20);

            byte[] fieldClear = smartPosApi.getTlvList(new int[]{
    //                    0X0082,
                    0x9f26, 0x9f37, 0x9f36, 0x0082, 0x9f10, 0x0095, 0x9f33, 0x9f02, 0x009a, 0x009c,
                    0x5f2a, 0x9f27, 0x9f1a, 0x9f03, 0x9F16, 0x5F24, 0x004F, 0x9F34, 0x9F06, 0x9F21,
                    0x9F12, 0x005A, 0x0057, 0x9F4E, 0x0082, 0x008E, 0x5F25, 0x9F07, 0x9F0D, 0x9F0E,
                    0x9F0F, 0x9F39, 0x9F40, 0x009B, 0x0084, 0x5F34, 0x9F09, 0x9F1E, 0x9F35, 0x9F41,
                    0x9F53, 0x5F20, 0x5F30, 0x5F28, 0x9F4C, 0x0050, 0x9F08, 0x9F01, 0x9F15, 0x9F1C, 0x5f25, 0x9f6e
            });

            byte[] CVR = smartPosApi.getTlvValue(0x9F10);

            Utils.LOGD(TAG, "cardNo(encrypted) : " + smartPosApi.getTlvValueEncrypted(0x005A, PinPadConstrants.DUKPT_DAT_KEY_REQ));
            Utils.LOGD(TAG, "track2 : " + track2);
            Utils.LOGD(TAG, "cardSequence : " + GPMethods.bytesToHexString(cardSequence));
            Utils.LOGD(TAG, "cardNo : " + GPMethods.bytesToHexString(cardNo));
            Utils.LOGD(TAG, "name : " + GPMethods.bytesToHexString(name));
            Utils.LOGD(TAG, "field_55 : " + field_55);
            Utils.LOGD(TAG, "field_55Clear : " + GPMethods.bytesToHexString(fieldClear));
            Utils.LOGD(TAG, "TVR : " + GPMethods.bytesToHexString(TVR));
            Utils.LOGD(TAG, "9F10 : " + GPMethods.bytesToHexString(CVR));
            appendLogAction("TVR(95): "+GPMethods.bytesToHexString(TVR)+" CVR(9F10): "+GPMethods.bytesToHexString(CVR));
        }
        return field_55;
    }

    private boolean checkNapasNFCNeedEnterPin() {
        boolean needEnterPin = false;
        Utils.LOGD(TAG, "TVR : " + GPMethods.bytesToHexString(TVR));
        if (TVR != null && TVR.length == 5) {
            byte byte3 = TVR[2];
            Utils.LOGD(TAG, "checkNapasNFCNeedEnterPin: byte3=" + HexUtil.byteToHexString(byte3));
            int valueBit3 = byte3 >> 2 & 1;
            Utils.LOGD(TAG, "checkNapasNFCNeedEnterPin: valueBit3="+valueBit3);
            needEnterPin = valueBit3 == 1;
            Utils.LOGD(TAG, "checkNapasNFCNeedEnterPin: needEnterPin="+needEnterPin);
        }
        return needEnterPin;
    }


    @Override
    protected void runScriptEMV(String scriptOrigin) {
        Utils.LOGD(TAG, "runScriptEMV() : emv_script = [" + scriptOrigin + "]"+" joRootEmvSale="+(joRootEmvSale==null?"null":"not null"));
        String scriptRun = parseIssuerScript(scriptOrigin);
        appendLogAction(isContactLess+"<-isContactLess - script:" + scriptRun);
        if (isContactLess && TextUtils.isEmpty(scriptRun)) {
            sendConfirmEmvSales();
        }
        else {
            currStageProcess = STAGE_PROCESS_EMV_RUN_SCRIPT;
            smartPosApi.tradeResponse("00", scriptRun);
        }
    }

    private void sendEmvSales(final String mPrivateTag) {
        appendLogAction("sendEmvSales: isContactLess=" + isContactLess);
        if (isContactLess) {
            playSoundBeep();
        }
        mDataEmv = buildEmvData(mPrivateTag);
        if (TextUtils.isEmpty(mDataEmv)) {
            showDialogErrorOnUiThread(getString(R.string.error_sp01_not_found_ksn));
        }
        else {
            runOnUiThread(() ->{
                if (isRunMacq) {
                    sendEmvSalesMacq();
                }
                else {
                    sendEmvSalesBank(mDataEmv);
                }
            });
        }
    }

    private String buildEmvData(String data) {
        String ksn = smartPosApi.dukptUpdateKSN(PinPadConstrants.GET_KSN).getData();
        appendLogAction("KSN=" + ksn);
        this.ksn = ksn;
        if (TextUtils.isEmpty(ksn)) {
            return null;
        }
        String C0 = "C0" + PayUtils.calculatorLength(ksn.length()) + ksn;
        String C2 = "C2" + PayUtils.calculatorLength(data.length()) + data;
        Utils.LOGD(TAG, "buildEmvData: C7-->" + (pinResult == null ? "null" : pinResult.getResultCode()));
        if (pinResult != null && pinResult.getResultCode() == 0) {
            appendLogAction("pBlock: not empty");
            String C7 = "C7" + PayUtils.calculatorLength(pinResult.getPinblock().length()) + pinResult.getPinblock();
            return C0 + C7 + C2;
        } else {
            appendLogAction("pBlock: empty");
            return C0+C2;
        }
    }

    @Override
    void handleSuccessSaleMacq(DataSaleSend dataSend, DataSaleRes dataRes) {
        if (nameTypeCard.equals(ConstantsPay.CARD_MAGSTRIPE) || dataSend.isNFC()) {
            onSuccessSaleMacq(dataSaleSuccess);
        }
        else {
            emvScript = dataRes.getScriptField();
            runScriptEMV(emvScript);
        }
    }

    /**
     * process magstripe card
     */

    private final String kcvWkk       = "00000000";

    private void sendMagSales() {
        sendMagSalesWithPinBlock(null);
    }
    private void sendMagSalesWithPinBlock(final String pinBlock) {
        encDataMag = MyTextUtils.removeSpace(encDataMag);
        encTrack1 = MyTextUtils.removeSpace(encTrack1);
        encTrack2 = MyTextUtils.removeSpace(encTrack2);

        if (TextUtils.isEmpty(encDataMag) && TextUtils.isEmpty(encTrack1) && TextUtils.isEmpty(encTrack2)) {
            appendLogAction("error: empty card data");
            showDialogErrorOnUiThread(new DataError(ERROR_CODE_SP01_NO_CARD_DATA, getString(R.string.error_sp01_not_data_magstripe)));
            return;
        }

        runProcessOnUiThread(() -> {
            if (checkCanContinuePay()) {
                if (isRunMacq) {
                    sendMagstripeSaleMacq(pinBlock);
                }
                else {
                    sendMagstripeSalesBank(pinBlock);
                }
            }
        });
    }

    private void processNapasNfcPin() {
        String wk = getWorkingKey();
        boolean isErrorWk = true;
        if (!TextUtils.isEmpty(wk)) {
            if (isRunMacq) {
                changeKeyPosition();
            }
            int resultUpdateWK = updateWorkingKey(wk);
            if (resultUpdateWK == 0) {
                isErrorWk = false;
                Runnable runnable = this::processPin;
                Thread thread = new Thread(runnable);
                thread.start();
            }
        }
        if (isErrorWk) {
            showDialogErrorOnUiThread(getString(R.string.error_update_wk_fail));
        }
    }

    @Override
    protected void processAlertRemoveCard() {}

    @Override
    protected void handleWorkingKeyFromServer(String ezpk) {
        processAtmPin(ezpk);
    }

    private void processAtmPin(String wk) {
        int resultUpdateWK = updateWorkingKey(wk);
        if (resultUpdateWK == 0) {
            Runnable runnable = this::processPin;
            Thread thread = new Thread(runnable);
            thread.start();
        }
        else {
            showDialogErrorOnUiThread(getString(R.string.error_update_wk_fail));
        }
    }

    private int updateWorkingKey(String wk) {
        if (!TextUtils.isEmpty(wk)) {
            wk = MyTextUtils.removeSpace(wk);
        }
        int res = smartPosApi.updateWorkKey(PinPadConstrants.PINKEY, wk, kcvWkk);
        Utils.LOGD(TAG, "updateWorkingKey: res=" + res);
        appendLogAction("update WK: wk=" + (Utils.checkTypeBuildIsCertify() ? wk : CardUtils.getMaskedPan(wk))
                + " res=" + res + "-->" + (res == 0 ? "success" : "fail"));
        return res;
    }

    private void processPin() {
        appendLogAction("processPin");
        requestEnterPin();
        runOnUiThread(this::handlerEnterPin);
    }
    private void requestEnterPin() {
        appendLogAction("requestEnterPin");

        Bundle bundle = buildBundlePinpadConfig();

        boolean res = smartPosApi.setPinPadConfig(bundle);

        Utils.LOGD(TAG, "requestEnterPin: res setConfig Pin="+res);

        pinResult = null;
        smartPosApi.startPinPad(context, 0);

        waitEnterPin();
    }

    private void handlerEnterPin() {
        if (pinResult == null) {
            Utils.LOGD(TAG, "handlerEnterPin: not found pin");
            appendLogAction("handlerEnterPin: not found pin");
            showDialogErrorOnUiThread(getString(R.string.error_pin_p20l));
            return;
        }
        Utils.LOGD(TAG, "result resultCode: " + pinResult.getResultCode() + " isContactLess=" + isContactLess);
//        Utils.LOGD(TAG, "result pinblock: " + pinResult.getPinblock());

        appendLogAction("resultP=" + pinResult.getResultCode());

        if (pinResult.getResultCode() == 0) {
            if (isContactLess) {
                sendEmvSales(field_55_cache);
            }
            else {
                sendMagSalesWithPinBlock(pinResult.getPinblock());
            }
        }
        else {
            handlerErrorEnterPin();
        }
    }

    private void handlerErrorEnterPin() {
        errorEnterPin = true;
        String msgError;
        switch (pinResult.getResultCode()) {
            case 1:
                msgError = getString(R.string.error_transaction_no_pin);
                break;
            case -1:
                msgError = getString(R.string.error_transaction_cancel_input_pin);
                break;
            case -2:
                msgError = getString(R.string.error_transaction_timeout_pin);
                break;
            case -3:
                msgError = getString(R.string.error_transaction_pin);
                break;
            default:
                msgError = getString(R.string.transaction_terminated);
                break;
        }
        appendLogAction("error pin:" + msgError);
        showDialogErrorOnUiThread(msgError);
    }

    private Bundle buildBundlePinpadConfig() {
        Bundle bundle = new Bundle();
        bundle.putString(PinPadConfig.AMOUNT, amount);
        bundle.putString(PinPadConfig.TIMEOUT, "30");
        bundle.putString(PinPadConfig.MIN_LEN, "4");
        bundle.putString(PinPadConfig.MAX_LEN, "6");
        bundle.putBoolean(PinPadConfig.WITH_SOUND, soundEnterPin);
        bundle.putBoolean(PinPadConfig.WITH_DISPLAY_BAR, true);

        bundle.putString(PinPadConfig.TEXT_CANCEL, getString(R.string.BTN_CANCEL));
        bundle.putString(PinPadConfig.TEXT_CLEAR, getString(R.string.BTN_DELETE));
        bundle.putString(PinPadConfig.TEXT_CONFIRM, getString(R.string.BTN_OK));
        bundle.putString(PinPadConfig.TEXT_HINT, getString(R.string.msg_guide_enter_pin));
        bundle.putString(PinPadConfig.TEXT_UPTIP, getString(R.string.ENTER_PIN));
        return bundle;
    }

    public void processUpdateConfigSp01(EmvConfigSp01 emvConfigSp01) {
        StringBuilder log = new StringBuilder();
        if (emvConfigSp01.getAid() != null && emvConfigSp01.getAid().size() > 0) {
            boolean result;
            result = smartPosApi.clearAID(EMVConstrants.CONTACT);
            log.append("clear aid ct: ").append(result);
            for (String item : emvConfigSp01.getAid()) {
                log.append(item.substring(0, Math.min(20, item.length())));
                result = smartPosApi.updateAID(GPMethods.str2bytes(item), EMVConstrants.CONTACT);
                log.append(" ->").append(" ct: ").append(result);
            }
        }
        if (emvConfigSp01.getAidContactless() != null && emvConfigSp01.getAidContactless().size() > 0) {
            boolean result;
            result = smartPosApi.clearAID(EMVConstrants.CONTACTLESS);
            log.append(" ctl: ").append(result);
            for (String item : emvConfigSp01.getAidContactless()) {
                log.append(item.substring(0, Math.min(20, item.length())));
                result = smartPosApi.updateAID(GPMethods.str2bytes(item), EMVConstrants.CONTACTLESS);
                log.append(" ctl: ").append(result).append("\n");
            }
        }
        if (emvConfigSp01.getCapk() != null && emvConfigSp01.getCapk().size() > 0) {
            boolean result;
            result = smartPosApi.clearCAPK();
            log.append("clear capk: ").append(result);
            for (String item : emvConfigSp01.getCapk()) {
                log.append(item.substring(0, Math.min(item.length(), 20))).append(" ->");
                result = smartPosApi.updateCAPK(GPMethods.str2bytes(item));
                log.append(" capk: ").append(result).append("\n");
            }
        }
        appendLogAction(log.toString());

    }

    private void showDialogWarningUseChip() {
        showDialogWarningRetryPayment(getString(R.string.ERROR_8103));
    }

    private void showDialogWarningRetryPayment(String msg) {
        showDialogWarningRetryPayment(msg, this::startPayment);
    }

    private void showDialogFallBack() {
        showDialogWarningRetryPayment(getString(R.string.FALLBACK_NOTI_SWIPE_CARD));
        isFallBack = true;
    }

}
