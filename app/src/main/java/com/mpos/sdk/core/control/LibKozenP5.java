package com.mpos.sdk.core.control;

import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;

import com.custom.mdm.CustomAPI;
import com.kozen.utils.tlv.BerTag;
import com.kozen.utils.tlv.BerTlv;
import com.kozen.utils.tlv.BerTlvBuilder;
import com.kozen.utils.tlv.BerTlvParser;
import com.kozen.utils.tlv.BerTlvs;
import com.kozen.utils.tlv.HexUtil;
import com.kozen.view.PasswordDialog;
import com.mpos.sdk.BuildConfig;
import com.mpos.sdk.R;
import com.mpos.sdk.core.common.EmvCardType;
import com.mpos.sdk.core.model.AppSp02;
import com.mpos.sdk.core.model.CapkSp02;
import com.mpos.sdk.core.model.DataError;
import com.mpos.sdk.core.model.DataPrePay;
import com.mpos.sdk.core.model.EmvConfigSP02;
import com.mpos.sdk.core.model.EmvConfigSp01;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.core.modelma.DataSaleRes;
import com.mpos.sdk.core.modelma.DataSaleSend;
import com.mpos.sdk.core.mposinterface.ItfHandlerSwipeIndustryCard;
import com.mpos.sdk.util.AppExecutors;
import com.mpos.sdk.util.CardUtils;
import com.mpos.sdk.util.Constants;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.DevicesUtil;
import com.mpos.sdk.util.ExceptionUtils;
import com.mpos.sdk.util.MyTextUtils;
import com.mpos.sdk.util.PayUtils;
import com.mpos.sdk.util.Utils;
import com.pos.sdk.accessory.POIGeneralAPI;
import com.pos.sdk.cardreader.POICardManager;
import com.pos.sdk.emvcore.IPosEmvCoreListener;
import com.pos.sdk.emvcore.POIEmvCoreManager;
import com.pos.sdk.emvcore.POIEmvCoreManager.EmvOnlineConstraints;
import com.pos.sdk.emvcore.POIEmvCoreManager.EmvResultConstraints;
import com.pos.sdk.emvcore.POIEmvCoreManager.EmvTransDataConstraints;
import com.pos.sdk.emvcore.PosEmvAid;
import com.pos.sdk.emvcore.PosEmvCapk;
import com.pos.sdk.emvcore.PosEmvErrorCode;
import com.pos.sdk.security.POIHsmManage;
import com.pos.sdk.security.PedKcvInfo;
import com.pos.sdk.security.PedKeyInfo;
import com.pos.sdk.utils.PosUtils;

import java.util.Arrays;
import java.util.List;

import static com.mpos.sdk.util.ConstantsPay.ERROR_CODE_SP02_DEFAULT;
import static com.mpos.sdk.util.ConstantsPay.ERROR_CODE_SP02_NO_APP;
import static com.mpos.sdk.util.ConstantsPay.ERROR_CODE_SP02_NO_DATA_CARD;
import static com.mpos.sdk.util.ConstantsPay.ERROR_CODE_SP02_NO_KSN;
import static com.mpos.sdk.util.ConstantsPay.ERROR_CODE_SP02_OTHER_ERROR;

/**
 * Create by anhnguyen on 2020-01-17
 */
public class LibKozenP5 extends LibReaderController{

    private static final String TAG = "LibKozenP5";

    private static final String TAG_ENCRYPT_TRACK1_DATA           = "DF30";
    private static final String TAG_ENCRYPT_TRACK2_DATA           = "DF31";
//    private static final String TAG_ENCRYPT_PAN                   = "DF32";
    private static final String TAG_PAN_MASK                      = "DF33";
    private static final String TAG_ENCRYPT_KSN                   = "DF34";
    private static final String TAG_ENCRYPT_TRACK2_MAGSTRIPE_DATA = "DF35";
//    private static final String TAG_ENCRYPT_TRACK3_DATA           = "DF36";
    private static final String TAG_ENCRYPT_DATA                  = "DF37";
//    private static final String TAG_TRACK1_DATA_MASK              = "DF41";
//    private static final String TAG_TRACK2_DATA_MASK              = "DF42";
//    private static final String TAG_TRACK3_DATA_MASK              = "DF43";

    public static final int INDEX_KEY_ADD    = 1;

//    public static final int POSITION_KEY_STB    = 0;
//    public static final int POSITION_KEY_BIDV   = 1;
//    public static final int POSITION_KEY_VTB    = 2;
//    public static final int POSITION_KEY_PVCB   = 3;
//    public static final int POSITION_KEY_OPEN99 = 4;
//    public static final int POSITION_KEY_VCB    = 5;

//    public static final String MODEL_NAME_L200  = "L200";   // prod
//    public static final String MODEL_NAME_P5    = "P5";     // dev
//    public static final String MODEL_NAME_SP02  = "SP02";     // dev
//    public static final String MODEL_NAME_STAR_POS = "P3";
    public final String PREFIX_SP02 = "SP02";    // SMART-POS-02
    public final String PREFIX_SP05 = "SP05";    // SMART-POS-P12
    public final String PREFIX_SP06 = "SP06";    // SMART-POS-P10
//    public final String PREFIX_SP05 = "SP05";    // SMART-POS-P12

    private int cardType;

    private POIEmvCoreManager mEmvCoreManager;
    private PosEmvCoreListener mPosEmvCoreListener;
    private ItfHandlerSwipeIndustryCard callbackSwipeIndustryCard;

    private String pinBlock;
    private String emvScript = "";

    private Bundle mPinBundleAtm;
    private boolean isProcessPinBank = false;
    private boolean isProcessPinMacqAtm = false;
    private boolean isPinCancel = false;
    private boolean isManualClose = false;
    private boolean isRunScriptFromHost = false;

    private int TIMEOUT_WAIT_SWIPE_CARD_SECOND = 60;

    public LibKozenP5(Context c) {
        super(c);
        init();
    }

    public LibKozenP5(Context c, DataPrePay dataPrePay, LibDspreadReader.ItfUpdateViewDspread updateUi, ItfResultPay callbackResult) {
        super(c, dataPrePay);

        this.cbUpdateUI = updateUi;
        initVariable(callbackResult);

        checkNeedInitCertify();

        init();
    }

    /**
     * set timeout wait search card
     * @param swipeTimeout is second and in range: [30-300] second
     */
    public void setSWIPE_TIMEOUT(int swipeTimeout) {
        if (Constants.isTypeIntegrationMpos()) {
            if (30 <= swipeTimeout && swipeTimeout <= 300) {
                this.TIMEOUT_WAIT_SWIPE_CARD_SECOND = swipeTimeout;
            }
        }
    }

    private void init() {
        mEmvCoreManager = POIEmvCoreManager.getDefault();
        mPosEmvCoreListener = new PosEmvCoreListener();
//        SoundEffects.getInstance().init(context);
    }

    private void checkNeedInitCertify() {
        if (Utils.checkTypeBuildIsCertify() && testNapas) {
            itfCertifyMacq = new ItfCertifyMacq() {
                @Override
                public void showDialogUseChip() {
                    udid = udid + "-fb";
                    showDialogWarningUseChip();
                }

                @Override
                public void sendErrorCodeToPosMacq(int codeError, String emvScript) {
                    sendErrorCodeToPos(String.valueOf(codeError), emvScript);
                }

                @Override
                public void sendDenialMacq() {
                    sendDenialToTerminal();
                }
            };
        }
    }

    public String getSerialNumber() {
        String originSN = POIGeneralAPI.getDefault().getVersion(POIGeneralAPI.VERSION_TYPE_DSN);
        Utils.LOGD(TAG, "getSerialNumber: originSN=" + originSN);
        if (!TextUtils.isEmpty(originSN) && originSN.length() > 13) {
            if ((DevicesUtil.isSP02P5() || DevicesUtil.isSP02P8())
                    && !originSN.startsWith(PREFIX_SP02)) {
                return PREFIX_SP02 + originSN.substring(originSN.length() - (14 - PREFIX_SP02.length()));   // 14: ~ 7byte KSN
            }
            else if (DevicesUtil.isSP02P10() && !originSN.startsWith(PREFIX_SP06)) {
                return PREFIX_SP06 + originSN.substring(originSN.length() - (14 - PREFIX_SP06.length()));   // 14: ~ 7byte KSN
            }
            else if (DevicesUtil.isSP02P12() && !originSN.startsWith(PREFIX_SP05)) {
                return PREFIX_SP05 + originSN.substring(originSN.length() - (14 - PREFIX_SP05.length()));   // 14: ~ 7byte KSN
            }
        }

        if (!TextUtils.isEmpty(originSN)){
            return originSN;
        } else {
            return "";
        }
    }

    public void disableStatusBarP5(boolean b) {
        CustomAPI.init(context);
        CustomAPI.setStatusBar(!b);
    }

    private void resetVariable() {
        mPinBundleAtm = null;
        isProcessPinBank = false;
        isProcessPinMacqAtm = false;
        isPinCancel = false;
        isRunScriptFromHost = false;
    }

    public void setItfHandlerSwipeIndustryCard(ItfHandlerSwipeIndustryCard itfHandlerSwipeIndustryCard) {
        this.callbackSwipeIndustryCard = itfHandlerSwipeIndustryCard;
    }
/*
    private AsyncStartPayment asyncStartPayment;
    public void startPaymentWitAsyncTask() {
        if (asyncStartPayment != null) {
            asyncStartPayment.cancel(true);
            asyncStartPayment = null;
        }
        Utils.LOGD(TAG, "startPaymentWitAsyncTask: -->");
        asyncStartPayment = new AsyncStartPayment(this);
        asyncStartPayment.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR);
    }

    private static class AsyncStartPayment extends AsyncTask<Void, Void, Void> {

        WeakReference<LibKozenP5> weakReference;

        AsyncStartPayment(LibKozenP5 helper) {
            this.weakReference = new WeakReference<>(helper);
        }

        @Override
        protected Void doInBackground(Void... voids) {
            Utils.LOGD(TAG, "doInBackground: ====>>");
            if (weakReference != null && weakReference.get().context != null) {
                weakReference.get().startReadCard();
            }
            return null;
        }
    }*/

    protected void startReadCard() {
        initPresale();
        initVariableNewPayment();
        resetVariable();
        updateViewByStage(UI_STAGE_WAIT_SWIPE_CARD);
        if (callbackSwipeIndustryCard != null) {
            processReadIndustryCard();
            return;
        }
        loadPure();
        try {
            Bundle bundle = new Bundle();

            bundle.putInt(EmvTransDataConstraints.TRANS_TYPE, POIEmvCoreManager.EMV_GOODS);

            if (fakeAmount) {
                bundle.putLong(EmvTransDataConstraints.TRANS_AMOUNT, lAmount*100);
            }
            else {
                bundle.putLong(EmvTransDataConstraints.TRANS_AMOUNT, lAmount);
            }
            bundle.putLong(EmvTransDataConstraints.TRANS_AMOUNT_OTHER, 0);

            int transMode = POIEmvCoreManager.DEVICE_CONTACT | POIEmvCoreManager.DEVICE_MAGSTRIPE;
            boolean restrictionNFC = checkRestrictionNfc();//PrefLibTV.getInstance(context).get(PrefLibTV.restrictionNFC, Boolean.class, false);
            if (Utils.checkTypeBuildIsCertify() && !restrictionNFC && isFallBack) {
                restrictionNFC = true;
            }
            if (!restrictionNFC) {
                transMode = transMode | POIEmvCoreManager.DEVICE_CONTACTLESS;
            }
            appendLogAction("transMode=" + transMode + " restrictionNFC=" + restrictionNFC);
            bundle.putInt(EmvTransDataConstraints.TRANS_MODE, transMode);
//            bundle.putInt(EmvTransDataConstraints.TRANS_MODE,
//                    POIEmvCoreManager.DEVICE_CONTACT | POIEmvCoreManager.DEVICE_CONTACTLESS
//                            | POIEmvCoreManager.DEVICE_MAGSTRIPE);

            bundle.putInt(EmvTransDataConstraints.TRANS_TIMEOUT, TIMEOUT_WAIT_SWIPE_CARD_SECOND);
            bundle.putBoolean(EmvTransDataConstraints.SPECIAL_CONTACT, false);
            bundle.putBoolean(EmvTransDataConstraints.SPECIAL_MAGSTRIPE, false);

            bundle.putBoolean(EmvTransDataConstraints.USE_ENCRYPT_AMEX_TRACK, true);

            bundle.putBoolean(EmvTransDataConstraints.TRANS_FALLBACK, true);

            bundle.putInt(EmvTransDataConstraints.ACCOUNT_MASK_HEAD, 8);
            bundle.putInt(EmvTransDataConstraints.ACCOUNT_MASK_TAIL, 4);

            int mode = EmvTransDataConstraints.ENCRYPT_OPEN_CONTACT;
            mode |= EmvTransDataConstraints.ENCRYPT_OPEN_CONTACTLESS;
            mode |= EmvTransDataConstraints.ENCRYPT_OPEN_MAGSTRIPE;

            bundle.putInt(EmvTransDataConstraints.OPEN_ENCRYPT, mode);

            Bundle encryptContact = buildEncryptConfig();
            bundle.putBundle(EmvTransDataConstraints.ENCRYPT_CONTACT, encryptContact);

//            Bundle encryptContactless = getEncryptConfig();
            bundle.putBundle(EmvTransDataConstraints.ENCRYPT_CONTACTLESS, encryptContact);

//            Bundle encryptMagstripe = getEncryptConfig();
            bundle.putBundle(EmvTransDataConstraints.ENCRYPT_MAGSTRIPE, encryptContact);

            // support NFC online pin: can remove card after show waiting pin
            bundle.putBoolean(EmvTransDataConstraints.USE_DELAY_PIN, true);

            int ret = mEmvCoreManager.startTransaction(bundle, mPosEmvCoreListener);

            String msgError = null;
            switch (ret) {
                case PosEmvErrorCode.EMV_OK:
                    appendLogAction("EMV_OK");
                    break;
                case PosEmvErrorCode.EMV_CANCEL:
                    appendLogAction("EMV_CANCEL");
                    msgError = getString(R.string.transaction_cancel);
                    break;
                case PosEmvErrorCode.EMV_FALLBACK:
//                    if (testNapas) {
//                        showDialogFallBack();
//                    }
//                    else {
                        msgError = getString(R.string.transaction_error);
//                    }
                    break;
                case PosEmvErrorCode.EMV_APP_BLOCKED:
                case PosEmvErrorCode.EMV_CARD_BLOCKED:
                case PosEmvErrorCode.EXCEPTION_ERROR:
                case PosEmvErrorCode.EMV_TERMINATED:
                case PosEmvErrorCode.EMV_COMMAND_FAIL:
                default:
                    msgError = getString(R.string.transaction_error_default_SP02, String.valueOf(ret));
            }
            appendLogAction("res after startTrans: code="+ret+" msg=" + msgError);
        } catch (Exception e) {
            e.printStackTrace();
            appendLogAction("startTransaction error: " + e.getMessage() + " detail=" + ExceptionUtils.getInstance().getStackTraceString(e));
            showDialogError(getString(R.string.transaction_error_start_trans_SP02));
        }
    }

    private Bundle buildEncryptConfig() {
        int indexKey = getKeyIndex();
        Utils.LOGD(TAG, "getEncryptConfig: indexKey=" + indexKey);
        byte[] padding = "0".getBytes();
        Utils.LOGD(TAG, "getEncryptConfig: padding=" + HexUtil.toHexString(padding));
        Bundle bundle = new Bundle();
        bundle.putInt(EmvTransDataConstraints.ENCRYPT_KEY_INDEX, indexKey);
        bundle.putInt(EmvTransDataConstraints.ENCRYPT_TYPE, EmvTransDataConstraints.ENCRYPT_TYPE_DUKPT_DATA_REQUEST);
        bundle.putByte(EmvTransDataConstraints.ENCRYPT_PADDING, padding[0]);
        bundle.putInt(EmvTransDataConstraints.ENCRYPT_MODE, EmvTransDataConstraints.ENCRYPT_MODE_CBC);
        bundle.putByteArray(EmvTransDataConstraints.ENCRYPT_VECTOR, HexUtil.parseHex("0000000000000000"));
        bundle.putBoolean(EmvTransDataConstraints.ENCRYPT_EMV_DATA, true);
        bundle.putBoolean(EmvTransDataConstraints.ENCRYPT_BASE64, false);
        return bundle;
    }

    public void cancelReadCard() {}
    public void closeReadCard() {
        if (mEmvCoreManager != null) {
            mEmvCoreManager.stopTransaction();
        }
        if (callbackSwipeIndustryCard != null) {
            isManualClose = true;
        }
    }

    public void loadPure() {
        try {
            if (!PrefLibTV.getInstance(context).get(PrefLibTV.isLoadPureSp02, Boolean.class)) {
                showLog("loadPure: ====>");
                Bundle bundle = new Bundle();
                // C705 36006043F9 DF1B0100 DF300107 DF310101 DF320101
                // C70536006043F9DF1B0100DF300107DF310101DF320101
                bundle.putByteArray(POIEmvCoreManager.EmvTerminalConstraints.CONFIG,
                        HexUtil.parseHex("C70536006043F9DF1B0100DF300106DF310101DF320101"));
                int result = POIEmvCoreManager.getDefault().EmvSetTerminal(POIEmvCoreManager.EmvTerminalConstraints.SETTINGS_PURE, bundle);
                showLog("loadPure: result = " + result);
                if (result == 0) {
                    PrefLibTV.getInstance(context).put(PrefLibTV.isLoadPureSp02, true);
                }
            }
        } catch (Exception e) {
            showLog("loadPure: err " + e.toString());
        }
    }

    /** -------- read gift magnetic card ------- */
    public void processReadIndustryCard() {
        int countSecond = 0;
        isManualClose = false;
        showLog("processReadIndustryCard: ");

        int result = POICardManager.getDefault(context).getMagCardReader().open();
        showLog("open result=" + result);
        int cardStatus = ItfHandlerSwipeIndustryCard.CARD_STATUS_TIMEOUT;
        do {
            result = POICardManager.getDefault(context).getMagCardReader().detect();
            showLog("detect result=" + result);

            if (result == 0) {
                showLog( "detect has card");
                cardStatus = ItfHandlerSwipeIndustryCard.CARD_STATUS_OK;
                break;
            }
            // 0xFD43(-701) == 64835 => ERR_MSR_NOSWIPED
            else if (result == 64835){
                countSecond++;
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                showLog("waiting... " + countSecond);
            } else {
                cardStatus = ItfHandlerSwipeIndustryCard.CARD_STATUS_FAIL;
                showLog("detect has error");
                break;
            }
        } while (countSecond < TIMEOUT_WAIT_SWIPE_CARD_SECOND && !isManualClose);

        if (!isManualClose) {
            if (result == 0) {
                String track1 = getTrackDataByTrackIndex(1);
                String track2 = getTrackDataByTrackIndex(2);
                String track3 = getTrackDataByTrackIndex(3);
                callbackSwipeIndustryCard.onHandlerSwipeIndustryCard(cardStatus, track1, track2, track3);
            }
            else {
                callbackSwipeIndustryCard.onHandlerSwipeIndustryCard(cardStatus, null, null, null);
            }
        }
        POICardManager.getDefault(context).getMagCardReader().close();
    }

    private String getTrackDataByTrackIndex(int index) {
        byte[] data = POICardManager.getDefault(context).getMagCardReader().getTraceData(index);
        String content = data == null ? "" : HexUtil.toHexString(data);
        if (!TextUtils.isEmpty(content)) {
            content = com.mpos.sdk.util.HexUtil.hexToAscii(content);
        }
        showLog(String.format("showDataInTrace: %d -> %s", index, content));
        return content;
    }

    private void showLog(String data) {
        Utils.LOGD(TAG, "->" + data);
        appendLogAction(data);
    }

    private void showDialogErrorSp02(String msg) {
        showDialogError(new DataError(ERROR_CODE_SP02_DEFAULT, msg));
    }

//    private void dismissDialog() {
//        Utils.LOGD(TAG, "dismissDialog: ");
//    }

    private int getKeyIndex() {
        int typeServer = getTypeServer();
//        if (isRunMacq) {
//            typeServer = ConstantsPay.getTypeServerByBanKName(preSaleConfig.getAcquirerMagstripe());
//        }
//        else {
//            typeServer = PrefLibTV.getInstance(context).getFlagServer();
//        }
//        int indexP5 = getKeyIndexByFlagServer(typeServer) + INDEX_KEY_ADD;
        int indexP5 = getKeyIndexByFlagServer(typeServer);
        if (typeServer != ConstantsPay.SERVER_NEXTPAY){
            indexP5 += INDEX_KEY_ADD;
        }

        appendLogAction("indexKz=" + indexP5);

        Utils.LOGD(TAG, "getKeyIndex: indexP5=" + indexP5);
        return indexP5;
    }

    /*public static int getKeyIndexByFlagServer(int typeServer) {
        int keyDataPosition = -1;
        Utils.LOGD(TAG, "getKeyDataIndex: typeServer="+typeServer);
        if (typeServer == ConstantsPay.SERVER_SCB) {
            keyDataPosition = POSITION_KEY_STB;
        }
        else if (typeServer == ConstantsPay.SERVER_BIDV) {
            keyDataPosition = POSITION_KEY_BIDV;
        }
        else if (typeServer == ConstantsPay.SERVER_VTB) {
            keyDataPosition = POSITION_KEY_VTB;
        }
        else if (typeServer == ConstantsPay.SERVER_OPEN99) {
            keyDataPosition = POSITION_KEY_OPEN99;
        }
//        else if (typeServer == ConstantsPay.SERVER_MAILINH) {
//            keyDataPosition = POSITION_KEY_MAILINH;
//        }
        else if (typeServer == ConstantsPay.SERVER_VCB) {
            keyDataPosition = POSITION_KEY_VCB;
        }
        else if (typeServer == ConstantsPay.SERVER_TCB) {
            keyDataPosition = LibDspreadReader.POSITION_KEY_TCB;
        }
        else if (typeServer == ConstantsPay.SERVER_NEXTPAY) {
            keyDataPosition = LibDspreadReader.POSITION_KEY_NEXTPAY;
        }
        Utils.LOGD(TAG, "getKeyDataIndex: index="+keyDataPosition);
        return keyDataPosition;
    }*/

    void onHandlerEnterPinSuccess(String pinBlock) {
        Utils.LOGD(TAG, "onHandlerEnterPinSuccess: " + pinBlock);
        appendLogAction("onHandlerEnterPinSuccess");
        this.pinBlock = pinBlock;
        if (!TextUtils.isEmpty(this.pinBlock)) {
            this.pinBlock = MyTextUtils.removeSpace(this.pinBlock);
        }
        if (isProcessPinBank) {
            sendMagstripeSalesBank(this.pinBlock);
        }
        else if (isProcessPinMacqAtm) {
            responseEnterPin();
        }
    }

    void onHandlerEnterPinFail(int verifyResult) {
        Utils.LOGD(TAG, "onHandlerEnterPinFail: " + pinBlock);
        appendLogAction("onHandlerEnterPinFail=" + verifyResult);
        String msgError;
        if (verifyResult == POIEmvCoreManager.EmvPinConstraints.VERIFY_CANCELED) {
            isPinCancel = true;
            msgError = getString(R.string.error_transaction_cancel_input_pin);
            showDialogErrorSp02(msgError);
        }
        else if (cardType == POIEmvCoreManager.DEVICE_MAGSTRIPE && verifyResult == POIEmvCoreManager.EmvPinConstraints.VERIFY_NO_PASSWORD) {
            isPinCancel = true;
            msgError = getString(R.string.error_transaction_no_pin);
            showDialogErrorSp02(msgError);
        }
//        else {
//            msgError = getString(R.string.error_transaction_no_pin);
//        }
    }

    private void processPin(Bundle mPinBundle) {
        wk = getWorkingKey();
        int resultUpdateWK = updateWorkingKey(wk);
        if (resultUpdateWK == 0) {
            Utils.LOGD(TAG, "processPin: --->>");
            showAllInBundle(mPinBundle);
            AppExecutors.getInstance().mainThread().execute(() -> {
                boolean isIcSlot = LibKozenP5.this.cardType == POIEmvCoreManager.DEVICE_CONTACT;
                showLog("show enter pin: isIcc=" + isIcSlot);
                PasswordDialog dialog = new PasswordDialog(context,
                        isIcSlot,
                        mPinBundle,
                        getKeyIndex(),
                        new PasswordDialog.ItfHandlerEnterPinSP02() {
                            @Override
                            public void onHandlerEnterPinSuccessSP02(String pinBlock) {
                                onHandlerEnterPinSuccess(pinBlock);
                            }

                            @Override
                            public void onHandlerEnterPinFailSP02(int verifyResult) {
                                onHandlerEnterPinFail(verifyResult);
                            }
                        });
                dialog.showDialog();
            });
        }
        else {
            StringBuilder msgError = new StringBuilder(getString(R.string.error_update_wk_fail));
            if (resultUpdateWK == 65235) {
                msgError.append(" \n(Not found key at idx: ").append(getKeyIndex()).append(")");
                resetCacheInjectKey();
            }
            showDialogError(msgError.toString());
        }
    }
    // ----------------- HANDLER wait confirm --------------------------
    private final String STATUS_OK 				= "OK";
    private final String[] waitInputPin = new String[]{STATUS_OK};

    private void waitEnterPin() {
        Utils.LOGD(TAG, "waitEnterPin: -->");
        synchronized (waitInputPin) {
            try {
                waitInputPin.wait();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        Utils.LOGD(TAG, "waitEnterPin: <--");
    }

    private void responseEnterPin() {
        appendLogAction("responseEnterPin");
        Utils.LOGD(TAG, "responseEnterPin");
        synchronized (waitInputPin) {
            waitInputPin.notify();
        }
    }

    // ----------------- HANDLER wait detect action after detect card --------------------------
    Handler handlerDetectActionAfterDetectCard;

    private void startHandleDetectActionAfterDetectIcc() {
        Utils.LOGD(TAG, "startHandleDetectActionAfterDetectCard: --->");
        int TIMEOUT_DETECT_KERNEL = 30000;
        handlerDetectActionAfterDetectCard = new Handler();
        handlerDetectActionAfterDetectCard.postDelayed(() -> {
            Utils.LOGD(TAG, "startHandleDetectActionAfterDetectCard: show dialog error==>");
            DataError dataError = new DataError(ConstantsPay.ERROR_CODE_SP02_KERNEL_NOT_RETURN, getString(R.string.error_kozen_no_response));
            showDialogError(dataError);
        }, TIMEOUT_DETECT_KERNEL);
    }

    private void cancelHandleDetectActionAfterDetectCard() {
        Utils.LOGD(TAG, "cancelHandleDetectActionAfterDetectCard: ---->");
        if (handlerDetectActionAfterDetectCard != null) {
            handlerDetectActionAfterDetectCard.removeCallbacksAndMessages(null);
            handlerDetectActionAfterDetectCard = null;
        }
    }

    class PosEmvCoreListener extends IPosEmvCoreListener.Stub {
        private final String TAG = "PosEmvCoreListener";

        @Override
        public void onEmvProcess(int cardMode, Bundle bundle) {
            showLog("onEmvProcess << cardMode=" + cardMode);
            if (cardMode == POIEmvCoreManager.DEVICE_CONTACT
                    || cardMode == POIEmvCoreManager.DEVICE_CONTACTLESS
                    || cardMode == POIEmvCoreManager.DEVICE_MAGSTRIPE) {

                currStageProcess = STAGE_PROCESS_PROCESS_CARD;

                cardType = cardMode;
                updateViewByStage(UI_STAGE_PROCESSING_CARD);

                appendLogAction("playSoundBeep");
                SoundEffects.getInstance().playSoundBeepWithVolume(0.3f);

                switch (cardMode) {
                    case POIEmvCoreManager.DEVICE_CONTACT:
                        isContactLess = false;
                        setNameTypeCard(ConstantsPay.CARD_EMV);
                        showLog("-> IcCard");
                        runOnUiThread(LibKozenP5.this::startHandleDetectActionAfterDetectIcc);
                        break;
                    case POIEmvCoreManager.DEVICE_CONTACTLESS:
                        setNameTypeCard(ConstantsPay.CARD_NFC);
                        isContactLess = true;
                        showLog("-> Contactless");
                        break;
                    case POIEmvCoreManager.DEVICE_MAGSTRIPE:
                         setNameTypeCard(ConstantsPay.CARD_MAGSTRIPE);
                        showLog("-> MagCard");
                        break;
                }
            }
//            else {
//                switch (cardMode) {
//                    case PosEmvErrorCode.EMV_TIMEOUT:
//                        freshProcessDialog("Detection Card Timeout");
//                        AddLog(" Detection Card Timeout");
//                        break;
//                    case PosEmvErrorCode.EMV_CANCEL:
//                        freshProcessDialog("Transaction Cancel");
//                        AddLog(" Transaction Cancel");
//                        break;
////                    case PosEmvErrorCode.EMV_MUTIL_PICC:
////                        freshProcessDialog("Multiple cards , Present a single card");
////                        AddLog(" Multiple cards , Present a single card");
////                        return;
//                    case PosEmvErrorCode.EMV_FALLBACK:
//                        freshProcessDialog("FallBack");
//                        AddLog(" FallBack");
//                        break;
//                    case PosEmvErrorCode.EMV_ICC_INTERFACE:
//                        freshProcessDialog("EMV_ICC_INTERFACE");
//                        AddLog(" EMV_ICC_INTERFACE");
//                        break;
//                }
//                new Handler(Looper.getMainLooper()).postDelayed(LibDozenP5.this::dismissDialog, 2000);
//            }
//            showLog("EmvProcess   >>>");
        }

        @Override
        public void onSelectApplication(List<String> appNameList,
                                        boolean isFirstSelect) {
            showLog("SelectApplication  >>");
            cancelHandleDetectActionAfterDetectCard();
//            dismissDialog();

            showDialogSelectApplication(appNameList, position -> mEmvCoreManager.onSetSelectResponse(position));

            showLog("SelectApplication  <<");
        }

        @Override
//        public void onConfirmCardInfo(int mode, String cardNo) {
        public void onConfirmCardInfo(int mode, Bundle bundle) {
            showLog("ConfirmCardInfo  << mode=" + mode);
            cancelHandleDetectActionAfterDetectCard();

//            dismissDialog();
//            final String card = cardNo;

            Bundle outBundle = new Bundle();
            if (mode == POIEmvCoreManager.CMD_AMOUNT_CONFIG) {
                outBundle.putString(POIEmvCoreManager.EmvCardInfoConstraints.OUT_AMOUNT, "11");
                outBundle.putString(POIEmvCoreManager.EmvCardInfoConstraints.OUT_AMOUNT_OTHER, "22");
            } else if (mode == POIEmvCoreManager.CMD_TRY_OTHER_APPLICATION) {
                outBundle.putBoolean(POIEmvCoreManager.EmvCardInfoConstraints.OUT_CONFIRM, true);
            } else if (mode == POIEmvCoreManager.CMD_ISSUER_REFERRAL) {
                outBundle.putBoolean(POIEmvCoreManager.EmvCardInfoConstraints.OUT_CONFIRM, true);
            }
            mEmvCoreManager.onSetCardInfoResponse(outBundle);

            showLog("ConfirmCardInfo  >>");
        }

        @Override
        public void onKernelType(int type) {
            showLog("onKernelType  >> type="+type+" -"+ EmvCardType.getCardType(type));
//            transData.setCardType(type);
            cancelHandleDetectActionAfterDetectCard();
        }

        @Override
        public void onSecondTapCard() {
            showLog("onSecondTapCard  >>");
            cancelHandleDetectActionAfterDetectCard();

        }

        @Override
        public void onRequestInputPin(Bundle mPinBundle) {
            showLog("RequestInputPin 123  << cardType=" + cardType + " maskedPan=" + maskedPan
                    + " typeProcessCard=" + getNameTypeProcessCard());
            cancelHandleDetectActionAfterDetectCard();

            showAllInBundle(mPinBundle);

            boolean needProcessPin = false;
            if (typeProcessCard == TYPE_PROCESS_NORMAL) {
                if (cardType == POIEmvCoreManager.DEVICE_MAGSTRIPE) {
                    if (isRunMacq) {
                        CardUtils cardUtils = new CardUtils();
                        if (preSaleConfig != null && !TextUtils.isEmpty(maskedPan)
                                && cardUtils.checkBinExitInList(maskedPan, preSaleConfig.getRequirePinPrefixes())
                        ) {
                            needProcessPin = true;
                        }
                    }
                }
                else {
                    needProcessPin = true;
                }
            }

            showLog("->need enter pin=" + needProcessPin);
            if (needProcessPin) {
                processPin(mPinBundle);
            }
            else {
                // pass input pin
                // 1 -> call sale to bank -> if card need enter pin, process will run processPin
                mPinBundleAtm = mPinBundle;
                Bundle bundle = new Bundle();
                bundle.putInt(POIEmvCoreManager.EmvPinConstraints.OUT_PIN_VERIFY_RESULT, POIEmvCoreManager.EmvPinConstraints.VERIFY_SUCCESS);
                bundle.putByteArray(POIEmvCoreManager.EmvPinConstraints.OUT_PIN_BLOCK, HexUtil.parseHex("****************"));
                POIEmvCoreManager.getDefault().onSetPinResponse(bundle);
            }
            showLog("RequestInputPin  >>");
        }

        @Override
        public void onRequestOnlineProcess(Bundle bundle) {
            showLog("RequestOnlineProcess  >> encryptResult=" + bundle.getInt(EmvOnlineConstraints.ENCRYPT_RESULT));
            cancelHandleDetectActionAfterDetectCard();
            AppExecutors.getInstance().networkIO().execute(() -> {
//                freshProcessDialog("  Authorizing,Please Wait  ");
//                showLog("   Authorizing,Please Wait  ");
                int encryptResult = bundle.getInt(EmvOnlineConstraints.ENCRYPT_RESULT, PosEmvErrorCode.EMV_UNENCRYPTED);
                byte[] emvData = bundle.getByteArray(EmvOnlineConstraints.EMV_DATA);

                byte[] encryptData = bundle.getByteArray(EmvOnlineConstraints.ENCRYPT_DATA);

                String privateTag = null;
                if (emvData != null) {
                    showLog("run: encryptResult=" + (encryptResult == PosEmvErrorCode.EMV_OK ? "ok" : "not ok")
                            + " encryptData=" + (encryptData == null ? "null" : encryptData.length));
//                    if (Utils.mDEBUG) {
//                        showLog( "run: emvData=" + HexUtil.toHexString(emvData));
//                    }
//                    BerTlvBuilder tlvBuilder = new BerTlvBuilder();
//                    BerTlvParser tlvParser = new BerTlvParser();

//                    showLog( "onRequestOnlineProcess: ---show value emvData---1");
//                    BerTlvs tlvs = tlvParser.parse(emvData);
//                    showTagLengthValue(tlvs, tlvBuilder);

                    if ((encryptResult == PosEmvErrorCode.EMV_OK || encryptResult == PosEmvErrorCode.EMV_APPROVED
                            || encryptResult == PosEmvErrorCode.EMV_APPROVED_ONLINE || encryptResult == PosEmvErrorCode.EMV_FORCE_APPROVED)
                            && encryptData != null) {
                        BerTlvs encryptTlvs = new BerTlvParser().parse(encryptData);
//                        showLog( "onRequestOnlineProcess: ---show value encryptTlvs---2");
//                        showTagLengthValue(encryptTlvs, tlvBuilder);

                        ksn = findTagInTlvs(encryptTlvs, TAG_ENCRYPT_KSN, false);
                        String maskPan = findTagInTlvs(encryptTlvs, TAG_PAN_MASK);

                        privateTag = findTagInTlvs(encryptTlvs, TAG_ENCRYPT_DATA);
                        if (cardType == POIEmvCoreManager.DEVICE_MAGSTRIPE) {
                            encTrack1 = findTagInTlvs(encryptTlvs, TAG_ENCRYPT_TRACK1_DATA);
                            encTrack2 = findTagInTlvs(encryptTlvs, TAG_ENCRYPT_TRACK2_MAGSTRIPE_DATA);

                            showLog( "onRequestOnlineProcess: t2-mag=" + encTrack2);
                            if (TextUtils.isEmpty(encTrack2)) {
                                encTrack2 = findTagInTlvs(encryptTlvs, TAG_ENCRYPT_TRACK2_DATA);
                            }
                            showLog( "run: 2 ksn=" + ksn);
                            showLog( "onRequestOnlineProcess: t1=" + encTrack1 + " t2=" + encTrack2);

                        }

                        if (!TextUtils.isEmpty(maskPan)) {
//                            showLog( "1--> maskPan=" + maskPan);
                            maskedPan = com.mpos.sdk.util.HexUtil.hexToAscii(maskPan);
//                            maskedPan = com.mpos.sdk.util.HexUtil.asciiToHex(maskPan);
                        }

                        if (Utils.checkTypeBuildIsCertify() && cardType == POIEmvCoreManager.DEVICE_CONTACTLESS) {
                            encTrack1 = findTagInTlvs(encryptTlvs, "DF50");
                            encTrack2 = findTagInTlvs(encryptTlvs, "DF51");
                            Utils.LOGD(TAG, "onRequestOnlineProcess: encTrack1=" + encTrack1 + " encTrack2=" + encTrack2);
                        }

                        showLog( "--> maskPan=" + maskedPan);
                    }
                }

                if (ksn == null) {
                    ksn = "";
                }
                if (privateTag == null) {
                    privateTag = "";
                }
                Utils.LOGD(TAG, "onRequestOnlineProcess: pTag=" + privateTag);
                Utils.LOGD(TAG, "onRequestOnlineProcess: ksn=" + ksn);

                if (TextUtils.isEmpty(ksn)) {
                    showLog("ksn is empty");
                    resetCacheInjectKey();
                    showDialogError(new DataError(ERROR_CODE_SP02_NO_KSN, getString(R.string.error_sp02_not_found_ksn)));
                    return;
                }
                else if (TextUtils.isEmpty(privateTag)) {
                    showLog("privateTag is empty");
                    showDialogError(new DataError(ERROR_CODE_SP02_NO_DATA_CARD, getString(R.string.error_not_read_card)));
                    return;
                }

                if (typeProcessCard == TYPE_PROCESS_NORMAL) {
                    CardUtils cardUtils = new CardUtils();
                    if (cardType == POIEmvCoreManager.DEVICE_MAGSTRIPE
                            && isRunMacq && preSaleConfig != null && !TextUtils.isEmpty(maskedPan)
                            && cardUtils.checkBinExitInList(maskedPan, preSaleConfig.getRequirePinPrefixes())) {
                        isProcessPinMacqAtm = true;
                        processPin(mPinBundleAtm);
                        waitEnterPin();
                    }
                }

                Utils.LOGD(TAG, "onRequestOnlineProcess: cardMOde=" + cardType);
                switch (cardType) {
                    case POIEmvCoreManager.DEVICE_CONTACT:
                    case POIEmvCoreManager.DEVICE_CONTACTLESS:
                        mDataEmv = buildEmvData(ksn, privateTag);

                        runOnUiThread(()->{
                            if (typeProcessCard == TYPE_PROCESS_GET_CARD) {
                                callbackCardData();
                            }
                            else if (checkCanContinuePay()) {
                                sendEmvSales();
                            }
                        });
                        break;
                    case POIEmvCoreManager.DEVICE_MAGSTRIPE:
                        encDataMag = privateTag;
                        runOnUiThread(()->{
                            if (typeProcessCard == TYPE_PROCESS_GET_CARD) {
                                callbackCardData();
                            }
                            else if (checkCanContinuePay()) {
                                sendMagSales();
                            }
                        });
                        break;
                }
                showLog("RequestOnlineProcess  <<");

//                    Bundle outBundle = new Bundle();
//
//                    outBundle.putInt(EmvOnlineConstraints.OUT_AUTH_RESP_CODE, OnlineResult.getOnlineResult("Approve"));
//                    outBundle.putByteArray(EmvOnlineConstraints.OUT_AUTH_DATA, HexUtil.parseHex("B7A68C6000860000"));
//                    mEmvCoreManager.onSetOnlineResponse(outBundle);
            });
        }

        @Override
        public void onTransactionResult(int result, Bundle bundle) {
            showLog("TransactionResult  >>result=" + result);

            cancelHandleDetectActionAfterDetectCard();
//            dismissDialog();

//            byte[] mBuff = data.getByteArray(EmvResultConstraints.EMVDATA);
//
//            BerTlvParser mTlvParser = new BerTlvParser();
//            BerTlvs mBerTlvs = mTlvParser.parse(mBuff);
//            for (BerTlv mTlv : mBerTlvs.getList()) {
//                Log.d(TAG, "Emv Tag :" + mTlv.getTag()+" Value :" + mTlv.getHexValue());
//            }

            byte[] mScriptBuff = bundle.getByteArray(EmvResultConstraints.SCRIPT_RESULT);
            if (mScriptBuff != null) {
//                Log.d(TAG, "Script :" + PosUtils.bytesToHexString(mScriptBuff));
                showLog(" Script :" + PosUtils.bytesToHexString(mScriptBuff));
            }

            int cvm = bundle.getInt(EmvResultConstraints.CVM);

            switch (cvm) {
                case EmvResultConstraints.CVM_SIGNATURE:
//                    Log.d(TAG, "Cvm :CVM_SIGNATURE");
                    showLog(" Cvm :CVM_SIGNATURE");
                    break;
                case EmvResultConstraints.CVM_CONFIRMATION_CODE_VERIFIED:
//                    Log.d(TAG, "Cvm :CVM_CONFIRMATION_CODE_VERIFIED");
                    showLog(" Cvm :CVM_CONFIRMATION_CODE_VERIFIED");
                    break;
                case EmvResultConstraints.CVM_NO_CVM:
//                    Log.d(TAG, "Cvm :CVM_NO_CVM");
                    showLog(" Cvm :CVM_NO_CVM");
                    break;
//                case EmvResultConstraints.CVM_SEE_PHONE:
//                    Log.d(TAG, "Cvm :CVM_SEE_PHONE");
//                    AddLog(" Cvm :CVM_SEE_PHONE");
//                    break;
            }

//            boolean isSuccess = false;
            boolean showFallback = false;
            String msgError = null;
            int errorCode = ERROR_CODE_SP02_DEFAULT;
            switch (result) {
                case PosEmvErrorCode.EMV_APPROVED:
                    showLog(" EMV_APPROVED");
//                        isSuccess = true;
                    break;
                case PosEmvErrorCode.EMV_FORCE_APPROVED:
                    showLog(" EMV_FORCE_APPROVED");
//                        isSuccess = true;
                    break;
                case PosEmvErrorCode.EMV_APPROVED_ONLINE:
                    showLog(" EMV_APPROVED_ONLINE");
//                        isSuccess = true;
                    break;
                case PosEmvErrorCode.EMV_TERMINATED:
                    showLog(" EMV_TERMINATED");
                    msgError = getString(R.string.transaction_terminated);
                    break;
                case PosEmvErrorCode.EMV_DECLINED:
                    showLog(" EMV_DECLINED");
                    msgError = getString(R.string.transaction_declined);
                    break;
                case PosEmvErrorCode.EMV_CANCEL:
                    showLog(" EMV_CANCEL");
                    msgError = getString(R.string.transaction_cancel);
                    break;
                case PosEmvErrorCode.EMV_TIMEOUT:
                    showLog(" EMV_TIMEOUT");
                    msgError = getString(R.string.error_timeout_wait_card);
                    break;
                case PosEmvErrorCode.EMV_APP_EMPTY:
                    showLog(" EMV_APP_EMPTY");
                    errorCode = ERROR_CODE_SP02_NO_APP;
                    Utils.LOGD(TAG, "onTransactionResult: testNapas="+testNapas+" testAmex="+testAmex);
                    if (Utils.checkTypeBuildIsCertify() && testAmex) {
                        showFallback = true;
                        runOnUiThread(LibKozenP5.this::showDialogFallBack);
                    }
                    else if (BuildConfig.DEBUG && testNapas) {
                        msgError = getString(R.string.FALLBACK_NOTI_SWIPE_CARD_NAPAS);
                    }
                    else {
                        msgError = getString(R.string.transaction_app_fail);
                    }
                    if (!Utils.checkTypeBuildIsCertify()) {
                        enableRetryUpdateEmvConfig();
                        msgError += getLastUpdatedEmvConfig();
                    }
                    break;
                case PosEmvErrorCode.EMV_FALLBACK:
                    showLog(" EMV_FALLBACK");
                    errorCode = ConstantsPay.ERROR_CODE_SP02_FALLBACK_ERROR;
                    if (BuildConfig.DEBUG && testNapas) {
                        showFallback = true;
                        runOnUiThread(LibKozenP5.this::showDialogFallBack);
                    } else {
                        msgError = getString(R.string.FALLBACK_NOTI_ERROR);
                    }
                    break;
                default:
                    errorCode = ERROR_CODE_SP02_OTHER_ERROR;
                    msgError = getString(R.string.transaction_error);
                    showLog(" EMV_OTHER_ERROR");
                    break;
            }

            if (isContactLess) {
                errorCode = ConstantsPay.ERROR_CODE_SP02_NFC_ERROR;
                msgError += "\n" + getString(R.string.error_nfc_change_use_chip);
            }

            if (!isFinishedPayment && !isErrorCodeToPos && !showFallback && !isPinCancel && currStageProcess < STAGE_SIGNATURE) {
                Utils.LOGD(TAG, "onTransactionResult: cvm=" + cvm + " result=" + result);
                showLog("onTransactionResult: cvm=" + cvm + " result=" + result + " isRunScriptFromHost=" + isRunScriptFromHost);
                if (!isContactLess
//                        && cvm == EmvResultConstraints.CVM_NO_CVM
//                        && (result == PosEmvErrorCode.EMV_DECLINED || result == PosEmvErrorCode.EMV_COMMAND_FAIL)
                        && !TextUtils.isEmpty(msgError)
                        && isRunScriptFromHost
                        && currStageProcess < STAGE_PROCESS_EMV_CONFIRM) {

                    String finalMsgError = msgError;
                    runOnUiThread(() -> runVoidFailedTransaction(TYPE_ERROR_NO_CVM_CHIP_DENIAL, txId, pan, holderName, false,
                            new DataError(ConstantsPay.ERROR_CODE_DEFAULT, finalMsgError)));
                }
                else if (TextUtils.isEmpty(msgError)) {
                    sendConfirmEmvSales();
                }
                else {
                    showDialogError(new DataError(errorCode, msgError));
                }
            }
            showLog("TransactionResult  << error:" + msgError + " isErrorCodeToPos=" + isErrorCodeToPos
                    + " showFallback=" + showFallback + " isPinCancel=" + isPinCancel
                    + " currStageProcess=" + currStageProcess + " isFinishedPayment=" + isFinishedPayment);
        }
    }

    private void showTagLengthValue(BerTlvs tlvs, BerTlvBuilder tlvBuilder) {
//        BerTlvs tlvs = tlvParser.parse(emvData);
        int i = 0;
        for (BerTlv tlv : tlvs.getList()) {
            tlvBuilder.addBerTlv(tlv);
            Utils.LOGD(TAG, "showTagLengthValue: " + (i++) + " tag:" + tlv.getTag().getBerTagHex() + "->value:" + tlv.getHexValue());
        }
    }

    private String findTagInTlvs(BerTlvs encryptTlvs, String tag) {
        return findTagInTlvs(encryptTlvs, tag, true);
    }
    private String findTagInTlvs(BerTlvs encryptTlvs, String tag, boolean resultIsHex) {
        BerTlv berTlv = encryptTlvs.find(new BerTag(tag));
        if (berTlv != null) {
            return resultIsHex?berTlv.getHexValue():berTlv.getTextValue();
        }
        return "";
    }

    private int updateWorkingKey(String keyData) {
        int keyIndex = getKeyIndex();
        showLog( "writeWorkingKey() called with: keyIndex = [" + keyIndex + "], keyData = [" + keyData + "]");
        PedKeyInfo pedKeyInfo = new PedKeyInfo(POIHsmManage.PED_TMK, keyIndex, POIHsmManage.PED_TPK, keyIndex, 0, 16, HexUtil.parseHex(keyData));
        int result = POIHsmManage.getDefault().PedWriteKey(pedKeyInfo, new PedKcvInfo(0, new byte[5]));
        showLog( "updateWorkingKey: resultUpdate=" + result);
        return result;
    }


    @Override
    protected String getReaderType() {
        return ConstantsPay.READER_TYPE_KOZEN;
    }

    @Override
    protected void runScriptEMV(String scriptOrigin) {
        showLog( "runScriptEMV() called with: emv_script = [" + scriptOrigin + "]");
        String scriptRun = parseIssuerScript(scriptOrigin);

        runEmvScript(scriptRun);
    }

    private void runEmvScript(String scriptRun) {
        sendResultScriptToTerminal(buildScriptToBundle(scriptRun));
    }

    private Bundle buildScriptToBundle(String emvScript) {
        showLog( "buildScriptToBundle: emvScript=" + emvScript);
        Bundle bundle = new Bundle();
        BerTlvBuilder tlvBuilder = new BerTlvBuilder();
//        String authRespCode = null;
        String authCode = null;
        String script = null;
        BerTlvParser tlvParser = new BerTlvParser();
        List<BerTlv> tlvs = tlvParser.parse(HexUtil.parseHex(emvScript)).getList();
        showLog( "buildScriptToBundle: size=" + tlvs.size());
        for (BerTlv tlv : tlvs) {
            switch (tlv.getTag().getBerTagHex()) {
                case "8A":
//                    authRespCode = tlv.getHexValue();
                    break;
                case "91":
                    authCode = tlv.getHexValue();
                    break;
                case "71":
                case "72":
                    tlvBuilder.addBerTlv(tlv);
                    break;
                default:
                    break;
            }
        }
        if (tlvBuilder.build() != 0) {
            script = HexUtil.toHexString(tlvBuilder.buildArray());
        }
        bundle.putInt(EmvOnlineConstraints.OUT_AUTH_RESP_CODE, EmvOnlineConstraints.EMV_ONLINE_APPROVE);
        if (authCode != null) {
            bundle.putByteArray(EmvOnlineConstraints.OUT_AUTH_DATA, HexUtil.parseHex(authCode));
        }
        if (script != null) {
            bundle.putByteArray(EmvOnlineConstraints.OUT_ISSUER_SCRIPT, HexUtil.parseHex(script));
        }
        if (tlvBuilder.build() != 0 || authCode != null || script != null) {
            isRunScriptFromHost = true;
        }
        showLog("buildScriptToBundle: authCode=" + authCode + " script=" + script + " isRunScriptFromHost=" + isRunScriptFromHost);
        return bundle;
    }

    @Override
    protected void sendErrorCodeToPos(String errorCode, String issuerAuthenData){
        Utils.LOGD(TAG, "sendErrorCodeToPos: " + errorCode + " issuerAuthenData: " + issuerAuthenData);
        isErrorCodeToPos = true;
        issuerAuthenData = parseIssuerScript(issuerAuthenData);
        Utils.LOGD(TAG, "sendErrorCodeToPos: afterParse=" + issuerAuthenData);
        if (BuildConfig.DEBUG && (testAmex || testJCB || testNapas)) {
            Bundle bundle = new Bundle();
            BerTlvBuilder tlvBuilder = new BerTlvBuilder();
//            String authRespCode = null;
            String authCode = null;
            String script = null;
            BerTlvParser tlvParser = new BerTlvParser();
            List<BerTlv> tlvs = tlvParser.parse(HexUtil.parseHex(issuerAuthenData)).getList();
            showLog( "buildScriptToBundle: size=" + tlvs.size());
            for (BerTlv tlv : tlvs) {
                switch (tlv.getTag().getBerTagHex()) {
                    case "8A":
//                        authRespCode = tlv.getHexValue();
                        break;
                    case "91":
                        authCode = tlv.getHexValue();
                        break;
                    case "71":
                    case "72":
                        tlvBuilder.addBerTlv(tlv);
                        break;
                    default:
                        break;
                }
            }
            bundle.putInt(EmvOnlineConstraints.OUT_AUTH_RESP_CODE, EmvOnlineConstraints.EMV_ONLINE_FAIL);
            if (tlvBuilder.build() != 0) {
                script = HexUtil.toHexString(tlvBuilder.buildArray());
            }
            String asciiCode = getAsciiCodeFromErrorCode(errorCode);
            Utils.LOGD(TAG, "sendErrorCodeToPos: asciiCode=" + asciiCode + " hex=" + Arrays.toString(HexUtil.parseHex(asciiCode)));
//            bundle.putByteArray(EmvOnlineConstraints.OUT_SPECIAL_AUTH_RESP_CODE, HexUtil.parseHex("3735"));
            bundle.putByteArray(EmvOnlineConstraints.OUT_SPECIAL_AUTH_RESP_CODE, HexUtil.parseHex(asciiCode));
            if (authCode != null) {
                bundle.putByteArray(EmvOnlineConstraints.OUT_AUTH_DATA, HexUtil.parseHex(authCode));
            }
            if (script != null) {
                bundle.putByteArray(EmvOnlineConstraints.OUT_ISSUER_SCRIPT, HexUtil.parseHex(script));
            }
            sendResultScriptToTerminal(bundle);
        }
    }

    private String getAsciiCodeFromErrorCode(String sCode) {
        if (sCode.length() % 2 != 0) {
            sCode = "0" + sCode;
        }
        Utils.LOGD(TAG, "getAsciiCodeFromErrorCode: errorCode=" + sCode + " sCode=" + sCode);
        return com.mpos.sdk.util.HexUtil.asciiToHex(sCode);
    }

    @Override
    protected void sendDenialToTerminal() {
        appendLogAction("send Z3 to terminal");

        String z3 = com.mpos.sdk.util.HexUtil.asciiToHex("Z3");
        Utils.LOGD(TAG, "sendDenialToTerminal: z3=" + z3);
        Bundle bundle = new Bundle();
        bundle.putInt(EmvOnlineConstraints.OUT_AUTH_RESP_CODE, EmvOnlineConstraints.EMV_ONLINE_DENIAL);
        bundle.putByteArray(EmvOnlineConstraints.OUT_SPECIAL_AUTH_RESP_CODE, HexUtil.parseHex(z3));
        sendResultScriptToTerminal(bundle);
    }

    private void sendResultScriptToTerminal(@NonNull  Bundle bundle) {
        sendResultScriptToTerminal(bundle, true);
    }
    private void sendResultScriptToTerminal(@NonNull  Bundle bundle, boolean showError) {
        try {
            currStageProcess = STAGE_PROCESS_EMV_RUN_SCRIPT;
            mEmvCoreManager.onSetOnlineResponse(bundle);
        } catch (Exception e) {
            e.printStackTrace();
            if (showError) {
                showDialogErrorSp02(getString(R.string.transaction_error_default_SP02,"script_fail"));
            }
        }
    }


    private void sendEmvSales() {
        runOnUiThread(() -> {
            if (isRunMacq) {
                sendEmvSalesMacq();
            }
            else {
                sendEmvSalesBank(mDataEmv);
            }
        });
    }

    private String buildEmvData(String ksn, String data) {
        appendLogAction("pBlock: " + (TextUtils.isEmpty(pinBlock) ? "empty" : "not empty"));
        String C0 = "C0" + PayUtils.calculatorLength(ksn.length()) + ksn;
        String C2 = "C2" + PayUtils.calculatorLength(data.length()) + data;
        if (TextUtils.isEmpty(pinBlock)) {
            return C0+C2;
        }
        else {
            String C7 = "C7" + PayUtils.calculatorLength(pinBlock.length()) + pinBlock;
            return C0 + C7 + C2;
        }
    }

    Bundle processOnlineResult(String arpc) {
        Bundle bundle = new Bundle();
        BerTlvBuilder tlvBuilder = new BerTlvBuilder();
        String authRespCode = null;
        String authCode = null;
        String script = null;
        BerTlvParser tlvParser = new BerTlvParser();
        List<BerTlv> tlvs = tlvParser.parse(HexUtil.parseHex(arpc)).getList();
        for (BerTlv tlv : tlvs) {
            switch (tlv.getTag().getBerTagHex()) {
                case "8A":
                    authRespCode = tlv.getHexValue();
                    break;
                case "91":
                    authCode = tlv.getHexValue();
                    break;
                case "71":
                case "72":
                    tlvBuilder.addBerTlv(tlv);
                    break;
                default:
                    break;
            }
        }
        if (tlvBuilder.build() != 0) {
            script = HexUtil.toHexString(tlvBuilder.buildArray());
        }
        bundle.putInt(EmvOnlineConstraints.OUT_AUTH_RESP_CODE, EmvOnlineConstraints.EMV_ONLINE_APPROVE);
        if (authCode != null) {
            bundle.putByteArray(EmvOnlineConstraints.OUT_AUTH_DATA, HexUtil.parseHex(authCode));
        }
        if (script != null) {
            bundle.putByteArray(EmvOnlineConstraints.OUT_ISSUER_SCRIPT, HexUtil.parseHex(script));
        }
        System.out.println("AuthRespCode" + authRespCode);
        System.out.println("AuthCode" + authCode);
        System.out.println("Script" + script);
        return bundle;
    }

    /**
     * CALL API
     **/
    private void sendMagSales() {
        sendMagSalesWithPinBlock(pinBlock);
    }
    private void sendMagSalesWithPinBlock(final String pinBlock) {
        runOnUiThread(() -> {
            if (isRunMacq) {
                sendMagstripeSaleMacq(pinBlock);
            }
            else {
                sendMagstripeSalesBank(pinBlock);
            }
        });
    }

    @Override
    protected void handleWorkingKeyFromServer(String ezpk) {
        PrefLibTV.getInstance(context).setWorkingKey(ezpk);
        isProcessPinBank = true;
        processPin(mPinBundleAtm);
    }

    @Override
    protected void processAlertRemoveCard() {}

    @Override
    void handleSuccessSaleMacq(DataSaleSend dataSend, DataSaleRes dataRes) {
        if (nameTypeCard.equals(ConstantsPay.CARD_MAGSTRIPE) || dataSend.isNFC()) {
            onSuccessSaleMacq(dataSaleSuccess);
        }
        else {
            emvScript = dataRes.getScriptField();
            runScriptEMV(emvScript);
        }
    }

    public void processUpdateEmvConfig(EmvConfigSp01 emvConfigSp02) {
        if (emvConfigSp02 == null) {
            return;
        }
        Utils.LOGD(TAG, "processUpdateEmvConfig: --start-->");
        StringBuilder log = new StringBuilder("processUpdateEmvConfig SP02");
        // AID
        if (emvConfigSp02.getAppSP02() != null && emvConfigSp02.getAppSP02().size() > 0) {
            POIEmvCoreManager emvCoreManager = POIEmvCoreManager.getDefault();
            List<PosEmvAid> list = emvCoreManager.EmvGetAid();
            log.append("app curr: size=").append(list.size()).append("\n");
            for (PosEmvAid item : list) {
                log.append(PosUtils.bytesToHexString(item.AID)).append("-");
            }
            emvCoreManager.EmvDeleteAid();
            log.append("\nStart add: size=").append(emvConfigSp02.getAppSP02().size()).append(" add:");
            int result;
            for (AppSp02 appSp02 : emvConfigSp02.getAppSP02()) {
                result = emvCoreManager.EmvSetAid(appSp02.convertToPosEmvAid());
                log.append(appSp02.getAid()).append(" ->").append(result).append("|");
            }
        }
        // CAPK
        if (emvConfigSp02.getCapkSP02() != null && emvConfigSp02.getCapkSP02().size() > 0) {
            POIEmvCoreManager emvCoreManager = POIEmvCoreManager.getDefault();

            List<PosEmvCapk> list = emvCoreManager.EmvGetCapk();
            log.append("\ncapk curr: size=").append(list.size()).append("\n");
            for (PosEmvCapk item : list) {
                log.append(PosUtils.bytesToHexString(item.RID)).append("-");
            }

            emvCoreManager.EmvDeleteCapk();
            log.append("\nStart add:");
            int result;
            for (CapkSp02 capkSp02 : emvConfigSp02.getCapkSP02()) {
                result = emvCoreManager.EmvSetCapk(capkSp02.convertToPosEmvCapk());
                log.append("add: ").append(capkSp02.getRid()).append(" ->").append(result);
            }
        }
        // special config
        if (emvConfigSp02.getEmvConfigSP02() != null && emvConfigSp02.getEmvConfigSP02().size() > 0) {
            for (EmvConfigSP02 configSP02 : emvConfigSp02.getEmvConfigSP02()) {
                Bundle bundle = new Bundle();
                BerTlvBuilder tlvBuilder = new BerTlvBuilder();
                log.append("->type=").append(configSP02.getType());
                for (EmvConfigSP02.TLV tlv : configSP02.getData()) {
                    tlvBuilder.addBerTlv(new BerTlv(new BerTag(tlv.getTag()), HexUtil.parseHex(tlv.getValue())));
                    log.append("|t=").append(tlv.getTag()).append("-v=").append(tlv.getValue());
                }

                bundle.putByteArray(POIEmvCoreManager.EmvTerminalConstraints.CONFIG, tlvBuilder.buildArray());
                POIEmvCoreManager.getDefault().EmvSetTerminal(configSP02.getType(), bundle);
            }
        }
        Utils.LOGD(TAG, "processUpdateEmvConfig: "+ log);
        String ifdSerialNumber = null;
        // N31
        if (DevicesUtil.isSP02P12()) {
            ifdSerialNumber = "SP05";
        }
        // mini
        else if (DevicesUtil.isSP02P5()) {
            ifdSerialNumber = "SP02";
        }
        // pro
        else if (DevicesUtil.isSP02P8()) {
            ifdSerialNumber = "SP03";
        }

        if (!TextUtils.isEmpty(ifdSerialNumber)) {
            ifdSerialNumber = String.format("%1$-8s", ifdSerialNumber);

            Bundle bundle = new Bundle();
            bundle.putByteArray(POIEmvCoreManager.EmvTerminalConstraints.IFD_SERIAL_NUMBER, ifdSerialNumber.getBytes());
            POIEmvCoreManager.getDefault().EmvSetTerminal(POIEmvCoreManager.EmvTerminalConstraints.TYPE_TERMINAL, bundle);

            log.append("update 9F1E: ").append(ifdSerialNumber);
        }
        loadSettingPure();
        appendLogAction(log.toString());
    }

    public void loadSettingPure() {
        try {
            Bundle bundle = new Bundle();

            // C705 36006043F9 DF1B0100 DF300107 DF310101 DF320101
            // C70536006043F9DF1B0100DF300107DF310101DF320101
            bundle.putByteArray(POIEmvCoreManager.EmvTerminalConstraints.CONFIG, HexUtil.parseHex("C70536006043F9DF1B0100DF300106DF310101DF320101"));
            int result = POIEmvCoreManager.getDefault().EmvSetTerminal(POIEmvCoreManager.EmvTerminalConstraints.SETTINGS_PURE, bundle);
            appendLogAction("loadSettingPure results="+result);
        } catch (Exception e) {
            Utils.LOGE(TAG, "loadSettingPure: "+e.getMessage(), e);
            appendLogAction("loadSettingPure error: " + e.getMessage());
        }
    }

    // -------------------------------------------
    private void showAllInBundle(Bundle bundle) {
        if (Utils.mDEBUG && bundle != null) {
            for (String key : bundle.keySet()) {
                Utils.LOGD(TAG, "bundle->" + key + " = " + (bundle.get(key) == null ? "" : bundle.get(key).toString()));
            }
        }
    }

    private void showDialogWarningUseChip() {
        showDialogWarningRetryPayment(getString(R.string.ERROR_8103));
    }

    private void showDialogFallBack() {
        showDialogWarningRetryPayment(getString(R.string.FALLBACK_NOTI_SWIPE_CARD_NAPAS));
        isFallBack = true;
    }
    private void showDialogWarningRetryPayment(String msg) {
        showDialogWarningRetryPayment(msg, this::startPayment);
    }

}
