package com.mpos.sdk.core.control;

import android.content.Context;
import android.graphics.Bitmap;

import androidx.annotation.NonNull;

import com.mpos.sdk.R;
import com.mpos.sdk.util.DevicesUtil;
import com.mpos.sdk.util.MyDialogShow;
import com.pax.dal.entity.EFontTypeAscii;
import com.pax.dal.entity.EFontTypeExtCode;
import com.pax.emvdemo.ITYPaxApi;

/**
 * Create by x.thuan on 2022-03-25
 */
public class LibPrinterPax {
    private static final String TAG = "LibPrinterPax";

    ITYPaxApi paxApi;
    Context context;
    ItfResultPrintPax itfResultPrintPax;

    /*   PRINTER    */
    public static final int PRINT_SUCCESS = 0;
    public static final int PRINT_BUSY = 1;
    public static final int PRINT_OUT_OF_PAPER = 2;
    public static final int PRINT_OVER_HEATS = 8;
    public static final int PRINT_VOLTAGE_LOW = 9;

    public LibPrinterPax(@NonNull Context c) throws Exception {
        if (!DevicesUtil.isPax()) {
            throw new Exception("Library is only use for reader PAX.");
        } else {
            this.context = c;
            initPax(c);
            initPrinter();
        }
    }

    private void initPax(Context context) {
        if (this.paxApi == null) {
            paxApi = ITYPaxApi.get(context);
        }
    }

    private void initPrinter() {
        if (this.paxApi != null) {
            paxApi.initPrinter();

            setGray(1);
            setSpace(Byte.parseByte("0"), Byte.parseByte("0"));
            setLeftIndent(0);
            setStepPrint(40);
            setFont(EFontTypeAscii.FONT_16_16, EFontTypeExtCode.FONT_16_16);
        }
    }

    public void disconnectPrinter() {
        if (this.paxApi != null) {
            paxApi.initPrinter();
        }
    }

    public void setGray(int i) {
        if (paxApi != null) {
            paxApi.setGray(i);
        }
    }

    public void setSpace(byte b, byte b1) {
        if (paxApi != null) {
//            paxApi.setSpace(Byte.parseByte("0"), Byte.parseByte("0"));
            paxApi.setSpace(b, b1);
        }
    }

    public void setLeftIndent(int i) {
        if (paxApi != null) {
            paxApi.setLeftIndent(i);
        }
    }

    public void setFont(EFontTypeAscii i, EFontTypeExtCode i1) {
        if (paxApi != null) {
            paxApi.setFont(i, i1);
        }
    }

    public void setStepPrint(int i) {
        if (paxApi != null) {
            paxApi.setStepPrint(i);
        }
    }

    public int getDotLine() {
        int res = -1;
        if (paxApi != null) {
            res = paxApi.getDotLine();
        }
        return res;
    }

    public void printBitmap(Bitmap bitmap) {
        if (paxApi != null) {
            paxApi.printBitmap(bitmap);
        }
    }

    public void printStr(String str, String charset) {
        if (paxApi != null) {
            paxApi.printStr(str, charset);
        }
    }

    public int start() {
        int result = -1;
        try {
            if (paxApi != null) {
                result = paxApi.start();

                if (itfResultPrintPax != null) {
                    itfResultPrintPax.result(result);
                } else {
                    onResultPrinter(result);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    private void onResultPrinter(int res) {
        switch (res) {
            case PRINT_SUCCESS:
                break;
            case PRINT_BUSY:
                MyDialogShow.showDialogError(context.getString(R.string.print_busy), context);

                break;
            case PRINT_VOLTAGE_LOW:
                MyDialogShow.showDialogError(context.getString(R.string.print_voltage_low), context);

                break;
            case PRINT_OUT_OF_PAPER:
                MyDialogShow.showDialogError(context.getString(R.string.error_printer_out_of_paper), context);

                break;
            case PRINT_OVER_HEATS:
                MyDialogShow.showDialogError(context.getString(R.string.print_over_heats), context);
                break;
            default:
                MyDialogShow.showDialogError(context.getString(R.string.error_printer_default), context);
                break;
        }
    }

    public void setItfResultPrintPax(ItfResultPrintPax itfResultPrintPax) {
        this.itfResultPrintPax = itfResultPrintPax;
    }

    public interface ItfResultPrintPax {
        void result(int result);
    }
}
