package com.mpos.sdk.core.control;

import android.content.Context;
import android.text.TextUtils;
import android.widget.Toast;

import androidx.annotation.NonNull;

import com.loopj.android.http.AsyncHttpResponseHandler;
import com.mpos.sdk.core.common.MyGson;
import com.mpos.sdk.core.common.MyTextHttpResponseHandler;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.core.modelma.IpekRes;
import com.mpos.sdk.core.modelma.IpekSend;
import com.mpos.sdk.core.mposinterface.ItfAppendLog;
import com.mpos.sdk.core.mposinterface.ItfShowLoading;
import com.mpos.sdk.core.network.ApiMultiAcquirerInterface;
import com.mpos.sdk.core.network.MposRestClient;
import com.mpos.sdk.util.AppExecutors;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.MacqUtil;
import com.mpos.sdk.util.Utils;

import org.json.JSONObject;

import java.util.Arrays;

import cz.msebera.android.httpclient.Header;
import cz.msebera.android.httpclient.HttpStatus;
import cz.msebera.android.httpclient.entity.StringEntity;

/**
 * Create by anhnguyen on 11/4/21
 */
public abstract class LibDownloadKey {

    private static final String TAG = "LibDownloadKey";


    public static int CODE_INJECT_SUCCESS = 0;
    public static int CODE_INJECT_FAIL_GETKEY = -1;

    public static int TYPE_KEY_NONE = 0;
    public static int TYPE_KEY_KSN  = 1;
    public static int TYPE_KEY_IPEK = 2;
    public static int TYPE_KEY_TMK  = 3;
    public static int TYPE_KEY_END  = 4;

    protected Context context;

    protected int typeDevice;
    protected int typeServer;
    protected String serialNumber;

    protected String IPEK_FROM_SERVER;
    protected String KSN_FROM_SERVER;
    protected String PIN_MASTER_KEY_FROM_SERVER;
    protected String KCV_PIN_MASTER_KEY_FROM_SERVER;
    protected int KEY_PIN_IPEK_POSITION = -1;

    protected boolean showToast = false;
    protected boolean processMA = false;


    protected ItfResultInjectKey callback;
    protected ItfShowLoading callbackShowLoading;

    protected ItfAppendLog cbLog;


    abstract void handlerKeyFromBank(String dataResponse) throws Exception;

    abstract void handlerKeyFromMA(IpekRes ipekRes, String ksn);

    public LibDownloadKey(@NonNull Context context, int typeDevice, int typeServer, @NonNull String serialNumber) {
        this.context = context;
        this.typeDevice = typeDevice;
        this.typeServer = typeServer;
        this.serialNumber = serialNumber;
    }


    public void setShowToast(boolean showToast) {
        this.showToast = showToast;
    }

    public void setCallback(ItfResultInjectKey callback) {
        this.callback = callback;
    }

    public void setCallbackShowLoading(ItfShowLoading callbackShowLoading) {
        this.callbackShowLoading = callbackShowLoading;
    }

    public void setCbLog(ItfAppendLog cbLog) {
        this.cbLog = cbLog;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    private String getString(int resId) {
        if (context != null) {
            context.getString(resId);
        }
        return "";
    }

    public void processInjectKey(){
        getKeyFromServer();
    }

    private void getKeyFromServer() {

        showLogInfo("get key - bank");
        // prepare call in Retrofit 2.0
        try {
            JSONObject paramObject = new JSONObject();
            if (typeDevice == ConstantsPay.DEVICE_DSPREAD) {
                paramObject.put("serviceName", "KEY_INJECTION_DSPREAD");
            }
            else {
                paramObject.put("serviceName", "KEY_INJECTION_P20L");
            }

            paramObject.put("sessionKey", PrefLibTV.getInstance(context).getSessionKey());
            paramObject.put("readerSerialNo", serialNumber);
            paramObject.put("udid", "0");
            paramObject.put("versionNo", android.os.Build.VERSION.RELEASE);
            paramObject.put("platform", ConstantsPay.PLATFORM);

            StringEntity entity = new StringEntity(EncodeDecode.doAESEncrypt(paramObject.toString(), PrefLibTV.getInstance(context).getSessionKey()));
            Utils.LOGD(TAG, "getKeyFromServerByRetrofit: 22 data send:"+paramObject.toString()+ " key="+PrefLibTV.getInstance(context).getSessionKey());
            Utils.LOGD(TAG, "getKeyFromServerByRetrofit:" + ConstantsPay.getUrlServer(context));

            MposRestClient.getInstance(context).post(context, ConstantsPay.getUrlServer(context),
                    entity, ConstantsPay.CONTENT_TYPE, new AsyncHttpResponseHandler() {

                        @Override
                        public void onStart() {
                            showLoading(true);
                            super.onStart();
                        }
                        @Override
                        public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {

                            String dataResponse;
                            try {
                                showLogInfo("get key success");
                                dataResponse = EncodeDecode.doAESDecrypt(new String(arg2), PrefLibTV.getInstance(context).getSessionKey());
                                Utils.LOGD(TAG, "dataResponse: "+dataResponse);
                                handlerKeyFromBank(dataResponse);
                            } catch (Exception e) {
                                showLogInfo("get key exception: " + e.getMessage());
                                showLoading(false);
                                e.printStackTrace();
                                callbackResult(TYPE_KEY_NONE, CODE_INJECT_FAIL_GETKEY, "Can not get data from server(rec data err)");
                            }
                        }

                        @Override
                        public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                            showLoading(false);
                            showLogInfo("get key onFail");
                            callbackResult(TYPE_KEY_NONE, CODE_INJECT_FAIL_GETKEY, "Can not get data from server(timeout)");
                        }
                    });
        } catch (Exception e) {
            e.printStackTrace();
            showLogInfo("get key exception");
            callbackResult(TYPE_KEY_NONE, CODE_INJECT_FAIL_GETKEY, "Can not get data from server(send data err)");
        }
    }

    public void getIpekFromMA() {
        processMA = true;
        String ksn = buildKsn(serialNumber);
        showLogInfo("get key - MA");
        IpekSend ipekSend = new IpekSend(ksn);

        MposRestClient.getInstance(context).postMacq(context, ApiMultiAcquirerInterface.URL_GET_IPEK,
                ipekSend, new MyTextHttpResponseHandler(context) {

            @Override
            public void onStart() {
                super.onStart();
                showLoading(true);
            }

            @Override
            public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                Utils.LOGD(TAG, "getIpek onFailure() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");
                String msgError;
                showLoading(false);
                if (statusCode == HttpStatus.SC_UNAUTHORIZED) {
                    msgError= "Thông tin đăng nhập không đúng";
                } else {
                    String clearData = MacqUtil.getInstance().parseAndDecryptData(rawJsonData);
                    Utils.LOGD(TAG, "getIpek onFailure: " + clearData);
                    msgError = "Không thể lấy dữ liệu từ server, vui lòng kiểm tra kết nối mạng và thử lại.";
                }
                callbackResult(TYPE_KEY_NONE, CODE_INJECT_FAIL_GETKEY, "Can not get data from server(send data err)");
                showLogInfo("get key onFail " + msgError);
            }

            @Override
            public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                Utils.LOGD(TAG, "getIpek onSuccess() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonResponse = [" + rawJsonResponse + "]");
                if (statusCode == HttpStatus.SC_OK) {
                    String clearData = MacqUtil.getInstance().parseAndDecryptData(rawJsonResponse);
                    Utils.LOGD(TAG, "onSuccess getIpek: " + clearData);

                    IpekRes ipekRes = MyGson.parseJson(clearData, IpekRes.class);

                    showLogInfo("load key ok");
                    Utils.LOGD(TAG, "onSuccessApi: ipek="+ipekRes.getIpek());
                    handlerKeyFromMA(ipekRes, ksn);
                }
            }
        });
    }


    protected void showLoading(boolean show) {
        if (callbackShowLoading != null) {
            callbackShowLoading.showLoading(show);
        }
    }

    protected void showLogInfo(String log) {
        Utils.LOGD(TAG, "log: " + log);
        if (cbLog != null) {
            cbLog.appendLog(ItfAppendLog.TypeLog.action, log);
        }
    }

    private String buildKsn(String serialNumber) {
        if (TextUtils.isEmpty(serialNumber)) {
            return "";
        }
        StringBuilder KSN = new StringBuilder(serialNumber.replaceAll("[^0-9A-F]", "F"));
        int length = (typeDevice == ConstantsPay.DEVICE_TAP_TO_PHONE) ? 12 : 10;
        if (KSN.length() / 2 < length) {
            for (int i = KSN.length()/2; i<length; i++) {
                KSN.append("00");
            }
        }
        return KSN.toString();
    }


    protected void callbackResult(int typeKey, int res, String msg) {
        AppExecutors.getInstance().mainThread().execute(() -> {
            showToast(msg);
            showLoading(false);
            if (callback != null) {
                callback.onResultInjectKey(typeKey, res, msg);
            }
        });
    }

    private void showToast(String msg) {
        if (showToast) {
            try {
                Toast.makeText(context, msg, Toast.LENGTH_SHORT).show();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public interface ItfResultInjectKey{
        void onResultInjectKey(int typeKey, int code, String msg);
    }
}
