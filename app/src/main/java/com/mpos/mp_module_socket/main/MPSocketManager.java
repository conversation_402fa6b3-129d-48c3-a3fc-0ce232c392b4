package com.mpos.mp_module_socket.main;

import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.mpos.mp_module_socket.model.Constant;
import com.mpos.mp_module_socket.service.SocketService;

/*
*
* create 22/09/2023 by UFO Thuan
*
* */

public class MPSocketManager extends ITYMpSocketManager {

    private static final String TAG = MPSocketManager.class.getSimpleName();

    Context context;
    Intent intentServiceSocket;
    Class<?> clsReOpen;
    public MPSocketManager() {
    }

    public MPSocketManager(Context context) {
        this.context = context;
    }

    //TODO public method
    @Override
    public void startServiceSocket() {
        appendLog( "startServiceSocket: ");
        intentServiceSocket = new Intent(context, SocketService.class);
        intentServiceSocket.putExtra("clsReOpen", clsReOpen);
        context.startService(intentServiceSocket);
    }

    @Override
    public void closeServiceSocket() {
        if (intentServiceSocket != null) {
            context.stopService(intentServiceSocket);
        }
    }

    @Override
    public void pushResultToClient(String data) {
        Intent intentState = new Intent(Constant.nameFilterActionReturnResult);
        intentState.putExtra(Constant.EXTRA_DATA_RESULT, data);
        context.sendBroadcast(intentState);
    }

    //TODO LOG
    private void appendLog(String logs) {
        Log.d(TAG, "appendLog: " + logs);
    }

    @Override
    public void setContext(Context context) {
        this.context = context;
    }

    @Override
    public void setClsReOpen(Class<?> clsReOpen) {
        this.clsReOpen = clsReOpen;
    }

    public enum StatusConnect {
        CONNECT,
        NONE
    }

    public enum ServiceName {
        ADD_ORDER,
        ADD_DEPOSIT,
        SET_FINAL_DEPOSIT,
        FINISH_DEPOSIT,
        ADD_MOTO,
        ADD_DEPOSIT_MOTO,
        CANCEL_ORDER,
        VOID_TRANS,
        NONE
    }
}
