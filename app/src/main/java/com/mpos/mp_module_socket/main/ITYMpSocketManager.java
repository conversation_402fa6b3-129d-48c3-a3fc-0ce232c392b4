package com.mpos.mp_module_socket.main;

import android.app.Service;
import android.content.Context;

//public abstract class ITYMpSocketManager extends Service {
public abstract class ITYMpSocketManager {
    private static ITYMpSocketManager socketManager;
    private static final Object lock = new Object();

    public abstract void setContext(Context context);
    public abstract void setClsReOpen(Class<?> clsReOpen);
    public abstract void pushResultToClient(String result);

    public abstract void startServiceSocket();
    public abstract void closeServiceSocket();

    public ITYMpSocketManager() {
    }

    public static ITYMpSocketManager get(Context mContext) {
        synchronized(lock) {
            if (socketManager == null) {
                socketManager = new MPSocketManager(mContext);
            }
            else {
                socketManager.setContext(mContext);
            }
        }

//        if (socketManager == null) {
//            socketManager = new MPSocketManager(mContext);
//        }

        return socketManager;
    }
}
