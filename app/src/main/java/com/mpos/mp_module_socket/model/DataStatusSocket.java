package com.mpos.mp_module_socket.model;
import com.google.gson.annotations.Expose;
import com.mpos.mp_module_socket.main.MPSocketManager.StatusConnect;
import com.mpos.mp_module_socket.main.MPSocketManager.ServiceName;

public class DataStatusSocket {
    @Expose
    StatusConnect statusConnect;
    @Expose
    ServiceName serviceName;
    @Expose
    DataOrder dataOrder;
    @Expose
    DataVoid dataVoid;
    DataDeposit dataDeposit;
    DataMoto dataMoto;
    @Expose
    String ipAddress;
    @Expose
    String errMsg;

    public DataStatusSocket() {
    }

    public DataStatusSocket(StatusConnect statusConnect, ServiceName serviceName) {
        this.statusConnect = statusConnect;
        this.serviceName = serviceName;
    }

    public DataStatusSocket(StatusConnect statusConnect, ServiceName serviceName, String errMsg) {
        this.statusConnect = statusConnect;
        this.serviceName = serviceName;
        this.errMsg = errMsg;
    }

    public DataStatusSocket(StatusConnect statusConnect, ServiceName serviceName, DataOrder dataOrder) {
        this.statusConnect = statusConnect;
        this.serviceName = serviceName;
        this.dataOrder = dataOrder;
    }

    public DataStatusSocket(StatusConnect statusConnect, ServiceName serviceName, DataDeposit dataDeposit) {
        this.statusConnect = statusConnect;
        this.serviceName = serviceName;
        this.dataDeposit = dataDeposit;
    }
    public DataStatusSocket(StatusConnect statusConnect, ServiceName serviceName, DataMoto dataMoto) {
        this.statusConnect = statusConnect;
        this.serviceName = serviceName;
        this.dataMoto = dataMoto;
    }

    public DataStatusSocket(StatusConnect statusConnect, ServiceName serviceName, DataVoid dataVoid) {
        this.statusConnect = statusConnect;
        this.serviceName = serviceName;
        this.dataVoid = dataVoid;
    }

    public DataStatusSocket(StatusConnect statusConnect) {
        this.statusConnect = statusConnect;
    }

    public DataStatusSocket(StatusConnect statusConnect, String ipAddress) {
        this.statusConnect = statusConnect;
        this.ipAddress = ipAddress;
    }

    public StatusConnect getStatusConnect() {
        return statusConnect;
    }

    public void setStatusConnect(StatusConnect statusConnect) {
        this.statusConnect = statusConnect;
    }

    public ServiceName getServiceName() {
        return serviceName;
    }

    public void setServiceName(ServiceName serviceName) {
        this.serviceName = serviceName;
    }

    public DataOrder getDataOrder() {
        return dataOrder;
    }

    public void setDataOrder(DataOrder dataOrder) {
        this.dataOrder = dataOrder;
    }

    public DataVoid getDataVoid() {
        return dataVoid;
    }

    public void setDataVoid(DataVoid dataVoid) {
        this.dataVoid = dataVoid;
    }

    public DataDeposit getDataDeposit() {
        return dataDeposit;
    }

    public void setDataDeposit(DataDeposit dataDeposit) {
        this.dataDeposit = dataDeposit;
    }

    public DataMoto getDataMoto() {
        return dataMoto;
    }

    public void setDataMoto(DataMoto dataMoto) {
        this.dataMoto = dataMoto;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }
}
