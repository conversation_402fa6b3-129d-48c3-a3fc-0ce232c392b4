apply plugin: 'com.android.library'
//apply plugin: 'com.android.application'

android {
    namespace 'com.mpos.mp_module_socket'
    compileSdk 33

    defaultConfig {
//        applicationId "com.mpos.mp_module_socket"
        minSdk 23
        targetSdk 33
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    android.libraryVariants.all { variant ->
        variant.outputs.all { vOutput ->
            def fileName = "mpModuleTcp.aar";
            outputFileName = fileName
        }
    }
}

dependencies {
    implementation 'androidx.appcompat:appcompat:1.5.0'
    implementation 'com.google.android.material:material:1.5.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'
    implementation 'com.google.code.gson:gson:2.8.6'
}