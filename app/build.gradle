apply plugin: 'com.android.library'
//apply plugin: 'com.jakewharton.butterknife'
//apply plugin: 'com.android.application'

android {
    compileSdk 34
    buildToolsVersion '34.0.0'
    android.buildFeatures.buildConfig true


    defaultConfig {
//        applicationId "com.mpos.demosdk"
        namespace = "com.mpos.sdk"

        minSdkVersion 23
        versionCode 2
        versionName '1.0.240827'
        version "1.0.240827"
        compileOptions {
            sourceCompatibility JavaVersion.VERSION_1_8
            targetCompatibility JavaVersion.VERSION_1_8
        }
//
//        multiDexEnabled true
    }
    signingConfigs {
        dev {
            keyAlias 'androiddebugkey'
            keyPassword 'android'
            storeFile file('/Volumes/DataWork/ws_android_studio_ps/debug.keystore')
            storePassword 'android'
        }
        KozenKey {
            keyAlias 'KozenOSSign'
            keyPassword 'Kozen2022'
            storePassword 'Kozen2022'
            storeFile file('../Kozen-signature-key/P0821/KozenOSSign.jks')
        }
    }
    buildTypes {
        def VERSION_CODE = "2"
        def VERSION_NAME = "\"1.0.1\""
        def URL_MPOS         = "\"https://api.mpos.vn/\""
        def URL_MPOS_DEV     = "\"https://devapi.mpos.vn\""
        def URL_MPOS_STAGING = "\"https://staging-api.mpos.vn\""
        def URL_MA_BASE      = "\"https://ma.nextpay.vn\""
//        def URL_MA_BASE_AWS  = "\"https://ma-aws.nextpay.vn/\""
//        def URL_MA_BASE_DEV  = "\"https://api-dev-macq.nextpay.global\""
        def URL_MA_BASE_DEV  = "\"https://dev-macq-gateway.nextpay.vn\""
        def URL_MA_BASE_STAGING  = "\"https://staging-api-macq.nextpay.vn\""
        def URL_SERVER_QR_NL_DEV        = "\"https://sandbox2.nganluong.vn/vietcombank-checkout/vcb/api/web/checkout/version_1_0\""
        def URL_SERVER_QR_NL            = "\"https://vietcombank.nganluong.vn/api/web/checkout/version_1_0/\""

        debugDisableLog {
            buildConfigField "int", "VERSION_CODE", VERSION_CODE
            buildConfigField "String", "VERSION_NAME", VERSION_NAME
            buildConfigField "boolean", "debugLog", "true"
//            buildConfigField "boolean", "debugLog", "true"
            buildConfigField "String", "typeBuild", "\"debug\""
            buildConfigField "String", "TYPE_INTEGRATION", "\"MPOS\""
            buildConfigField "String", "URL_MPOS", URL_MPOS_DEV
            buildConfigField "String", "URL_MA_BASE", URL_MA_BASE_DEV
            buildConfigField "String", "URL_SERVER_QR_NL", URL_SERVER_QR_NL_DEV
        }
        debug {
            buildConfigField "int", "VERSION_CODE", VERSION_CODE
            buildConfigField "String", "VERSION_NAME", VERSION_NAME
//            buildConfigField "boolean", "debugLog", "false"
            buildConfigField "boolean", "debugLog", "true"
            buildConfigField "String", "typeBuild", "\"debug\""
            buildConfigField "String", "TYPE_INTEGRATION", "\"MPOS\""
            buildConfigField "String", "URL_MPOS", URL_MPOS_DEV
            buildConfigField "String", "URL_MA_BASE", URL_MA_BASE_DEV
            buildConfigField "String", "URL_SERVER_QR_NL", URL_SERVER_QR_NL_DEV
        }
        debugKozen {
            signingConfig signingConfigs.KozenKey

            buildConfigField "int", "VERSION_CODE", VERSION_CODE
            buildConfigField "String", "VERSION_NAME", VERSION_NAME
            buildConfigField "boolean", "debugLog", "true"
            buildConfigField "String", "typeBuild", "\"debug\""
            buildConfigField "String", "TYPE_INTEGRATION", "\"MPOS\""
            buildConfigField "String", "URL_MPOS", URL_MPOS
            buildConfigField "String", "URL_MA_BASE", URL_MA_BASE
            buildConfigField "String", "URL_SERVER_QR_NL", URL_SERVER_QR_NL_DEV
        }
        live {
            signingConfig signingConfigs.dev
            debuggable true

            buildConfigField "int", "VERSION_CODE", VERSION_CODE
            buildConfigField "String", "VERSION_NAME", VERSION_NAME
            buildConfigField "boolean", "debugLog", "true"
            buildConfigField "String", "typeBuild", "\"live\""
            buildConfigField "String", "TYPE_INTEGRATION", "\"MPOS\""
            buildConfigField "String", "URL_MPOS", URL_MPOS
            buildConfigField "String", "URL_MA_BASE", URL_MA_BASE
            buildConfigField "String", "URL_SERVER_QR_NL", URL_SERVER_QR_NL
        }
        staging {
            signingConfig signingConfigs.dev
            debuggable true
//            matchingFallbacks = ['release']
            buildConfigField "int", "VERSION_CODE", VERSION_CODE
            buildConfigField "String", "VERSION_NAME", VERSION_NAME
            buildConfigField "boolean", "debugLog", "true"
            buildConfigField "String", "typeBuild", "\"live\""
            buildConfigField "String", "TYPE_INTEGRATION", "\"MPOS\""
            buildConfigField "String", "URL_MPOS", URL_MPOS_STAGING
            buildConfigField "String", "URL_MA_BASE", URL_MA_BASE_STAGING
        }
        stagingPartner {
            signingConfig signingConfigs.dev
            debuggable false
//            matchingFallbacks = ['release']
            buildConfigField "int", "VERSION_CODE", VERSION_CODE
            buildConfigField "String", "VERSION_NAME", VERSION_NAME
            buildConfigField "boolean", "debugLog", "false"
            buildConfigField "String", "typeBuild", "\"live\""
            buildConfigField "String", "TYPE_INTEGRATION", "\"PARTNER\""
            buildConfigField "String", "URL_MPOS", URL_MPOS_STAGING
            buildConfigField "String", "URL_MA_BASE", URL_MA_BASE_STAGING
        }
        stagingMpos3rd {
            signingConfig signingConfigs.dev
            debuggable false
//            matchingFallbacks = ['release']
            buildConfigField "int", "VERSION_CODE", VERSION_CODE
            buildConfigField "String", "VERSION_NAME", VERSION_NAME
            buildConfigField "boolean", "debugLog", "false"
            buildConfigField "String", "typeBuild", "\"live\""
            buildConfigField "String", "TYPE_INTEGRATION", "\"MPOS_3RD\""
            buildConfigField "String", "URL_MPOS", URL_MPOS_STAGING
            buildConfigField "String", "URL_MA_BASE", URL_MA_BASE_STAGING
        }
        certify {
            signingConfig signingConfigs.dev
            debuggable true
//            matchingFallbacks = ['release']

            buildConfigField "int", "VERSION_CODE", VERSION_CODE
            buildConfigField "String", "VERSION_NAME", VERSION_NAME
            buildConfigField "boolean", "debugLog", "true"
            buildConfigField "String", "typeBuild", "\"certify\""
            buildConfigField "String", "TYPE_INTEGRATION", "\"MPOS\""
            buildConfigField "String", "URL_MPOS", URL_MPOS_DEV
            buildConfigField "String", "URL_MA_BASE", URL_MA_BASE_DEV
        }
        certifyStaging {
            signingConfig signingConfigs.dev
            debuggable true
//            matchingFallbacks = ['release']

            buildConfigField "int", "VERSION_CODE", VERSION_CODE
            buildConfigField "String", "VERSION_NAME", VERSION_NAME
            buildConfigField "boolean", "debugLog", "true"
            buildConfigField "String", "typeBuild", "\"certify\""
            buildConfigField "String", "TYPE_INTEGRATION", "\"MPOS\""
            buildConfigField "String", "URL_MPOS", URL_MPOS_STAGING
            buildConfigField "String", "URL_MA_BASE", URL_MA_BASE_STAGING
        }
        release {

            buildConfigField "int", "VERSION_CODE", VERSION_CODE
            buildConfigField "String", "VERSION_NAME", VERSION_NAME
            buildConfigField "boolean", "debugLog", "false"
            buildConfigField "String", "typeBuild", "\"release\""
            buildConfigField "String", "TYPE_INTEGRATION", "\"MPOS\""
            buildConfigField "String", "URL_MPOS", URL_MPOS
            buildConfigField "String", "URL_MA_BASE", URL_MA_BASE
            buildConfigField "String", "URL_SERVER_QR_NL", URL_SERVER_QR_NL

            proguardFile 'proguard-rules.pro'
        }
        releaseKozenV2 {
            signingConfig signingConfigs.dev

            buildConfigField "int", "VERSION_CODE", VERSION_CODE
            buildConfigField "String", "VERSION_NAME", VERSION_NAME
            buildConfigField "boolean", "debugLog", "false"
            buildConfigField "String", "typeBuild", "\"release\""
            buildConfigField "String", "TYPE_INTEGRATION", "\"MPOS\""
            buildConfigField "String", "URL_MPOS", URL_MPOS
            buildConfigField "String", "URL_MA_BASE", URL_MA_BASE
            buildConfigField "String", "URL_SERVER_QR_NL", URL_SERVER_QR_NL

            proguardFile 'proguard-rules.pro'
        }
        releasePartner {

            buildConfigField "int", "VERSION_CODE", VERSION_CODE
            buildConfigField "String", "VERSION_NAME", VERSION_NAME
//            buildConfigField "boolean", "debugLog", "false"
            buildConfigField "boolean", "debugLog", "true"
            buildConfigField "String", "typeBuild", "\"release\""
            buildConfigField "String", "TYPE_INTEGRATION", "\"PARTNER\""
            buildConfigField "String", "URL_MPOS", URL_MPOS
            buildConfigField "String", "URL_MA_BASE", URL_MA_BASE

            proguardFile 'proguard-rules.pro'
        }
        releaseMpos3rd {

            buildConfigField "int", "VERSION_CODE", VERSION_CODE
            buildConfigField "String", "VERSION_NAME", VERSION_NAME
            buildConfigField "boolean", "debugLog", "false"
//            buildConfigField "boolean", "debugLog", "true"
            buildConfigField "String", "typeBuild", "\"release\""
            buildConfigField "String", "TYPE_INTEGRATION", "\"MPOS_3RD\""
            buildConfigField "String", "URL_MPOS", URL_MPOS
            buildConfigField "String", "URL_MA_BASE", URL_MA_BASE

            proguardFile 'proguard-rules.pro'
        }

    }
    buildFeatures{
        viewBinding true
//        dataBinding = true
    }
    compileOptions {
        sourceCompatibility = '1.8'
        targetCompatibility = '1.8'
    }

//    sourceSets{
////---- name       -+-export to-+-         desc
////+ taptophone:    |    ttp    | only code for taptophone (not contains lib/process/handler other reader)
////+ coreMposSdk:   |           | contains mpos sdk, SP01, PR02
////+ release:       |   mpos    | contains AR01, PR01
////+ releasePartner:|  partner  | not contains process/handler  AR01, PR01
//
//        def pathCoreSDK = 'src/coreMposSdk/java'
//        def pathCoreDatec = 'src/release/java'
//
//        def pathRes = 'src/coreMposSdk/res'
//        certify{
////            setRoot 'src/release'
//            java.srcDirs = [pathCoreDatec, pathCoreSDK]
//            res.srcDirs = [pathRes]
//        }
//        debug{
////            setRoot 'src/release'
//            java.srcDirs = [pathCoreDatec, pathCoreSDK]
//            res.srcDirs = [pathRes]
//        }
//        debugKozen{
////            setRoot 'src/release'
//            java.srcDirs = [pathCoreDatec, pathCoreSDK]
//            res.srcDirs = [pathRes]
//        }
//        live{
////            setRoot 'src/release'
//            java.srcDirs = [pathCoreDatec, pathCoreSDK]
//            res.srcDirs = [pathRes]
//        }
////        awsTest{
//////            setRoot 'src/release'
////            java.srcDirs = [pathCoreDatec, pathCoreSDK]
////            res.srcDirs = [pathRes]
////        }
//        release{
////            setRoot 'src/release'
//            java.srcDirs = [pathCoreDatec, pathCoreSDK]
//            res.srcDirs = [pathRes]
//        }
//        releaseKozenV2{
////            setRoot 'src/release'
//            java.srcDirs = [pathCoreDatec, pathCoreSDK]
//            res.srcDirs = [pathRes]
//        }
//        releasePartner{
//            java.srcDirs = ['src/releasePartner/java', pathCoreSDK]
//        }
////        taptophoneDebug{
////            setRoot 'src/taptophone'
////        }
////        taptophone{
////            setRoot 'src/taptophone'
////        }
//    }
//
//    variantFilter{
//        variant ->
//            def names = variant.flavors*.name
//            def dimension = variant.flavors*.dimension
//            println("--->"+names+" -->"+dimension)
//            if (names.contains("partner")
////                    && names.contains("Custom")
////                    (names.contains("Debug") || names.contains("Custom"))
//            ) {
//                setIgnore(true)
//            }
//    }

    // edit out put file name aar
//    libraryVariants.all { variant ->
//        variant.outputs.all { vOutput ->
//            def fileName = "mpos-sdk.aar"
////            def fileName = "core_mpos-${version}.aar"
//            outputFileName = fileName
//        }
//    }
    android.libraryVariants.all { variant ->
        variant.outputs.all { vOutput ->
            def buildType = variant.buildType.name
            def fileName = "core_mpos-${version}"

            if (buildType == "releasePartner") {
                fileName = "mpos-sdk.aar";
            }
            if (buildType == "debugDisableLog") {
                fileName = "core_mpos-1.0.1-debug.aar";
            }
            else if (buildType == "releaseKozenV2") {
                fileName = "mpos-sdk-kozen-v2.aar";
            } else {
                def type = "";
                if (buildType != "release") {
                    type = "-debug"
                }
                fileName += "-${buildType}.aar"
            }

            outputFileName = fileName
        }
    }
    // ...
    testOptions {
        unitTests.returnDefaultValues = true
    }
}

dependencies {
    compileOnly files('extern_libs/com.pos.sdk_release.jar')
    implementation files('extern_libs/nvram_ard112.jar')
//    implementation files('extern_libs/nvram-ard10.jar')


    def pathLibPR02 = 'libs/libs-pr02/dspread_pos_release_sdk_4.1.3.jar'
//    def pathLibPR02 = 'libs/libs-pr02/dspread_pos_sdk_3.6.0.jar'
//    def pathLibPR02 = 'libs/libs-pr02/dspread_android_pos_sdk_3.1.7.jar'
    releaseImplementation files( pathLibPR02)
    certifyImplementation files(pathLibPR02)
    certifyStagingImplementation files(pathLibPR02)
    stagingImplementation files(pathLibPR02)
    stagingPartnerImplementation files(pathLibPR02)
    stagingMpos3rdImplementation files(pathLibPR02)
    debugImplementation files(pathLibPR02)
    debugDisableLogImplementation files(pathLibPR02)
    liveImplementation files(pathLibPR02)
    debugKozenImplementation files(pathLibPR02)
    releaseKozenV2Implementation files(pathLibPR02)
    releasePartnerImplementation files(pathLibPR02)

    // sp02
    implementation files('libs/libs-sp02/libCustomAPI-1.6.1.jar')

//    implementation files('libs/tysmartposlib_R3649_20210319155350.jar')   // fix not show pan
    def pathLibSP01 = 'libs/libs-sp01/tysmartposlib_R4778_20220609132610.jar'
    releaseImplementation files( pathLibSP01)
    certifyImplementation files(pathLibSP01)
    certifyStagingImplementation files(pathLibSP01)
    stagingImplementation files(pathLibSP01)
    stagingPartnerImplementation files(pathLibSP01)
    stagingMpos3rdImplementation files(pathLibSP01)
    debugImplementation files(pathLibSP01)
    debugDisableLogImplementation files(pathLibSP01)
    liveImplementation files(pathLibSP01)
    debugKozenImplementation files(pathLibSP01)
    releaseKozenV2Implementation files(pathLibSP01)
    releasePartnerImplementation files(pathLibSP01)

    def pathLibPax = 'libs/libs-pax'
    implementation fileTree(include: ['*.jar'], dir: pathLibPax)


    implementation files('libs/android-async-http-1.4.11.jar')
    implementation files('libs/httpclient-4.5.8.jar')
    implementation files('libs/ber-tlv-1.0-8.jar')
    implementation files('libs/lib_sticky_mpos.jar')

    implementation files('libs/printsdk_S85_new.jar')
    implementation files('libs/printer_xp-p210-sdk.jar')
    implementation files('libs/universal-image-loader-1.9.5.jar')


    // --> start: dependencies for core
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.annotation:annotation:1.5.0'
    implementation 'androidx.lifecycle:lifecycle-extensions:2.2.0'
    implementation 'androidx.security:security-crypto:1.0.0'

    implementation 'com.google.android.material:material:1.6.0'
    implementation 'com.google.android.gms:play-services-location:21.0.0'
    implementation 'com.google.code.gson:gson:2.8.9@jar'

    implementation 'com.google.crypto.tink:tink-android:1.8.0'

    // --> start: animation for smart pos
    def lottieVersion = "4.2.0"
    implementation "com.airbnb.android:lottie:$lottieVersion"

    // Http
//    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
//    implementation 'com.squareup.retrofit2:converter-gson:2.5.0'
//    implementation 'com.squareup.retrofit2:converter-scalars:2.3.0'
//
//    implementation 'com.squareup.okhttp3:okhttp-urlconnection:3.12.12'
//    implementation "com.squareup.okhttp3:logging-interceptor:4.0.1"
//
    implementation 'androidx.multidex:multidex:2.0.1'


    testImplementation 'junit:junit:4.12'
    testImplementation 'org.json:json:20140107'


    implementation 'androidx.databinding:viewbinding:7.1.2'

}


//task generateSourcesJar(type: Jar) {
//    from android.sourceSets.main.java.srcDirs
//    classifier 'sources'
//}
//
//artifacts {
//    archives generateSourcesJar
//}

