//package com.ufo.gateway_card_promo.config;
//
//
//import com.ufo.gateway_card_promo.utils.RSADecryption;
//import jakarta.servlet.ServletOutputStream;
//import jakarta.servlet.http.HttpServletResponse;
//import jakarta.servlet.http.HttpServletResponseWrapper;
//
//
//import java.io.ByteArrayOutputStream;
//import java.io.IOException;
//import java.io.PrintWriter;
//import java.nio.charset.StandardCharsets;
//
//public class ResponseEncryptionFilter extends HttpServletResponseWrapper {
//    private ByteArrayOutputStream buffer;
//    private PrintWriter writer;
//
//    public ResponseEncryptionFilter(HttpServletResponse response) {
//        super(response);
//        this.buffer = new ByteArrayOutputStream();
//        this.writer = new PrintWriter(buffer);
//    }
//
//    @Override
//    public PrintWriter getWriter() throws IOException {
//        return writer;
//    }
//
//    public void encryptAndWrite() throws Exception {
//        writer.flush();
//        String originalContent = new String(buffer.toByteArray(), StandardCharsets.UTF_8);
//        String encryptedContent = RSADecryption.encryptData(originalContent);
//        getResponse().setContentLength(encryptedContent.length());
//
//        ServletOutputStream out = getResponse().getOutputStream();
//        out.write(encryptedContent.getBytes(StandardCharsets.UTF_8));
//        out.flush();
//    }
//}
