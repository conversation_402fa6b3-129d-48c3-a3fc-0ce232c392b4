package com.ufo.promotion.document;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Document(collection = "banner")
public class BannerData {
    @Id
    String id;
    String urlBanner;
    String urlLink;
}
