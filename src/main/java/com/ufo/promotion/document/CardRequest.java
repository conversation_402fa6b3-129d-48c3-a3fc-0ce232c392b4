package com.ufo.promotion.document;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CardRequest {
    @Id
    private String id;
    private String phone;
    private String email;
    private String note;
    private String bankName;
    private String cardName;
    private boolean status;
}
