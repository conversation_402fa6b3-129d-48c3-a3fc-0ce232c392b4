package com.ufo.promotion.util;

import com.ufo.promotion.exception.AppException;
import com.ufo.promotion.exception.ErrCode;
import com.ufo.promotion.model.McCategory;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class Constant {
    public static final String SESSION_USER = "session_user";

    public enum MerchantCategory {
        SIEU_THI("Siêu thị", "Supermarket"),
        COFFEE("Quán coffee", "Coffee"),
        NHA_HANG("Nhà hàng", "Restaurant"),
        HOTEL("Khách sạn", "Hotel"),
        KHAC("Khác", "Orther");

        private final String name_vi;
        private final String name_en;

        MerchantCategory(String name_vi, String name_en) {
            this.name_vi = name_vi;
            this.name_en = name_en;
        }

        public String getName_vi() {
            return name_vi;
        }

        public String getName_en() {
            return name_en;
        }

        public static List<McCategory> getAllMerchantCategories() {
            return Arrays.stream(MerchantCategory.values())
                    .map(e -> new McCategory(e.name(), e.getName_vi(), e.getName_en()))
                    .collect(Collectors.toList());
        }

    }
}
