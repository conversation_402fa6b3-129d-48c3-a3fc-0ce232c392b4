package com.ufo.promotion.util;

import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import org.bson.types.ObjectId;
import java.io.IOException;
public class ObjectIdTypeAdapter extends TypeA<PERSON>pter<ObjectId> {
    @Override
    public void write(JsonWriter out, ObjectId value) throws IOException {
        if (value == null) {
            out.nullValue();
        } else {
            out.value(value.toString());
        }
    }

    @Override
    public ObjectId read(JsonReader in) throws IOException {
        String idString = in.nextString();
        return new ObjectId(idString);
    }
}