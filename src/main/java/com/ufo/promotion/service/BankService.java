package com.ufo.promotion.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ufo.promotion.document.BankCard;
import com.ufo.promotion.document.CardRequest;
import com.ufo.promotion.model.BankCardItem;
import com.ufo.promotion.exception.AppException;
import com.ufo.promotion.exception.ErrCode;
import com.ufo.promotion.model.BankInfo;
//import com.ufo.promotion.repository.BankRepository;
import com.ufo.promotion.repository.BankRepository;
import com.ufo.promotion.repository.CardRequestRepository;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;


@Service
public class BankService {
    private final Logger log = LogManager.getLogger(this.getClass());

    private static List<BankInfo> bankList;

    @Autowired
    private BankRepository bankRepository;

    @Autowired
    private CardRequestRepository cardRequestRepository;

    static {
        loadBankData(); // Gọi hàm này ngay khi class được load
    }

    private static void loadBankData() {
        try {
            ClassPathResource resource = new ClassPathResource("data_bank.txt");
            ObjectMapper objectMapper = new ObjectMapper();
            bankList = objectMapper.readValue(resource.getInputStream(), new TypeReference<List<BankInfo>>() {});

            if (bankList == null || bankList.isEmpty()) {
                throw new RuntimeException("Dữ liệu ngân hàng trống hoặc không hợp lệ!");
            }
        } catch (Exception e) {
            throw new RuntimeException("Lỗi khi tải danh sách ngân hàng: " + e.getMessage(), e);
        }
    }

    public static List<BankInfo> getAllBanks() {
        return bankList;
    }

    public static String getBankNameByCode(String bankCode) {
        if (bankCode == null || bankCode.isEmpty()) {
            throw new AppException(ErrCode.REQUEST_WRONG);
        }

        Optional<BankInfo> bank = bankList.stream()
                .filter(b -> b.getBankCode().equalsIgnoreCase(bankCode))
                .findFirst();

        return bank.map(BankInfo::getBankName)
                .orElseThrow(() -> new AppException(ErrCode.BANK_CODE_NOT_FOUND));
    }

    public static String getShortBankNameByCode(String bankCode) {
        if (bankCode == null || bankCode.isEmpty()) {
            throw new AppException(ErrCode.REQUEST_WRONG);
        }

        Optional<BankInfo> bank = bankList.stream()
                .filter(b -> b.getBankCode().equalsIgnoreCase(bankCode))
                .findFirst();

        return bank.map(BankInfo::getShortName)
                .orElseThrow(() -> new AppException(ErrCode.BANK_CODE_NOT_FOUND));
    }

    public static String getIconBankByCode(String bankCode) {
        if (bankCode == null || bankCode.isEmpty()) {
            throw new AppException(ErrCode.REQUEST_WRONG);
        }

        Optional<BankInfo> bank = bankList.stream()
                .filter(b -> b.getBankCode().equalsIgnoreCase(bankCode))
                .findFirst();

        return bank.map(BankInfo::getIconUrl)
                .orElseThrow(() -> new AppException(ErrCode.BANK_CODE_NOT_FOUND));
    }

    public List<BankCard> getAllBankCard() {
        return bankRepository.findAll();
    }

    // 2. Tạo một ngân hàng cùng danh sách thẻ
    public BankCard createBankCard(BankCard bankCard) {
        bankCard.setBankName(getShortBankNameByCode(bankCard.getBankCode()));
        return bankRepository.save(bankCard);
    }

    // 3. Thêm một thẻ vào ngân hàng theo bankCode
    public BankCard addCardToBank(String bankCode, BankCardItem newCard) {
        BankCard bankCard = bankRepository.findById(bankCode).orElse(null);
        if (bankCard == null) {
            throw new RuntimeException("Ngân hàng không tồn tại!");
        }
        bankCard.getCards().add(newCard);
        return bankRepository.save(bankCard);
    }

    // 4. Xóa một thẻ của ngân hàng theo bankCode và cardName
    public BankCard removeCardFromBank(String bankCode, String cardName) {
        BankCard bankCard = bankRepository.findById(bankCode).orElse(null);
        if (bankCard == null) {
            throw new RuntimeException("Ngân hàng không tồn tại!");
        }
        bankCard.getCards().removeIf(card -> card.getCardName().equalsIgnoreCase(cardName));
        return bankRepository.save(bankCard);
    }

    /**
     * Tạo yêu cầu mở thẻ mới
     */
    public CardRequest createRequestOpenCard(CardRequest request) {
        request.setStatus(false); // Mặc định trạng thái chưa xử lý
        return cardRequestRepository.save(request);
    }

    /**
     * Lấy danh sách tất cả các yêu cầu mở thẻ
     */
    public List<CardRequest> getAllRequests() {
        return cardRequestRepository.findAll();
    }

    /**
     * Cập nhật trạng thái của yêu cầu mở thẻ
     */
    public CardRequest updateRequestStatus(String id, boolean newStatus) {
        Optional<CardRequest> optionalRequest = cardRequestRepository.findById(id);
        if (optionalRequest.isPresent()) {
            CardRequest request = optionalRequest.get();
            request.setStatus(newStatus);
            return cardRequestRepository.save(request);
        } else {
            throw new RuntimeException("Không tìm thấy yêu cầu mở thẻ với ID: " + id);
        }
    }
}
