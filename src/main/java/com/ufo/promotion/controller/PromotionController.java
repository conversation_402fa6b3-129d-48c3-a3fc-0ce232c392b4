package com.ufo.promotion.controller;

import com.ufo.promotion.document.Promotion;
import com.ufo.promotion.exception.AppException;
import com.ufo.promotion.exception.ErrCode;
import com.ufo.promotion.model.*;
import com.ufo.promotion.service.BannerService;
import com.ufo.promotion.service.PromotionService;
import com.ufo.promotion.util.MyGson;
import com.ufo.promotion.util.RSADecryption;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/v1")
public class PromotionController {
    private final Logger log = LogManager.getLogger(this.getClass());

    @Value("${app.env:prod}")
    private String appEnv;

    @Value("${app.encryption.enabled:true}")
    private boolean encryptionEnabled;

    @Autowired
    PromotionService promotionService;

    @Autowired
    BannerService bannerService;

    private <T> String processResponse(T data) {
        ResponseModel<T> responseModel = new ResponseModel<>();
        responseModel.setResult(data);
        String jsonResponse = MyGson.getGson().toJson(responseModel);

        // Nếu là môi trường production và encryption được bật thì mã hóa
        if ("prod".equalsIgnoreCase(appEnv) && encryptionEnabled) {
            log.info("Environment: PROD - Encrypting response");
            return RSADecryption.doAESEncrypt(jsonResponse);
        } else {
            log.info("Environment: DEV - Returning clear response");
            return jsonResponse;
        }
    }

    /*
    * Get promo and banner, config, ... show home screen
    * */
    @PostMapping("/get_promo")
    public String getPromo(@RequestBody PromoSearchRequest request) {
        PromoSearchResponse promotions = promotionService.findPromotions(request);
        return processResponse(promotions);
    }

    @PostMapping("/get-detail")
    public String getDetailPromotion(@RequestBody PromoGetDetailReq req) {
        Promotion promotions = promotionService.getDetailPromo(req.getPromoId());
        return processResponse(promotions);
    }

    @PostMapping("/search")
    public String searchPromotion(@RequestBody PromoSearchRequest request) {
        PromoSearchResponse promotions = promotionService.findPromotions(request);
        return processResponse(promotions);
    }

    @PostMapping("/create-new-promo")
    public String createPromotion(@RequestBody CreatePromoReq request) {
        log.info("Received create promotion request: {}", MyGson.getGson().toJson(request));

        try {
            if (invalidData(request)) {
                log.error("Invalid data in create promotion request");
                throw new AppException(ErrCode.REQUEST_WRONG);
            }

            log.info("Creating promotion with title: {}, bankCode: {}", request.getTitle(), request.getBankCode());
            log.info("Active: {}, SendNotification: {}", request.isActive(), request.isSendNotification());

            Promotion promotions = promotionService.createPromotion(request);
            CreatePromoResponse createPromoResponse = new CreatePromoResponse();
            createPromoResponse.setPromoId(promotions.getId());

            log.info("Promotion created successfully with ID: {}", promotions.getId());
            return processResponse(createPromoResponse);
        } catch (Exception e) {
            log.error("Error creating promotion: {}", e.getMessage(), e);
            throw e;
        }
    }

    @PostMapping("/nearby-promo")
    public String getPromoNearbyMerchants(@RequestBody PromoNearMerchantReq merchant) {
        List<PromoNearMerchantResponse> merchantList = promotionService.findPromoNearbyMerchants(merchant);
        return processResponse(merchantList);
    }

    private boolean invalidData(CreatePromoReq req) {
        if (req.getBankCode() == null || req.getBankCode().isEmpty()
                || req.getTitle() == null || req.getTitle().isEmpty()
                || req.getScheme() == null || req.getScheme().isEmpty()
                || req.getCategory() == null || req.getCategory().isEmpty()) {
            return true;
        }
        return false;
    }
}
