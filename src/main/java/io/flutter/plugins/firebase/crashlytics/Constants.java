package io.flutter.plugins.firebase.crashlytics;

public class Constants {
  public static final String EXCEPTION = "exception";
  public static final String REASON = "reason";
  public static final String INFORMATION = "information";
  public static final String STACK_TRACE_ELEMENTS = "stackTraceElements";
  public static final String FLUTTER_ERROR_EXCEPTION = "flutter_error_exception";
  public static final String FLUTTER_ERROR_REASON = "flutter_error_reason";
  public static final String MESSAGE = "message";
  public static final String ENABLED = "enabled";
  public static final String IDENTIFIER = "identifier";
  public static final String KEY = "key";
  public static final String VALUE = "value";
  public static final String FILE = "file";
  public static final String LINE = "line";
  public static final String CLASS = "class";
  public static final String METHOD = "method";
  public static final String DID_CRASH_ON_PREVIOUS_EXECUTION = "didCrashOnPreviousExecution";
  public static final String UNSENT_REPORTS = "unsentReports";
  public static final String IS_CRASHLYTICS_COLLECTION_ENABLED = "isCrashlyticsCollectionEnabled";
  public static final String FATAL = "fatal";
  public static final String BUILD_ID = "buildId";
  public static final String TIMESTAMP = "timestamp";
  public static final String FIREBASE_APPLICATION_EXCEPTION = "_ae";
  public static final String CRASH_EVENT_KEY = "com.firebase.crashlytics.flutter.fatal";
}
