import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import '{{projectName}}_platform_interface.dart';

/// An implementation of [{{pluginDartClass}}Platform] that uses method channels.
class MethodChannel{{pluginDartClass}} extends {{pluginDartClass}}Platform {
  /// The method channel used to interact with the native platform.
  @visibleForTesting
  final methodChannel = const MethodChannel('{{projectName}}');

  @override
  Future<String?> getPlatformVersion() async {
    final version = await methodChannel.invokeMethod<String>('getPlatformVersion');
    return version;
  }
}
