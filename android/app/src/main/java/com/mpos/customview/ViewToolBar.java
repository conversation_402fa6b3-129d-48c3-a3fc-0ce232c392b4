package com.mpos.customview;

import android.app.Activity;
import android.content.Context;
import android.text.Html;
import android.text.TextUtils;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.appcompat.widget.Toolbar;

import butterknife.BindView;
import butterknife.ButterKnife;
import vn.mpos.R;

public class ViewToolBar {
    String tag = this.getClass().getSimpleName();

    Context context;
    View viewRoot;

    @BindView(R.id.ll_toolbar)
    public Toolbar toolbar;
    @BindView(R.id.toolbar_title)
    protected TextView tvTitleBar;
    @BindView(R.id.imv_search)
    protected ImageView imvSearch;

    public ViewToolBar(Context context, View viewRoot) {
        this.context = context;
        this.viewRoot = viewRoot;

        ButterKnife.bind(this, viewRoot);
    }

    public void setBackgroundColor(int color) {
        toolbar.setBackgroundColor(context.getResources().getColor(color));
    }

    public void showButtonBack(boolean show) {
//        Utils.LOGD(tag, "---showActionbar: show=" + show);
        if (show) {
            toolbar.setNavigationIcon(R.drawable.ic_back);    // support in 24.0.0
//            toolbar.setNavigationIcon(R.drawable.abc_ic_ab_back_material);    // support in 24.0.0
//			toolbar.setNavigationIcon(R.drawable.abc_ic_ab_back_mtrl_am_alpha);
            toolbar.setNavigationOnClickListener(v -> {
//                Utils.LOGD("ViewToolBar", "onclick navigation---------");
                hideKeyboard((Activity) context);
                ((Activity) context).onBackPressed();
            });
        } else {
            toolbar.setNavigationIcon(null);
        }
    }

    public void showButtonCancel(boolean show, View.OnClickListener listener) {
//        Utils.LOGD(tag, "---showActionbar: show=" + show);
        if (show) {
            toolbar.setNavigationIcon(R.drawable.abc_ic_clear_material);
//			toolbar.setNavigationIcon(R.drawable.abc_ic_clear_mtrl_alpha);
            toolbar.setNavigationOnClickListener(listener);
        }
    }

    public void showButtonBack(boolean show, View.OnClickListener listener) {
//        Utils.LOGD(tag, "---showActionbar: show=" + show);
        if (show) {
            toolbar.setNavigationIcon(R.drawable.ic_back);
//			toolbar.setNavigationIcon(R.drawable.abc_ic_clear_mtrl_alpha);
            toolbar.setNavigationOnClickListener(listener);
        }
    }

    public void showTextTitle(String title) {
        if (toolbar != null) {
//            Utils.LOGD(tag, "title setToolBar:" + title);
            if (TextUtils.isEmpty(title)) {
                tvTitleBar.setVisibility(View.GONE);
            } else {
                tvTitleBar.setVisibility(View.VISIBLE);
                tvTitleBar.setText(title);
            }
        }
    }

    public void showTextTitleFormatHtml(String title) {
        if (toolbar != null) {
//            Utils.LOGD(tag, "title setToolBar:" + title);
            if (TextUtils.isEmpty(title)) {
                tvTitleBar.setVisibility(View.GONE);
            } else {
                tvTitleBar.setVisibility(View.VISIBLE);
                tvTitleBar.setText(Html.fromHtml(title));
            }
        }
    }

    public void setVisible(int i) {
        toolbar.setVisibility(i);
    }

    public void setBgColorToolbar(int color) {
        if (toolbar != null) {
            toolbar.setBackgroundColor(context.getResources().getColor(color));
        }
    }

    public void setOnclickSearch(View.OnClickListener onclickSearch) {
        imvSearch.setVisibility(View.VISIBLE);
        imvSearch.setOnClickListener(onclickSearch);
    }

    public void showIconSearch(boolean show) {
        imvSearch.setVisibility(show ? View.VISIBLE : View.GONE);
    }

    public void showHideToolbar(boolean show) {
        toolbar.setVisibility(show ? View.VISIBLE : View.GONE);
    }

    public static void hideKeyboard(Activity activity) {
        InputMethodManager imm = (InputMethodManager) activity.getSystemService(Activity.INPUT_METHOD_SERVICE);
        //Find the currently focused view, so we can grab the correct window token from it.
        View view = activity.getCurrentFocus();
        //If no view currently has focus, create a new one, just so we can grab a window token from it
        if (view == null) {
            view = new View(activity);
        }
        imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
    }
}
