package com.mpos.customview;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.TextView;

import com.mpos.sdk.util.Utils;
import com.mpos.utils.MyUtils;

import vn.mpos.R;

/**
 * Created by noe on 6/2/17
 */

public class MposDialogInstallmentGuide extends Dialog {

    private static final String TAG = "MposDialogInstallmentGu";

    private boolean setContentCustom = false;
    private final Context context;
    private String urlGuideInstallmentOfBank;

    /**
     * constructor
     *
     * @param context
     * @param theme
     */
    public MposDialogInstallmentGuide(Context context, int theme) {
        super(context, theme);
        this.context = context;
    }

    /**
     * constructor
     *
     * @param context
     * @param cancelable
     * @param cancelListener
     */
    protected MposDialogInstallmentGuide(Context context, boolean cancelable, OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
        this.context = context;
    }

    /**
     * constructor
     *
     * @param context
     */
    public MposDialogInstallmentGuide(Context context, String urlGuideInstallmentOfBank) {
        super(context);
        this.context = context;
        this.urlGuideInstallmentOfBank = urlGuideInstallmentOfBank;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentCustom();
    }

    private void setContentCustom() {
        if (setContentCustom)
            return;
        setContentCustom = true;
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_layout_general_installment_guide);
        getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        TextView txtTitleGuideInstallment = findViewById(R.id.tv_title_dialog_guide_installment);

        Button buttonViaPhone = findViewById(R.id.btn_via_phone);
        buttonViaPhone.setText(MyUtils.setTextHtml(context.getString(R.string.dialog_error_via_contact)));
        Button buttonViaEmail = findViewById(R.id.btn_understand);
        buttonViaEmail.setText(MyUtils.setTextHtml(context.getString(R.string.dialog_installment_guide_understand)));

        ProgressBar progressBarInstallment = findViewById(R.id.progress_bar_installment_guide);

        WebView webViewGuideInstallment = findViewById(R.id.webviewGuideInstallment);
//        webViewGuideInstallment.setBackgroundColor(Color.parseColor("#faf5de"));
        loadGuideInstallmentPage(webViewGuideInstallment, urlGuideInstallmentOfBank);
    }

    /**
     * listener when user click button CONTACT VIA PHONE
     *
     * @param onClickListener
     */
    public void setOnClickListenerDialogViaPhone(View.OnClickListener onClickListener) {
        setContentCustom();
        findViewById(R.id.btn_via_phone).setOnClickListener(onClickListener);
    }

    /**
     * listener when user click button UNDERSTAND
     *
     * @param onClickListener
     */
    public void setOnClickListenerDialogUnderstand(View.OnClickListener onClickListener) {
        setContentCustom();
        findViewById(R.id.btn_understand).setOnClickListener(onClickListener);
    }


    /**
     * set title dialog guide installment
     *
     * @param title
     */
    public void setTitleDialogGuideInstallment(String title) {
        setContentCustom();
        ((TextView) findViewById(R.id.tv_title_dialog_guide_installment)).setText(title);
    }

    public String getNumberHotline() {
        setContentCustom();
        String contactStr = ((Button) findViewById(R.id.btn_via_phone)).getText().toString().trim();
        return contactStr.substring(contactStr.indexOf("1900"));
    }

    private void loadGuideInstallmentPage(WebView webViewGuide, String url) {
        Utils.LOGD(TAG, "loadGuideInstallmentPage: url="+url);
        settingWebViewWeChoice(webViewGuide);
        webViewGuide.loadUrl(url);
    }

    @SuppressLint("SetJavaScriptEnabled")
    private void settingWebViewWeChoice(WebView webViewGuide) {
        // enable javascript
        webViewGuide.getSettings().setJavaScriptEnabled(true);
//        webViewGuide.getSettings().setUserAgentString(USER_AGENT);
        // set maximum render
//        webViewGuide.getSettings().setRenderPriority(WebSettings.RenderPriority.HIGH);

        webViewGuide.getSettings().setTextZoom(webViewGuide.getSettings().getTextZoom() - 10);
        webViewGuide.setWebViewClient(new DetailPageClientGuide());
        webViewGuide.getSettings().setCacheMode(WebSettings.LOAD_NO_CACHE);

        webViewGuide.setLayerType(View.LAYER_TYPE_HARDWARE, null);
        webViewGuide.getSettings().setDefaultFontSize(10);
    }

    private class DetailPageClientGuide extends WebViewClient {
        private boolean loadingFinished = false;
        private boolean redirect = false;

        String TAG = "DetailPageClientGuide";

        @Override
        public boolean shouldOverrideUrlLoading(WebView view, String url) {
//        public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
            Utils.LOGD(TAG,"Processing webview url click...loadingFinished="+loadingFinished+" url="+url);
            if (loadingFinished) {
                Intent intent = new Intent();
                intent.setAction(Intent.ACTION_VIEW);
                intent.setData(Uri.parse(url));
                getContext().startActivity(intent);
                return true;
            } else {
                redirect = true;
            }

            loadingFinished = false;
            view.loadUrl(url);
            return true;
        }

        @Override
        public void onPageStarted(WebView view, String url, Bitmap favicon) {
            Utils.LOGD(TAG, "onPageStarted() called with: url = [" + url + "], favicon = [" + favicon + "]");
            findViewById(R.id.progress_bar_installment_guide).setVisibility(View.VISIBLE);
            super.onPageStarted(view, url, favicon);
            loadingFinished = false;
        }

        @Override
        public void onPageFinished(WebView view, String url) {
            Utils.LOGD(TAG, "onPageFinished() called with: url = [" + url + "] redirect=" + redirect);
            //set font color inside webview
//            view.loadUrl(
//                    "javascript:document.body.style.setProperty(\"color\", \"#eb9522\");"
//            );
            if (!redirect) {
                loadingFinished = true;
            }

            if (loadingFinished && !redirect) {
                // HIDE LOADING IT HAS FINISHED
//                llLoading.setVisibility(View.GONE);
                findViewById(R.id.progress_bar_installment_guide).setVisibility(View.GONE);
            } else {
                redirect = false;
            }
        }

        @Override
        public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
            super.onReceivedError(view, request, error);
            Utils.LOGD(TAG, "onReceivedError: " + error.toString());
        }
    }
}

