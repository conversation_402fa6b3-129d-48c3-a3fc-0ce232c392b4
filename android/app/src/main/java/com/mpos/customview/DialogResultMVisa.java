package com.mpos.customview;

public class DialogResultMVisa{}
/*
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.mpos.screen.BaseDialogFragment;

import vn.mpos.R;

*//**
 * Created by noe on 10/20/17
 *//*

public class DialogResultMVisa  extends BaseDialogFragment {
    public static final String TAG = DialogResultMVisa.class.getSimpleName();

    private String soTienThanhToan    = "";
    private String maGiaoDich         = "";
    private String thoiGianTao        = "";
    private String thanhToanBang      = "";
    private String soThe              = "";
    private String emailKhachHang     = "";
    private String trangThaiThanhToan = "";

    public static DialogResultMVisa newInstance(String trangThaiThanhToan, String soTienThanhToan, String maGiaoDich, String thoiGianTao, String thanhToanBang, String soThe, String emailKhachHang) {
        DialogResultMVisa dialogResultMVisa = new DialogResultMVisa();
        Bundle bundle = new Bundle();

        bundle.putString("trangThaiThanhToan",  trangThaiThanhToan);
        bundle.putString("soTienThanhToan", soTienThanhToan);
        bundle.putString("maGiaoDich",      maGiaoDich);
        bundle.putString("thoiGianTao",     thoiGianTao);
        bundle.putString("thanhToanBang",   thanhToanBang);
        bundle.putString("soThe",           soThe);
        bundle.putString("emailKhachHang",  emailKhachHang);

        dialogResultMVisa.setArguments(bundle);
        return dialogResultMVisa;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(STYLE_NO_FRAME, android.R.style.Theme_Translucent);
    }

//    @Override
//    public Dialog onCreateDialog(Bundle savedInstanceState) {
//        Dialog dialog = super.onCreateDialog(savedInstanceState);
//        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
//        dialog.getWindow().getAttributes().windowAnimations = R.style.updownDialog;
//        dialog.getWindow().setLayout(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT);
//        return dialog;
//    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        Bundle bundle = getArguments();
        if (bundle != null) {
            soTienThanhToan    = bundle.getString("soTienThanhToan");
            maGiaoDich         = bundle.getString("maGiaoDich");
            thoiGianTao        = bundle.getString("thoiGianTao");
            thanhToanBang      = bundle.getString("thanhToanBang");
            soThe              = bundle.getString("soThe");
            emailKhachHang     = bundle.getString("emailKhachHang");
            trangThaiThanhToan = bundle.getString("trangThaiThanhToan");
        }

        View view = inflater.inflate(R.layout.dialog_result_mvisa, container);
        initView(view);

        return view;
    }

    private void initView(View view) {
        TextView tvSoTien       = (TextView) view.findViewById(R.id.tv_mvisa_money);
        TextView tvMaGiaoDich   = (TextView) view.findViewById(R.id.tv_mvisa_magiaodich);
        TextView tvThoiGian     = (TextView) view.findViewById(R.id.tv_mvisa_time_create);

        TextView tvThanhToanQua = (TextView) view.findViewById(R.id.tv_mvisa_payment_baseon);
        TextView tvSoThe        = (TextView) view.findViewById(R.id.tv_mvisa_number_card);

        TextView tvEmail        = (TextView) view.findViewById(R.id.tv_mvisa_email);

        ImageView imgStatusPayment = (ImageView) view.findViewById(R.id.imgStatusPayment);
        TextView tvStatusPayment   = (TextView) view.findViewById(R.id.tvStatusPayment);

        if (trangThaiThanhToan.equalsIgnoreCase("APPROVED")) {
            imgStatusPayment.setImageResource(R.drawable.ic_success_big);
            tvStatusPayment.setText(getString(R.string.dialog_mvisa_success));
            tvStatusPayment.setTextColor(ContextCompat.getColor(getActivity(), R.color.tv_color_pay_success));
        } else {
            imgStatusPayment.setImageResource(R.drawable.ic_failed_mvisa);
            tvStatusPayment.setText(getString(R.string.dialog_mvisa_not_success));
            tvStatusPayment.setTextColor(ContextCompat.getColor(getActivity(), R.color.tv_bg_red_want_money));
        }

        tvSoTien.setText(TextUtils.isEmpty(soTienThanhToan)    ? "" : soTienThanhToan);
        tvMaGiaoDich.setText(TextUtils.isEmpty(maGiaoDich)     ? "" : maGiaoDich);
        tvThoiGian.setText(TextUtils.isEmpty(thoiGianTao)      ? "" : thoiGianTao);
        tvThanhToanQua.setText(TextUtils.isEmpty(thanhToanBang)? "" : thanhToanBang);
        tvSoThe.setText(TextUtils.isEmpty(soThe)               ? "" : soThe);
        tvEmail.setText("n/a");
//      tvEmail.setText(TextUtils.isEmpty(emailKhachHang)      ? "" : emailKhachHang);

        view.findViewById(R.id.tvGotoMain).setOnClickListener(v -> actionBackToMain());

        view.findViewById(R.id.tvViewDetailHistory).setOnClickListener(v -> {
//                actionViewDetailTransaction();
        });

    }

    public void actionBackToMain(){
//        Intent resultIntent = new Intent(((Activity) context), ActivityLogin.class);
//        resultIntent.setAction(Intent.ACTION_MAIN);
//        resultIntent.addCategory(Intent.CATEGORY_LAUNCHER);
//        ((Activity) context).startActivity(resultIntent);
    }

//    public void actionViewDetailTransaction(){
//        if (!TextUtils.isEmpty(maGiaoDich)) {
//            DetailTransactionMVISA dialog = DetailTransactionMVISA.newInstance(maGiaoDich);
//            dialog.setCancelable(false);
//            dialog.show(getFragmentManager(), DetailTransactionMVISA.class.getName());
//        } else {
//            Utils.LOGD(TAG, "maGiaoDich NULL");
//        }
//    }

    private void dismissDialog() {
        this.dismiss();
    }
}*/
