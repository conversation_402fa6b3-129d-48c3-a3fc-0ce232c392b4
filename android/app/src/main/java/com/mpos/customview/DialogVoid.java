package com.mpos.customview;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.fragment.app.DialogFragment;

import com.mpos.screen.BaseDialogFragment;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.Utils;

import vn.mpos.R;

public class DialogVoid extends BaseDialogFragment {



    OnMyClickListener clickListener;
    private String amount;
    private String pan;
    private String cardType;
    private String approvalCode;
    private String invoiceNumber;
    private String transId;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setColorStatusBar(R.color.red);
        setStyle(STYLE_NO_FRAME, android.R.style.Theme_Translucent);
    }

	
	@Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
            Bundle savedInstanceState) {
		View view = inflater.inflate(R.layout.dialog_void_payment, container);
		setContentCustom(view);
		return view;
	}
	
	private void setContentCustom(View v) {

        ViewToolBar vToolBarDialog = new ViewToolBar(context, v);
        vToolBarDialog.showTextTitle(getString(R.string.NAV_BAR_TITLE_VOID_PAYMENT));
        vToolBarDialog.showButtonBack(true, v12 -> dismiss());

        String amountShow = amount;
        if (!TextUtils.isEmpty(amount) && TextUtils.isDigitsOnly(amount) && amount.length() > 3) {
            amountShow = Utils.zenMoney(amount);
        }
        if (!TextUtils.isEmpty(amountShow)) {
            amountShow += ConstantsPay.CURRENCY_SPACE_PRE;
        }

        ((TextView) v.findViewById(R.id.amount)).setText(amountShow);
        ((TextView) v.findViewById(R.id.card_number)).setText(pan);
        ((TextView) v.findViewById(R.id.card_type)).setText(cardType);
        ((TextView) v.findViewById(R.id.approval_code)).setText(approvalCode);
        ((TextView) v.findViewById(R.id.invoice_number)).setText(invoiceNumber);
        ((TextView) v.findViewById(R.id.transaction_id)).setText(getString(R.string.VOID_TRANS_ID) + ": " + transId);
        v.findViewById(R.id.btn_void_payment).setOnClickListener(v1 -> {
            if (clickListener != null) {
                clickListener.clickVoid(DialogVoid.this);
            }
//                if (transactionStatus == Constants.TRANS_TYPE_PENDING_SIGNATURE) {
//                    reversalPayment(dialog.getDialog());
//                } else {
//                    voidPayment(dialog.getDialog());
//                }
        });
	}
	
	public void initVariable(String amount, String pan, String cardType, String approvalCode, String invoiceNumber,
			String hodlerName) {
		this.amount = amount;
		this.pan = pan;
		this.cardType = cardType;
		this.approvalCode = approvalCode;
		this.invoiceNumber = invoiceNumber;
		this.transId = hodlerName;
	}

	public void setClickListener(OnMyClickListener clickListener) {
		this.clickListener = clickListener;
	}
	
	public interface OnMyClickListener{
        void clickVoid(DialogFragment d);
    }

}
