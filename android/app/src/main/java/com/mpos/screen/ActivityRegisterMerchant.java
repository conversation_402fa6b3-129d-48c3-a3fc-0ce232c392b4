package com.mpos.screen;

import android.os.Bundle;
import android.text.TextUtils;

import androidx.appcompat.app.AppCompatActivity;

import com.mpos.common.DataStoreApp;
import com.mpos.customview.ViewToolBar;
import com.mpos.utils.Config;
import com.mpos.utils.MyUtils;

import vn.mpos.R;

public class ActivityRegisterMerchant extends AppCompatActivity {

//	@BindView(R.id.spn_location)		Spinner spnLocation;
//	@BindView(R.id.spn_user_type)		Spinner spnUserType;
	
	ViewToolBar vToolBar;
//	MyProgressDialog mPrgdl;
//	ToastUtil mToast;
	
	@Override
	protected void onCreate(Bundle savedInstanceState) {
		super.onCreate(savedInstanceState);
		setContentView(R.layout.activity_register);

        MyUtils.setRequestedOrientation(this, DataStoreApp.getInstance().getIsLandscape());

        String title    = getIntent().getStringExtra("title");
        String urlLoad  = getIntent().getStringExtra("urlLoad");
        int typeLoad    = getIntent().getIntExtra("typeLoad", 0);
        title = TextUtils.isEmpty(title)?getString(R.string.register_merchant):title;
        urlLoad = TextUtils.isEmpty(urlLoad)?Config.URL_REGISTER:urlLoad;

//		View viewRoot = (View) findViewById(R.id.container);
		vToolBar = new ViewToolBar(this, findViewById(R.id.container));
		vToolBar.showTextTitle(title);
		vToolBar.showButtonBack(true);
		
		if (savedInstanceState == null) {
			getSupportFragmentManager().beginTransaction().add(R.id.frame, FragmentWebview.newInstance(urlLoad, typeLoad)).commit();
		}
		
		
//		mPrgdl = new MyProgressDialog(this);
		
		
		
//		ButterKnife.bind(this);
//		
//		initView();
	}

	
	/*private void initView() {
		List<String> listLocation = new ArrayList<String>();
        listLocation.add(getString(R.string.select_regiron));
        listLocation.add(getString(R.string.txt_mien_bac));
        listLocation.add(getString(R.string.txt_mien_trung));
        listLocation.add(getString(R.string.txt_mien_nam));
         
		ArrayAdapter<String> adapterLocation = new ArrayAdapter<String>(this,
				android.R.layout.simple_spinner_item, listLocation);

		adapterLocation.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);

		spnLocation.setAdapter(adapterLocation);
		
		List<String> listUserType = new ArrayList<String>();
		listUserType.add(getString(R.string.select_type_account));
		listUserType.add(getString(R.string.txt_acc_company));
		listUserType.add(getString(R.string.txt_acc_individual));
		
		ArrayAdapter<String> adapterUserType = new ArrayAdapter<String>(this,
				android.R.layout.simple_spinner_item, listUserType);
		
		adapterUserType.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
		
		spnUserType.setAdapter(adapterUserType);
	}*/


	/*@OnClick({R.id.register, R.id.cancel})
	public void onClick(View v) {
		switch (v.getId()) {
		case R.id.register:
			runPreRegister();
			break;
		case R.id.cancel:
			finish();
			break;
		default:
			break;
		}
	}
	
	private void runPreRegister() {
		String email = ((EditText) findViewById(R.id.user_id)).getText().toString();
		String pass = ((EditText) findViewById(R.id.user_pin)).getText().toString();
		String pass_confirm = ((EditText) findViewById(R.id.user_pin_confirm)).getText().toString();
		String phone_number = ((EditText) findViewById(R.id.phone_number)).getText().toString();
		
		if (email.equals("") || pass_confirm.equals("") || pass.equals("") || phone_number.equals("")
				|| spnLocation.getSelectedItemPosition()==0 || spnUserType.getSelectedItemPosition()==0) {
			
			mToast.showToast(getString(R.string.ALERT_MISSING_FIELD_TITLE));
		}
		else if (!DataUtils.validateMobile(phone_number)){
			mToast.showToast(getString(R.string.error_wrong_mobile));
		}
		else if (!DataUtils.validateEmail(email)){
			mToast.showToast(getString(R.string.APPLICATION_TRANSACTION_SEND_RECEIPT_EMAIL));
		}
		else if (pass.length()<6){
			mToast.showToast(getString(R.string.error_pass_min_6));
		}
		else if (!pass.equals(pass_confirm)){
			mToast.showToast(getString(R.string.CHANGE_PIN_LBL_PIN_NOT_MATCH));
		}
		else{
			register(email, pass, phone_number);
		}
	}
	
	public void register(String email, String pass, String phone) {

		StringEntity entity = null;
		try {
			String region = "";
			switch (spnLocation.getSelectedItemPosition()) {
			case 1:
				region = "MB";
				break;
			case 2:
				region = "MT";
				break;
			case 3:
				region = "MN";
				break;
			default:
				break;
			}
			String typeAcc = "";
			switch (spnUserType.getSelectedItemPosition()) {
			case 1:
				typeAcc = "COMPANY";
				break;
			case 2:
				typeAcc = "INDIVIDUAL";
				break;
			default:
				break;
			}
			JSONObject jo = new JSONObject();
			jo.put("username", email);
			jo.put("mobile", phone);
			jo.put("password", pass);
			jo.put("region", region);
			jo.put("userType", typeAcc);
//			entity = new StringEntity(jo.toString());
			Utils.LOGD("Data: ", jo.toString());

			jo.put("id", getIntent().getIntExtra("id", 0));

			long time_stamp = System.currentTimeMillis();
			String checksum = Config.md5(Config.MPOS_API + "MPOS.VN~!@#$%^&*()_+}{POIUYTREWQASDFGHJKL:\"?><MNBVCXZSOFTPAY" + time_stamp);

			JSONObject joo = new JSONObject();
			joo.put("serviceName", Config.MPOS_API);
			joo.put("checksum", checksum);
			joo.put("requestTime", time_stamp + "");
			joo.put("jsonParams", jo.toString());
			Utils.LOGD("Data: ", joo.toString());

			entity = new StringEntity(EncodeDecode.doAESEncrypt(joo.toString(), ConstantsPay.KEY_SOFT_PAY));

		} catch (UnsupportedEncodingException e1) {
			e1.printStackTrace();
		} catch (Exception e) {
			Utils.LOGE("Exception", e.getMessage());
		}
		MposRestClient.getInstance(this).post(this, Config.ip_v2_mpos + "/s/adduser", entity, Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
			
			@Override
			public void onStart() {
				mPrgdl.showLoading("");
				super.onStart();
			}
			
			@SuppressWarnings("deprecation")
			@Override
			public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
				mPrgdl.hideLoading();
				try {
					String data = EncodeDecode.doAESDecrypt(new String(arg2), ConstantsPay.KEY_SOFT_PAY);
					Utils.LOGD("Register: ", " success:"+data);
					JSONObject jo = new JSONObject(data);
					JSONObject response = null;//
					if(jo.has("jsonResponse")){
						response = new JSONObject(jo.getString("jsonResponse"));
					}
//					Utils.LOGD("Register: ", response.toString());
					if(jo!=null && jo.has("error")){
						JSONObject jerror = jo.getJSONObject("error");
						String msg = JsonParser.getDataJson(jerror,"message");
						MyDialogShow.showDialogError(msg, ActivityRegisterMerchant.this);
					} 
					else if (response!=null && !response.getBoolean("status")) {
						String msg = JsonParser.getDataJson(response,"message");
						MyDialogShow.showDialogError(TextUtils.isEmpty(msg)?"":getString(R.string.error) + ": " + msg,
								ActivityRegisterMerchant.this);
					} else {
						String msg = JsonParser.getDataJson(response,"message");
						MyDialogShow.showDialogError(TextUtils.isEmpty(msg)?"":msg, ActivityRegisterMerchant.this);
					}
				} catch (JSONException e) {
					Utils.LOGE("Exception", e.getMessage());
					MyDialogShow.showDialogError(getString(R.string.SERVICE_CURRENTLY_NOT_AVAILABLE), ActivityRegisterMerchant.this);
				} catch (Exception e1) {
					e1.printStackTrace();
					MyDialogShow.showDialogErrorFinish(getString(R.string.SERVICE_CURRENTLY_NOT_AVAILABLE), ActivityRegisterMerchant.this);
				}
			}

			@Override
			public void onFinish() {
			}

			@SuppressWarnings("deprecation")
			@Override
			public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
				Utils.LOGE("Register ERROR: ", arg3.getMessage());
				mPrgdl.hideLoading();
				MyDialogShow.showDialogError(getString(R.string.SERVICE_CURRENTLY_NOT_AVAILABLE), ActivityRegisterMerchant.this);
			}
		});
	}*/
	
}
