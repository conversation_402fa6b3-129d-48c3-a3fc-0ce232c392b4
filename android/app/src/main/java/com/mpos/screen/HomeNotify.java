package com.mpos.screen;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Dialog;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.webkit.WebView;
import android.widget.Button;

import com.loopj.android.http.AsyncHttpResponseHandler;
import com.mpos.common.CheckUpdateAppController;
import com.mpos.models.MposVersionUpdate;
import com.mpos.sdk.core.common.MyGson;
import com.mpos.sdk.core.control.GetData;
import com.mpos.sdk.core.network.MposRestClient;
import com.mpos.sdk.util.DevicesUtil;
import com.mpos.sdk.util.Utils;
import com.mpos.utils.Config;
import com.mpos.utils.SharedPrefs;

import org.json.JSONObject;

import java.util.Locale;

import cz.msebera.android.httpclient.Header;
import vn.mpos.R;

public abstract class HomeNotify extends Activity {

    String tag = "HomeNotify";

//    private ProcessUpdate pu;
//	private String notify;
//	private static String url_notify = Config.ntf;
//	private String url_notify_backup_one;

	public boolean isForceUpdate = false;
    private boolean runCheckUpdate = false;

//	public static final String PREFIX_NUM_OF_WIDGET = "LIBNOTIFY";
/*

	public static void setNotify(int index) {
		SharedPrefs.getInstance().put("lib_kyc_notify", index);
	}

	public static int getNotify() {
		return SharedPrefs.getInstance().get("lib_kyc_notify", Integer.class, 1);
	}
*/

	@SuppressLint("NewApi")
	@Override
	public void onCreate(Bundle savedInstanceState) {
		super.onCreate(savedInstanceState);
        if (!DevicesUtil.isP20L() && !DevicesUtil.isSP02()) {
            runCheckUpdate();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (!runCheckUpdate) {
            runCheckUpdate();
        }
    }

    void runCheckUpdate() {
		// app mpos fix: no check version
//        if (GetData.CheckInternet(this) && Utils.checkTypeBuildIsRelease()) {
//            runCheckUpdate = true;
//            CheckUpdateAppController checkUpdateAppController = new CheckUpdateAppController(this);
//            checkUpdateAppController.processCheckVersionApp();
////            loadConfig();
//        }
    }
	@Override
	protected void onDestroy() {
		super.onDestroy();
	}

/*
	private void checkHaveUpdate(String data) {
		try {
			JSONObject jRoot = new JSONObject(data);
            MposVersionUpdate mposVersionUpdate = null;
            if (DevicesUtil.isP20L()) {
                // sp01
                mposVersionUpdate = MyGson.parseJson(jRoot.getJSONObject("SP01").toString(), MposVersionUpdate.class);
            }
            else if (DevicesUtil.isSP02() || DevicesUtil.isSP02P8()) {
                // mini sp + P8 + sp03
                mposVersionUpdate = MyGson.parseJson(jRoot.getJSONObject("SP02").toString(), MposVersionUpdate.class);
            }
            else if (jRoot.has("android")) {
                mposVersionUpdate = MyGson.parseJson(jRoot.getJSONObject("android").toString(), MposVersionUpdate.class);
            }

            if (mposVersionUpdate != null) {

                if (getPackageManager().getPackageInfo(getPackageName(), 0).versionCode < mposVersionUpdate.getVc()) {
                    isForceUpdate = false;
                    int counterNotify = -1;
                    // type:
//                    0: -> force update
//                    1: -> notify
//                    2: -> notify sau bao nhiêu lần sẽ force (max lần notify ở key ntf)

                    // force update
                    if (mposVersionUpdate.getType() == 0) {
                        isForceUpdate = true;
                    }
                    // type = 1: old app
                    // show notify: counter number show notify
                    else if (mposVersionUpdate.getType() == 2) {
                        counterNotify = getNotify();
                        if (counterNotify > mposVersionUpdate.getNtf()) {
                            isForceUpdate = true;
                        }
                    }
                    // other case: only show update

                    if (Locale.getDefault().getLanguage().equals("en")){
                        mposVersionUpdate.setContentShow(mposVersionUpdate.getContentEn());
                    } else {
                        mposVersionUpdate.setContentShow(mposVersionUpdate.getContentVi());
                    }

                    showDialogUpdateApp(mposVersionUpdate, counterNotify, isForceUpdate);
                }
                else {
                    setNotify(-1);
                }
            }
		} catch (Exception e) {
			Utils.LOGE(tag, "Exception", e);
		}
	}

    private void showDialogUpdateApp(MposVersionUpdate mposVerUpdate, final int iNtf, boolean isForce) {
        if (iNtf >= 0) {
            setNotify(iNtf+1);
        }
        Dialog dialog = new Dialog(this, R.style.SpecialDialog);
        LayoutInflater inflater = (LayoutInflater) getSystemService(LAYOUT_INFLATER_SERVICE);
        View dialogLayout = inflater.inflate(R.layout.dialog_ntf, null);
        ((WebView) dialogLayout.findViewById(R.id.webView1)).loadDataWithBaseURL(null,
                TextUtils.isEmpty(mposVerUpdate.getContentShow()) ?
                        getString(R.string.ALERT_UPDATE_LATEST_VERSION_OPTIONAL_MSG) : mposVerUpdate.getContentShow(),
                "text/html", "utf-8", null);
        ((Button) dialogLayout.findViewById(R.id.ok)).setText(getString(R.string.ALERT_BTN_UPDATE_NOW));
        dialogLayout.findViewById(R.id.ok).setOnClickListener(v -> {
            try {
                if (mposVerUpdate.getLink().startsWith("http")) {
                    startActivity(new Intent(Intent.ACTION_VIEW, Uri.parse(mposVerUpdate.getLink())));
                }
                else {
                    openAppByPackageName(mposVerUpdate.getLink());
                }
            } catch (Exception e) {
                Utils.LOGE(tag, "Exception", e);
            }
        });
        if (!isForce) {
            dialogLayout.findViewById(R.id.btn_close).setVisibility(View.VISIBLE);
            dialogLayout.findViewById(R.id.v_space).setVisibility(View.VISIBLE);
            dialogLayout.findViewById(R.id.btn_close).setOnClickListener(v -> dialog.dismiss());
        }
        dialog.setCancelable(false);
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.getWindow().getAttributes().windowAnimations = R.style.PauseDialogAnimation;
        dialog.setCanceledOnTouchOutside(false);
        dialog.setContentView(dialogLayout);
        dialog.show();
    }

    private void openAppByPackageName(String packageName) throws Exception{
        Intent launchIntent = getPackageManager().getLaunchIntentForPackage(packageName);
        if (launchIntent != null) {
            startActivity(launchIntent);//null pointer check in case package name was not found
        }
    }

    private void loadConfig() {
        if (Utils.checkTypeBuildIsRelease()) {
            MposRestClient.getInstance(this).get(Config.ntf, new AsyncHttpResponseHandler() {
                @Override
                public void onSuccess(int i, Header[] headers, byte[] bytes) {
                    String content = new String(bytes);
                    Utils.LOGD(tag, "onSuccess: "+content);
                    checkHaveUpdate(content);
                }

                @Override
                public void onFailure(int i, Header[] headers, byte[] bytes, Throwable throwable) {
                    Utils.LOGD(tag, "onFailure: ");
                }
            });
        }
    }*/


}
