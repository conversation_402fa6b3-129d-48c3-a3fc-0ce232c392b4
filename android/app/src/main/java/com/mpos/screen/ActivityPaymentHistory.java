package com.mpos.screen;

import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;

import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.fragment.app.FragmentStatePagerAdapter;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.google.android.material.tabs.TabLayout;
import com.mpos.common.DataStoreApp;
import com.mpos.common.MyApplication;
import com.mpos.customview.SearchViewLayout;
import com.mpos.customview.ViewToolBar;
import com.mpos.sdk.util.Utils;

import net.yslibrary.android.keyboardvisibilityevent.util.UIUtil;

import org.jetbrains.annotations.NotNull;

import java.util.Locale;

import butterknife.BindView;
import butterknife.ButterKnife;
import vn.mpos.R;

public class ActivityPaymentHistory extends BaseActivity{

    String TAG = "ActivityPaymentHistoryNew";

    @BindView(R.id.main_content)        View vRoot;
    @BindView(R.id.search_view)         View searchViewLayout;
    @BindView(R.id.viewpager)           ViewPager mViewPager;
    @BindView(R.id.tabs)                TabLayout tabLayout;
    /**
     * The {@link PagerAdapter} that will provide
     * fragments for each of the sections. We use a
     * {@link FragmentPagerAdapter} derivative, which will keep every
     * loaded fragment in memory. If this becomes too memory intensive, it
     * may be best to switch to a
     * {@link FragmentStatePagerAdapter}.
     */
    private SectionsPagerAdapter mSectionsPagerAdapter;
    private boolean isUseReader = false;
    private boolean isUseNewUi = false;
//    private boolean isShowFailHistory = true;
    private boolean isMultiAcquirer = false;
    private ViewToolBar vToolBar;
    private SearchViewLayout mSearchView;

    boolean loadedHistoryMpos = false;
    boolean loadedHistoryFail = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (checkLanguageFromFlutter()) {
            return;
        }
        setContentView(R.layout.activity_payment_history);

        Utils.LOGD(TAG, "onCreate: ------>>");

        ButterKnife.bind(this);
        isUseReader = DataStoreApp.getInstance().isUseReader();
//        isUseNewUi = DataStoreApp.getInstance().getDataByKey(DataStoreApp.isUseNewUI, Boolean.class, false);
        isMultiAcquirer = MyApplication.self().isRunMA();

        vToolBar = new ViewToolBar(this, vRoot);
        vToolBar.showTextTitle(getString(R.string.NAV_BAR_TITLE_SALES_HISTORY));
        vToolBar.showButtonBack(true);

        vToolBar.setOnclickSearch(v -> {
            vToolBar.showHideToolbar(false);
            searchViewLayout.setVisibility(View.VISIBLE);
            mSearchView.focusEditTextSearch();
            UIUtil.showKeyboard(ActivityPaymentHistory.this, mSearchView.getEdtSearch());
        });

        // Create the adapter that will return a fragment for each of the three
        // primary sections of the activity.
        mSectionsPagerAdapter = new SectionsPagerAdapter(getSupportFragmentManager());

//        if (isUseReader && PrefLibTV.getInstance(context).getFlagServer() == ConstantsPay.SERVER_SCB) {
//            isShowFailHistory = true;
            mViewPager.setOffscreenPageLimit(3);
//        }
        // Set up the ViewPager with the sections adapter.
        mViewPager.setAdapter(mSectionsPagerAdapter);

        tabLayout.setupWithViewPager(mViewPager);
        if (isUseReader) {
            tabLayout.setVisibility(View.VISIBLE);
            tabLayout.setTabMode(TabLayout.MODE_FIXED);
        } else {
            tabLayout.setVisibility(View.GONE);
        }
        mViewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
//            boolean isStartChange = false;
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
//                Utils.LOGD(TAG, "onPageScrolled: ");
            }

            @Override
            public void onPageSelected(int position) {
                Utils.LOGD(TAG, "onPageSelected: "+position);
                if (position == 1 && !loadedHistoryMpos) {
                    doLoadHisMpos();
                }
                else if (position == 2 && !loadedHistoryFail) {
                    doLoadHisFail();
                }

                if (mSearchView.getVisibility()==View.VISIBLE) {
                    mSearchView.cancelSearch();
                }

                // don't show icon_search in tab: fail
                vToolBar.showIconSearch(position != 2);
            }

            @Override
            public void onPageScrollStateChanged(int state) {
                Utils.LOGD(TAG, "onPageScrollStateChanged() called with: state = [" + state + "]");
//                if (state == 1 && mSearchView.getVisibility()==View.VISIBLE) {
//                    mSearchView.cancelSearch();
//                }
            }
        });


        mSearchView = new SearchViewLayout(this, searchViewLayout);
        mSearchView.setItfViewSearchListener(new SearchViewLayout.ItfViewSearchListener() {
            @Override
            public void onQuerySearch(String querySearch) {
                searchData(querySearch);
            }

            @Override
            public void onDelStringSearch() {
            }

            @Override
            public void onDismissSearch() {
                mSearchView.setVisibility(View.GONE);
                vToolBar.showHideToolbar(true);
                dismissKeyBroad();
            }
        });
    }

    @Override
    protected void onResume() {
        super.onResume();
        dismissKeyBroad();
    }

    private void dismissKeyBroad() {
        UIUtil.hideKeyboard(this, vRoot);
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {

        return super.onOptionsItemSelected(item);
    }

    /*private boolean checkLanguageFromFlutter() {
        String language = DataStoreApp.getInstance().getDataByKey(DataStoreApp.languageFlutter, String.class, "");
        if (!TextUtils.isEmpty(language)) {
            String currLanguage = Locale.getDefault().getLanguage();
            Utils.LOGD(TAG, "checkLanguageFromFlutter: currLang=" + currLanguage + " lang=" + language);
            if (!currLanguage.equals(language)) {
                changeLanguage(language);
                return true;
            }
        }
        return false;
    }

    public void changeLanguage(String language) {
        // Set the locale to French
        Locale locale = new Locale(language);
        Locale.setDefault(locale);
        Configuration config = new Configuration();
        config.setLocale(locale);

        Resources resources = getResources();
        resources.updateConfiguration(config, resources.getDisplayMetrics());

        // Restart the activity to apply the new language
        recreate();
    }*/

    private void doLoadHisMpos() {
        Fragment fragment = mSectionsPagerAdapter.getActiveFragment(mViewPager, mViewPager.getCurrentItem());
        if (fragment instanceof FragmentPaymentHistory) {
            loadedHistoryMpos = true;
            ((FragmentPaymentHistory) fragment).loadSalesHistoryMpos();
        }
    }
    private void doLoadHisFail() {
        Fragment fragment = mSectionsPagerAdapter.getActiveFragment(mViewPager, mViewPager.getCurrentItem());
        if (fragment instanceof FragmentPaymentHistory) {
            loadedHistoryFail = true;
            if (isMultiAcquirer) {
                ((FragmentPaymentHistory) fragment).loadFailHistoryMA();
            }
            else {
                ((FragmentPaymentHistory) fragment).loadFailHistoryBank();
            }
        }
    }

    Handler handlerSearch = new Handler();

    private void searchData(final String text) {
        handlerSearch.removeCallbacksAndMessages(null);
        handlerSearch.postDelayed(() -> {
            Utils.LOGD(TAG, "run: search="+text);
            doSearchData(text);
        }, 800);
    }

    private void doSearchData(String text) {
        Utils.LOGD(TAG, "doSearchData: currtag="+mViewPager.getCurrentItem());
        Fragment fragment = mSectionsPagerAdapter.getActiveFragment(mViewPager, mViewPager.getCurrentItem());
        if (fragment instanceof FragmentPaymentHistory) {

            ((FragmentPaymentHistory) fragment).searchPaymentByKey(text);
        }
    }

    /**
     * A {@link FragmentPagerAdapter} that returns a fragment corresponding to
     * one of the sections/tabs/pages.
     */
    public class SectionsPagerAdapter extends FragmentPagerAdapter {

        public SectionsPagerAdapter(FragmentManager fm) {
            super(fm,FragmentStatePagerAdapter.BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT);
        }

        @NotNull
        @Override
        public Fragment getItem(int position) {
            Utils.LOGD(TAG, "getItem: " + position + " isUseReader=" + isUseReader+ " isUseNewUi="+isUseNewUi);
            if (isUseReader || isUseNewUi) {
                if (position == 0) {
                    if (isMultiAcquirer) {
                        return FragmentPaymentHistory.newInstanceHistoryMultiAcquirer();
                    }
                    else {
                        return FragmentPaymentHistory.newInstanceHistoryBank(getIntent().getStringExtra("tokenL2"));
                    }
                }
                else if (position == 1) {
                    return FragmentPaymentHistory.newInstanceHistoryMpos(getIntent().getStringExtra("tokenL2"));
                }
                else {
                    if (isMultiAcquirer) {
                        return FragmentPaymentHistory.newInstanceHistoryFailMA();
                    }
                    else {
                        return FragmentPaymentHistory.newInstanceFailHistoryBank(getIntent().getStringExtra("tokenL2"));
                    }
                }
            }
            else {
                return FragmentPaymentHistory.newInstanceHistoryMpos(getIntent().getStringExtra("tokenL2"));
            }
        }

        @Override
        public int getCount() {
            int size;
//            if (isMultiAcquirer) {
//                size = 1;
//            }
//            else
            if (isUseReader || isUseNewUi) {
                size = 3;
            }
            else {
                size = 1;
            }
//            Utils.LOGD(TAG, "getCount: " + size);
            return size;
        }

        @Override
        public CharSequence getPageTitle(int position) {
            if (isUseReader || isUseNewUi){
                switch (position) {
                    case 0:
                        if (isMultiAcquirer) {
                            return getString(R.string.history_macq);
                        }
                        else {
                            return getString(R.string.history_in_day);
                        }
                    case 1:
                        return getString(R.string.history_other);
                    case 2:
                        return getString(R.string.history_fail);
                }
            } else {
//                if (isMultiAcquirer) {
//                    return getString(R.string.history);
//                }
//                else {
                    return getString(R.string.history_other);
//                }
            }
            return null;
        }

        public Fragment getActiveFragment(ViewPager container, int position) {
            String name = makeFragmentName(container.getId(), position);
            return  getSupportFragmentManager().findFragmentByTag(name);
        }

        private String makeFragmentName(int viewId, int index) {
            return "android:switcher:" + viewId + ":" + index;
        }
    }
}
