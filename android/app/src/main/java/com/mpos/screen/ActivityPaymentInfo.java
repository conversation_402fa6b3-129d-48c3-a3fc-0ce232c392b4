package com.mpos.screen;

import android.app.Dialog;
import android.bluetooth.BluetoothAdapter;
import android.content.ClipboardManager;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.text.style.ClickableSpan;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.DialogFragment;

import com.loopj.android.http.AsyncHttpResponseHandler;
import com.mpos.common.DataStoreApp;
import com.mpos.screen.printer.BaseActivityPrint;
import com.mpos.sdk.core.control.JsonParser;
import com.mpos.common.MyApplication;
import com.mpos.common.NotifyPushController;
import com.mpos.customview.DialogQuickWithdrawMoney;
import com.mpos.customview.DialogVoid;
import com.mpos.customview.MposDialog;
import com.mpos.customview.ViewCashierReward;
import com.mpos.customview.ViewToolBar;
import com.mpos.dialogs.CancelTransDialogFragment;
import com.mpos.dialogs.SuccessDialogFragment;
import com.mpos.dspread.DeviceListActivity;
import com.mpos.sdk.core.model.BaseObjJson;
import com.mpos.models.DataQrPrint;
import com.mpos.models.PaymentItem;
import com.mpos.sdk.core.common.MyGson;
import com.mpos.sdk.core.common.MyTextHttpResponseHandler;
import com.mpos.sdk.core.control.CryptoInterface;
import com.mpos.sdk.core.control.EncodeDecode;
import com.mpos.sdk.core.control.GetData;
import com.mpos.sdk.core.control.SaveLogController;
import com.mpos.sdk.core.model.DataError;
import com.mpos.sdk.core.model.DataPay;
import com.mpos.sdk.core.model.DataReversalLogin;
import com.mpos.sdk.core.model.LibError;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.core.modelma.DataBaseObj;
import com.mpos.sdk.core.modelma.TransItemMacq;
import com.mpos.sdk.core.modelma.WfDetailRes;
import com.mpos.sdk.core.modelma.WorkFlow;
import com.mpos.sdk.core.network.ApiMultiAcquirerInterface;
import com.mpos.sdk.core.network.MposRestClient;
import com.mpos.sdk.util.CardUtils;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.DevicesUtil;
import com.mpos.sdk.util.Intents;
import com.mpos.sdk.util.MyOnClickListenerView;
import com.mpos.sdk.util.MyTextUtils;
import com.mpos.sdk.util.Utils;
import com.mpos.sdk.util.UtilsSystem;
import com.mpos.utils.ButterKnifeUtils;
import com.mpos.utils.Config;
import com.mpos.utils.Constants;
import com.mpos.utils.IntentsMP;
import com.mpos.utils.MyDialogShow;
import com.mpos.utils.MyUtils;
import com.pps.core.MyProgressDialog;
import com.pps.core.ToastUtil;

import org.json.JSONException;
import org.json.JSONObject;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;

import butterknife.BindView;
import butterknife.BindViews;
import butterknife.ButterKnife;
import butterknife.OnClick;
import butterknife.ViewCollections;
import cz.msebera.android.httpclient.Header;
import cz.msebera.android.httpclient.HttpStatus;
import cz.msebera.android.httpclient.entity.StringEntity;
import vn.mpos.R;

public class ActivityPaymentInfo extends BaseActivityPrint implements CancelTransDialogFragment.OnClickButtonCancelTransListener, SuccessDialogFragment.OnDismissSuccessDialogListener {

    public static final String tag = ActivityPaymentInfo.class.getSimpleName();

    public static final int RESULT_CODE_RE_SIGNATURE = 1;
    static final int REQUEST_ENABLE_BT 			    = 4;
    static final int REQUEST_DEVICE_DSPREAD 	    = 5;

    final int requestCodePermissionBluetooth     = 6;


    //	@BindView(R.id.root) LinearLayout llRootNormal;
    @BindViews({R.id.v_button, R.id.btn_quick_recei_money, R.id.v_top_info_card, R.id.v_info_card})
    List<View> vNormalPay;
    @BindView(R.id.btn_quick_recei_money)
    protected Button btnQuickReceiMoney;
    @BindView(R.id.void_payment)
    protected Button btnVoidPay;
    @BindView(R.id.btn_continue)
    protected Button btnContinue;
    @BindView(R.id.btn_send_mail)
    protected Button btnSendMail;
    @BindView(R.id.ll_btn_vaymuon)
    protected LinearLayout llBtnVaymuon;
    @BindView(R.id.btn_vm_quick_recei_money)
    protected Button btnVMQuickReceiMoney;
    @BindView(R.id.btn_vm_check_status)
    protected Button btnVMCheckStatus;
    @BindView(R.id.log_out)
    protected ImageView imvArrow;
    @BindView(R.id.imv_status)
    protected ImageView imvStatus;
    @BindView(R.id.time)
    protected View vTimeInclude;
    @BindView(R.id.tv_status)
    protected TextView tvStatus;
    @BindView(R.id.tv_amount)
    protected TextView tvAmount;
    @BindView(R.id.tv_trx_type)
    protected TextView tvTrxType;
    @BindView(R.id.number)
    protected TextView tvCardNumber;
    @BindView(R.id.tv_card_type)
    protected TextView tvCardType;
    @BindView(R.id.approval_code)
    protected TextView tvApprovalCode;
    @BindView(R.id.invoice_no)
    protected TextView tvInvoiceNo;
    @BindView(R.id.tv_card_holder_name)
    protected TextView tvCardHolderName;
    @BindView(R.id.tv_batch_no)
    protected TextView tvBatchNo;
    @BindView(R.id.tv_trans_id)
    protected TextView tvTransId;
    @BindView(R.id.description)
    protected TextView tvDesc;
    @BindView(R.id.tv_trans_time)     protected TextView tvTransTime;
    @BindView(R.id.tv_trans_date)     protected TextView tvTransDate;
    @BindView(R.id.tv_tid)     protected TextView tvTid;
    @BindView(R.id.tv_mid)     protected TextView tvMid;
//    @BindView(R.id.time)        protected TextView tvTime;


    @BindView(R.id.v_feedback)
    protected View vRootCashierReward;

    // mvisa
    @BindView(R.id.llParentDetailInfoMVisa)
    LinearLayout llIncludeMVISA;

    @BindView(R.id.tv_mvisa_money_detail)
    TextView tvSoTien;
    @BindView(R.id.tv_mvisa_magiaodich_detail)
    TextView tvMaGiaoDich;
    @BindView(R.id.tv_mvisa_time_create_detail)
    TextView tvThoiGian;
    @BindView(R.id.tv_mvisa_payment_baseon_detail)
    TextView tvThanhToanQua;
    @BindView(R.id.tv_mvisa_number_card_detail)
    TextView tvSoThe;
    @BindView(R.id.tv_mvisa_email_detail)
    TextView tvEmail;
    @BindView(R.id.tv_mvisa_type_qr)
    TextView tvMvisaTypeQR;
    @BindView(R.id.tv_mvisa_desc_dvcnt)
    TextView tvMvisaDescDvcnt;
    @BindView(R.id.tv_mvisa_desc_bank)
    TextView tvMvisaDescBank;
    @BindView(R.id.tv_note_detail)
    TextView tvNote;

    // vaymuon
    @BindView(R.id.llParentDetailInfoVaymuon)
    LinearLayout llIncludeVaymuon;

    @BindView(R.id.tv_vm_money_detail)
    TextView tvVMAmount;
    @BindView(R.id.tv_vm_cardholdername_detail)
    TextView tvVMCustomerName;
    @BindView(R.id.tv_vm_payment_method)
    TextView tvVMPaymentMethod;
    @BindView(R.id.tv_vm_phone_number)
    TextView tvVMCustomerPhone;
    @BindView(R.id.tv_vm_status)
    TextView tvVMStatus;
    @BindView(R.id.rl_vm_wait_explain)
    RelativeLayout rlVMStatusExplain;
    @BindView(R.id.rl_vm_kt_explain)
    LinearLayout rlVMStatusKTExplain;
    @BindView(R.id.tv_vm_magiaodich_detail)
    TextView tvVMTransCode;
    @BindView(R.id.tv_vm_time_create_detail)
    TextView tvVMTransTime;
    @BindView(R.id.tv_vm_content_payment)
    TextView tvVMPaymentContent;
    @BindView(R.id.tv_vm_void_reason)
    TextView tvVMVoidReason;
    @BindView(R.id.rl_vm_void_reason)
    LinearLayout rlVMVoidReason;

    // Vimolink
    @BindView(R.id.llParentDetailInfoVimoLink)
    LinearLayout llIncludeVimolink;

    @BindView(R.id.tv_link_money_detail)
    TextView tvLinkAmount;
    @BindView(R.id.tv_link_payment_method)
    TextView tvLinkPaymentMethod;
    @BindView(R.id.tv_link_cardholdername_detail)
    TextView tvLinkCardName;
    @BindView(R.id.tv_link_card_type)
    TextView tvLinkCardType;
    @BindView(R.id.tv_link_card_number)
    TextView tvLinkCardNumber;
    @BindView(R.id.tv_link_status)
    TextView tvLinkStatus;
    @BindView(R.id.tv_link_magiaodich_detail)
    TextView tvLinkTransCode;
    @BindView(R.id.tv_link_time_create_detail)
    TextView tvLinkTimeCreate;
    @BindView(R.id.tv_link_content_payment)
    TextView tvLinkContentPayment;
    @BindView(R.id.ll_btn_link)
    protected LinearLayout llBtnVimoLink;

    @BindView(R.id.tv_mvisa_cardholdername_detail)
    TextView tvTenChuThe;
    @BindView(R.id.tv_mvisa_type_card_detail)
    TextView tvLoaiThe;

    @BindViews({R.id.v_info, R.id.v_button, R.id.btn_quick_recei_money})
    List<View> vInfo;

    // enter card
    @BindView(R.id.ll_link_pending_reason)
    LinearLayout llLinkPendingReason;
    @BindView(R.id.tv_link_pending_reason)
    TextView tvLinkPendingReason;
    @BindView(R.id.iv_polygon)
    ImageView ivPolygon;

    ViewToolBar vToolBar;

    private String mTranId;
    private String mTokenL2;
    private String trxType;
    private String transactionRequestId;
    private String transactionDate;
    private String itemDescription = "";
    private String udid;

    private String amountPay;
    private String pan;
    private String holderName;
    private boolean isPayInstallment = false;
    private boolean isPayCashBack = false;
    private int transactionStatus;

    private MyProgressDialog mPgdl;
    private ToastUtil mToast;
    private SaveLogController logUtil;

    private ViewCashierReward vCashierReward;
    //params for MVISA
    //-- begin --
    public static final String KEY_TXID = "key_txid";
    //--- end ---

    PaymentItem paymentInfo;
    TransItemMacq  transItemMA;
    String TAG = "ActivityPaymentInfo";
    private boolean needReloadOnBack = false;
    private boolean showVMQuickDraw = false;

    private boolean isDomesticInMVisa;
    private boolean isTranMultiAcquirer = false;

    private boolean isSupportPrintQR = false;
    private DataQrPrint dataQrPrint;
    private String snDoTrans = "";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (checkLanguageFromFlutter()) {
            return;
        }
        setContentView(R.layout.activity_payment_info);

        MyUtils.setRequestedOrientation(this, DataStoreApp.getInstance().getIsLandscape());

        vToolBar = new ViewToolBar(this, findViewById(R.id.container));
        vToolBar.showTextTitle(getString(R.string.NAV_BAR_TITLE_SALES_DETAIL));
        vToolBar.showButtonBack(true);

        logUtil = MyApplication.self().getSaveLogController();
        mPgdl = new MyProgressDialog(this);
        mToast = new ToastUtil(this);

        initUI();

        if (getIntent() != null) {
            transItemMA = (TransItemMacq) getIntent().getSerializableExtra(IntentsMP.EXTRA_TRANS_INFO);
            if (transItemMA == null) {
                paymentInfo = (PaymentItem) getIntent().getSerializableExtra(IntentsMP.EXTRA_PAYMENT_INFO);
                if (getIntent().getExtras() != null && getIntent().getExtras().containsKey(KEY_TXID)) {
                    isDomesticInMVisa = getIntent().getBooleanExtra(IntentsMP.EXTRA_IS_DOMESTIC_IN_MVISA, false);
                    String txId = getIntent().getStringExtra(KEY_TXID);
                    if (!TextUtils.isEmpty(txId)) {
                        setLayoutTransFromMpos(!isDomesticInMVisa);
                        loadDataDetailMpos(txId);
                        if (!DataStoreApp.getInstance().isUseReader()) {
                            btnSendMail.setVisibility(View.GONE);
                        }
                    }
                } else {
                    setLayoutTransFromMpos(false);
                    loadSalesDetailBank();
                }
            }
            else {
                isTranMultiAcquirer = true;
                setLayoutTransFromMpos(false);
                loadSaleDetailMa(transItemMA.getWfId());
            }

        }
        showView(vInfo, false);
    }

    private void setLayoutTransFromMpos(boolean isMVISA) {
        Utils.LOGD(TAG, "setLayoutTransFromMpos: isMVISA=" + isMVISA);
        if (isMVISA) {
            if (paymentInfo != null && Constants.VAYMUONQR.equals(paymentInfo.getTransactionPushType())) {
                llIncludeMVISA.setVisibility(View.GONE);
                llIncludeVaymuon.setVisibility(View.VISIBLE);
                llIncludeVimolink.setVisibility(View.GONE);
                btnSendMail.setVisibility(View.GONE);
            } else if (paymentInfo != null && Constants.LINKCARD.equals(paymentInfo.getTransactionPushType())) {
                llIncludeMVISA.setVisibility(View.GONE);
                llIncludeVaymuon.setVisibility(View.GONE);
                llIncludeVimolink.setVisibility(View.VISIBLE);
                btnSendMail.setVisibility(View.GONE);
            } else {
                llIncludeMVISA.setVisibility(View.VISIBLE);
                llIncludeVaymuon.setVisibility(View.GONE);
                llIncludeVimolink.setVisibility(View.GONE);

                if (DevicesUtil.isDeviceSupportPrint()) {
                    isSupportPrintQR = true;
                    btnSendMail.setVisibility(View.VISIBLE);
                    if (DevicesUtil.isDeviceSupportPrint()) {
                        btnSendMail.setText(getString(R.string.receipt));
                    }
                }
                else {
                    btnSendMail.setVisibility(View.GONE);
                } 
            }
            showView(vNormalPay, false);
        } else {
            llIncludeMVISA.setVisibility(View.GONE);
            llIncludeVaymuon.setVisibility(View.GONE);
            showView(vNormalPay, true);
            btnSendMail.setVisibility(View.VISIBLE);
            if (DevicesUtil.isDeviceSupportPrint()) {
                btnSendMail.setText(getString(R.string.receipt));
            }
        }
    }

    private void initUI() {
        ButterKnife.bind(this);

        imvArrow.setVisibility(View.GONE);
        vTimeInclude.setVisibility(View.GONE);

    }

    @Override
    public void onBackPressed() {
        if (needReloadOnBack) {
            finishSuccess();
        } else {
            super.onBackPressed();
        }
    }

    private void showView(List<View> vInfo, boolean b) {
        ViewCollections.set(vInfo, ButterKnifeUtils.SHOW, b);
    }

    @OnClick({R.id.btn_send_mail, R.id.void_payment, R.id.btn_continue, R.id.btn_void_vaymuon, R.id.btn_vm_quick_recei_money, R.id.btn_vm_check_status, R.id.btn_link_quick_recei_money})
    public void OnClick(View v) {
        switch (v.getId()) {
            case R.id.btn_send_mail:
                onClickSendMail();
                break;
            case R.id.void_payment:
                handleClickVoid();
                break;
            case R.id.btn_continue:

                gotoSignature(buildDataReversalLogin(), null);
                break;
            case R.id.btn_void_vaymuon:
                CancelTransDialogFragment cancelTransDialogFragment = new CancelTransDialogFragment();
                cancelTransDialogFragment.setOnClickButtonCancelTransListener(this);
                cancelTransDialogFragment.show(getSupportFragmentManager(), "DIALOG_CANCEL_TRANS");
                break;
            case R.id.btn_vm_quick_recei_money:
            case R.id.btn_link_quick_recei_money:
                showDialogQuickWithdrawMoney();
                break;
            case R.id.btn_vm_check_status:
                llBtnVaymuon.setVisibility(View.GONE);
                llBtnVimoLink.setVisibility(View.GONE);
                llIncludeMVISA.setVisibility(View.GONE);
                llIncludeVaymuon.setVisibility(View.GONE);
                if (getIntent().getExtras() != null && getIntent().getExtras().containsKey(KEY_TXID)) {
                    String txId = getIntent().getStringExtra(KEY_TXID);
                    if (!TextUtils.isEmpty(txId)) {
                        setLayoutTransFromMpos(true);
                        loadDataDetailMpos(txId);
                    }
                }
                break;
            default:
                break;
        }
    }

    private void handleClickVoid() {
        if (MyApplication.self().isRunMA()) {
            String readerPaired = PrefLibTV.getInstance(this).getSerialNumber();
            int deviceType = PrefLibTV.getInstance(this).getFlagDevices();
            logUtil.appendLogAction("click void: sn=" + readerPaired + " type=" + deviceType);
            Utils.LOGD(TAG, "handleClickVoid: sn=" + readerPaired + " type=" + deviceType);
            if (deviceType == ConstantsPay.DEVICE_DSPREAD || deviceType == ConstantsPay.DEVICE_NONE) {
                if (TextUtils.isEmpty(readerPaired) || !snDoTrans.equals(readerPaired)) {
                    String msg = getString(R.string.warning_connect_device_to_void, snDoTrans);
                    boolean hasClickPair = true;
                    if (!TextUtils.isEmpty(snDoTrans)) {
                        if(snDoTrans.startsWith("SP01") || snDoTrans.startsWith("SP02")){
                            hasClickPair = false;
                            msg = getString(R.string.warning_use_correct_device_to_void, snDoTrans);
                        }
                    }
                    MyDialogShow.showDialogWarningConnectDevice(this, msg, hasClickPair?
                            new MyOnClickListenerView(view -> processPairDevice()):null);
                }
                else {
                    showDialogConfirmVoid();
                }
            }
            // other devices
            else {
                if (TextUtils.isEmpty(readerPaired)) {
                    String msg = getString(R.string.warning_use_correct_device_to_void, snDoTrans);
                    MyDialogShow.showDialogWarningConnectDevice(this, msg, new MyOnClickListenerView(view -> processPairDevice()));
                }
                else {
                    showDialogConfirmVoid();
                }
            }
        } else {
            showDialogConfirmVoid();
        }
    }

    private void processPairDevice() {
        initBluetoothAndDevice();
    }

    @NonNull
    private DataReversalLogin buildDataReversalLogin() {
        DataReversalLogin dataReversalLogin = new DataReversalLogin();

        dataReversalLogin.amount = MyUtils.convertMoney(amountPay);
        dataReversalLogin.trxType = trxType;
        dataReversalLogin.pan = pan;
        dataReversalLogin.itemDesc = itemDescription;
        dataReversalLogin.cardholderName = holderName;
        dataReversalLogin.transReqId = transactionRequestId;
        dataReversalLogin.transactionDate = transactionDate;
        dataReversalLogin.paymentIdentify = udid;

        return dataReversalLogin;
    }

    private void doVoidVaymuon(String reason) {
        if (paymentInfo != null) {
            logUtil.appendLogRequestApi(Config.TRANSACTION_PUSH_REFUND);
            StringEntity entity = null;
            try {
                JSONObject jo = new JSONObject();
                jo.put(Constants.STR_SERVICE_NAME, Config.TRANSACTION_PUSH_REFUND);
                jo.put("udid", paymentInfo.getUdid());
                jo.put("type", paymentInfo.getTransactionPushType());
                jo.put("voidReason", new String(reason.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
                entity = new StringEntity(jo.toString());
            } catch (Exception e1) {
                Utils.LOGE(tag, "doVoidVaymuon: " + e1.getMessage());
            }
            MposRestClient.getInstance(this).post(ActivityPaymentInfo.this, Config.URL_CHECK_STATUS_MVISA,
                    entity, Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
                        @Override
                        public void onStart() {
                            mPgdl.showLoading();
                            super.onStart();
                        }

                        @Override
                        public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                            mPgdl.hideLoading();
                            String msg = null;
                            try {
                                JSONObject jRoot = new JSONObject(new String(arg2));

                                Utils.LOGD(TAG, "RESPONSE: " + jRoot);

                                if (jRoot.has("error")) {
                                    final JSONObject jError = jRoot.getJSONObject("error");
                                    errorCode = jError.getInt("code");
                                    msg = jError.getString("message");
                                    logUtil.appendLogAction("logIn: error=" + errorCode);
                                    logUtil.saveLog();

                                    if (errorCode == 1000) {
                                        SuccessDialogFragment successDialogFragment = new SuccessDialogFragment();
                                        Bundle bundle = new Bundle();
                                        bundle.putString(SuccessDialogFragment.TITLE_DIALOG_SUCCESS, getString(R.string.cancel_transaction_confirm_success));
                                        bundle.putString(SuccessDialogFragment.CONTENT_DIALOG_SUCCESS, "");
                                        successDialogFragment.setArguments(bundle);
                                        successDialogFragment.setOnDismissSuccessDialogListener(ActivityPaymentInfo.this);
                                        successDialogFragment.show(getSupportFragmentManager(), "DIALOG_SUCCESS");
                                    } else {
                                        //  ERROR!!!
                                        final MposDialog mposDialogError = MyUtils.initDialogGeneralError(ActivityPaymentInfo.this, errorCode, msg, ActivityScanQRCode.class.getName());
                                        mposDialogError.setOnClickListenerDialogClose(v -> mposDialogError.dismiss());
                                        mposDialogError.show();
                                    }
                                }
                            } catch (Exception e) {
                                logUtil.appendLogRequestApi(Config.URL_CHECK_STATUS_MVISA + " Exception:" + e.getMessage());
                                logUtil.saveLog();
                                Utils.LOGE(TAG, "Exception", e);
                            }

                            if (!TextUtils.isEmpty(msg)) {
                                logUtil.appendLogException(msg);
                                logUtil.saveLog();
                            }

                            logUtil.pushLog();
                        }

                        @Override
                        public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                            Utils.LOGD(TAG, "onFailure: " + arg3.getMessage());
                            logUtil.appendLogRequestApiFail(Config.URL_CHECK_STATUS_MVISA + " onFailure", arg2);
                            mPgdl.hideLoading();
                        }
                    });
        }
    }

    private void onClickSendMail() {
        logUtil.appendLogAction(" select send Email: status=" + tvStatus.getText());
        if (transItemMA != null) {
            if (tvStatus.getText().equals(getString(R.string.txt_title_success))
                    || tvStatus.getText().equals(getString(R.string.settled))
            ) {
                gotoSendMail();
            }
            else {
                MyDialogShow.showDialogContinueCancel(getString(R.string.warning_trans_not_success, tvStatus.getText()), this, true,
                        new MyOnClickListenerView(view -> gotoSendMail()));
            }
        } else {
            gotoSendMail();
        }
    }

    private void gotoSendMail() {
        Intent i = new Intent(ActivityPaymentInfo.this, ActivitySendEmail.class);
        i.putExtra("type", false);
        i.putExtra("tid", mTranId);
        i.putExtra("amount", "-1");

        if (transItemMA != null) {
            i.putExtra("txid", transItemMA.getTxid());
        }

        if (dataQrPrint != null) {
            i.putExtra("dataQrPrint", dataQrPrint);
        }

        startActivityForResult(i, 2);
    }

    public void loadSalesDetailBank() {
        String tid = getIntent().getStringExtra("tid");
        logUtil.appendLogRequestApi(Config.SALES_HISTORY_DETAIL + "--" + tid);
        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, Config.SALES_HISTORY_DETAIL);
            jo.put("udid", "0");
            jo.put("readerSerialNo", PrefLibTV.getInstance(ActivityPaymentInfo.this).getSerialNumber());
            jo.put("versionNo", android.os.Build.VERSION.RELEASE);
            jo.put("platform", Config.PLATFORM);
            jo.put("userID", PrefLibTV.getInstance(ActivityPaymentInfo.this).getUserId());
            jo.put("sessionKey", PrefLibTV.getInstance(ActivityPaymentInfo.this).getSessionKey());
            jo.put("transactionID", tid);
            jo.put("tokenL2", getIntent().getStringExtra("tokenL2"));
            Utils.LOGD(TAG, jo.toString());
            Utils.LOGD(TAG, "getSalesDetail: send=>" + PrefLibTV.getInstance(ActivityPaymentInfo.this).getSessionKey());
            //			entity = new StringEntity(jo.toString());
            entity = new StringEntity(EncodeDecode.doAESEncrypt(jo.toString(), PrefLibTV.getInstance(ActivityPaymentInfo.this).getSessionKey()));
        } catch (Exception e1) {
            Utils.LOGE(tag, "getSalesDetail: " + e1.getMessage());
        }

        Utils.LOGD(tag, "getSalesDetail: url=" + ConstantsPay.getUrlServer(this));

        MposRestClient.getInstance(this).post(ActivityPaymentInfo.this, ConstantsPay.getUrlServer(this),
                entity, Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
                    @Override
                    public void onStart() {
                        mPgdl.showLoading();
                        super.onStart();
                    }

                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                        try {
                            mPgdl.hideLoading();
                            Utils.LOGD(tag, "onSuccess: sskey=" + PrefLibTV.getInstance(ActivityPaymentInfo.this).getSessionKey() + " data=" + new String(arg2));

                            JSONObject response = new JSONObject(EncodeDecode.doAESDecrypt(new String(arg2), PrefLibTV.getInstance(ActivityPaymentInfo.this).getSessionKey()));
                            PrefLibTV.getInstance(ActivityPaymentInfo.this).setSessionKey( response.getString("sessionKey"));
                            Utils.LOGD(tag, "Sales detail: " + response);
                            if (response.has("error")) {
                                try {
                                    final JSONObject jo = response.getJSONObject("error");
                                    int errCode = jo.getInt("code");
                                    String msg = String.format("%s %02d: %s", getString(R.string.error), errCode, LibError.getErrorMsg(errCode, ActivityPaymentInfo.this));
//                                    String msg = getString(R.string.error) + " " + String.format("%02d", errCode)
//                                            + ": " + LibError.getErrorMsg(errCode, ActivityPaymentInfo.this);
                                    logUtil.appendLogRequestApi(Config.SALES_HISTORY_DETAIL + " error server:" + msg);
                                    if (LibError.isSessionExpired(errCode)) {
                                        MyDialogShow.showDialogErrorReLogin(msg, ActivityPaymentInfo.this);
                                    } else {
                                        MyDialogShow.showDialogErrorFinish(msg, ActivityPaymentInfo.this);
                                    }
                                } catch (JSONException e) {
                                    MyDialogShow.showDialogErrorFinish("", ActivityPaymentInfo.this);
                                }
                            } else {
                                try {
                                    logUtil.appendLogRequestApi(Config.SALES_HISTORY_DETAIL + " onsuccess");
                                    PrefLibTV.getInstance(ActivityPaymentInfo.this).setSessionKey( response.getString("sessionKey"));
                                    mTranId = response.getString("transactionID");
                                    mTokenL2 = response.getString("tokenL2");
                                    showView(vInfo, true);
                                    fillSalesDataBank(response.getJSONObject("transactionDetail"));
                                } catch (JSONException e) {
                                    Utils.LOGE(tag, "Exception", e);
                                    MyDialogShow.showDialogRetryCancelFinish("", getString(R.string.error_try_again),
                                            ActivityPaymentInfo.this, v -> loadSalesDetailBank(), true);
                                }
                            }
                        } catch (Exception e) {
                            logUtil.appendLogRequestApi(Config.SALES_HISTORY_DETAIL + " exception(timeout)");
                            Utils.LOGE(tag, "Exception", e);
                            MyDialogShow.showDialogErrorReLogin(getString(R.string.SERVICE_ERROR_SESSION_TIMEOUT_L2), ActivityPaymentInfo.this);
                        }
                    }

                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        Utils.LOGE(TAG, "Sales detail error: " + MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2, arg3));
                        logUtil.appendLogRequestApiFail(Config.SALES_HISTORY_DETAIL + " onFailure"+MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2, arg3), arg2);
                        mPgdl.hideLoading();
                        MyDialogShow.showDialogRetryCancelFinish("", getString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT),
                                ActivityPaymentInfo.this, v -> loadSalesDetailBank(), true);
                    }
                });
    }

    private void loadSaleDetailMa(String wfId) {
        mPgdl.showLoading();
        WorkFlow workFlow = new WorkFlow(wfId);
//        StringEntity entity = ApiMultiAcquirerInterface.getInstance().buildStringEntity(workFlow);
        MposRestClient.getInstance(this).postMacq(this, ApiMultiAcquirerInterface.URL_GET_TRANS_DETAIL, workFlow, new MyTextHttpResponseHandler() {

            @Override
            public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                Utils.LOGD(TAG, "TransDetails onFailure() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");
                mPgdl.hideLoading();
                DataError dataError = new DataError();
                dataError.build(statusCode, rawJsonData, getString(R.string.error_ma_default, String.valueOf(statusCode)));
                MyDialogShow.showDialogError(getString(R.string.dialog_error_title, dataError.getErrorCode()), dataError.getMsg(), ActivityPaymentInfo.this, true);

            }

            @Override
            public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                Utils.LOGD(TAG, "TransDetails onSuccess() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonResponse = [" + rawJsonResponse + "]");
                mPgdl.hideLoading();
                DataBaseObj data = MyGson.parseJson(rawJsonResponse, DataBaseObj.class);
                String clearData = CryptoInterface.getInstance().decryptData(data.getData());

                Utils.LOGD(TAG, "TransDetails onSuccess: " + clearData);

                WfDetailRes transItem = MyGson.parseJson(clearData, WfDetailRes.class);
                Utils.LOGD(TAG, "onSuccessApi: transItem.readerSerial=" + transItem.getReaderSerial());
                fillSaleDataMacq(transItem);
            }
        });
    }

    private void gotoSignature(DataReversalLogin dataReversal, String email) {
        Intent i = new Intent(this, ActivityPrePayment.class);

        DataPay dataPay = new DataPay(dataReversal);
        dataPay.setEmail(email);
        if (isTranMultiAcquirer) {
            dataPay.setWfId(transItemMA.getWfId());
        }
        i.putExtra(Intents.EXTRA_DATA_PAY_MP, dataPay);

        startActivityForResult(i, RESULT_CODE_RE_SIGNATURE);
    }

    public void fillSalesDataDomesticInMVisa(JSONObject jo) throws JSONException {
        trxType = JsonParser.getDataJson(jo, "applicationUsageControl");
        initUdid(JsonParser.getDataJson(jo, "udid"));

        transactionRequestId = getIntent().getStringExtra("transactionRequestId");
        holderName = JsonParser.getDataJson(jo,"cardholderName");
        pan = JsonParser.getDataJson(jo,"pan");
        transactionDate = Utils.convertTimestamp(jo.getLong("createdDate"), 3);

        tvCardHolderName.setText(JsonParser.getDataJson(jo,"cardholderName"));
//        tvTransId.setText(JsonParser.getDataJson(jo,"txid"));
        showTransId(JsonParser.getDataJson(jo,"txid"));
        tvTransTime.setText(Utils.convertTimestamp(jo.getLong("createdDate"), 1));
        tvTransDate.setText(Utils.convertTimestamp(jo.getLong("createdDate"), 2));
        tvCardType.setText(JsonParser.getDataJson(jo,"issuerName"));
        tvTid.setText(JsonParser.getDataJson(jo,"tid"));
        tvMid.setText(JsonParser.getDataJson(jo,"mid"));
        showPan();
        tvApprovalCode.setText(getString(R.string.SALES_DETAIL_APPROVAL_CODE) + " " + (jo.has("authCode") ? jo.getString("authCode") : ""));
        amountPay = JsonParser.getDataJson(jo,"amount");
        tvAmount.setText(String.format("%s %s", ConstantsPay.CURRENCY_SPACE_POST, Utils.zenMoney(amountPay)));
//        ((TextView) findViewById(R.id.time)).setText(Utils.convertTimestamp(jo.getLong("createdDate"), 1));

        String desc = "";

        if (jo.has("description")) {
            desc = jo.getString("description").trim();
            itemDescription = desc;
        }
        boolean isServicePay = checkPayServiceByDesc(desc);

        if (PrefLibTV.getInstance(ActivityPaymentInfo.this).getPermitVoid()) {
            btnVoidPay.setVisibility(View.VISIBLE);
        }

        if (isServicePay) {
            btnVoidPay.setVisibility(View.GONE);
            btnQuickReceiMoney.setVisibility(View.GONE);
        } else {
            checkReceiveMoney();
        }

        transactionStatus = jo.getInt("status");
        MyUtils myUtils = new MyUtils();
        myUtils.setViewStatusByType(this, transactionStatus, tvStatus, tvAmount, false);
        boolean canCashierReward = true;
        if (transactionStatus == Constants.TRANS_TYPE_VOID
                || transactionStatus == Constants.TRANS_TYPE_REVERSAL) {
            imvStatus.setBackgroundResource(R.drawable.img_alert_voided);
            btnVoidPay.setVisibility(View.GONE);
            btnQuickReceiMoney.setVisibility(View.GONE);
            canCashierReward = false;
        } else if (transactionStatus == Constants.TRANS_TYPE_SETTLE) {
            btnVoidPay.setVisibility(View.GONE);
            btnQuickReceiMoney.setVisibility(View.GONE);
        } else if (transactionStatus == Constants.TRANS_TYPE_PENDING_SIGNATURE) {
            btnContinue.setVisibility(View.VISIBLE);
            btnSendMail.setVisibility(View.GONE);
            btnQuickReceiMoney.setVisibility(View.GONE);
            canCashierReward = false;
        }

        if (canCashierReward) {
            initCashierReward();
        }
        if (Constants.TRANS_TYPE_REFUND == transactionStatus || isServicePay) {
            btnVoidPay.setVisibility(View.GONE);
        }
    }

    private void checkDescForPayService() {
        if (!TextUtils.isEmpty(udid) && TextUtils.isEmpty(itemDescription) && udid.startsWith(ConstantsPay.PREFIX_UDID_SERVICE)) {
            itemDescription = udid.substring(ConstantsPay.PREFIX_UDID_SERVICE.length()).replace(Constants.CHAR_REPLACE_SPACE_OF_UDID," ");
        }
    }

    private boolean checkPayServiceByDesc(String desc) {
        Utils.LOGD(TAG, "checkPayServiceByDesc: desc=" + desc);
        boolean isServicePay = false;
        if (TextUtils.isEmpty(desc)) {
            isServicePay = ConstantsPay.TRX_TYPE_SERVICE.equals(trxType);
        }
        else {
            String descUpperCase = desc.toUpperCase();

            if (descUpperCase.startsWith(ConstantsPay.PREFIX_DESCRIPTION_SERVICE_PREPAID)
                    || descUpperCase.startsWith(ConstantsPay.PREFIX_DESCRIPTION_SERVICE_POSTPAID)
                    || descUpperCase.startsWith(ConstantsPay.PREFIX_DESCRIPTION_SERVICE_BUY_CARD)
                    || ConstantsPay.TRX_TYPE_SERVICE.equals(trxType)
            ) {
                isServicePay = true;
                if (desc.startsWith("undefined-")) {
                    desc = "";
                }
                else {
                    desc = getDescOfPayService(descUpperCase);
                }
            }
            else if (descUpperCase.startsWith(ConstantsPay.PREFIX_DESCRIPTION_INSTALLMENT)) {
                desc = getString(R.string.pay_installment) + ": " + desc.substring(ConstantsPay.PREFIX_DESCRIPTION_INSTALLMENT.length());
                isPayInstallment = true;
            }
            else if (descUpperCase.startsWith(ConstantsPay.PREFIX_DESCRIPTION_CASHBACK)) {
                isPayCashBack = true;
                desc = desc.substring(ConstantsPay.PREFIX_DESCRIPTION_CASHBACK.length());
            }
            if (!TextUtils.isEmpty(desc)) {
                tvDesc.setText(String.format("%s %s", getString(R.string.SALES_DETAIL_DESC), desc));
            }
        }
        Utils.LOGD(TAG, "checkPayServiceByDesc: isServicePaid=" + isServicePay);
        return isServicePay;
    }

    public void fillSalesDataBank(JSONObject jo) throws JSONException {
        trxType = JsonParser.getDataJson(jo, "trxType");

        transactionRequestId = getIntent().getStringExtra("transactionRequestId");

        initUdid(JsonParser.getDataJson(jo, "udid"));


        String label = jo.getString("applicationLabel");
        MyUtils myUtils = new MyUtils();
        myUtils.setImgThumbByTypeCard(this, label, findViewById(R.id.thumb));

        transactionStatus = jo.getInt("transactionStatus");

        myUtils.setViewStatusByType(this, transactionStatus, tvStatus, tvAmount, false);//Constants.VAYMUONQR.equals(transactionPushType

        amountPay = jo.getString("amountAuthorized");
        holderName = (jo.has("cardHolderName") ? jo.getString("cardHolderName") : "NO NAME");
        pan = jo.getString("maskedPAN");
        transactionDate = jo.getString("transactionDate");

        tvCardHolderName.setText(holderName);
        tvBatchNo.setText(jo.getString("batchNo"));
//        tvTransId.setText(jo.getString("transactionID"));
        showTransId(jo.getString("transactionID"));

        tvTransTime.setText(Utils.convertTimestamp(jo.getLong("transactionDate"), 1));
        tvTransDate.setText(Utils.convertTimestamp(jo.getLong("transactionDate"), 2));
        tvCardType.setText(jo.getString("applicationLabel"));
        tvTid.setText(jo.getJSONObject("application").getString("TID"));
        tvMid.setText(jo.getJSONObject("application").getString("MID"));
        showPan();
        tvApprovalCode.setText(String.format("%s %s", getString(R.string.SALES_DETAIL_APPROVAL_CODE), jo.has("approvalCode") ? jo.getString("approvalCode") : ""));
        tvInvoiceNo.setText(String.format("%s %s", getString(R.string.SALES_DETAIL_RECEIPT_NO), jo.getString("invoiceNumber")));
        tvAmount.setText(String.format("%s %s", ConstantsPay.CURRENCY_SPACE_POST, amountPay));
//        tvTime.setText(Utils.convertTimestamp(jo.getLong("transactionDate"), 1));

        String desc;
        if (jo.has("itemDescription")) {
            desc = jo.getString("itemDescription").trim();
            itemDescription = desc;
        }
        checkDescForPayService();
        boolean isServicePay = checkPayServiceByDesc(itemDescription);

        if (PrefLibTV.getInstance(ActivityPaymentInfo.this).getPermitVoid()) {
            btnVoidPay.setVisibility(View.VISIBLE);
        }

        if (isServicePay) {
            btnQuickReceiMoney.setVisibility(View.GONE);
        } else {
            checkReceiveMoney();
        }

        boolean canCashierReward = true;
        if (transactionStatus == Constants.TRANS_TYPE_VOID
                || transactionStatus == Constants.TRANS_TYPE_REVERSAL) {
            imvStatus.setBackgroundResource(R.drawable.img_alert_voided);
            btnVoidPay.setVisibility(View.GONE);
            btnQuickReceiMoney.setVisibility(View.GONE);
            canCashierReward = false;
        } else if (transactionStatus == Constants.TRANS_TYPE_SETTLE) {
            btnVoidPay.setVisibility(View.GONE);
            btnQuickReceiMoney.setVisibility(View.GONE);
        } else if (transactionStatus == Constants.TRANS_TYPE_PENDING_SIGNATURE) {
            btnContinue.setVisibility(View.VISIBLE);
            btnVoidPay.setVisibility(View.GONE);
            btnSendMail.setVisibility(View.GONE);
            btnQuickReceiMoney.setVisibility(View.GONE);
            canCashierReward = false;
        }

        if (isServicePay) {
            btnVoidPay.setVisibility(View.GONE);
        }

        if (canCashierReward) {
            initCashierReward();
        }
    }

    private void fillSaleDataMacq(WfDetailRes transItem) {
        trxType = transItem.getTrxType();
        snDoTrans = transItem.getReaderSerial();

        showView(vInfo, true);

        initUdid(transItem.getUdid());

        mTranId = transItem.getTxid();
        amountPay = transItem.getAmount();
        holderName = (TextUtils.isEmpty(transItem.getCardHolderName()) ? "NO NAME" : transItem.getCardHolderName());
        pan = transItem.getPan();
        transactionDate = "0";//transItem.getCreatedTimestamp();

        MyUtils myUtils = new MyUtils();
        myUtils.setImgThumbByTypeCard(this, transItem.getIssuerCode(), findViewById(R.id.thumb));

        tvCardHolderName.setText(holderName);
        tvBatchNo.setText(transItem.getBatchNo());
//        tvTransId.setText(transItem.getTxid());
        showTransId(transItem.getTxid());
        long timeCreate = 0;
        try {
            timeCreate = Long.parseLong(transItem.getTransactionDate());
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        tvTransTime.setText(Utils.convertTimestamp(timeCreate, 1));
        tvTransDate.setText(Utils.convertTimestamp(timeCreate, 2));

        tvCardType.setText(transItem.getIssuerCode());
        tvTid.setText(transItem.getTid());
        tvMid.setText(transItem.getMid());
        showPan();
        tvApprovalCode.setText(String.format("%s %s", getString(R.string.SALES_DETAIL_APPROVAL_CODE), TextUtils.isEmpty(transItem.getAuthCode()) ? "" : transItem.getAuthCode()));
        tvInvoiceNo.setText(String.format("%s %s", getString(R.string.SALES_DETAIL_RECEIPT_NO), transItem.getInvoiceNo()));
        tvAmount.setText(String.format("%s %s", ConstantsPay.CURRENCY_SPACE_POST, Utils.zenMoney(amountPay)));

        if (ConstantsPay.TRX_TYPE_SERVICE.equals(transItem.getTrxType())) {
            tvTrxType.setVisibility(View.VISIBLE);
        } else if (ConstantsPay.TRX_TYPE_PAY_MOTO.equals(transItem.getTrxType())) {
            tvTrxType.setVisibility(View.VISIBLE);
            tvTrxType.setText("MOTO");
        } else if (ConstantsPay.TRX_TYPE_PAY_DEPOSIT.equals(transItem.getTrxType())) {
            tvTrxType.setVisibility(View.VISIBLE);
            tvTrxType.setText(getString(R.string.title_deposit));
        } else {
            tvTrxType.setVisibility(View.GONE);
        }
//        tvTime.setText(Utils.convertTimestamp(timeCreate, 1));

        itemDescription = transItem.getDescription();
        checkDescForPayService();
        boolean isServicePay = checkPayServiceByDesc(itemDescription);

        if (PrefLibTV.getInstance(ActivityPaymentInfo.this).getPermitVoid()) {
            btnVoidPay.setVisibility(View.VISIBLE);
        }

        if (ConstantsPay.TRX_TYPE_NORMAL.equals(trxType)) {
            checkReceiveMoney();
        } else {
            btnQuickReceiMoney.setVisibility(View.GONE);
        }
//        if (isServicePay) {
//            btnQuickReceiMoney.setVisibility(View.GONE);
//        }
//        else {
//            checkReceiveMoney();
//        }

        boolean canCashierReward = true;
        if (TextUtils.isEmpty(transItem.getStatus())) {
            tvStatus.setVisibility(View.GONE);
            canCashierReward = false;
            btnContinue.setVisibility(View.GONE);
            btnVoidPay.setVisibility(View.GONE);
            btnSendMail.setVisibility(View.GONE);
            btnQuickReceiMoney.setVisibility(View.GONE);
        }
        else {
            myUtils.setViewStatusByStatus(this, transItem.getStatus(), tvStatus);

            switch (transItem.getStatus()) {
                case Constants.TRANS_STATUS_APPROVED:
//                    btnVoidPay.setVisibility(View.VISIBLE);
                    btnSendMail.setVisibility(View.VISIBLE);
                    break;
                case Constants.TRANS_STATUS_REVERSAL:
                case Constants.TRANS_STATUS_VOIDED:
                    imvStatus.setBackgroundResource(R.drawable.img_alert_voided);
                    btnVoidPay.setVisibility(View.GONE);
                    btnQuickReceiMoney.setVisibility(View.GONE);
                    canCashierReward = false;
                    break;
                case Constants.TRANS_STATUS_SETTLE:
                case Constants.TRANS_STATUS_SETTLED:
                    btnVoidPay.setVisibility(View.GONE);
                    btnQuickReceiMoney.setVisibility(View.GONE);
                    break;
                case Constants.TRANS_STATUS_PENDING_SIGNATURE:
                    btnContinue.setVisibility(View.VISIBLE);
                    btnVoidPay.setVisibility(View.GONE);
                    btnSendMail.setVisibility(View.GONE);
                    btnQuickReceiMoney.setVisibility(View.GONE);
                    canCashierReward = false;
                    break;

            }
        }

        if (isServicePay) {
            btnVoidPay.setVisibility(View.GONE);
        }

        if (canCashierReward) {
            initCashierReward();
        }
    }

    private void showPan() {
        if (pan != null && !pan.isEmpty()) {
            tvCardNumber.setText(CardUtils.getMaskedPan(pan));
        }
        else {
            tvCardNumber.setVisibility(View.GONE);
        }
    }

    private void showTransId(String transId) {
        tvTransId.setText(transId);
        MyTextUtils.makeLinks(tvTransId, new String[]{transId}, new ClickableSpan[]{new ClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                MyTextUtils.pushTextToClipboard(transId, (ClipboardManager) ActivityPaymentInfo.this.getSystemService(CLIPBOARD_SERVICE));
                showToast(getString(R.string.copied));
            }
        }});
    }

    private void checkReceiveMoney() {
        if (DataStoreApp.getInstance().isEnableQuickDrawal()
                && MyUtils.checkMinMaxAmount(amountPay, DataStoreApp.getInstance().getAmountMinQuick(), DataStoreApp.getInstance().getAmountMaxQuick())
//					&& !desc.contains(getString(R.string.pay_installment))
        ) {
            showBtnQuickWithdrawal();
            showVMQuickDraw = true;
        } else {
            btnQuickReceiMoney.setVisibility(View.GONE);
            showVMQuickDraw = false;
        }
    }

    private void initUdid(String udidFromServer) {
        if (paymentInfo != null && !TextUtils.isEmpty(paymentInfo.getUdid())) {
            udid = paymentInfo.getUdid();
        } else {
            udid = udidFromServer;
        }
        Utils.LOGD(tag, "initUdid: curr=" + udid + " udidFromServer=" + udidFromServer);
    }

    private String getDescOfPayService(String desc) {
        String result = getString(R.string.txt_desc_service_pay);
        String type = "";
        if (desc.startsWith(ConstantsPay.PREFIX_DESCRIPTION_SERVICE_PREPAID)) {
            type = getString(R.string.prepay);
            desc = desc.substring(ConstantsPay.PREFIX_DESCRIPTION_SERVICE_PREPAID.length());
        } else if (desc.startsWith(ConstantsPay.PREFIX_DESCRIPTION_SERVICE_POSTPAID)) {
            type = getString(R.string.postpay);
            desc = desc.substring(ConstantsPay.PREFIX_DESCRIPTION_SERVICE_POSTPAID.length());
        } else if (desc.startsWith(ConstantsPay.PREFIX_DESCRIPTION_SERVICE_BUY_CARD)) {
            type = getString(R.string.card_code);
            desc = desc.substring(ConstantsPay.PREFIX_DESCRIPTION_SERVICE_BUY_CARD.length());
        }

        result += "-" + type + "-" + (desc.startsWith(ConstantsPay.PREFIX_MOBILE_DESCRIPTION_SERVICE) ? desc.substring(ConstantsPay.PREFIX_MOBILE_DESCRIPTION_SERVICE.length()) : desc);
        return result;
    }


    private void showDialogConfirmVoid() {
        logUtil.appendLogAction("show screen confirm void");
        final DialogVoid dialogVoid = new DialogVoid();
        dialogVoid.initVariable(amountPay, pan, tvCardType.getText().toString(), tvApprovalCode.getText().toString(), tvInvoiceNo.getText().toString(), tvTransId.getText().toString());
        dialogVoid.setClickListener(d -> {
            logUtil.appendLogAction("select -> void");
            if (isTranMultiAcquirer) {
                voidPaymentMA();
            }
            else {
                voidPayment(dialogVoid.getDialog());
            }
        });
        dialogVoid.show(getSupportFragmentManager(), "BaseDialogFragment");
    }

    public void voidPayment(final Dialog dialog) {
        logUtil.appendLogRequestApi(Config.CONFIRM_VOID + " mTranId=" + mTranId + " amount:" + amountPay);
        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, Config.CONFIRM_VOID);
            jo.put("udid", "0");
            jo.put("readerSerialNo", PrefLibTV.getInstance(ActivityPaymentInfo.this).getSerialNumber());
            jo.put("versionNo", android.os.Build.VERSION.RELEASE);
            jo.put("platform", Config.PLATFORM);
            jo.put("userID", PrefLibTV.getInstance(ActivityPaymentInfo.this).getUserId());
            jo.put("sessionKey", PrefLibTV.getInstance(ActivityPaymentInfo.this).getSessionKey());
            jo.put("transactionID", mTranId);
            jo.put("tokenL2", mTokenL2);
            Utils.LOGD("Data: ", jo.toString());
            entity = new StringEntity(EncodeDecode.doAESEncrypt(jo.toString(), PrefLibTV.getInstance(ActivityPaymentInfo.this).getSessionKey()));
        } catch (Exception e1) {
            Utils.LOGE(tag, "voidPayment: " + e1.getMessage());
        }

        MposRestClient.getInstance(this).post(ActivityPaymentInfo.this, ConstantsPay.getUrlServer(this),
                entity, Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
                    @Override
                    public void onStart() {
                        mPgdl.showLoading();
                        super.onStart();
                    }

                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                        try {
                            mPgdl.hideLoading();
                            JSONObject response = new JSONObject(EncodeDecode.doAESDecrypt(new String(arg2), PrefLibTV.getInstance(ActivityPaymentInfo.this).getSessionKey()));
                            PrefLibTV.getInstance(ActivityPaymentInfo.this).setSessionKey( response.getString("sessionKey"));
                            Utils.LOGD("Confirm void: ", response.toString());
                            if (response.has("error")) {
                                try {
                                    final JSONObject jo = response.getJSONObject("error");
                                    int errCode =  jo.getInt("code");
                                    String msg = String.format("%s %02d: %s", getString(R.string.error), errCode, LibError.getErrorMsg(errCode, ActivityPaymentInfo.this));
                                    logUtil.appendLogRequestApi(Config.CONFIRM_VOID + " error server:" + msg);
                                    if (LibError.isSessionExpired(errCode)) {
                                        MyDialogShow.showDialogErrorReLogin(msg, ActivityPaymentInfo.this);
                                    } else {
                                        MyDialogShow.showDialogErrorFinish(msg, ActivityPaymentInfo.this);
                                    }
                                } catch (JSONException e) {
                                    Utils.LOGE(tag, "Exception", e);
                                    MyDialogShow.showDialogErrorFinish("", ActivityPaymentInfo.this);
                                }
                            } else {
                                logUtil.appendLogRequestApi(Config.CONFIRM_VOID + " onsuccess");
                                pushNotifyVoid(amountPay, pan, holderName);
                                checkUpdateTransToMpos(dialog);
                            }
                        } catch (Exception e) {
                            logUtil.appendLogRequestApi(Config.CONFIRM_VOID + " exception(timeout)");
                            Utils.LOGE(tag, "Exception", e);
                            MyDialogShow.showDialogErrorReLogin(getString(R.string.SERVICE_ERROR_SESSION_TIMEOUT_L2), ActivityPaymentInfo.this);
                        }
                    }

                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        Utils.LOGE(TAG, "Confirm void error: "+MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2, arg3));
                        logUtil.appendLogRequestApiFail(Config.CONFIRM_VOID + " onFailure " + MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2, arg3), arg2);

                        mPgdl.hideLoading();
                        MyDialogShow.showDialogRetryCancel("", getString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT), ActivityPaymentInfo.this,
                                v -> voidPayment(dialog), true);
                    }
                });
    }

    private void voidPaymentMA() {
        mPgdl.showLoading();
        logUtil.appendLogRequestApi("do-confirm-void wfid=" + transItemMA.getWfId() + " amount:" + amountPay);
        WorkFlow workFlow = new WorkFlow(transItemMA.getWfId());
//        StringEntity entity = ApiMultiAcquirerInterface.getInstance().buildStringEntity(workFlow);
        MposRestClient.getInstance(this).postMacq(this, ApiMultiAcquirerInterface.URL_DO_VOID, workFlow, new MyTextHttpResponseHandler() {

            @Override
            public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                Utils.LOGD(TAG, "voidMultiAcquirer onFailure() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");

                mPgdl.hideLoading();
                DataError dataError = new DataError();
                dataError.build(statusCode, rawJsonData, getString(R.string.error_ma_default, String.valueOf(statusCode)));
                MyDialogShow.showDialogError(getString(R.string.dialog_error_title, dataError.getErrorCode()), dataError.getMsg(), ActivityPaymentInfo.this, true);
                logUtil.appendLogRequestApi("do-confirm-void wfid=" + transItemMA.getWfId() + " -> fail: "+dataError.getMsg());
            }

            @Override
            public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                Utils.LOGD(TAG, "voidMultiAcquirer onSuccess() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonResponse = [" + rawJsonResponse + "]");
                mPgdl.hideLoading();

                DataBaseObj data = MyGson.parseJson(rawJsonResponse, DataBaseObj.class);
                String clearData = CryptoInterface.getInstance().decryptData(data.getData());

                logUtil.appendLogRequestApi("do-confirm-void wfid=" + transItemMA.getWfId() + " -> success: " + clearData);
                Utils.LOGD(TAG, "voidMultiAcquirer onSuccess: " + clearData);
                if (statusCode == HttpStatus.SC_OK) {
                    // todo: need to new lib sdk
                    boolean printVoidReceipt = false;
                    if (DevicesUtil.isDeviceSupportPrint() && PrefLibTV.getInstance(ActivityPaymentInfo.this)
                                    .get(PrefLibTV.permitPrintVoidTrans, Boolean.class, false)) {
                        printVoidReceipt = true;
                    }
                    if (printVoidReceipt) {
                        txId = mTranId;
                        processPrintReceipt(PRINT_VOID_TRANS);
                        setCallbackPrinter(() -> {
                            finishSuccess();
                        });
                    }
                    else {
                        finishSuccess();
                    }
                }
                else {
                    DataError dataError = new DataError();
                    dataError.build(statusCode, clearData, getString(R.string.error_ma_default, String.valueOf(statusCode)));
                    MyDialogShow.showDialogError(getString(R.string.dialog_error_title, dataError.getErrorCode()), dataError.getMsg(), ActivityPaymentInfo.this, true);
                }
            }
        });
    }

    @Override
    public StringEntity buildStringEntityReceipt() {
        return null;
    }

    private void pushNotifyVoid(String amountPay, String pan, String holderName) {
        NotifyPushController notifyController = new NotifyPushController(this);
        notifyController.pushNotify(NotifyPushController.NOTIFY_PAY_VOID, amountPay, pan, holderName);
    }

    private void checkUpdateTransToMpos(Dialog dialog) {
        if (isPayInstallment || isPayCashBack) {
            updateTransactionStatusInMpos(dialog, udid, Utils.getOnlyNumber(amountPay), mTranId);
        } else {
            checkSendEmailToMerchant(dialog);
        }
    }

    private void updateTransactionStatusInMpos(final Dialog dialog, String udid, String amount, String txId) {
        logUtil.appendLogRequestApi(Config.UPDATE_TRANSACTION_STATUS + ": " + udid);
        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, Config.UPDATE_TRANSACTION_STATUS);
            if (!TextUtils.isEmpty(udid)) {
                jo.put("udid", udid);
            }
            jo.put("amount", amount);

            jo.put("status", Constants.STATUS_TRANS_VOIDED);
            jo.put("txId", txId);
            jo.put("muid", PrefLibTV.getInstance(this).getUserId());
            Utils.LOGD("Data: ", jo.toString());

            entity = new StringEntity(jo.toString());
        } catch (Exception e) {
            Utils.LOGE(tag, "Exception", e);
        }

        MposRestClient.getInstance(this).post(this, Config.URL_GATEWAY_API, entity, Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
            @Override
            public void onStart() {
                mPgdl.showLoading();
                super.onStart();
            }

            @Override
            public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                logUtil.appendLogRequestApiFail(Config.UPDATE_TRANSACTION_STATUS + " onFailure", arg2);
                mPgdl.hideLoading();
                checkSendEmailToMerchant(dialog);
            }

            @Override
            public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                mPgdl.hideLoading();
                String msgError = null;
                try {
                    JsonParser jsonParser = new JsonParser();
                    BaseObjJson errorBean = new BaseObjJson();
                    JSONObject jRoot = new JSONObject(new String(arg2));
                    Utils.LOGD(tag, "-updateTransactionStatusInMpos:" + jRoot);
                    jsonParser.checkHaveError(jRoot, errorBean);
                    if (Config.CODE_REQUEST_SUCCESS.equals(errorBean.code)) {
                        logUtil.appendLogRequestApi(Config.UPDATE_TRANSACTION_STATUS + " success");
                    } else {
                        msgError = TextUtils.isEmpty(errorBean.message) ?
                                getString(R.string.error_get_info_merchant) : errorBean.message;
                        logUtil.appendLogRequestApi(Config.UPDATE_TRANSACTION_STATUS + " error:" + msgError);
                    }
                } catch (Exception e) {
                    logUtil.appendLogRequestApi(Config.UPDATE_TRANSACTION_STATUS + " Exception:" + e.getMessage());
                    msgError = getString(R.string.error_try_again);
                    Utils.LOGE(tag, "Exception", e);
                }
                if (!TextUtils.isEmpty(msgError)) {
                    mToast.showToast(msgError);
                }
                checkSendEmailToMerchant(dialog);
            }
        });
    }

    private void checkSendEmailToMerchant(Dialog dialog) {
        try {
            if (dialog != null) dialog.dismiss();
        } catch (Exception e) {
            Utils.LOGE(tag, "Exception", e);
        }

        String email = PrefLibTV.getInstance(this).getEmailMerchant();
        if (PrefLibTV.getInstance(this).get(PrefLibTV.sendTrxReceipt, Boolean.class, false)  && !TextUtils.isEmpty(email)) {
//        if (DataStoreApp.getInstance().getSendEmailMerchant() && !TextUtils.isEmpty(email)) {
            sendEmailByType(email);
        } else {
            finishSuccess();
        }
    }

    private void finishSuccess() {
        setResult(3);
        finish();
    }

    public void sendEmailByType(String email) {
        if (!GetData.CheckInternet(getApplicationContext())) {
            showToastResultSendEmail(true);
            finishSuccess();
            return;
        }
        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, Config.SEND_RECEIPT);
            jo.put("udid", "0");
            jo.put("readerSerialNo", PrefLibTV.getInstance(getApplicationContext()).getSerialNumber());
            jo.put("versionNo", android.os.Build.VERSION.RELEASE);
            jo.put("platform", Config.PLATFORM);
            jo.put("userID", PrefLibTV.getInstance(getApplicationContext()).getUserId());
            jo.put("sessionKey", PrefLibTV.getInstance(getApplicationContext()).getSessionKey());
            jo.put("transactionID", mTranId);
            jo.put("email", email);
            Utils.LOGD(TAG, "Data: "+ jo);
            //			entity = new StringEntity(jo.toString());
            entity = new StringEntity(EncodeDecode.doAESEncrypt(jo.toString(), PrefLibTV.getInstance(getApplicationContext()).getSessionKey()));
        } catch (Exception e1) {
            Utils.LOGE(tag, "sendEmailByType: " + e1.getMessage());
        }

        MposRestClient.getInstance(this).post(this, ConstantsPay.getUrlServer(this),
//				MposRestClient.getInstance(this).post(this, ConstantsPay.getUrlServer(context),
                entity, Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
                    @Override
                    public void onStart() {
                        mPgdl.showLoading();
                        super.onStart();
                    }

                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                        boolean isError = false;
                        try {
                            mPgdl.hideLoading();
                            JSONObject response = new JSONObject(EncodeDecode.doAESDecrypt(new String(arg2), PrefLibTV.getInstance(getApplicationContext()).getSessionKey()));
                            PrefLibTV.getInstance(getApplicationContext()).setSessionKey(response.getString("sessionKey"));
                            Utils.LOGD(TAG, "Send receipt response: "+ response);
                            if (response.has("error")) {
                                isError = true;
                            }
                        } catch (Exception e) {
                            Utils.LOGE(tag, "Exception", e);
                            isError = true;
                        }

                        showToastResultSendEmail(isError);
                        finishSuccess();
                    }

                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        Utils.LOGE(TAG, "Send receipt error: " + MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2, arg3));
                        mPgdl.hideLoading();
                        showToastResultSendEmail(true);
                        finishSuccess();
                    }
                });
    }

    private void showToastResultSendEmail(boolean isError) {
        if (isError) {
            mToast.showToast(getString(R.string.error_send_email_invoice_merchant));
        } else {
            mToast.showToast(getString(R.string.success_send_email_invoice_merchant));
        }
    }

    private void showToast(String msg) {
        if (!TextUtils.isEmpty(msg)) {
            Toast.makeText(this, msg, Toast.LENGTH_LONG).show();
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        Utils.LOGD(tag, "onActivityResult() called with: requestCode = [" + requestCode + "], resultCode = [" + resultCode + "], data = [" + data + "]");
        if (requestCode == RESULT_CODE_RE_SIGNATURE && resultCode == RESULT_OK) {
            finishSuccess();
        }
        else if (requestCode == REQUEST_ENABLE_BT) {
//            mPgdl.hideLoading();
            if (resultCode == RESULT_OK) {
                showDeviceDspread();
            }
        }
        else if (requestCode == REQUEST_DEVICE_DSPREAD) {
//            mPgdl.hideLoading();
            if (resultCode == RESULT_OK && data != null && data.getExtras() != null) {
                String address = data.getExtras().getString(DeviceListActivity.EXTRA_DEVICE_ADDRESS);
                String serialnumber = data.getExtras().getString(DeviceListActivity.EXTRA_DEVICE_SERIALNUMBER);
                handlePairPr02(address, serialnumber);
            }
        }

    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (logUtil != null) {
            logUtil.saveLog();
        }
    }

    private void showBtnQuickWithdrawal() {
        btnQuickReceiMoney.setVisibility(View.VISIBLE);
        btnQuickReceiMoney.setEnabled(true);
        btnQuickReceiMoney.setOnClickListener(v -> showDialogQuickWithdrawMoney());
    }

    private void showDialogQuickWithdrawMoney() {
        DialogQuickWithdrawMoney mdialog = new DialogQuickWithdrawMoney();
        mdialog.setCancelable(false);

        mdialog.initVariable(amountPay, mTranId, DataStoreApp.getInstance(), mPgdl, logUtil, mToast, null);
        mdialog.setCardPayment(isTranMultiAcquirer);

        mdialog.setClickListener(new DialogQuickWithdrawMoney.OnMyClickListener() {
            @Override
            public void clickCancel(DialogFragment dialogFragment) {
                dialogFragment.dismiss();
            }

            @Override
            public void backToMain() {
                backToMainAfterQuickWithdrawal();
            }
        });
        mdialog.show(getSupportFragmentManager(), DialogQuickWithdrawMoney.class.getName());
    }

    public void backToMainAfterQuickWithdrawal() {
        btnQuickReceiMoney.setVisibility(View.GONE);

//        setResult(2);
//        finish();
    }


    //----------------------------- DETAIL PAYMENT FOR MVISA ---------------------------------------
    //--begin--
    int errorCode;

    private void loadDataDetailMpos(final String txid) {
        Utils.LOGD(tag, "------- loadDataDetailMpos ----->>> txid: " + txid);

        if (!GetData.CheckInternet(this)) {
            MyDialogShow.showDialogRetry(null, getString(R.string.check_internet), this, v -> loadDataDetailMpos(txid));
            return;
        }

        logUtil.appendLogRequestApi(Config.URL_GATEWAY_API);
        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, "TRANSACTION_VIEW");
            jo.put("txid", txid);
            jo.put("muid", PrefLibTV.getInstance(this).getUserId());
            Utils.LOGD(tag, "REQUEST: " + jo);
            entity = new StringEntity(jo.toString());
        } catch (Exception e) {
            Utils.LOGE(tag, "Exception", e);
        }

        MposRestClient.getInstance(ActivityPaymentInfo.this).post(ActivityPaymentInfo.this, Config.URL_GATEWAY_API, entity,
                Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
                    @Override
                    public void onStart() {
                        mPgdl.showLoading();
                        super.onStart();
                    }

                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        Utils.LOGD(tag, "onFailure: " + arg3.getMessage());
                        logUtil.appendLogRequestApiFail(Config.URL_GATEWAY_API + " onFailure", arg2);
                        mPgdl.hideLoading();
                        finishSuccess();
                    }

                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                        mPgdl.hideLoading();
                        String msg = null;
                        try {
//							JsonParser jsonParser = new JsonParser();
                            Utils.LOGE(tag, "RESPONSE: " + new String(arg2));
                            JSONObject jRoot = new JSONObject(new String(arg2));

                            if (jRoot.has("error")) {
                                final JSONObject jError = jRoot.getJSONObject("error");
                                errorCode = jError.getInt("code");
                                msg = jError.getString("message");
                                logUtil.appendLogAction("TRANSACTION_VIEW: error=" + errorCode);

                                //  ERROR!!!
                                final MposDialog mposDialogError = MyUtils.initDialogGeneralError(
                                        ActivityPaymentInfo.this, errorCode, msg, ActivityPaymentInfo.class.getName());
                                mposDialogError.setOnClickListenerDialogClose(v -> {
                                    mposDialogError.dismiss();
                                    finishSuccess();
                                });
                                mposDialogError.show();

                            } else {
                                //  SUCCESSFULLY!!!
                                boolean enableQuickDraw = false;

                                amountPay = JsonParser.getDataJson(jRoot, "amount");
                                String thoiGianTao = JsonParser.getDataJson(jRoot, "createdDate");
                                String soThe = JsonParser.getDataJson(jRoot, "pan");
                                mTranId = JsonParser.getDataJson(jRoot, "txid");
                                String tenChuThe = JsonParser.getDataJson(jRoot, "cardholderName");
                                String loaiThe = JsonParser.getDataJson(jRoot, "issuerCode");
                                String status = JsonParser.getDataJson(jRoot, "status");
                                String description = JsonParser.getDataJson(jRoot, "description");
                                String voidReason = JsonParser.getDataJson(jRoot, "voidReason");
                                String transactionPushType = JsonParser.getDataJson(jRoot, "transactionPushType");
                                String issuerBank = JsonParser.getDataJson(jRoot, "issuerBank");
                                String transactionType = JsonParser.getDataJson(jRoot, "transactionType");
                                String qrStaticType = JsonParser.getDataJson(jRoot, "qrStaticType");
                                String remark = JsonParser.getDataJson(jRoot, "remark");
                                String isCreateQuickWithdraw = JsonParser.getDataJson(jRoot, "isCreateQuickWithdraw");
                                if (isCreateQuickWithdraw.equals("true")) {
                                    enableQuickDraw = true;
                                }
                                String errorMsg = "";
                                if (jRoot.has("transactionPush")) {
                                    JSONObject transactionPush = jRoot.getJSONObject("transactionPush");
                                    String errorCode = JsonParser.getDataJson(transactionPush, "errorCode");
                                    String errorMessage = JsonParser.getDataJson(transactionPush, "errorMessage");
                                    if (!TextUtils.isEmpty(errorCode)) errorMsg += getString(R.string.error_code) + " " + errorCode;
                                    if (!TextUtils.isEmpty(errorMessage)) errorMsg += ": " + errorMessage;
                                }

                                String fullDes = description;
                                if (Constants.LINKCARD.equals(transactionPushType)) {
                                    fullDes =  issuerBank + " - " + description;
                                }

                                if (jRoot.has("transactionInstallment")) {
                                    JSONObject transactionInstallment = jRoot.getJSONObject("transactionInstallment");
                                    String customerMobile = JsonParser.getDataJson(transactionInstallment, "customerMobile");
                                    String period = JsonParser.getDataJson(transactionInstallment, "period");
                                    String periodType = JsonParser.getDataJson(transactionInstallment, "periodType");
                                    if ("month".equals(periodType)) {
                                        fullDes = getString(R.string.payment_period) + " " + String.format("%s %s", period, getString(R.string.month)) + (TextUtils.isEmpty(fullDes) ? "" : (" - " + fullDes));
                                    } else if ("day".equals(periodType)) {
                                        fullDes = getString(R.string.payment_period) + " " + String.format("%s %s", period, getString(R.string.day)) + (TextUtils.isEmpty(fullDes) ? "" : (" - " + fullDes));
                                    } else {
                                        fullDes = getString(R.string.payment_period) + " " + period + (TextUtils.isEmpty(fullDes) ? "" : (" - " + fullDes));
                                    }
                                    tvVMCustomerPhone.setText(customerMobile);
                                }

                                tvTenChuThe.setText(tenChuThe);
                                tvThanhToanQua.setText(getByLanguageLocal(jRoot));
                                tvLoaiThe.setText(loaiThe);
                                tvSoTien.setText(String.format("%s %s", Utils.zenMoney(amountPay), ConstantsPay.CURRENCY_SPACE_PRE));
                                tvMaGiaoDich.setText(mTranId);
                                tvThoiGian.setText(MyUtils.formatTimeTransaction(thoiGianTao));
                                tvSoThe.setText(soThe);

                                if (!Constants.VAYMUONQR.equals(transactionPushType) && !Constants.LINKCARD.equals(transactionPushType)) {
                                    tvMvisaTypeQR.setText(MyUtils.getNameTypeQR(qrStaticType));
                                    tvMvisaDescBank.setText(remark);
                                    tvMvisaDescDvcnt.setText(description);
                                }

                                if ((String.valueOf(Constants.TRANS_TYPE_VOID).equals(status) || String.valueOf(Constants.TRANS_TYPE_REFUND).equals(status))
                                        && !TextUtils.isEmpty(voidReason)) {
                                    rlVMVoidReason.setVisibility(View.VISIBLE);
                                    tvVMVoidReason.setText(String.format("%s: %s", getString(R.string.void_reason), voidReason));
                                } else {
                                    rlVMVoidReason.setVisibility(View.GONE);
                                }

                                tvVMAmount.setText(String.format("%s %s", Utils.zenMoney(amountPay), ConstantsPay.CURRENCY_SPACE_PRE));
                                tvVMPaymentMethod.setText(loaiThe);
                                tvVMCustomerName.setText(tenChuThe);
                                if (String.valueOf(Constants.TRANS_TYPE_PENDING_TC).equals(status)) {
                                    rlVMStatusKTExplain.setVisibility(View.GONE);
                                    rlVMStatusExplain.setVisibility(View.VISIBLE);
                                } else if (String.valueOf(Constants.TRANS_TYPE_SETTLE).equals(status)) {
                                    rlVMStatusKTExplain.setVisibility(View.VISIBLE);
                                    rlVMStatusExplain.setVisibility(View.GONE);
                                } else {
                                    rlVMStatusKTExplain.setVisibility(View.GONE);
                                    rlVMStatusExplain.setVisibility(View.GONE);
                                }
                                if (!TextUtils.isEmpty(status)) {
                                    tvVMStatus.setText(MyUtils.getStatusNameType(ActivityPaymentInfo.this, Integer.parseInt(status), Constants.VAYMUONQR.equals(transactionPushType)));
                                    tvVMStatus.setBackground(MyUtils.getStatusBackgroundType(ActivityPaymentInfo.this, Integer.parseInt(status), Constants.VAYMUONQR.equals(transactionPushType)));
                                }
                                tvVMTransCode.setText(mTranId);
                                tvVMTransTime.setText(MyUtils.formatTimeTransaction(thoiGianTao));
                                tvVMPaymentContent.setText(fullDes);

                                showView(vInfo, true);
                                initUdid("");
//                                checkReceiveMoney();
                                initCashierReward();

                                if (Constants.VAYMUONQR.equals(transactionPushType)) {
                                    btnQuickReceiMoney.setVisibility(View.GONE);
                                }
                                if (Constants.VAYMUONQR.equals(transactionPushType)
                                        && (String.valueOf(Constants.TRANS_TYPE_PENDING_TC).equals(status)
                                                || String.valueOf(Constants.TRANS_TYPE_SETTLE).equals(status)
                                            )
                                ) {
                                    llBtnVaymuon.setVisibility(View.VISIBLE);
                                    if (String.valueOf(Constants.TRANS_TYPE_SETTLE).equals(status) && showVMQuickDraw) {
                                        btnVMQuickReceiMoney.setVisibility(View.GONE); // Tạm ẩn
                                    } else {
                                        btnVMQuickReceiMoney.setVisibility(View.GONE);
                                    }

                                    if (String.valueOf(Constants.TRANS_TYPE_PENDING_TC).equals(status)) {
                                        btnVMCheckStatus.setVisibility(View.VISIBLE);
                                    } else {
                                        btnVMCheckStatus.setVisibility(View.GONE);
                                    }
                                }

                                // Vimo link
                                if (showVMQuickDraw && Constants.LINKCARD.equals(transactionPushType) && (String.valueOf(Constants.TRANS_TYPE_SUCCESS).equals(status) || String.valueOf(Constants.TRANS_TYPE_SETTLE).equals(status))) {
                                    btnQuickReceiMoney.setVisibility(View.GONE);
//                                    llBtnVimoLink.setVisibility(View.VISIBLE);
                                }
                                tvLinkAmount.setText(String.format("%s %s", Utils.zenMoney(amountPay), ConstantsPay.CURRENCY_SPACE_PRE));
                                tvLinkCardName.setText(tenChuThe);
                                tvLinkCardType.setText(loaiThe);
                                tvLinkCardNumber.setText(soThe.replaceAll("X", "*"));
                                if (!TextUtils.isEmpty(status)) {
                                    tvLinkStatus.setText(MyUtils.getStatusNameType(ActivityPaymentInfo.this, Integer.parseInt(status), Constants.VAYMUONQR.equals(transactionPushType)));
                                    tvLinkStatus.setBackground(MyUtils.getStatusBackgroundType(ActivityPaymentInfo.this, Integer.parseInt(status), Constants.VAYMUONQR.equals(transactionPushType)));
                                }
                                tvLinkTransCode.setText(mTranId);
                                tvLinkTimeCreate.setText(MyUtils.formatTimeTransaction(thoiGianTao));
                                tvLinkContentPayment.setText(fullDes);

                                // enter card
                                if (Constants.LINKCARD.equals(transactionPushType) && Constants.NORMAL.equals(transactionType)
                                        && (String.valueOf(Constants.TRANS_TYPE_FAILED).equals(status) || String.valueOf(Constants.TRANS_TYPE_PROCESSING).equals(status))) {
                                    btnQuickReceiMoney.setVisibility(View.GONE);
                                    if (!TextUtils.isEmpty(errorMsg)) {
                                        if (String.valueOf(Constants.TRANS_TYPE_PROCESSING).equals(status)) {
                                            ivPolygon.setColorFilter(ContextCompat.getColor(ActivityPaymentInfo.this, R.color.orange_overlay));
                                            tvLinkPendingReason.setBackground(ContextCompat.getDrawable(ActivityPaymentInfo.this, R.drawable.bg_status_transaction_orange_overlay));
                                            tvLinkPendingReason.setTextColor(ContextCompat.getColor(ActivityPaymentInfo.this, R.color.orange_1));
                                        } else {
                                            ivPolygon.setColorFilter(ContextCompat.getColor(ActivityPaymentInfo.this, R.color.red_overlay));
                                            tvLinkPendingReason.setBackground(ContextCompat.getDrawable(ActivityPaymentInfo.this, R.drawable.bg_status_transaction_red_overlay));
                                            tvLinkPendingReason.setTextColor(ContextCompat.getColor(ActivityPaymentInfo.this, R.color.red_1));
                                        }
                                        tvLinkPendingReason.setText(errorMsg);
                                        llLinkPendingReason.setVisibility(View.VISIBLE);
                                    }
                                }
                                if (Constants.INSTALLMENT.equals(transactionType)) {
                                    tvLinkPaymentMethod.setText(getString(R.string.type_vimolink));
                                } else if (Constants.NORMAL.equals(transactionType)) {
                                    btnQuickReceiMoney.setVisibility(View.GONE);
                                    tvLinkPaymentMethod.setText(getString(R.string.type_vimonormal));
                                    tvLinkContentPayment.setText(String.format("%s: %s", getString(R.string.description), description));
                                }

                                // isDomesticInMVisa
                                if (isDomesticInMVisa) {
                                    fillSalesDataDomesticInMVisa(jRoot);
                                }

                                if (enableQuickDraw) {
                                    showBtnQuickWithdrawal();
                                }

                                if (isSupportPrintQR) {
                                    if (String.valueOf(Constants.TRANS_TYPE_SETTLE).equals(status)) {
                                        dataQrPrint = new DataQrPrint();
                                        dataQrPrint.setTid(PrefLibTV.getInstance(getApplicationContext()).getTId());
                                        dataQrPrint.setMid(PrefLibTV.getInstance(getApplicationContext()).getMId());
                                        dataQrPrint.setRrn(JsonParser.getDataJson(jRoot, "rrn",""));
                                        dataQrPrint.setAmount(amountPay);
                                        dataQrPrint.setTimeTrans(thoiGianTao);

                                        dataQrPrint.setMcName(PrefLibTV.getInstance(getApplicationContext()).get(PrefLibTV.BUSINESS_NAME, String.class));
                                        dataQrPrint.setMcAddress(PrefLibTV.getInstance(getApplicationContext()).get(PrefLibTV.BUSINESS_ADDRESS, String.class));

                                        dataQrPrint.setTxId(mTranId);
                                        dataQrPrint.setIssuerName(JsonParser.getDataJson(jRoot, "issuerName",""));
                                        dataQrPrint.setAuthCode(JsonParser.getDataJson(jRoot, "authCode",""));
                                    }
                                    else {
                                        btnSendMail.setVisibility(View.GONE);
                                    }
                                }
                            }
                        } catch (Exception e) {
                            logUtil.appendLogRequestApi(Config.URL_GATEWAY_API + " Exception:" + e.getMessage());
                            Utils.LOGE(tag, "Exception", e);
                            finishSuccess();
                        }

                        if (!TextUtils.isEmpty(msg)) {
                            logUtil.appendLogException(msg);
                        }
                    }
                });
    }

    private String getByLanguageLocal(JSONObject jsonObject) {
        if ("en".equals(Locale.getDefault().getLanguage())) {
            return JsonParser.getDataJson(jsonObject, "issuerNameEn");
        } else {
            return JsonParser.getDataJson(jsonObject, "issuerName");
        }
    }

//    private boolean checkQrTransCanWithdraw(String issuerCode) {
//        if (Constants.ISSUER_CODE_RTN_QR.contains("," + issuerCode + ",")) {
//            return true;
//        }
//        return false;
//    }

    //---end---

    //** cashier reward
    private void initCashierReward() {
//	    if (paymentInfo.getFeedbackStatus())
        vCashierReward = new ViewCashierReward(this, vRootCashierReward, this::registerFeedbackTrans);
        if (!DataStoreApp.getInstance().isCanFeedback() || paymentInfo == null || Constants.STATUS_CR_DENIED.equals(paymentInfo.getFeedbackStatus())) {
            vRootCashierReward.setVisibility(View.GONE);
        } else if (paymentInfo != null && !TextUtils.isEmpty(paymentInfo.getMobileUserPhone())) {
            vCashierReward.showMobileRegistered(paymentInfo.getFeedbackStatus(), paymentInfo.getMobileUserPhone());
            vRootCashierReward.setVisibility(View.VISIBLE);
        } else if (paymentInfo != null && Constants.STATUS_CR_PENDING.equals(paymentInfo.getFeedbackStatus())) {
            vCashierReward.setShowBtnConfirm(true);
            vCashierReward.checkFeedback(DataStoreApp.getInstance(), amountPay);
        }
    }

    public void registerFeedbackTrans(final String mobileFeedback) {
        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, Config.FEEDBACK_TRANSACTION);

            jo.put("description", itemDescription);
            jo.put("udid", udid);
            jo.put("deviceIdentifier", DataStoreApp.getInstance().getRegisterId());
            jo.put("muid", PrefLibTV.getInstance(this).getUserId());
            jo.put("merchantId", PrefLibTV.getInstance(this).getMerchantsId());
            jo.put("amount", amountPay);
            jo.put("mobileUserPhone", mobileFeedback);
            Utils.LOGD(tag, "REQ FEEDBACK_TRANSACTION: " + jo);
            entity = new StringEntity(jo.toString());

            logUtil.appendLogRequestApi(Config.FEEDBACK_TRANSACTION + " a:" + amountPay + " m:" + mobileFeedback);
        } catch (Exception ex) {
            Utils.LOGE(tag, "Exception: " + ex.getMessage());
        }

        MposRestClient.getInstance(this).post(this, Config.URL_GATEWAY_API, entity, Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
            @Override
            public void onStart() {
                mPgdl.showLoading();
                super.onStart();
            }

            @Override
            public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                String msgError = null;
                try {
                    mPgdl.hideLoading();
                    JSONObject response = new JSONObject(new String(arg2));
                    Utils.LOGD(tag, "RES FEEDBACK_TRANSACTION: " + response);

                    JSONObject jsonObjectErr = new JSONObject(JsonParser.getDataJson(response, "error"));
                    String responseCode = JsonParser.getDataJson(jsonObjectErr, "code");
                    String responseMsg = JsonParser.getDataJson(jsonObjectErr, "message");

                    Utils.LOGD(tag, "responseCode:" + responseCode);
                    Utils.LOGD(tag, "responseMsg:" + responseMsg);

                    if (!responseCode.equals(Config.CODE_REQUEST_SUCCESS)) {
                        String msg = getString(R.string.error) + " " + responseCode + ": " + responseMsg;
                        Utils.LOGD(tag, "msg:" + msg);
                        logUtil.appendLogRequestApi(Config.FEEDBACK_TRANSACTION + " Error on Server: " + msg);
                        if (responseCode.equals("8003")) {
                            MyDialogShow.showDialogInfo(ActivityPaymentInfo.this, msg, true);
                        } else {
                            msgError = msg;
                        }
                    } else {
                        DataStoreApp.getInstance().saveMobileUserPhone(mobileFeedback);
                        logUtil.appendLogRequestApi(Config.FEEDBACK_TRANSACTION + " onSuccessfully!!!");
                        MyDialogShow.showDialogSuccess(ActivityPaymentInfo.this, responseMsg, false, v -> finishSuccess());
                    }
                } catch (Exception e) {
                    logUtil.appendLogRequestApi(Config.FEEDBACK_TRANSACTION + " Error on parse: " + e.getMessage());
                    msgError = getString(R.string.error_default_contact_hotline);
                    Utils.LOGE(tag, "Exception", e);
                }
                if (!TextUtils.isEmpty(msgError)) {
                    MyDialogShow.showDialogError(msgError, ActivityPaymentInfo.this);
                }
            }

            @Override
            public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                Utils.LOGD(tag, "onFailure");
                logUtil.appendLogRequestApiFail(Config.FEEDBACK_TRANSACTION + " onFailure", arg2);
                mPgdl.hideLoading();
                MyDialogShow.showDialogRetryCancel("", getString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT), ActivityPaymentInfo.this, v -> registerFeedbackTrans(mobileFeedback), true);
            }
        });
    }

    @Override
    public void onClickSkipCancelTrans() {

    }

    @Override
    public void onClickConfirmCancelTrans(String content) {
        doVoidVaymuon(content);
    }

    @Override
    public void onDismissSuccessDialog() {
        llBtnVaymuon.setVisibility(View.GONE);
        llBtnVimoLink.setVisibility(View.GONE);
        llIncludeMVISA.setVisibility(View.GONE);
        llIncludeVaymuon.setVisibility(View.GONE);
        if (getIntent().getExtras() != null && getIntent().getExtras().containsKey(KEY_TXID)) {
            String txId = getIntent().getStringExtra(KEY_TXID);
            if (!TextUtils.isEmpty(txId)) {
                setLayoutTransFromMpos(true);
                loadDataDetailMpos(txId);
            }
        }
        needReloadOnBack = true;
    }



    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        handleRequestPermission(requestCode, permissions, grantResults);
    }
    public void handleRequestPermission(int requestCode, String[] permissions, int[] grantResults) {
        if (requestCode == requestCodePermissionBluetooth &&
                ((grantResults.length == 1 && grantResults[0] != PackageManager.PERMISSION_GRANTED)
                        ||grantResults.length == 2 && (grantResults[0] != PackageManager.PERMISSION_GRANTED || grantResults[1] != PackageManager.PERMISSION_GRANTED))
        ){
            MyDialogShow.showDialogWarning(this, getString(R.string.msg_warning_denial_permission_bluetooth), false);
        }
        else {
            initBluetoothAndDevice();
        }
    }

    private void initBluetoothAndDevice() {

        BluetoothAdapter btAdapter = BluetoothAdapter.getDefaultAdapter();
        if (btAdapter != null) {
            boolean havePermissionBluetooth = true;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S && !UtilsSystem.checkHaveBluetoothPermission(this)) {

                havePermissionBluetooth = false;
                requestBluetoothPermission();
            }
            if (havePermissionBluetooth) {
                if (btAdapter.isEnabled()) {
                    showDeviceDspread();
                } else {
                    UtilsSystem.enableBluetooth(this, REQUEST_ENABLE_BT);
                }
            }
        } else {
            showToast(getString(R.string.msg_bluetooth_is_not_supported));
        }
    }

    private void requestBluetoothPermission() {
        Toast.makeText(this, getString(R.string.msg_request_permission_bluetooth), Toast.LENGTH_LONG).show();
        UtilsSystem.requestBluetoothPermission(this, requestCodePermissionBluetooth);
    }

    private void showDeviceDspread() {
        Intent intentRequestDevice = new Intent(this, DeviceListActivity.class);
        intentRequestDevice.putExtra(DeviceListActivity.EXTRA_IS_CONNECT_DEVICE, true);
        intentRequestDevice.putExtra(DeviceListActivity.EXTRA_AUTO_CONNECT, true);
        intentRequestDevice.putExtra(DeviceListActivity.EXTRA_DISCONNECT_WHEN_DESTROY, true);
        intentRequestDevice.putExtra(DeviceListActivity.EXTRA_SHOW_IMG_INFO, true);
        intentRequestDevice.setFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT);
        startActivityForResult(intentRequestDevice, REQUEST_DEVICE_DSPREAD);
    }


    private void handlePairPr02(String bleAddress, String serialnumber) {
        if (TextUtils.isEmpty(snDoTrans) || snDoTrans.equals(serialnumber)) {
            if (serialnumber.equals(snDoTrans)) {
                PrefLibTV.getInstance(this).setBluetoothAddress(bleAddress);
                PrefLibTV.getInstance(this).setSerialNumber(serialnumber);
                PrefLibTV.getInstance(this).setFlagDevices(ConstantsPay.DEVICE_DSPREAD);
            }
            showDialogConfirmVoid();
        }
        else {
            MyDialogShow.showDialogError(getString(R.string.title_incorrect_card_reader),
                    getString(R.string.warning_incorrect_card_reader, snDoTrans), this);
        }
    }
}
