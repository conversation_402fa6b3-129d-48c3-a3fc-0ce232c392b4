package com.mpos.screen;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import com.mpos.common.DataStoreApp;
import com.mpos.common.MyApplication;
import com.mpos.sdk.core.control.SaveLogController;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.utils.MposUtil;
import com.mpos.utils.MyUtils;

import butterknife.ButterKnife;
import butterknife.OnClick;
import vn.mpos.R;

public class ActivityResultPending extends Activity {

    String tag = "ActivityResultPending";

    SaveLogController logUtil;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_result_pending);

        MyUtils.setRequestedOrientation(this, DataStoreApp.getInstance().getIsLandscape());
        logUtil = MyApplication.self().getSaveLogController();

        ButterKnife.bind(this);
    }

    @OnClick({R.id.btn_result_pending_backHistory, R.id.btn_result_pending_callHotline})
    public void onClick(View v) {
        int i = v.getId();
        switch (i) {
            case R.id.btn_result_pending_backHistory:
                Intent returnIntent = new Intent();
                setResult(Activity.RESULT_OK, returnIntent);
                gotoHistory();
                finish();
                break;
            case R.id.btn_result_pending_callHotline:
                MyUtils.callToSupport("19002079", this);
                break;
            default:
                break;
        }
    }

    private void gotoHistory() {
        boolean isUseReader = DataStoreApp.getInstance().isUseReader();
        Intent i;
        if (isUseReader && (MposUtil.getInstance().checkNeedLoginLevel2() || TextUtils.isEmpty(PrefLibTV.getInstance(this).getTKL2()))) {
            i = new Intent(this, ActivitySubLogin.class);
        } else {
            i = new Intent(this, ActivityPaymentHistory.class);
            i.putExtra("tokenL2", PrefLibTV.getInstance(this).getTKL2());
        }
        startActivity(i);
    }

    @Override
    public void onBackPressed() {
    }
}
