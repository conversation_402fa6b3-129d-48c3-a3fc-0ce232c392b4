package com.mpos.dialogs;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 7/30/19.
 */

import android.content.DialogInterface;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bumptech.glide.Glide;
import com.mpos.screen.BaseDialogFragment;

import butterknife.BindView;
import butterknife.ButterKnife;
import vn.mpos.R;

public class AffiliateDialogFragment extends BaseDialogFragment {

    public static final String TITLE_DIALOG_AFFILIATE = "TITLE_DIALOG_AFFILIATE";
    public static final String CONTENT_DIALOG_AFFILIATE = "CONTENT_DIALOG_AFFILIATE";
    public static final String BUTTON_TITLE_DIALOG_AFFILIATE = "BUTTON_TITLE_DIALOG_AFFILIATE";
    public static final String COVER_LINK_DIALOG_AFFILIATE = "COVER_LINK_DIALOG_AFFILIATE";
    public static final String EVENT_LINK_DIALOG_AFFILIATE = "EVENT_LINK_DIALOG_AFFILIATE";

    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.tv_content)
    TextView tvContent;
    @BindView(R.id.btn_dialog)
    Button btnDialog;
    @BindView(R.id.iv_close)
    ImageView ivClose;
    @BindView(R.id.iv_cover)
    ImageView ivCover;

    private OnDismissAffiliateDialogListener onDismissAffiliateDialogListener;

    public void setOnDismissAffiliateDialogListener(OnDismissAffiliateDialogListener onDismissAffiliateDialogListener) {
        this.onDismissAffiliateDialogListener = onDismissAffiliateDialogListener;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setApplyAnimation(true);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View v = inflater.inflate(R.layout.fragment_dialog_affiliate, container, false);
        ButterKnife.bind(this, v);
        Bundle bundle = getArguments();
        if (bundle != null) {
            String title = bundle.getString(TITLE_DIALOG_AFFILIATE);
            String content = bundle.getString(CONTENT_DIALOG_AFFILIATE);
            String buttonTitle = bundle.getString(BUTTON_TITLE_DIALOG_AFFILIATE);
            String coverLink = bundle.getString(COVER_LINK_DIALOG_AFFILIATE);
            String eventLink = bundle.getString(EVENT_LINK_DIALOG_AFFILIATE);

            if (!TextUtils.isEmpty(title)) {
                tvTitle.setText(title);
            } else {
                tvTitle.setVisibility(View.GONE);
            }
            if (!TextUtils.isEmpty(content)) {
                tvContent.setText(content);
            } else {
                tvContent.setVisibility(View.GONE);
            }
            if (!TextUtils.isEmpty(buttonTitle)) {
                btnDialog.setText(buttonTitle);
            } else {
                btnDialog.setVisibility(View.GONE);
            }
            if (coverLink != null) {
                Glide.with(context).load(coverLink).into(ivCover);
                ivCover.post(() -> {
                    int width = ivCover.getWidth();
                    ivCover.getLayoutParams().height = (int) (width * 1.16);
                    ivCover.requestLayout();
                });
                ivCover.setOnClickListener(v12 -> {
                    if (onDismissAffiliateDialogListener != null) {
                        onDismissAffiliateDialogListener.onClickCoverAffiliateDialog(eventLink);
                    }
                    dismiss();
                });
            }
        }
        btnDialog.setOnClickListener(v1 -> dismiss());
        ivClose.setOnClickListener(v1 -> dismiss());
        if (getDialog() != null && getDialog().getWindow() != null) {
            getDialog().getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        }
        return v;
    }

    @Override
    public void onDismiss(DialogInterface dialog) {
        super.onDismiss(dialog);
        if (onDismissAffiliateDialogListener != null) onDismissAffiliateDialogListener.onDismissAffiliateDialog();
    }

    public interface OnDismissAffiliateDialogListener {
        void onDismissAffiliateDialog();
        void onClickCoverAffiliateDialog(String eventLink);
    }
}
