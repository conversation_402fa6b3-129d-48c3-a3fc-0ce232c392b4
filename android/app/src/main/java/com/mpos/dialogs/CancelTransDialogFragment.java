package com.mpos.dialogs;

/**
 * Created by DucLQ on 7/30/19.
 */

import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;

import butterknife.BindView;
import butterknife.ButterKnife;
import vn.mpos.R;

public class CancelTransDialogFragment extends DialogFragment {

    @BindView(R.id.iv_close)
    ImageView ivClose;
    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.tv_content)
    TextView tvContent;
    @BindView(R.id.et_cancel)
    EditText etCancel;
    @BindView(R.id.btn_skip)
    Button btnSkip;
    @BindView(R.id.btn_cancel)
    Button btnCancel;

    private OnClickButtonCancelTransListener onClickButtonCancelTransListener;

    public void setOnClickButtonCancelTransListener(OnClickButtonCancelTransListener onClickButtonCancelTransListener) {
        this.onClickButtonCancelTransListener = onClickButtonCancelTransListener;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View v = inflater.inflate(R.layout.fragment_dialog_cancel_transaction_vm, container, false);
        ButterKnife.bind(this, v);
        ivClose.setOnClickListener(v1 -> dismiss());
        btnSkip.setOnClickListener(v12 -> {
            if (onClickButtonCancelTransListener != null) {
                onClickButtonCancelTransListener.onClickSkipCancelTrans();
            }
            dismiss();
        });
        btnCancel.setOnClickListener(v13 -> {
            String content = etCancel.getText().toString();
            if (TextUtils.isEmpty(content)) {
                Toast.makeText(getContext(), getString(R.string.cancel_transaction_confirm_empty), Toast.LENGTH_LONG).show();
            } else if (content.trim().length() < 6) {
                Toast.makeText(getContext(), getString(R.string.cancel_transaction_confirm_too_short), Toast.LENGTH_LONG).show();
            } else {
                if (onClickButtonCancelTransListener != null) {
                    onClickButtonCancelTransListener.onClickConfirmCancelTrans(content);
                }
                dismiss();
            }
        });
        if (getDialog() != null && getDialog().getWindow() != null) {
            getDialog().getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        }
        return v;
    }

    public interface OnClickButtonCancelTransListener{
        void onClickSkipCancelTrans();
        void onClickConfirmCancelTrans(String content);
    }
}
