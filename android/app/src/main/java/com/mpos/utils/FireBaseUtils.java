package com.mpos.utils;

import android.os.Bundle;
import android.text.TextUtils;

import com.google.firebase.analytics.FirebaseAnalytics;

public class FireBaseUtils {

    static FireBaseUtils fireBaseUtils;

    public static synchronized FireBaseUtils getInstance() {
        if (fireBaseUtils == null) {
            fireBaseUtils = new FireBaseUtils();
        }
        return fireBaseUtils;
    }

    public void logEvent(FirebaseAnalytics mFirebaseAnalytics, String content) {
        logEvent(mFirebaseAnalytics, null, null, content);
    }
    public void logEvent(FirebaseAnalytics mFirebaseAnalytics, String name, String content) {
        logEvent(mFirebaseAnalytics, null, name, content);
    }
    public void logEvent(FirebaseAnalytics mFirebaseAnalytics, String id, String name, String content) {
        Bundle bundle = new Bundle();
        if (!TextUtils.isEmpty(id)) {
            bundle.putString(FirebaseAnalytics.Param.ITEM_ID, id);
        }
        if (!TextUtils.isEmpty(name)) {
            bundle.putString(FirebaseAnalytics.Param.ITEM_NAME, name);
        }
        bundle.putString(FirebaseAnalytics.Param.CONTENT_TYPE, content);
        logEvent(mFirebaseAnalytics, name, bundle);
    }

    public void logEvent(FirebaseAnalytics mFirebaseAnalytics, String nameEvent, Bundle bundle) {
        mFirebaseAnalytics.logEvent(TextUtils.isEmpty(nameEvent)?"notName":nameEvent, bundle);
    }

}
