package com.mpos.common;

import android.content.Context;

import com.mpos.sdk.util.Utils;
import com.mpos.utils.MyDialogShow;
import com.whty.smartpos.tysmartposapi.printer.PrinterListener;

import vn.mpos.R;

/**
 * Create by an<PERSON><PERSON><PERSON>n on 8/28/20
 */
public class MyP20LPrinterListener implements PrinterListener {

    private static final String TAG = "MyPrinterListener";

    Context context;
    boolean haveShowError = false;

    public MyP20LPrinterListener(Context context) {
        this.context = context;
    }

    public void resetHaveShowError() {
        this.haveShowError = false;
    }

    @Override
    public void onPrinterOutOfPaper() {
        Utils.LOGD(TAG, "onPrinterOutOfPaper: ------->>");
        if (!haveShowError) {
            MyDialogShow.showDialogError(getString(R.string.error_printer_out_of_paper), context);
            haveShowError = true;
        }
    }

    @Override
    public void onPrinterStart(String templateName) {
        Utils.LOGD(TAG, "onPrinterStart: ---------->>>" + templateName);
    }

    @Override
    public void onPrinterEnd(String templateName) {
        Utils.LOGD(TAG, "onPrinterEnd: ------>>" + templateName);
    }

    @Override
    public void onPrinterError(String msg) {
        Utils.LOGD(TAG, "onPrinterError: " + msg);
        String moreDetailError = "";
        switch (msg) {
            case "1":
            case "01":
            case "2":
            case "02":
                moreDetailError = getString(R.string.error_printer_1_2);
                break;
            case "-1":
            case "-2":
                moreDetailError = getString(R.string.error_printer_negative_1_2);
                break;
            case "-11":
                moreDetailError = getString(R.string.error_printer_negative_11);
                break;
            case "-12":
                moreDetailError = getString(R.string.error_printer_negative_12);
                break;
        }
        MyDialogShow.showDialogError(getString(R.string.error_printer_default)+"\n("+msg+ " "+moreDetailError+")", context);
    }

    private String getString(int id) {
        return context.getString(id);
    }
}
