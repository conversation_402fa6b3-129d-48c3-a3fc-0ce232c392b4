package com.mpos.rnmodules;
//import cl.json.ShareApplication;
//import android.annotation.TargetApi;
//import android.content.Context;
//import android.content.Intent;
//import android.os.Build;
//import android.os.Bundle;
//import android.text.TextUtils;
//import android.util.Log;
//import android.view.KeyEvent;
//import android.view.LayoutInflater;
//import android.view.View;
//import android.widget.FrameLayout;
//
//import androidx.annotation.NonNull;
//import androidx.annotation.Nullable;
//
//import com.BV.LinearGradient.LinearGradientPackage;
//import com.RNFetchBlob.RNFetchBlobPackage;
//import com.facebook.react.ReactInstanceManager;
//import com.facebook.react.ReactRootView;
//import com.facebook.react.bridge.Callback;
//import com.facebook.react.common.LifecycleState;
//import com.facebook.react.modules.core.DefaultHardwareBackBtnHandler;
//import com.facebook.react.modules.core.PermissionAwareActivity;
//import com.facebook.react.modules.core.PermissionListener;
//import com.facebook.react.shell.MainReactPackage;
//import com.facebook.soloader.SoLoader;
//import com.horcrux.svg.SvgPackage;
//import com.mpos.common.DataStoreApp;
//import com.mpos.sdk.core.control.JsonParser;
//import com.mpos.models.responses.ServiceResponse;
//import com.mpos.screen.ActivityPrePayment;
//import com.mpos.sdk.core.control.LocationManagerMp;
//import com.mpos.sdk.core.model.PrefLibTV;
//import com.mpos.sdk.util.ConstantsPay;
//import com.mpos.utils.Constants;
//import com.mpos.utils.MyUtils;
//import com.reactnativecommunity.rnpermissions.RNPermissionsPackage;
//import com.reactnativecommunity.webview.RNCWebViewPackage;
//import com.swmansion.gesturehandler.react.RNGestureHandlerPackage;
//import com.swmansion.reanimated.ReanimatedPackage;
//
//import org.devio.rn.splashscreen.SplashScreen;
//import org.devio.rn.splashscreen.SplashScreenReactPackage;
//import org.json.JSONArray;
//import org.json.JSONException;
//import org.json.JSONObject;
//
//import java.util.Locale;
//
//import ca.jaysoo.extradimensions.ExtraDimensionsPackage;
//import cl.json.RNSharePackage;
//import fr.greweb.reactnativeviewshot.RNViewShotPackage;
//import io.github.mr03web.softinputmode.SoftInputModePackage;
//import com.reactnativecommunity.cameraroll.CameraRollPackage;
//import vn.mpos.BuildConfig;
//import vn.mpos.R;
//
//public class MyReactActivity extends ActivityPrePayment implements DefaultHardwareBackBtnHandler, PermissionAwareActivity, ActivityPrePayment.ItfShowScreenLoading {
//
//    private static final String TAG = "MyReactActivity";
//
//    private ReactRootView mReactRootView;
//    private ReactInstanceManager mReactInstanceManager;
//
//    private @Nullable
//    Callback mPermissionsCallback;
//    private @Nullable
//    PermissionListener mPermissionListener;
//    @Nullable
//    private PermissionListener permissionListener;
//
//    private String stringDataMC;
//
//    private FrameLayout frameLayout;
//
//    LocationManagerMp locationManagerMp;
//
//
//    @Override
//    protected void onCreate(Bundle savedInstanceState) {
//        super.onCreate(savedInstanceState);
//
//        setItfShowScreenLoading(this);
//
//        SoLoader.init(this, false);
//
//        String language = Locale.getDefault().getLanguage();
//
//        stringDataMC = PrefLibTV.getInstance(this).getDataLoginMerchant();
//
//        mReactRootView = new ReactRootView(this);
//        mReactInstanceManager = ReactInstanceManager.builder()
//                .setApplication(getApplication())
//                .setCurrentActivity(this)
//                .setBundleAssetName("index.android.bundle")
//                .setJSMainModulePath("index")
//                .addPackage(new MainReactPackage())
//                .addPackage(new MyReactPackage())
//                .addPackage(new RNGestureHandlerPackage())
//                .addPackage(new ExtraDimensionsPackage())
//                .addPackage(new LinearGradientPackage())
//                .addPackage(new ReanimatedPackage())
//                .addPackage(new SoftInputModePackage())
//                .addPackage(new RNSharePackage())
//                .addPackage(new SplashScreenReactPackage())
//                .addPackage(new SvgPackage())
//                .addPackage(new RNCWebViewPackage())
//                .addPackage(new RNPermissionsPackage())
//                .addPackage(new RNFetchBlobPackage())
//                .addPackage(new RNViewShotPackage())
//                .addPackage(new CameraRollPackage())
//                .setUseDeveloperSupport(BuildConfig.DEBUG)
//                .setInitialLifecycleState(LifecycleState.RESUMED)
//                .build();
//        ServiceResponse serviceResponse = (ServiceResponse) getIntent().getSerializableExtra(Constants.EXTRA_INTENT);
//        String listServiceResponseString = getIntent().getStringExtra("LIST_SERVICES");
//        String rnRouteName = getIntent().getStringExtra("RN_ROUTE_NAME");
//        boolean rnBNPL = getIntent().getBooleanExtra("rnBNPL", false);
//        boolean rnQrIsInternational = getIntent().getBooleanExtra("RN_QR_IS_INTERNATIONAL", false);
//        Bundle bundle = new Bundle();
//        bundle.putBoolean("rnQrIsInternational", rnQrIsInternational);
//        if (serviceResponse != null) {
//            bundle.putString("serviceId", serviceResponse.getServiceId() + "");
//            bundle.putString("serviceCode", serviceResponse.getServiceCode());
//            bundle.putString("description", language.equals("vi") ? serviceResponse.getServiceTitleResponse().getVi() : serviceResponse.getServiceTitleResponse().getEn());
//        }
//        if (!TextUtils.isEmpty(listServiceResponseString)) {
//            bundle.putString("listService", listServiceResponseString);
//        }
//        if (!TextUtils.isEmpty(rnRouteName)) {
//            bundle.putString("rnRouteName", rnRouteName);
//            if (rnRouteName.equals("InstallmentListBankContainer") || rnRouteName.equals("PromotionListContainer")) {
//                String rnInstallmentListBank = getIntent().getStringExtra("RN_INSTALLMENT_LIST_BANK");
//                if (!TextUtils.isEmpty(rnInstallmentListBank)) {
//                    bundle.putString("rnInstallmentListBank", rnInstallmentListBank);
//                }
//                String rnInstallmentListFeeCard = getIntent().getStringExtra("RN_INSTALLMENT_LIST_FEE_CARD");
//                if (!TextUtils.isEmpty(rnInstallmentListFeeCard)) {
//                    bundle.putString("rnInstallmentListFeeCard", rnInstallmentListFeeCard);
//                }
//                String rnInstallmentListFeeCardLink = getIntent().getStringExtra("RN_INSTALLMENT_LIST_FEE_CARD_LINK");
//                if (!TextUtils.isEmpty(rnInstallmentListFeeCardLink)) {
//                    bundle.putString("rnInstallmentListFeeCardLink", rnInstallmentListFeeCardLink);
//                }
//                bundle.putBoolean("rnInstallmentAllowChangeFee", getIntent().getBooleanExtra("RN_INSTALLMENT_ALLOW_CHANGE_FEE", false));
//                bundle.putBoolean("rnInstallmentEnableFeeTrans", getIntent().getBooleanExtra("RN_INSTALLMENT_ENABLE_FEE_TRANS", false));
//                bundle.putBoolean("rnInstallmentEnableFeeInstallment", getIntent().getBooleanExtra("RN_INSTALLMENT_ENABLE_FEE_INSTALLMENT", false));
//                bundle.putInt("rnInstallmentEnableCheckBin", getIntent().getIntExtra("RN_CHECK_BILL_INSTALLMENT", 0));
//                bundle.putInt("rnInstallmentCheckIsPayLink", getIntent().getIntExtra("RN_CHECK_IS_PAY_LINK", 0));
//                bundle.putInt("rnInstallmentCheckIsPayCard", getIntent().getIntExtra("RN_CHECK_IS_PAY_CARD", 0));
//            } else if (rnRouteName.equals("InstallmentByVaymuonContainer")) {
//                bundle.putString("rnInstallmentByVaymuonData", getIntent().getStringExtra("RN_INSTALLMENT_BY_VAYMUON_DATA"));
//                bundle.putString("rnExchangeByVaymuonData", getIntent().getStringExtra("RN_EXCHANGE_BY_VAYMUON_DATA"));
//            } else if (rnRouteName.equals("PaymentLinkContainer")) {
//                bundle.putString("rnLinkPayment", getIntent().getStringExtra("RN_LINK_PAYMENT"));
//                bundle.putString("rnMinuteExpired", getIntent().getStringExtra("RN_MINUTE_EXPIRED"));
//                bundle.putString("rnLinkPaymentAmount", getIntent().getStringExtra("RN_LINK_PAYMENT_AMOUNT"));
//            }
//        }
//        bundle.putString("deviceIdentifier", DataStoreApp.getInstance().getRegisterId());
//        bundle.putBoolean("isFeedback", DataStoreApp.getInstance().isCanFeedback());
//        bundle.putString("merchantId", PrefLibTV.getInstance(context).getMerchantsId());
//        bundle.putString("muid", PrefLibTV.getInstance(context).getUserId());
//        bundle.putString("lang", Locale.getDefault().getLanguage());
//        bundle.putString("baseUrl", com.mpos.utils.BuildConfig.URL_MPOS);
//        bundle.putInt("rnConvertVimo", DataStoreApp.getInstance().getDataConvertVimo());
//        bundle.putDouble("rnConvertVimoAmount", DataStoreApp.getInstance().getDataConvertVimoAmount());
//        bundle.putBoolean("rnIsUseReader", DataStoreApp.getInstance().isUseReader());
//        bundle.putBoolean("rnIsVASEnable", checkVASEnable());
//        bundle.putString("serialNumber", PrefLibTV.getInstance(context).getSerialNumber());
//        bundle.putString("sessionKey", PrefLibTV.getInstance(context).getSessionKey());
//        bundle.putString("urlServer", ConstantsPay.getUrlServer(this));
//        bundle.putString("versionNo", android.os.Build.VERSION.RELEASE);
//        bundle.putString("emailMerchant", DataStoreApp.getInstance().getemailMerchant());
//        bundle.putBoolean("rnBNPL", rnBNPL);
//        bundle.putString("rnAmount", getIntent().getStringExtra("rnAmount"));
//        bundle.putString("rnDescription", getIntent().getStringExtra("rnDescription"));
//        bundle.putString("rnPhone", getIntent().getStringExtra("rnPhone"));
//        bundle.putString("rnEmail", getIntent().getStringExtra("rnEmail"));
//        bundle.putString("rnIdentity", getIntent().getStringExtra("rnIdentity"));
//        bundle.putString("rnServiceCode", getIntent().getStringExtra("rnServiceCode"));
//        bundle.putBoolean("enableNewUI", getIntent().getBooleanExtra("enableNewUI", false));
//        try {
////            String stringDataMC = DataStoreApp.getInstance().getDataLoginMerchant();
//            JSONObject jsonDataMC = new JSONObject(stringDataMC);
//            String paybyVasBalanceFlag = JsonParser.getDataJson(jsonDataMC, "paybyVasBalanceFlag");
//            String hasPasswordLv2 = JsonParser.getDataJson(jsonDataMC, "hasPasswordLv2");
//            String username = JsonParser.getDataJson(jsonDataMC, "username");
//            String utmSource = JsonParser.getDataJson(jsonDataMC, "utmSource");
//            String requiredInsDescription = JsonParser.getDataJson(jsonDataMC, "requiredInsDescription");
//            bundle.putBoolean("rnPaybyVasBalanceFlag", Constants.SVALUE_1.equals(paybyVasBalanceFlag));
//            bundle.putBoolean("hasPasswordLv2", Constants.SVALUE_TRUE.equals(hasPasswordLv2));
//            bundle.putString("username", username);
//            bundle.putString("utmSource", utmSource);
//            bundle.putBoolean("requiredInsDescription", Constants.SVALUE_TRUE.equals(requiredInsDescription));
//            bundle.putString("dataLoginMerchant", stringDataMC);
//        } catch (JSONException e) {
//            e.printStackTrace();
//            bundle.putBoolean("rnPaybyVasBalanceFlag", false);
//            bundle.putBoolean("hasPasswordLv2", false);
//            bundle.putString("username", "");
//            bundle.putString("utmSource", "");
//            bundle.putBoolean("requiredInsDescription", false);
//            bundle.putString("dataLoginMerchant", "");
//        }
//        mReactRootView.startReactApplication(mReactInstanceManager, "MposReact", bundle);
//        SplashScreen.show(this, R.style.SplashScreenRNTheme);
////        SplashScreen.show(this, R.style.SplashScreenRNTheme, false);
//
//        frameLayout = new FrameLayout(this);
//        frameLayout.addView(mReactRootView);
//
//        setContentView(frameLayout);
//
//        MyUtils.setRequestedOrientation(this, DataStoreApp.getInstance().getIsLandscape());
//
//        locationManagerMp = new LocationManagerMp(context);
//        locationManagerMp.startGetLocation();
//    }
//
//    private boolean checkVASEnable() {
//        boolean canSaleService = DataStoreApp.getInstance().getCanSaleService();
////        String stringDataLoginMerchant = DataStoreApp.getInstance().getDataLoginMerchant();
//        try {
//            JSONObject jRoot = new JSONObject(stringDataMC);
//            String menuHome = JsonParser.getDataJson(jRoot, "menuHome");
//            if (!TextUtils.isEmpty(menuHome)) {
//                JSONArray jsonArrayMenuHome = new JSONArray(menuHome);
//                for (int i = 0; i < jsonArrayMenuHome.length(); i++) {
//                    JSONObject itemHome = jsonArrayMenuHome.getJSONObject(i);
//                    String serviceCode = JsonParser.getDataJson(itemHome, "serviceCode");
//                    if (canSaleService && "VAS_PAYMENT".equals(serviceCode)) {
//                        return true;
//                    }
//                }
//            }
//            return false;
//        } catch (JSONException e) {
//            e.printStackTrace();
//            return false;
//        }
//    }
//
//    @Override
//    protected void onPause() {
//        super.onPause();
//
//        if (mReactInstanceManager != null) {
//            mReactInstanceManager.onHostPause(this);
//        }
//    }
//
//    @Override
//    protected void onResume() {
//        super.onResume();
//        Log.e("-------------------", "R");
//
//        if (mReactInstanceManager != null) {
//            mReactInstanceManager.onHostResume(this, this);
//        }
//    }
//
//    @Override
//    protected void onDestroy() {
//        super.onDestroy();
//
//        locationManagerMp.stopGetLocation();
//
//        if (mReactInstanceManager != null) {
//            mReactInstanceManager.onHostDestroy(this);
//        }
//        if (mReactRootView != null) {
//            mReactRootView.unmountReactApplication();
//        }
//    }
//
//    @Override
//    public void onBackPressed() {
//        if (mReactInstanceManager != null) {
////            mReactInstanceManager.onBackPressed();
//        } else {
//            super.onBackPressed();
//        }
//    }
//
//    @Override
//    public boolean onKeyUp(int keyCode, KeyEvent event) {
//        if (keyCode == KeyEvent.KEYCODE_MENU && mReactInstanceManager != null) {
//            mReactInstanceManager.showDevOptionsDialog();
//            return true;
//        }
//        return super.onKeyUp(keyCode, event);
//    }
//
//    @Override
//    public void invokeDefaultOnBackPressed() {
//
//    }
//
//    @Override
//    public void onPointerCaptureChanged(boolean hasCapture) {
//
//    }
//
//    @Override
//    @TargetApi(Build.VERSION_CODES.M)
//    public void requestPermissions(String[] permissions, int requestCode, PermissionListener listener) {
//        mPermissionListener = listener;
//        requestPermissions(permissions, requestCode);
//    }
//
//    @Override
//    public void onRequestPermissionsResult(final int requestCode, @NonNull final String[] permissions, @NonNull final int[] grantResults) {
//        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
//        mPermissionsCallback = args -> {
//            if (mPermissionListener != null && mPermissionListener.onRequestPermissionsResult(requestCode, permissions, grantResults)) {
//                mPermissionListener = null;
//            }
//        };
//    }
//
//    public void startPay(Intent intent) {
//
//        callSdkPayment(intent);
//    }
//
//    @Override
//    public void onShowScreenLoadingAfterPayment() {
//        if (frameLayout != null) {
//            LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
//            View vSplash = inflater.inflate(R.layout.view_splash, null, false);
//            runOnUiThread(() -> frameLayout.addView(vSplash));
//        }
//    }
//
//    @Override
//    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
//        super.onActivityResult(requestCode, resultCode, data);
//        Log.d(TAG, "MyReactActivity-------onActivityResult() called with: requestCode = [" + requestCode + "], resultCode = [" + resultCode + "], data = [" + data + "]");
//        mReactInstanceManager.onActivityResult(this, requestCode, resultCode, data);
//    }
//}
