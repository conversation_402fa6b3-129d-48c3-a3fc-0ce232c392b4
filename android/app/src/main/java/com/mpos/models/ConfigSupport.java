package com.mpos.models;

/**
 * Created by anhnguyen on 5/18/18.
 */

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;

public class ConfigSupport {

    @SerializedName("afterSale")
    @Expose
    private ArrayList<SupportObj> afterSale = null;
    @SerializedName("support")
    @Expose
    private ArrayList<SupportObj> support = null;
    @SerializedName("intime")
    @Expose
    private String intime;
    @SerializedName("outtime")
    @Expose
    private String outtime;

    public ArrayList<SupportObj> getAfterSale() {
        return afterSale;
    }

    public void setAfterSale(ArrayList<SupportObj> afterSale) {
        this.afterSale = afterSale;
    }

    public ArrayList<SupportObj> getSupport() {
        return support;
    }

    public void setSupport(ArrayList<SupportObj> support) {
        this.support = support;
    }

    public String getIntime() {
        return intime;
    }

    public void setIntime(String intime) {
        this.intime = intime;
    }

    public String getOuttime() {
        return outtime;
    }

    public void setOuttime(String outtime) {
        this.outtime = outtime;
    }

}