package com.mpos.models;

/**
 * Created by anhnguyen on 2/1/18.
 */
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import com.mpos.sdk.core.model.BaseObjJson;

import java.io.Serializable;

public class VetcAccount implements Serializable {

    @SerializedName("error")
    @Expose
    private BaseObjJson error;

    @SerializedName("status")
    @Expose
    private String status;
    @SerializedName("accountNo")
    @Expose
    private String accountNo;
    @SerializedName("accountName")
    @Expose
    private String accountName;
    @SerializedName("accountType")
    @Expose
    private String accountType;
    @SerializedName("check")
    @Expose
    private Boolean check;

    public BaseObjJson getError() {
        return error;
    }

    public void setError(BaseObjJson error) {
        this.error = error;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public Boolean getCheck() {
        return check;
    }

    public void setCheck(Boolean check) {
        this.check = check;
    }
}
