package com.nextpay.mposxs.models;

import android.content.Context;
import android.media.AudioManager;
import android.os.Bundle;
import android.speech.tts.TextToSpeech;
import android.util.Log;

import java.util.Locale;

public class TTSHelper {

    private TextToSpeech tts;

    public void speakSuccess(Context context, String amount) {
        tts = new TextToSpeech(context, new TextToSpeech.OnInitListener() {
            @Override
            public void onInit(int status) {
                if (status == TextToSpeech.SUCCESS) {

                    // Thiết lập ngôn ngữ tiếng Việt
                    int result = tts.setLanguage(new Locale("vi", "VN"));
                    if (result == TextToSpeech.LANG_MISSING_DATA || result == TextToSpeech.LANG_NOT_SUPPORTED) {
                        Log.e("TTS", "Ngôn ngữ không được hỗ trợ.");
                        return;
                    }

                    // Tăng âm lượng hệ thống lên mức tối đa
                    AudioManager audioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
                    if (audioManager != null) {
                        int maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC);
                        audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, maxVolume, 0);
                    }

                    // Thiết lập tham số âm lượng cho TextToSpeech
                    Bundle params = new Bundle();
                    params.putFloat(TextToSpeech.Engine.KEY_PARAM_VOLUME, 1.0f);  // Âm lượng lớn nhất

                    // Tạo nội dung thông báo
                    String message = "Thanh toán thành công " + amount + " đồng";

                    // Đọc nội dung bằng TTS
                    tts.speak(message, TextToSpeech.QUEUE_FLUSH, params, "tts_id");

                } else {
                    Log.e("TTS", "Khởi tạo TTS thất bại.");
                }
            }
        });
    }

    // Dọn tài nguyên nếu cần
    public void shutdown() {
        if (tts != null) {
            tts.stop();
            tts.shutdown();
        }
    }
}
