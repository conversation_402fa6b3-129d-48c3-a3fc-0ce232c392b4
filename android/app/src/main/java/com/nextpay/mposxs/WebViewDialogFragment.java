package com.nextpay.mposxs;

import android.app.DialogFragment;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.mpos.sdk.util.DevicesUtil;

public class WebViewDialogFragment extends DialogFragment {

    public static WebViewDialogFragment newInstance(String url, String title, String textClose) {
        WebViewDialogFragment f = new WebViewDialogFragment();
        Bundle args = new Bundle();
        args.putString("url", url);
        args.putString("title", title);
        args.putString("textClose", textClose);
        f.setArguments(args);
        return f;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        getDialog().getWindow().requestFeature(Window.FEATURE_NO_TITLE);
        return inflater.inflate(R.layout.webview_dialog_fragment, container);
    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        WebView webView = view.findViewById(R.id.webView);
        TextView tvTitle = view.findViewById(R.id.tv_title);
        Button btClose = view.findViewById(R.id.bt_close);

        String url = getArguments().getString("url");
        String title = getArguments().getString("title");
        String textClose = getArguments().getString("textClose");

        btClose.setOnClickListener(v -> {
            this.dismiss();
        });
        btClose.setText(textClose);

        tvTitle.setText(title);

        webView.getSettings().setJavaScriptEnabled(true);
        webView.setWebViewClient(new WebViewClient() {
            @Override
            public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
                super.onReceivedError(view, errorCode, description, failingUrl);
            }

            @Override
            public void onPageStarted(WebView view, String url, Bitmap favicon) {
                super.onPageStarted(view, url, favicon);
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                Log.i("FragmentWebview", "onPageFinished: url=" + url);
            }

        });
        webView.loadUrl(url);
    }

    @Override
    public void onResume() {
        super.onResume();
        if (DevicesUtil.isSP02P12()) {
            ViewGroup.LayoutParams params = getDialog().getWindow().getAttributes();
            params.width = ViewGroup.LayoutParams.MATCH_PARENT;
            params.height = ViewGroup.LayoutParams.MATCH_PARENT;
            getDialog().getWindow().setAttributes((android.view.WindowManager.LayoutParams) params);
        }
    }
}
