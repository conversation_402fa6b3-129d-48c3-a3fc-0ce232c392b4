package com.nextpay.mposxs.models;

import com.google.gson.annotations.Expose;

public class DataPrePayTcp {
    @Expose
    String serviceName;
    @Expose
    String status;
    @Expose
    String responseCode;
    @Expose
    String transDate;
    @Expose
    String totalAmount;
    @Expose
    String issuerCode;
    @Expose
    String muid;
    @Expose
    String orderId;
    @Expose
    String authCode;
    @Expose
    String transCode;
    @Expose
    String paymentIdentifier;
    @Expose
    String cardHolderName;
    @Expose
    String depositID;
    @Expose
    String pan;
    @Expose
    String wfId;
    @Expose
    String responseMess;

    public DataPrePayTcp() {
    }

    public DataPrePayTcp(String serviceName, String status, String responseCode, String transDate, String totalAmount, String issuerCode, String muid, String orderId, String authCode, String transCode, String paymentIdentifier, String cardHolderName, String pan) {
        this.serviceName = serviceName;
        this.status = status;
        this.responseCode = responseCode;
        this.transDate = transDate;
        this.totalAmount = totalAmount;
        this.issuerCode = issuerCode;
        this.muid = muid;
        this.orderId = orderId;
        this.authCode = authCode;
        this.transCode = transCode;
        this.paymentIdentifier = paymentIdentifier;
        this.cardHolderName = cardHolderName;
        this.pan = pan;
    }

    public DataPrePayTcp(String serviceName, String orderId, String responseMess) {
        this.serviceName = serviceName;
        this.orderId = orderId;
        this.responseMess = responseMess;
    }

    public DataPrePayTcp(String amount, String paymentIdentifier) {
        this.totalAmount = amount;
        this.paymentIdentifier = paymentIdentifier;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(String responseCode) {
        this.responseCode = responseCode;
    }

    public String getTransDate() {
        return transDate;
    }

    public void setTransDate(String transDate) {
        this.transDate = transDate;
    }

    public String getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(String totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getIssuerCode() {
        return issuerCode;
    }

    public void setIssuerCode(String issuerCode) {
        this.issuerCode = issuerCode;
    }

    public String getMuid() {
        return muid;
    }

    public void setMuid(String muid) {
        this.muid = muid;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getAuthCode() {
        return authCode;
    }

    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }

    public String getTransCode() {
        return transCode;
    }

    public void setTransCode(String transCode) {
        this.transCode = transCode;
    }

    public String getPaymentIdentifier() {
        return paymentIdentifier;
    }

    public void setPaymentIdentifier(String paymentIdentifier) {
        this.paymentIdentifier = paymentIdentifier;
    }

    public String getCardHolderName() {
        return cardHolderName;
    }

    public void setCardHolderName(String cardHolderName) {
        this.cardHolderName = cardHolderName;
    }

    public String getPan() {
        return pan;
    }

    public void setPan(String pan) {
        this.pan = pan;
    }

    public String getDepositID() {
        return depositID;
    }

    public void setDepositID(String depositID) {
        this.depositID = depositID;
    }

    public String getWfId() {
        return wfId;
    }

    public void setWfId(String wfId) {
        this.wfId = wfId;
    }

    public String getResponseMess() {
        return responseMess;
    }

    public void setResponseMess(String responseMess) {
        this.responseMess = responseMess;
    }
}
