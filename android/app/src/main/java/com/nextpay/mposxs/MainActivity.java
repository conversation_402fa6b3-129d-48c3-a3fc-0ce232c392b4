package com.nextpay.mposxs;

import static android.util.Log.getStackTraceString;
import static com.mpos.mp_module_socket.service.SocketServer.APPROVED;
import static com.nextpay.mposxs.MposSocketServer.nameFilterActionPayment;

import android.app.FragmentManager;
import android.content.Context;
import android.content.Intent;
import android.media.AudioManager;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.speech.tts.TextToSpeech;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;

import androidx.annotation.NonNull;

import com.mpos.mp_module_socket.main.MPSocketManager;
import com.mpos.mp_module_socket.model.DataVoid;
import com.mpos.sdk.core.common.MyGson;
import com.mpos.sdk.core.common.MyTextHttpResponseHandler;
import com.mpos.sdk.core.control.CryptoInterface;
import com.mpos.sdk.core.control.EncodeDecode;
import com.mpos.sdk.core.control.JsonParser;
import com.mpos.sdk.core.control.LibDspreadReader;
import com.mpos.sdk.core.control.LibInjectKey;
import com.mpos.sdk.core.control.LibKozenP5;
import com.mpos.sdk.core.control.LibLoginHandler;
import com.mpos.sdk.core.control.LibLoginMacq;
import com.mpos.sdk.core.control.LibP20L;
import com.mpos.sdk.core.control.LibPrinterS85;
import com.mpos.sdk.core.control.LibReaderController;
import com.mpos.sdk.core.control.MposSdk;
import com.mpos.sdk.core.control.MposTransactions;
import com.mpos.sdk.core.control.MposTransactionsMacq;
import com.mpos.sdk.core.control.SaveLogController;
import com.mpos.sdk.core.model.CurrencyShow;
import com.mpos.sdk.core.model.DataError;
import com.mpos.sdk.core.model.DataPay;
import com.mpos.sdk.core.model.DataProcessCard;
import com.mpos.sdk.core.model.DataReversalLogin;
import com.mpos.sdk.core.model.LanguageCode;
import com.mpos.sdk.core.model.MposCustom;
import com.mpos.sdk.core.model.PaymentItem;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.core.model.ResultPay;
import com.mpos.sdk.core.model.ResultPayWrapper;
import com.mpos.sdk.core.model.TransItem;
import com.mpos.sdk.core.modelma.DataBaseObj;
import com.mpos.sdk.core.modelma.DataSaleSend;
import com.mpos.sdk.core.model.UserCard;
import com.mpos.sdk.core.modelma.LoginRes;
import com.mpos.sdk.core.modelma.WfDetailRes;
import com.mpos.sdk.core.modelma.WorkFlow;
import com.mpos.sdk.core.mposinterface.ItfAppendLog;
import com.mpos.sdk.core.mposinterface.ResultProcessCard;
import com.mpos.sdk.core.network.ApiMultiAcquirerInterface;
import com.mpos.sdk.core.network.MposRestClient;
import com.mpos.sdk.screen.MposPaymentActivity;
import com.mpos.sdk.util.Constants;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.DevicesUtil;
import com.mpos.sdk.util.Intents;
import com.mpos.sdk.util.MacqUtil;
import com.mpos.sdk.util.MyDialogShow;
import com.mpos.sdk.util.Utils;
import com.nextpay.common.LibPrinterMpos;

import com.nextpay.mposxs.models.*;
//import com.nextpay.mposxs.models.DialogResult;
import com.nextpay.util.FlutterUtils;
import com.pps.core.ToastUtil;

import net.yslibrary.android.keyboardvisibilityevent.KeyboardVisibilityEvent;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

import cz.msebera.android.httpclient.Header;
import cz.msebera.android.httpclient.HttpStatus;
import cz.msebera.android.httpclient.entity.StringEntity;
import io.flutter.embedding.android.FlutterActivity;
import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.plugin.common.EventChannel;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugins.GeneratedPluginRegistrant;

public class MainActivity extends FlutterActivity {
    private final String CHANNEL = "com.mpos/framework";
    private final String CHANNEL_EVENT_KEYBOARD_ANDROID = "com.mpos/eventKeyboardAndroid";

    public static final String ACTION_SET_STATUSBAR_PULL_DOWN   = "android.intent.action.CUSTOM_ACTION_SET_STATUSBAR_PULL_DOWN";
    public static final String ACTION_SET_NVBAR   				= "android.intent.action.CUSTOM_ACTION_SET_NVBAR";
    public static final String EXTRA_INTENT_STATUS_BAR_STATE_KEY = "status_bar_pull_down";//Intent extra, put 1 enable pull down, put 0 disable pull down

    public static final int REQUEST_ENABLE_BT = 1;
    public static final int REQUEST_DEVICE = 2;
    public static final int REQUEST_ACTIVATION = 3;
//    static final int REQUEST_AUDIO_PERMISSION = 4;
    public static final int REQUEST_DEVICE_DSPREAD = 5;
    public static final int REQUEST_CODE_PAYMENT = 13;
    private ToastUtil toastUtil;
    private int mFlagDevices;
//    private String bluetoothAddressDspread;
    private String serialNumber;
    MethodChannel methodChannel;

    private MethodChannel.Result resultChannel;
    private EventChannel.EventSink eventChannelKeyboard;

//    private boolean isUseReader = false;
    private MposSdk mposSdk;
    MposTransactions mposTransactions;
    private LibLoginHandler libLoginHandler;
    private int currentLoginLevelRequest = 1;
    private ConstantsPay.ReaderType readerType;
    private String readerTest;
    private String languageCode;
    private String merchantConfig;
    String route;
    public static final String ROUTE_MOTO       = "/motoPaymentInfor";

    static String TAG = "--MPOS_SDK--";

    private boolean isUiLiteNew = true;

    SaveLogController saveLog;

//    boolean isPayCashier = false;

//    private PinpadManager mPinpadManager;
//    private String mFWVersion;
//    private String mEMVVersion;

//    private boolean isRegisterHeadset = false;
//    private boolean isConnectedAR;
//    private boolean isClickSelectDevice = false;
//    private TaskAudioReaderConnect taskConnectAR;

    private final Handler handlerTimeoutCallback = new Handler();
    private final Runnable runnableTimeoutCallback = () -> returnResultError("", getCurrentLanguageCode().equals(LanguageCode.LANGUAGE_EN) ? "Connection timeout, please try again later" : "Hết thời gian kết nối, vui lòng thử lại sau.", null);

    //    private LibPrinterSp01 libPrinter;
//    private final MyP20LPrinterListener myP20LPrinterListener = new MyP20LPrinterListener(this);
    private LibPrinterMpos printerMpos;

    LibInjectKey libInjectKey;
    // Cashier
    private final String CHANNEL_CASHIER = "Lister_Native_Channel";
    private MethodChannel.Result resultChannelCashier;
    MethodChannel methodChannelCashier;

    final String ACTION_PAY         = "Action_Pay";
    final String Action_Pay_Normal         = "Action_Pay_Normal";       //module installment call
    final String Action_Pay_Deposit= "Action_Pay_Deposit";
    final String ACTION_HISTORY     = "Action_Get_List_Trans";
    final String ACTION_GET_CONFIG  = "Action_Get_Config";
    final String ACTION_PRINT       = "Action_Print";
    final String Action_Print_Offline       = "Action_Print_Offline";
    final String ACTION_PAY_MOTO    = "Push_Pay_Moto_Info";
    final String Action_Check_Is_P20L= "Action_Check_Is_P20L";
    final String Action_Check_Is_SP02= "Action_Check_Is_SP02";
    final String Action_Check_Is_SP02P8= "Action_Check_Is_SP02P8";
    final String Action_Check_Is_AutoGoScreenCashier    = "Action_Check_Is_AutoGoScreenCashier";
    final String Action_Set_AutoGoScreenCashier         = "Action_Set_AutoGoScreenCashier";
    final String Action_Cancel_Payment         = "Action_Cancel_Payment";
    static final String _actionGetMerchantInfo = "Action_Get_Merchant_Info";

    final String Action_CALL_API_MACQ       = "Action_CALL_API_MACQ";
    final String Action_LOGIN               = "Action_LOGIN";
    final String Action_Read_Card           = "Action_Read_Card";
    final String Action_Get_SN               = "Action_Get_SN";


    final String Action_Check_Is_Pax= "Action_Check_Is_Pax";
    final String CALLBACK_PAY_RESULT    = "CallBack_Pay_Result";
    final String CALLBACK_SETTING       = "Callback_Setting";
    final String CALLBACK_MOTO_PAYMENT  = "Callback_Moto_Payment";
    final String CALLBACK_MACQ_INFO     = "Callback_Macq_Info";
    static final String _actionArdGetMacqConfig = "Action_Get_Macq_Config";
    final String CALLBACK_NOTIFICATION_SUCCESS_QR     = "Callback_notification";
    final String Action_Check_PermitDeposit = "Action_Check_PermitDeposit";
    final String Action_Get_Deposit_Config = "Action_Get_Deposit_Config";

    final String ActionGetDetailAndPrintReceipt = "Action_getDetail_And_Print_Receipt";
    final String Action_Print_Summary_Receipt_Deposit = "Action_Print_Summary_Receipt_Deposit";
    final String ActionVoidDeposit = "Action_Void_Deposit";
    static final String action_tcp_state_payment = "action_tcp_state_payment";
    static final String action_tcp_add_order = "action_tcp_add_order";
    static final String action_tcp_void = "action_tcp_void";
    static final String action_tcp_cancel_order = "action_tcp_cancel_order";
    static final String action_print_qr_receipt = "Action_Print_Qr_Receipt";

    //    ITYMpSocketManager mpSocketManager;
    MposSocketServer mposSocketServer;
    N04KeyBoard n04KeyBoard;

    int ROUTE_APP = 0;
    int ROUTE_CASHIER = 1;
    int routeChannel = ROUTE_APP;


    @Override
    public void configureFlutterEngine(@NonNull FlutterEngine flutterEngine) {
        mposTransactions = new MposTransactions(getContext());
        GeneratedPluginRegistrant.registerWith(flutterEngine);
        methodChannel = new MethodChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), CHANNEL);
        methodChannel.setMethodCallHandler(
                        ((call, result) -> {
                            resultChannel = result;
                            routeChannel = ROUTE_APP;
                            Utils.LOGD(TAG, "call native - method: "+call.method);
                            appendLogAct(call.method);
                            initFlutterMposHandle();
                            switch (call.method) {
                                case "initBluetoothAndDevice": {
                                    Integer readerTypeReceiver = call.argument("readerType");
                                    readerTest = call.argument("readerTest");
                                    if (readerTest != null) {
                                        returnResultSuccess(readerTest);
                                        readerType = ConstantsPay.ReaderType.PR02;
                                    } else {
                                        initReaderType(readerTypeReceiver);
                                    }
                                    break;
                                }
                                case "initMposSdk": {
                                    String userName = call.argument("userName");
                                    String password = call.argument("password");
                                    String appType = call.argument("appType");
                                    initMposSdk(userName, password, appType);
                                    returnResultSuccess("");
                                    break;
                                }
                                case "callLoginLevel1": {
                                    Integer readerTypeReceiver = call.argument("readerType");
                                    initReaderType(readerTypeReceiver);

                                    String serialNumber = call.argument("serialNumber");
                                    String mAcc = call.argument("userName");
                                    String mPin = call.argument("password");
                                    String dataFromLoginSdk = call.argument("dataFromLoginSdk");

                                    int isMacqFlow = FlutterUtils.getArgument(call, "isMacqFlow");
                                    if (isMacqFlow == 1) {
                                        mposTransactions.setRunMacq(true);
                                    }

                                    callLoginLevel1(dataFromLoginSdk, serialNumber, mAcc, mPin);
                                    break;
                                }
                                case "callLoginLevel2": {
                                    String mAcc2 = call.argument("userName");
                                    String mPin2 = call.argument("password");
                                    callLoginLevel2(mAcc2, mPin2);
                                    break;
                                }
                                case "callChangePass": {
                                    String oldPass = call.argument("oldPass");
                                    String newPass = call.argument("newPass");
                                    callChangePass(oldPass, newPass);
                                    break;
                                }
                                case "callPaymentScanCard": {
//                                    isPayCashier = false;

//                                    Integer amountInternational = call.argument("amountInternational");
//                                    Integer amountDomestic = call.argument("amountDomestic");
//                                    Integer amount = call.argument("amount");
                                    String sAmountInternational = call.argument("amountInternational");
                                    String sAmountDomestic = call.argument("amountDomestic");
                                    String sAmount = call.argument("amount");
                                    String phone = call.argument("phone");
                                    String email = call.argument("email");
                                    String description = call.argument("description");
                                    String paymentID = call.argument("paymentID");
                                    String stageHandler = call.argument("stageHandler");

                                    long amount;
                                    try {
                                        amount = Long.parseLong(sAmount);
                                    } catch (Exception e) {
                                        amount = 0;
                                    }
                                    Long amountDomestic;
                                    try {
                                        amountDomestic = Long.parseLong(sAmountDomestic);
                                    } catch (Exception e) {
                                        amountDomestic = null;
                                    }
                                    Long amountInternational;
                                    try {
                                        amountInternational = Long.parseLong(sAmountInternational);
                                    } catch (Exception e) {
                                        amountInternational = null;
                                    }

                                    attemptGotoPayment(amount, amountDomestic, amountInternational, phone, email, description, paymentID, stageHandler);
                                    break;
                                }
                                case "getListTransactionComplete": {
                                    mposTransactions.getListTransactionComplete(itfHandlerActionTransListTrans);
                                    break;
                                }
                                case "settleAllTransactionComplete": {
                                    mposTransactions.settleAllTransaction_Complete(itfHandlerActionTransSettle);
                                    break;
                                }
                                case "getTransactionDetail": {
                                    String transactionID = call.argument("transactionID");
                                    mposTransactions.getTransactionDetail(transactionID, itfHandlerActionTransDetail);
                                    break;
                                }
                                case "getTransactionStatus": {
                                    String udid = call.argument("udid");
                                    String transactionID = call.argument("transactionID");
                                    mposTransactions.getTransactionStatus(transactionID, udid, itfHandlerActionTransStatus);
                                    break;
                                }
                                case "voidTransaction": {
                                    String transactionIDVoid = call.argument("transactionID");
                                    mposTransactions.voidTransaction(transactionIDVoid, itfHandlerActionTransVoid);
                                    break;
                                }
                                case "logout": {
                                    mposTransactions = new MposTransactions(getContext());
                                    PrefLibTV.getInstance(getContext()).clearDataAuto();
                                    resetCurrData();
                                    break;
                                }
                                case "sendReceiptWithTransaction": {
                                    String transactionIDEmail = call.argument("transactionID");
                                    String emailSend = call.argument("email");
                                    mposTransactions.sendReceiptWithTransaction(transactionIDEmail, emailSend, itfHandlerActionTransEmail);
                                    break;
                                }
                                case "callCheckLoginLevel2": {
                                    callCheckLoginLevel2();
                                    break;
                                }
                                case "hideKeyBoard": {
                                    View view = this.getCurrentFocus();
                                    if (view != null) {
                                        InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
                                        imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
                                    }
                                    returnResultSuccess("");
                                    break;
                                }
                                case "setLanguage": {
                                    languageCode = call.argument("languageCode");
                                    if (mposSdk != null) {
                                        MposCustom mposCustom = getMposCustom();
                                        mposSdk.setMposCustom(mposCustom);
                                    }
                                    returnResultSuccess("");
                                    break;
                                }
                                case "encryptData": {
                                    String data = call.argument("data");
                                    String secretKey = call.argument("secretKey");
                                    if (!TextUtils.isEmpty(data) && !TextUtils.isEmpty(secretKey)) {
                                        try {
                                            assert data != null;
                                            assert secretKey != null;
                                            String dataResult = EncodeDecode.doAESEncrypt(data, secretKey);
                                            returnResultSuccess(dataResult);
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                            returnResultError("", e.getMessage(), null);
                                        }
                                    } else {
                                        returnResultError("", getCurrentLanguageCode().equals(LanguageCode.LANGUAGE_EN) ? "Data empty" : "Nhập dữ liệu cần mã hoá", null);
                                    }
                                    break;
                                }
                                case "decryptData": {
                                    String data = call.argument("data");
                                    String secretKey = call.argument("secretKey");
                                    if (!TextUtils.isEmpty(data) && !TextUtils.isEmpty(secretKey)) {
                                        try {
                                            assert data != null;
                                            assert secretKey != null;
                                            String dataResult = EncodeDecode.doAESDecrypt(data, secretKey);
                                            returnResultSuccess(dataResult);
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                            returnResultError("", e.getMessage(), null);
                                        }
                                    }
                                    else {
                                        returnResultError("", getCurrentLanguageCode().equals(LanguageCode.LANGUAGE_EN) ? "Data empty" : "Nhập dữ liệu cần giải mã", null);
                                    }
                                    break;
                                }
                                case "continueTransactionUnsign": {
                                    String amount = call.argument("amount");
                                    String pan = call.argument("pan");
                                    String trxType = call.argument("trxType").toString();
                                    String itemDesc = call.argument("itemDesc");
                                    String cardholderName = call.argument("cardholderName");
                                    String transReqId = call.argument("transReqId");
                                    String transactionDate = call.argument("transactionDate").toString();
                                    String paymentIdentify = call.argument("paymentIdentify");
                                    String description_payment = call.argument("description_payment");
                                    String wfId = call.argument("wfId");
//                                    continueTransactionUnsign(paymentIdentify);
                                    gotoSignature(buildDataReversalLogin(amount, trxType, pan, itemDesc, cardholderName, transReqId, transactionDate, paymentIdentify, wfId));
                                    break;
                                }
//                                case "checkIsP20L": {
//                                    processCheckIsP20L();
//                                    break;
//                                }
                                case "getSerialNumber": {
                                    processGetSerialnumber();
                                    break;
                                }
                                case "initPrinter": {
                                    initPrinter();
                                    break;
                                }
                                case "getTransactionReceipt": {
                                    String transId = call.argument("transId");
                                    String transRqId = call.argument("transRqId");
                                    mposTransactions.getTransactionReceipt(transId, transRqId, itfHandlerActionTransReceipt);
                                    break;
                                }
                                case "getSettlementReceipt": {
                                    mposTransactions.getSettlementReceipt(itfHandlerActionSettleReceipt);
                                    break;
                                }
//                                case "printData": {
//                                    String data = call.argument("data");
//                                    if (libPrinter == null) {
//                                        returnResultError("-2", "Máy in chưa được khởi tạo, vui lòng thử lại sau", null);
//                                    } else {
//                                        myPrinterListener.resetHaveShowError();
//                                        libPrinter.printBitmap(MyTextUtils.convertTextBase64ToBitmap(data));
//                                        libPrinter.pushPage(5);
//                                        returnResultSuccess(true);
//                                    }
//                                    break;
//                                }
                                case "printBase64": {
                                    String data = call.argument("data");
                                    Utils.LOGD(TAG, "configureFlutterEngine: "+data);
                                    printBase64Image(data);
                                    break;
                                }
//                                case "setTextFormat": {
//                                    int size = FlutterUtils.getArgument(call,"size");
//                                    int align = FlutterUtils.getArgument(call,"align");
//                                    boolean isBold = FlutterUtils.getArgument(call,"isBold");
//                                    if (libPrinter == null) {
//                                        returnResultError("-2", "Máy in chưa được khởi tạo, vui lòng thử lại sau", null);
//                                    } else {
//                                        libPrinter.setTextFormat(size, align, isBold);
//                                        returnResultSuccess(true);
//                                    }
//                                    break;
//                                }
//                                case "printText": {
//                                    String data = call.argument("data");
//                                    if (libPrinter == null) {
//                                        returnResultError("-2", "Máy in chưa được khởi tạo, vui lòng thử lại sau", null);
//                                    } else {
//                                        myP20LPrinterListener.resetHaveShowError();
//                                        libPrinter.printText(data != null ? data : "");
//                                        returnResultSuccess(true);
//                                    }
//                                    break;
//                                }
//                                case "printPush": {
//                                    int numberPush = FlutterUtils.getArgument(call, "numberPush");
//                                    if (libPrinter == null) {
//                                        returnResultError("-2", "Máy in chưa được khởi tạo, vui lòng thử lại sau", null);
//                                    } else {
//                                        myP20LPrinterListener.resetHaveShowError();
//                                        libPrinter.pushPage(numberPush);
//                                        returnResultSuccess(true);
//                                    }
//                                    break;
//                                }
                                case "setSoftInputMode": {
                                    String type = call.argument("type");
                                    getWindow().setSoftInputMode("SOFT_INPUT_ADJUST_PAN".equals(type) ? WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN : WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
                                    returnResultSuccess(true);
                                    break;
                                }
                                case "getTidAndMid": {
                                    getTidAndMid();
                                    break;
                                }

                                case "getTID": {
                                    returnResultSuccess(PrefLibTV.getInstance(getContext()).get(PrefLibTV.MPOS_TID, String.class, ""));
                                    break;
                                }
                                case "getMID": {
                                    returnResultSuccess(PrefLibTV.getInstance(getContext()).getMId());
                                    break;
                                }
                                case "showDialogWebView": {
                                    String url = call.argument("url");
                                    String title = call.argument("title");
                                    String textClose = call.argument("textClose");
                                    showDialogWebView(url, title, textClose);
                                    break;
                                }
                                case "clearDataAuto": {
                                    PrefLibTV.getInstance(getContext()).clearDataAuto();
                                    resetCurrData();
                                    returnResultSuccess(true);
                                    break;
                                }
                                case "callLoginMacqByDevice": {
                                    Integer readerTypeReceiver = call.argument("readerType");
                                    initReaderType(readerTypeReceiver);

                                    String userName = call.argument("userName");
                                    String serialNumber = call.argument("serialNumber");
                                    String deviceIdentifier = call.argument("deviceIdentifier");
                                    String merchantId = call.argument("merchantId");
                                    callLoginMacqByDevice(userName, serialNumber, deviceIdentifier, merchantId);
                                    break;
                                }
                                case "verifySerialNumber": {
                                    String serialNumber = call.argument("serialNumber");
                                    String password = call.argument("password");
                                    verifyReader(serialNumber, password);
                                    break;
                                }
                                case "initAuthenMA": {
                                    String serialNumber = call.argument("serialNumber");
                                    String password = call.argument("password");
                                    String user = call.argument("user");
                                    initAuthenMA(user, serialNumber, password);
                                    break;
                                }
                                case "fetchStaticQr": {
                                    String muid = call.argument("muid");
                                    fetchStaticQr(muid);
                                    break;
                                }
                                case "fetchQrConfig": {
                                    String muid = call.argument("muid");
                                    fetchQrConfig(muid);
                                    break;
                                }
                                case "putSuccessQrNotification": {
                                    returnResultSuccess("");
                                    String dataNoti = call.arguments.toString();
                                    Utils.LOGD(TAG, "putSuccessQrNotification: dataNoti = " + dataNoti);
                                    methodChannelCashier.invokeMethod(CALLBACK_NOTIFICATION_SUCCESS_QR, dataNoti);
                                    break;
                                }
                                case Action_LOGIN: {
                                    Integer readerTypeReceiver = call.argument("readerType");
                                    initReaderType(readerTypeReceiver);

                                    String user = call.argument("username");
                                    String pass = call.argument("password");
                                    String sn = call.argument("serialNumber");
                                    String deviceIdentifier = call.argument("deviceIdentifier");
                                    mposTransactions.setRunMacq(true);
                                    if (TextUtils.isEmpty(pass)) {
                                        initMposSdk(user, null, null);
                                        flutterMposHandle.processLoginMacqByUserAndDevice(user, sn, deviceIdentifier);
                                    }else {
                                        initMposSdk(user, pass, null);
                                        flutterMposHandle.processLoginMacq(user, pass, deviceIdentifier, sn);
                                    }
                                    break;
                                }
                                case Action_CALL_API_MACQ:
                                    String path = call.argument("path");
                                    String content = call.argument("content");
                                    flutterMposHandle.processSendApiMacq(path, content);
                                    break;
                                case Action_Check_Is_AutoGoScreenCashier: {
                                    processCheckIsAutoGoScreenCashier(false);
                                    break;
                                }
//                                case "isPermitSocket": {
//                                    returnResultSuccess(PrefLibTV.getInstance(getContext()).get(PrefLibTV.permitSocket, Boolean.class, false));
//                                    break;
//                                }
//                                case "isPermitVoidSocket": {
//                                    String permitVoid = PrefLibTV.getInstance(getContext()).get(PrefLibTV.permitVoidSocket, String.class, Constants.SVALUE_0);
//                                    if (permitVoid.equals(Constants.SVALUE_1)) {
//                                        returnResultSuccess(true);
//                                    } else {
//                                        returnResultSuccess(true);
//                                    }
//                                    break;
//                                }
                                case "nativeInitSocket": {
                                    registerReceiverTCP();
                                    returnResultSuccess(true);
                                    break;
                                }
                                case "closeAndResetSocket": {
                                    closeAndResetSocket();
//                                    returnResultSuccess(true);
                                    break;
                                }
                                case "callbackSocketData": {
                                    Utils.LOGD(TAG, "App Flutter call back cancel: callbackSocketData");
                                    String callbackData = call.arguments().toString();
                                    mposSocketServer.callbackTrans(callbackData);
                                    break;
                                }
                                case "callbackCancelPayment": {
                                    Utils.LOGD(TAG, "App Flutter call back cancel: callbackCancelTCP");
                                    String status = call.argument("status");
                                    String orderID = call.argument("orderID");
                                    String totalAmount = call.argument("totalAmount");
                                    String label = call.argument("label");
                                    String orderCode = call.argument("orderCode");
                                    handlerCallbackCancelPaymentTcp(status, orderID, totalAmount, label, orderCode);
                                    break;
                                }
                                case "callbackResultPayment": {
                                    Utils.LOGD(TAG, "App Flutter call back cancel: callbackDataTcp");
                                    String status = call.argument("status");
                                    String orderID = call.argument("orderID");
                                    String totalAmount = call.argument("totalAmount");
                                    String label = call.argument("label");
                                    String orderCode = call.argument("orderCode");
                                    String transId = call.argument("transId");
                                    int errCode = call.argument("errCode");
                                    callbackTransToSocket(status, orderID, totalAmount, label, orderCode, transId, errCode);
                                    break;
                                }
                                case "callbackResultVoid": {
                                    Utils.LOGD(TAG, "App Flutter call back cancel: callbackResultVoid");
                                    String serviceName = call.argument("serviceName");
                                    String orderID = call.argument("orderID");
                                    String transCode = call.argument("transCode");
                                    String responseCode = call.argument("responseCode").toString();
//                                    int confirmVoid = Integer.parseInt(call.argument("confirmVoid").toString());
//                                    int permitPrintReceipt = Integer.parseInt(call.argument("permitPrintReceipt").toString());
                                    handlerCallbackVoidResult(serviceName, orderID, transCode, responseCode);
                                    break;
                                }
                                case Action_Pay_Deposit:
                                    processPayDeposit(call.arguments.toString(), false);
                                    break;
                                case ACTION_PAY_MOTO:
                                    processPayMoto(call.arguments.toString());
                                    break;
                                case "ActionDisplayNavbar": {
                                    Utils.LOGD(TAG, "App Flutter call back cancel: ActionDisplayNavbar");
                                    boolean isDisplayNavbar = call.argument("isDisplayNavbar");
                                    handlerActionDisplayNavbar(isDisplayNavbar);
                                    returnResultSuccess(true);
                                    break;
                                }
                                case "ActionDisableStatusBar": {
                                    Utils.LOGD(TAG, "App Flutter call back cancel: ActionDisableStatusBar");
                                    boolean isDisableStatusBar = call.argument("isDisableStatusBar");
                                    handlerActionDisableStatusBar(isDisableStatusBar);
                                    returnResultSuccess(true);
                                    break;
                                }
                                case "setCacheDataFormKey": {
                                    Utils.LOGD(TAG, "App Flutter call: setCacheDataFormKey");
                                    String key = call.argument("key");
                                    Object data = call.argument("data");
                                    PrefLibTV.getInstance(getContext()).put(key, data);
                                    break;
                                }
                                case "getCacheDataFormKey": {
                                    Utils.LOGD(TAG, "App Flutter call back: getCacheDataFormKey");
                                    String key = call.argument("key");
                                    int keyType = call.argument("type");
                                    getCacheDataFormKey(key, keyType);
                                    break;
                                }
                                case "actionOpenApp": {
                                    Utils.LOGD(TAG, "App Flutter call back: actionOpenApp");
                                    sendIntentOpenAppToMposSDK();
                                    returnResultSuccess(true);
                                    break;
                                }
                                case ActionGetDetailAndPrintReceipt:
                                    processDownAndPrintReceipt(call.arguments.toString());
                                    break;
                                case "SET_PRINT_MORE_RECEIPT":
                                    int value = Integer.parseInt(call.arguments.toString());
                                    PrefLibTV.getInstance(getApplicationContext()).put("PRINT_MORE", value);
                                    break;
                                case "INIT_KEYBOARD_RECEIVER":
                                    n04KeyBoard = new N04KeyBoard(getContext(), saveLog, new N04KeyBoard.ItfReceiverKeyAction() {
                                        @Override
                                        public void doReceiverKeyAction(String type, double amount) {
                                            methodChannel.invokeMethod(type, amount);
                                        }
                                    });
                                    n04KeyBoard.registerReceiver();
                                    break;
                                default:
                                    result.notImplemented();
                                    break;
                            }
                            handlerTimeoutCallback.removeCallbacks(runnableTimeoutCallback);
                            if (!"callPaymentScanCard".equals(call.method) && !"initBluetoothAndDevice".equals(call.method)) {
                                handlerTimeoutCallback.postDelayed(runnableTimeoutCallback, 50000);
                            }
                        })
                );

        methodChannelCashier =  new MethodChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), CHANNEL_CASHIER);
        methodChannelCashier.setMethodCallHandler(
                        ((call, result) -> {
                            resultChannelCashier = result;
                            routeChannel = ROUTE_CASHIER;
                            Utils.LOGD(TAG, "call native (module) - method: "+call.method);
                            appendLogAct(call.method);
                            handleMethodReceiverCashier(call);
                        })
                );

        new EventChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), CHANNEL_EVENT_KEYBOARD_ANDROID).setStreamHandler(
                new EventChannel.StreamHandler() {
                    @Override
                    public void onListen(Object args, EventChannel.EventSink events) {
                        eventChannelKeyboard = events;
                    }

                    @Override
                    public void onCancel(Object args) {
                        eventChannelKeyboard = null;
                    }
                }
        );

        init();
    }

    private void getTidAndMid() {
        String result = "";
        String tid = PrefLibTV.getInstance(getContext()).get(PrefLibTV.MPOS_TID, String.class, "");
        String mid = PrefLibTV.getInstance(getContext()).get(PrefLibTV.MPOS_MID, String.class, "");
        Map<String, String> resultMap = new HashMap<>();
        resultMap.put("tid", tid);
        resultMap.put("mid", mid);
        try {
            JSONObject jsonObject = new JSONObject(resultMap);
            result = jsonObject.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }

        returnResultSuccess(result);
    }

    private void sendIntentOpenAppToMposSDK() {
        Intent intent = new Intent();
        intent.setAction(Intents.nameFilterActionToPayment);
        intent.putExtra(Intents.EXTRA_DATA_BC_HANDLE_ACTION, "AppIsOpen");
        sendBroadcast(intent);
    }

    private void getCacheDataFormKey(String key, int keyType) {
        Class cls;
        if (keyType == 1) {
            cls = String.class;
        } else if (keyType == 2) {
            cls = Integer.class;
        } else if (keyType == 3) {
            cls = Boolean.class;
        } else if (keyType == 4) {
            cls = Float.class;
        } else {
            cls = Long.class;
        }
        returnResultSuccess(PrefLibTV.getInstance(getContext()).get(key, cls));
    }

    private void getCacheDataFormKeyCashier(String key, int keyType) {
        Class cls;
        if (keyType == 1) {
            cls = String.class;
        } else if (keyType == 2) {
            cls = Integer.class;
        } else if (keyType == 3) {
            cls = Boolean.class;
        } else if (keyType == 4) {
            cls = Float.class;
        } else {
            cls = Long.class;
        }
        resultChannelCashier.success(PrefLibTV.getInstance(getContext()).get(key, cls));
    }

    private void handlerCallbackVoidResult(String serviceName, String orderID, String transCode, String responseCode) {
        DataVoid dataVoid = new DataVoid(serviceName, orderID, transCode);
        dataVoid.setResponseCode(responseCode);
        mposSocketServer.handleCallbackVoidTrans(MyGson.getGson().toJson(dataVoid));
    }

    private void handlerCallbackCancelPaymentTcp(String status, String orderID, String totalAmount, String label, String orderCode) {
        DataPay dataPay = new DataPay(totalAmount, "", orderCode);
        dataPay.setResign(false);
        dataPay.setWfDetailRes(new WfDetailRes());
        dataPay.setLabel(label);
        ResultPay resultObj = new ResultPay(orderID, status, dataPay.getTxId(), dataPay);
        ResultPayWrapper resultWrapper = new ResultPayWrapper(new UserCard(dataPay), resultObj);
        mposSocketServer.handlerCancelTrans(resultWrapper, true);
    }

    private void callbackTransToSocket(String status, String orderID, String totalAmount, String label, String orderCode, String txId, int errCode) {
        DataPay dataPay = new DataPay(totalAmount, "", orderCode);
        dataPay.setResign(false);
        dataPay.setWfDetailRes(new WfDetailRes());
        dataPay.setLabel(label);
        if (!TextUtils.isEmpty(orderCode) && ((orderCode.length() - 6) >= 0)) {
            dataPay.setAuthCode(orderCode.substring(orderCode.length() - 6));          // 6 số cuối
        }
        dataPay.setTxId(txId);
        ResultPay resultObj;
        if (status.equals(APPROVED)) {
            resultObj = new ResultPay(orderID, status, dataPay.getTxId(), dataPay);
        } else {
            resultObj = new ResultPay(orderID, status, dataPay.getTxId(), new DataError(errCode));
        }
        ResultPayWrapper resultWrapper = new ResultPayWrapper(new UserCard(dataPay), resultObj);
        mposSocketServer.handleResultPay(resultWrapper,false);
    }

    public void handlerActionDisplayNavbar(boolean display) {
        if (DevicesUtil.isSP02()) {
            Utils.LOGD(TAG, "switchDisableNavbar = " + display);
            Intent intent = new Intent();
            intent.setAction(ACTION_SET_NVBAR);
            intent.addFlags(Intent.FLAG_ACTIVITY_PREVIOUS_IS_TOP);
            intent.putExtra("navbar_display", display);
            sendBroadcast(intent);
        }
    }

    public void handlerActionDisableStatusBar(boolean b) {
        Utils.LOGD(TAG, "switchDisableStatusBar = " + b);
        if (DevicesUtil.isSP02P8()) {
            int isDisable = 0;
            if (!b) {
                isDisable = 1;
            }
            try {
                Intent intent = new Intent(ACTION_SET_STATUSBAR_PULL_DOWN);
                intent.addFlags(Intent.FLAG_ACTIVITY_PREVIOUS_IS_TOP);
                intent.putExtra(EXTRA_INTENT_STATUS_BAR_STATE_KEY, isDisable);
                intent.putExtra("status_bar", isDisable == 1);  //true or false, default true, new version
                sendBroadcast(intent);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else if (DevicesUtil.isSP02()){
            LibKozenP5 libKozenP5 = new LibKozenP5(getApplicationContext());
            libKozenP5.disableStatusBarP5(b);
        }
    }

    private void fetchQrConfig(String muid) {
        appendLogReq("fetchQrConfig");
        DataQr dataQr = new DataQr(PrefLibTV.getInstance(getContext()).getMerchantsId(), muid);
        StringEntity entity = ApiMultiAcquirerInterface.getInstance().buildStringEntity(dataQr);

        MposRestClient.getInstance(getContext()).post(getContext(), ApiMultiAcquirerInterface.URL_GET_QR_CONFIG, entity, ConstantsPay.CONTENT_TYPE, new MyTextHttpResponseHandler(getContext()) {
            @Override
            public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                Utils.LOGD(TAG, "fetchQrConfig onFailure() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");
                appendLogAct("fetchQrConfig fail: "+statusCode);

                String clearData = MacqUtil.getInstance().parseAndDecryptData(rawJsonData);
                Utils.LOGD(TAG, "onFailApi: clearContent="+clearData);
                returnResultError(new DataError(statusCode), rawJsonData);
            }

            @Override
            public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                Utils.LOGD(TAG, "fetchQrConfig onSuccess() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonResponse = [" + rawJsonResponse + "]");
                if (statusCode == HttpStatus.SC_OK) {

                    String clearData = MacqUtil.getInstance().parseAndDecryptData(rawJsonResponse);
                    Utils.LOGD(TAG, "onSuccess fetchQrConfig: " + clearData);

                    appendLogAct("fetchQrConfig success");
                    returnResultSuccess(clearData);
                }
            }
        });

    }

    private void fetchStaticQr(String muid){
        DataQr dataQr = new DataQr(PrefLibTV.getInstance(getContext()).getMerchantsId(), muid);
        StringEntity entity = ApiMultiAcquirerInterface.getInstance().buildStringEntity(dataQr);

        MposRestClient.getInstance(getContext()).post(getContext(), ApiMultiAcquirerInterface.URL_GET_QR_STATIC, entity, ConstantsPay.CONTENT_TYPE, new MyTextHttpResponseHandler(getContext()) {
            @Override
            public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                Utils.LOGD(TAG, "fetchStaticQr onFailure() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");
                appendLogAct("fetchStaticQr fail: "+statusCode);

                String clearData = MacqUtil.getInstance().parseAndDecryptData(rawJsonData);
                Utils.LOGD(TAG, "onFailApi: clearContent="+clearData);
                returnResultError(new DataError(statusCode), rawJsonData);
            }

            @Override
            public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                Utils.LOGD(TAG, "fetchStaticQr onSuccess() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonResponse = [" + rawJsonResponse + "]");
                if (statusCode == HttpStatus.SC_OK) {

                    String clearData = MacqUtil.getInstance().parseAndDecryptData(rawJsonResponse);
                    Utils.LOGD(TAG, "onSuccess fetchStaticQr: " + clearData);

                    appendLogAct("fetchStaticQr success");
                    returnResultSuccess(clearData);
                }
            }
        });
    }

    private void initReaderType(Integer readerTypeReceiver) {
        if (readerTypeReceiver != null) {
            mFlagDevices = readerTypeReceiver;
            PrefLibTV.getInstance(getContext()).setFlagDevices(readerTypeReceiver);
            readerType = convertReaderType(readerTypeReceiver);
        }
    }

    private void initPrinter() {
        if (printerMpos == null) {
            printerMpos = new LibPrinterMpos(this, saveLog);
        }
        printerMpos.setLanguageByLocate(getCurrentLanguageCode());

//        returnResultSuccess(true);


//        if (DevicesUtil.isP20L() && libPrinter == null) {
//            try {
//                libPrinter = new LibPrinterSp01(this, myP20LPrinterListener);
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
//        if (DevicesUtil.isP20L() && libPrinter == null) {
//            returnResultError("-1", "Không thể khởi tạo máy in, vui lòng thử lại sau", null);
//        } else {
//            returnResultSuccess(true);
//        }
    }

    private void printBase64Image(String base64Image) {
        if (printerMpos == null) {
            initPrinter();
        }
        printerMpos.actionPrintImageBase64(base64Image);
        returnResultSuccess(true);
//        if (DevicesUtil.isP20L()) {
//            if (libPrinter == null) {
//                returnResultError("-2", "Máy in chưa được khởi tạo, vui lòng thử lại sau", null);
//            }
//            else {
//                myP20LPrinterListener.resetHaveShowError();
//                libPrinter.printBitmap(MyTextUtils.convertTextBase64ToBitmap(data));
//                libPrinter.pushPage(3);
//                returnResultSuccess(true);
//            }
//        }
//        // smartpos pro
//        else if (DevicesUtil.isSP02P8()) {
//
//        }
    }

    private void init() {
        saveLog = SaveLogController.getInstance(getApplicationContext());
    }

    /*private void processCheckIsP20L() {
        String serialP20L = "";
        if (DevicesUtil.isP20L()) {
            libP20L = new LibP20L(getApplicationContext());
            libP20L.setCallBackSaveLog((typeLog, s) -> {
                if (typeLog == ItfAppendLog.TypeLog.action) {
                    appendLogAct(s);
                }
                else {
                    appendLogReq(s);
                }
            });
            serialP20L = libP20L.getSerialNumber();
            if (TextUtils.isEmpty(serialP20L)) {
                serialP20L = PrefLibTV.getSerialNumber(this);
            }
        }
        returnResultSuccess(serialP20L);
    }*/
    private void processGetSerialnumber() {
        String serialNumber = "";
        int readerType = 0;
        if (DevicesUtil.isP20L()) {
            LibP20L libP20L = new LibP20L(getApplicationContext());
            libP20L.setCallBackSaveLog((typeLog, s) -> {
                if (typeLog == ItfAppendLog.TypeLog.action) {
                    appendLogAct(s);
                }
                else {
                    appendLogReq(s);
                }
            });
            serialNumber = libP20L.getSerialNumber();
            readerType = ConstantsPay.ReaderType.SP01.getReaderType();
        }
        else if (DevicesUtil.isSP02() || DevicesUtil.isSP02P12()) {
            LibKozenP5 libKozenP5 = new LibKozenP5(getApplicationContext());
            libKozenP5.setCallBackSaveLog((typeLog, s) -> {
                if (typeLog == ItfAppendLog.TypeLog.action) {
                    appendLogAct(s);
                }
                else {
                    appendLogReq(s);
                }
            });
            serialNumber = libKozenP5.getSerialNumber();
            readerType = ConstantsPay.ReaderType.SP02.getReaderType();
        }
        if (TextUtils.isEmpty(serialNumber)) {
            serialNumber = PrefLibTV.getInstance(getContext()).getSerialNumber();
        }
        returnResultSuccess(MyGson.getGson().toJson(new ReaderMpos(serialNumber, readerType)));
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        route = getInitialRoute();

        KeyboardVisibilityEvent.setEventListener(
                getActivity(),
                isOpen -> {
                    if (eventChannelKeyboard != null) {
                        eventChannelKeyboard.success(isOpen);
                    }
                });

        createHandleUncaughtException();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
//        if (libPrinter != null) {
//            libPrinter.disconnectPrinter();
//        }
        if (printerMpos != null) {
            printerMpos.disconnectPrinter();
        }

        closeAndResetSocket();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        Utils.LOGD(TAG, requestCode + "    " + resultCode);
        switch (requestCode) {
//            case REQUEST_ENABLE_BT:
//                if (resultCode == RESULT_OK) {
//                    showViewConnectDevicePage(mFlagDevices);
//                } else {
//                    returnResultSuccess("");
//                }
//                break;
//            case REQUEST_DEVICE:
//                if (resultCode == RESULT_OK) {
////                    connectPr01();
//                } else {
//                    returnResultSuccess("");
//                }
//                break;
//            case REQUEST_ACTIVATION:
//                if (resultCode == RESULT_OK) {
//                    finish();
//                }
//                break;
//            case REQUEST_DEVICE_DSPREAD:
//                // When DeviceListActivity returns with a device to connect
//                if (resultCode == RESULT_OK) {
//                    // Get the device MAC address
//                    //                    int index = data.getExtras().getInt("index");
////                    bluetoothAddressDspread = (data.getExtras() == null) ? "" : data.getExtras().getString(DeviceListActivity.EXTRA_DEVICE_ADDRESS);
//                    serialNumber = (data.getExtras() == null) ? "" : data.getExtras().getString(DeviceListActivity.EXTRA_DEVICE_SERIALNUMBER);
//
//                    setSerialNumber(ConstantsPay.DEVICE_DSPREAD, serialNumber);
//                    returnResultSuccess(serialNumber);
//                } else {
//                    returnResultSuccess("");
//                }
//                break;
            case LibPrinterS85.REQUEST_ENABLE_BT:
            case LibPrinterS85.REQUEST_CONNECT_DEVICE:
                if (printerMpos != null) {
                    printerMpos.handlerResultSelectBluetoothPrinter(requestCode, resultCode, data);
                }
                break;
            case REQUEST_CODE_PAYMENT:
                if (resultCode == RESULT_OK) {
                    if (data != null) {
                        String result = data.getStringExtra(Intents.EXTRA_DATA_CALLBACK);
                        Utils.LOGD(TAG, "onActivityResult: " + result);
                        if (routeChannel != ROUTE_APP && DevicesUtil.isSP02N4()) {
                            // check status = APPROVED thì bật sound truền vào amountAuthorized
                            // sound

                            try {
                                JSONObject resultJson = new JSONObject(result);

                                if (resultJson.has("result")) {
                                    JSONObject resultObj = resultJson.getJSONObject("result");

                                    // Kiểm tra nếu status là APPROVED
                                    String status = resultObj.optString("status", "");
                                    if ("APPROVED".equalsIgnoreCase(status)) {

                                        JSONObject userCard = resultJson.optJSONObject("userCard");
                                        if (userCard != null) {
                                            int amountAuthorized = userCard.optInt("amountAuthorized", 0);

                                            // Gọi TTS phát âm thanh
                                            speakSuccess(this, String.valueOf(amountAuthorized));
                                        }
                                    }
                                }
                            } catch (Exception e) {
                                Log.e(TAG, "Lỗi parse JSON kết quả thanh toán", e);
                            }
                        }
                        returnSuccessResultForFlutter(result);
                    }
                }
                break;
            default:
                break;
        }
    }

    public void speakSuccess(Context context, String amount) {
        TTSHelper ttsHelper = new TTSHelper();
        ttsHelper.speakSuccess(context, amount);
    }


    private DataPay convertFromResultPayWrapper(ResultPayWrapper resultPay) {
        DataPay dataPay = new DataPay(resultPay.getResult().amount, "", resultPay.getResult().paymentIdentifier);
        dataPay.setTrId(resultPay.getResult().trId);
        dataPay.setTxId(resultPay.getResult().transId);
        dataPay.setPan(resultPay.getUserCard().getPan());
        dataPay.setLabel(resultPay.getUserCard().getApplicationLabel());
        dataPay.setName(resultPay.getUserCard().getCardHolderName());
        dataPay.setAuthCode(resultPay.getUserCard().getAuthCode());
        return dataPay;
    }


    /*private void showViewConnectDevicePage(int typeDevice) {
        if (typeDevice == ConstantsPay.DEVICE_DSPREAD) {
            showDeviceDspread();
        } else {
            showActivityListDevicesPR();
        }
    }

    private void showDeviceDspread() {
        Intent intentRequestDevice = new Intent(this, DeviceListActivity.class);
        intentRequestDevice.putExtra(DeviceListActivity.EXTRA_IS_CONNECT_DEVICE, true);
        intentRequestDevice.putExtra(DeviceListActivity.EXTRA_AUTO_CONNECT, true);
        intentRequestDevice.putExtra(DeviceListActivity.EXTRA_DISCONNECT_WHEN_DESTROY, true);
        intentRequestDevice.putExtra(DeviceListActivity.EXTRA_SHOW_IMG_INFO, true);
        intentRequestDevice.setFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT);
        startActivityForResult(intentRequestDevice, REQUEST_DEVICE_DSPREAD);
    }

    private void showActivityListDevicesPR() {
        Intent intentRequestDevice = new Intent(this, DeviceActivity.class);
        intentRequestDevice.putExtra(DeviceListActivity.EXTRA_FILTER_NAME, true);
        intentRequestDevice.setFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT);
        startActivityForResult(intentRequestDevice, REQUEST_DEVICE);
    }

    private void setSerialNumber(int type, String serialNumber) {
        Utils.LOGD(TAG, serialNumber);
    }*/

    private void showToast(int p) {
        if (toastUtil == null) {
            toastUtil = new ToastUtil(this);
        }
        toastUtil.showToast(getString(p));
    }

    private void initMposSdk(String userName, String password, String appType) {
        Utils.LOGD(TAG, "initMposSdk: ==>");
//        if (mposSdk != null) {
//            return;
//        }
        mposSdk = new MposSdk(userName, password, ConstantsPay.ReaderType.SP02);
        mposSdk.setVersionLite(true);
//        MposCustom mposCustom = getMposCustom();
        mposSdk.setMposCustom(getMposCustom());
        mposSdk.setLoginByDevice(DevicesUtil.isSP02P12() || DevicesUtil.isSP02N4());
//        mposSdk.setAppType(appType);
        PrefLibTV.getInstance(this).put(PrefLibTV.versionLiteNew, isUiLiteNew);
//        if (PrefLibTV.getInstance(getContext()).getPermitSocket()) {
//            mposSdk.setHandlerActionPayment(nameFilterActionPayment);
//        }
    }

    @NonNull
    private MposCustom getMposCustom() {
        Utils.LOGD(TAG, "getMposCustom: ==>");
        MposCustom mposCustom = new MposCustom();
        mposCustom.setContactEmail("<EMAIL>");
        mposCustom.setHotline("1900-63-64-88");
        mposCustom.setLanguage(getCurrentLanguageCode());
        if (!isUiLiteNew) {
            mposCustom.setColorBackground("#FFFFFF");
            mposCustom.setColorStatusBar("#008BF4");
        }
        mposCustom.setCurrencyFormat(".");
        mposCustom.setCurrencyShow(CurrencyShow.d);
        if (DevicesUtil.isSP02N4()) {
            mposCustom.setShowToolbar(false);
        } else if (DevicesUtil.isSP02P12()) {
//            mposCustom.setShowBackToolbar(true);
//            mposCustom.setShowToolbar(false);
            mposCustom.setShowToolbar(true);
            mposCustom.setColorBackground("#E7E7E9");
//            mposCustom.setColorAmountPay("#F48232");
        } else {
            mposCustom.setShowToolbar(true);
        }
//        mposCustom.setBtnBackStatusBarId(R.drawable.ic_close);
        return mposCustom;
    }

    private void callLoginLevel1(String dataFromLoginSdk, String serialNumber, String mAcc, String pin) {
        libLoginHandler = new LibLoginHandler(getContext(), itfHandlerResultLogin, readerType.getReaderType());
        libLoginHandler.setCustomLanguage(getCurrentLanguageCode(), true);
        libLoginHandler.setHandlerTranWaitSignature(true);
        libLoginHandler.setItfHandlerWaitSignatureMA(itfHandlerTransWaitSignature);
        currentLoginLevelRequest = 1;
        libLoginHandler.loginBankWithConfigMpos(dataFromLoginSdk, serialNumber, mAcc, pin);
//        libLoginHandler.fetchMerchantConfig(mAcc, serialNumber, pin);
    }

    private void continueTransactionUnsign(String paymentIdentify) {
        try {
            mposSdk.processUnsignedTransaction(this, paymentIdentify, REQUEST_CODE_PAYMENT);
        } catch (Exception e) {
            e.printStackTrace();
            returnResultError("", e.getMessage(), null);
        }
    }

    private void gotoSignature(DataReversalLogin dataReversal) {
//        Intent i = new Intent(this, MposPaymentActivity.class);

        DataPay dataPay = new DataPay(dataReversal);
//        dataPay.setEmail(email);
        dataPay.setWfId(dataReversal.wfId);
        dataPay.setResign(true);

//        i.putExtra(Intents.EXTRA_DATA_PAY_MP, dataPay);
//        startActivityForResult(i, -4);

//        initLibReaderForSignature(dataPay);

        try {
            mposSdk.processUnsignedTransaction(this, dataPay, REQUEST_CODE_PAYMENT);
        } catch (Exception e) {
            e.printStackTrace();
            returnResultError("", e.getMessage(), null);
        }
    }

    private DataReversalLogin buildDataReversalLogin(String amount, String trxType, String pan, String itemDescription, String holderName, String transactionRequestId, String transactionDate, String udid, String wfId) {
        DataReversalLogin dataReversalLogin = new DataReversalLogin();

        dataReversalLogin.amount = amount;
        dataReversalLogin.trxType = trxType;
        dataReversalLogin.pan = pan;
        dataReversalLogin.itemDesc = itemDescription;
        dataReversalLogin.cardholderName = holderName;
        dataReversalLogin.transReqId = transactionRequestId;
        dataReversalLogin.transactionDate = transactionDate;
        dataReversalLogin.paymentIdentify = udid;
        dataReversalLogin.wfId = wfId;

        return dataReversalLogin;
    }

    private void initLibReaderForSignature(DataPay dataPay) {
        LibReaderController libReaderController = new LibDspreadReader(MainActivity.this);
//        initMoreInfoTrans(libReaderController, intent);
        libReaderController.setUseNewSignature(true);
        libReaderController.setShowErrorCodeInMsg(false);
        libReaderController.setCallback(new LibReaderController.ItfResultPay() {
            @Override
            public void onSuccessPay(DataPay dataPay) {

            }

            @Override
            public void onFailPay(DataPay dataPay, DataError dataError, int i, int i1, boolean b) {

            }

            @Override
            public void onFinishWithTypeError(int i) {

            }
        });
        libReaderController.setCallBackSaveLog(new ItfAppendLog() {
            @Override
            public void appendLog(TypeLog typeLog, String s) {

            }
        });
//        libReaderController.setShowErrorCodeInMsg(false);
        libReaderController.startSignature(dataPay);
    }

    private void callLoginLevel2(String mAcc, String pin) {
        libLoginHandler = new LibLoginHandler(getContext(), itfHandlerResultLogin, readerType.getReaderType());
        libLoginHandler.setCustomLanguage(getCurrentLanguageCode(), true);
        currentLoginLevelRequest = 2;
        libLoginHandler.loginLevel2(mAcc, pin);
    }

    private void callCheckLoginLevel2() {
        libLoginHandler = new LibLoginHandler(getContext(), readerType.getReaderType());
        libLoginHandler.setCustomLanguage(getCurrentLanguageCode(), true);
        returnResultSuccess(!libLoginHandler.checkNeedLoginLevel2());
    }

    private void callChangePass(String oldPass, String newPass) {
        libLoginHandler = new LibLoginHandler(getContext(), ConstantsPay.ReaderType.SP02.getReaderType());
        libLoginHandler.setCustomLanguage(getCurrentLanguageCode(), true);
        if (mposTransactions.isRunMacq()) {
            libLoginHandler.changePasswordWithNewPassMacq(PrefLibTV.getInstance(getContext()).getUserId(), newPass, oldPass, new LibLoginHandler.ItfResultChangePass() {
                @Override
                public void onSuccessChangePass() {
                    returnResultSuccess("SUCCESS");
                }

                @Override
                public void onFailChangePass(@NonNull DataError dataError) {
                    returnResultError(dataError.getErrorCode() + "", dataError.getMsg(), null);
                }
            });
        } else {
            libLoginHandler.changePasswordWithNewPass(oldPass, newPass, new LibLoginHandler.ItfResultChangePass() {
                @Override
                public void onSuccessChangePass() {
                    returnResultSuccess("SUCCESS");
                }

                @Override
                public void onFailChangePass(@NonNull DataError dataError) {
                    returnResultError(dataError.getErrorCode() + "", dataError.getMsg(), null);
                }
            });
        }
    }

    private void callLoginMacqByDevice(String userName, String serialNumber, String deviceIdentifier, String merchantId) {
        mposTransactions.setRunMacq(true);

        initMposSdk(userName, null, null);
        libLoginHandler = new LibLoginHandler(getContext(), itfHandlerResultLogin, readerType.getReaderType());
        libLoginHandler.setCustomLanguage(getCurrentLanguageCode(), true);
        libLoginHandler.setItfHandlerMposConfig(config ->
                {
                    merchantConfig = config;
                    checkConfigRethink(config);
                }
        );

        currentLoginLevelRequest = 1;
        libLoginHandler.setTypeUseCache(Constants.TYPE_CACHE_SESSION_IN_DAY_AND_ONFAIL_API_FOREVER);
        libLoginHandler.setDeviceIdentifier(deviceIdentifier);
        libLoginHandler.fetchMerchantAllConfigMacq(userName, serialNumber, merchantId);
    }

    private void initAuthenMA(String user, String password, String serialNumber) {
        LibLoginMacq libLoginMacq = new LibLoginMacq(this, user,
                password, serialNumber,
                new LibLoginMacq.ItfHandlerResultLoginMacq() {
                    public void appendLogMacq(String log) {
                        appendLogAct("" + log);
                    }

                    @Override
                    public void onFailLoginMacq(int type, int statusCode, String rawJsonData) {
                        appendLogAct("onFailLoginMacq statusCode= " + statusCode);
                    }

                    public void onSuccessLoginMacq(LoginRes loginRes) {
                        appendLogAct("onSuccessLoginMacq");
                    }
                });
        libLoginMacq.initAuthenMA();
    }

    private void verifyReader(String serialNumber, String password) {
        LibLoginMacq libLoginMacq = new LibLoginMacq(this, null, password, serialNumber, new LibLoginMacq.ItfHandlerResultLoginMacq() {
            @Override
            public void appendLogMacq(String s) {
                appendLogReq(s);
            }

            @Override
            public void onFailLoginMacq(int type, int statusCode, String rawJsonData) {
                appendLogAct("onFailLoginMacq statusCode= " + statusCode);
                DataError dataError = new DataError();
                if (statusCode == 0) {
                    dataError.build(statusCode, rawJsonData, getString(R.string.error_ma_default_code0, String.valueOf(statusCode)));
                }
                else {
                    dataError.build(statusCode, rawJsonData, getString(R.string.error_ma_default, String.valueOf(statusCode)));
                }

                returnResultError(String.valueOf(statusCode), dataError.getMsg(), null);
            }

            @Override
            public void onSuccessLoginMacq(LoginRes loginRes) {
                returnResultSuccess("");
            }
        });
        libLoginMacq.verifySerialNumber();
    }

    private final LibLoginHandler.ItfHandlerResultLogin itfHandlerResultLogin = new LibLoginHandler.ItfHandlerResultLogin() {
        @Override
        public void showLoading(boolean b) {
            Utils.LOGD(TAG, "showLoading: " + b);
        }

        @Override
        public void onFailureLogin(DataError dataError, int i) {
            Utils.LOGD(TAG, "onFailureLogin: " + dataError.getErrorCode() + " - " + dataError.getMsg());
            returnResultError(dataError.getErrorCode() + "", dataError.getMsg(), null);
        }

        @Override
        public void onHaveTranWaitSignature(DataReversalLogin dataReversalLogin) {
            Utils.LOGD(TAG, "onHaveTranWaitSignature: ");
            returnResultSuccess(MyGson.getGson().toJson(dataReversalLogin));
        }

        @Override
        public void onSuccessLogin() {
            Utils.LOGD(TAG, "onSuccessLogin: level " + currentLoginLevelRequest);
            returnResultSuccess(merchantConfig);
        }
    };

    private final LibLoginHandler.ItfHandlerTransWaitSignature itfHandlerTransWaitSignature =
            list -> returnResultSuccess(MyGson.getGson().toJson(list));

    private final MposTransactionsMacq.ItfHandlerActionTrans<ArrayList<PaymentItem>> itfHandlerActionTransListTrans = new MposTransactions.ItfHandlerActionTrans<ArrayList<PaymentItem>>() {
        @Override
        public void onFailureActionTrans(@NonNull DataError dataError, MposTransactions.ActionTrans actionTrans) {
            returnResultError(dataError.getErrorCode() + "", dataError.getMsg(), actionTrans.toString());
        }

        @Override
        public void onSuccessActionTrans(ArrayList<PaymentItem> paymentItems, MposTransactions.ActionTrans actionTrans) {
            DataListTransactionHistory dataListTransactionHistory = new DataListTransactionHistory(PrefLibTV.getInstance(getContext()).getPermitVoid(),
                    PrefLibTV.getInstance(getContext()).getPermitSettlement(), paymentItems);
            returnResultSuccess(MyGson.getGson().toJson(dataListTransactionHistory));
        }
    };

    private final MposTransactionsMacq.ItfHandlerActionTrans<Boolean> itfHandlerActionTransSettle = new MposTransactions.ItfHandlerActionTrans<Boolean>() {
        @Override
        public void onFailureActionTrans(@NonNull DataError dataError, MposTransactions.ActionTrans actionTrans) {
            returnResultError(dataError.getErrorCode() + "", dataError.getMsg(), actionTrans.toString());
        }

        @Override
        public void onSuccessActionTrans(Boolean aBoolean, MposTransactions.ActionTrans actionTrans) {
            returnResultSuccess(aBoolean);
        }
    };

    private final MposTransactionsMacq.ItfHandlerActionTrans<TransItem> itfHandlerActionTransDetail = new MposTransactions.ItfHandlerActionTrans<TransItem>() {
        @Override
        public void onFailureActionTrans(@NonNull DataError dataError, MposTransactions.ActionTrans actionTrans) {
            returnResultError(dataError.getErrorCode() + "", dataError.getMsg(), actionTrans.toString());
        }

        @Override
        public void onSuccessActionTrans(TransItem transItem, MposTransactions.ActionTrans actionTrans) {
            returnResultSuccess(MyGson.getGson().toJson(transItem.getTransactionDetail()));
        }
    };

    private final MposTransactionsMacq.ItfHandlerActionTrans<TransItem> itfHandlerActionTransStatus = new MposTransactions.ItfHandlerActionTrans<TransItem>() {
        @Override
        public void onFailureActionTrans(@NonNull DataError dataError, MposTransactions.ActionTrans actionTrans) {
            returnResultError(dataError.getErrorCode() + "", dataError.getMsg(), actionTrans.toString());
        }

        @Override
        public void onSuccessActionTrans(TransItem transItem, MposTransactions.ActionTrans actionTrans) {
            returnResultSuccess(MyGson.getGson().toJson(transItem.getTransactionDetail()));
        }
    };

    private final MposTransactionsMacq.ItfHandlerActionTrans<Boolean> itfHandlerActionTransVoid = new MposTransactions.ItfHandlerActionTrans<Boolean>() {
        @Override
        public void onFailureActionTrans(@NonNull DataError dataError, MposTransactions.ActionTrans actionTrans) {
            returnResultError(dataError.getErrorCode() + "", dataError.getMsg(), actionTrans.toString());
        }

        @Override
        public void onSuccessActionTrans(Boolean aBoolean, MposTransactions.ActionTrans actionTrans) {
            returnResultSuccess(aBoolean);
        }
    };

    private final MposTransactionsMacq.ItfHandlerActionTrans<Boolean> itfHandlerActionTransEmail = new MposTransactions.ItfHandlerActionTrans<Boolean>() {
        @Override
        public void onFailureActionTrans(@NonNull DataError dataError, MposTransactions.ActionTrans actionTrans) {
            returnResultError(dataError.getErrorCode() + "", dataError.getMsg(), actionTrans.toString());
        }

        @Override
        public void onSuccessActionTrans(Boolean aBoolean, MposTransactions.ActionTrans actionTrans) {
            returnResultSuccess(aBoolean);
        }
    };

    private final MposTransactionsMacq.ItfHandlerActionTrans<String> itfHandlerActionTransReceipt = new MposTransactions.ItfHandlerActionTrans<String>() {
        @Override
        public void onFailureActionTrans(@NonNull DataError dataError, MposTransactions.ActionTrans actionTrans) {
            returnResultError(dataError.getErrorCode() + "", dataError.getMsg(), actionTrans.toString());
        }

        @Override
        public void onSuccessActionTrans(String stringData, MposTransactions.ActionTrans actionTrans) {
            returnResultSuccess(stringData);
        }
    };

    private final MposTransactionsMacq.ItfHandlerActionTrans<String> itfHandlerActionSettleReceipt = new MposTransactions.ItfHandlerActionTrans<String>() {
        @Override
        public void onFailureActionTrans(@NonNull DataError dataError, MposTransactions.ActionTrans actionTrans) {
            returnResultError(dataError.getErrorCode() + "", dataError.getMsg(), actionTrans.toString());
        }

        @Override
        public void onSuccessActionTrans(String stringData, MposTransactions.ActionTrans actionTrans) {
            returnResultSuccess(stringData);
        }
    };

    private void attemptGotoPayment(Long amount, Long amountDomestic, Long amountInternational, String phone, String email, String desc, String paymentID, String stageHandler) {
        if (mposSdk == null) {
            Utils.LOGE(TAG, "mposSdk == null");
            returnResultError("", getCurrentLanguageCode().equals(LanguageCode.LANGUAGE_EN) ? "Mpos service has not been initialized yet, please contact customer care." : "Dịch vụ Mpos chưa được khởi tạo, vui lòng thử lại sau hoặc liên hệ bộ phận CSKH để được hỗ trợ", null);
            return;
        }
        try {
            mposSdk.setHandleState(stageHandler); //anhvt xu ly rieng cho ben MuaRoi android
            appendLogAct("before chargeAmount: amount-" + amount + "-paymentID-" + paymentID);
            String paymentId;
            if (amountDomestic != null && amountInternational != null) {
                paymentId = mposSdk.chargeAmount(this, paymentID, amount, amountDomestic, amountInternational, desc, email, paymentID, null, REQUEST_CODE_PAYMENT);
            } else {
                paymentId = mposSdk.chargeAmount(this, paymentID, amount, desc, email, paymentID, REQUEST_CODE_PAYMENT);
            }
            Utils.LOGD(TAG, "paymentId: " + paymentId);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private ConstantsPay.ReaderType convertReaderType(Integer readerTypeReceiver) {
        return ConstantsPay.getReaderTypeByDevice(readerTypeReceiver);
    }

    public static boolean isEmulator() {
        return Build.FINGERPRINT.startsWith("generic")
                || Build.FINGERPRINT.startsWith("unknown")
                || Build.MODEL.contains("google_sdk")
                || Build.MODEL.contains("Emulator")
                || Build.MODEL.contains("Android SDK built for x86")
                || Build.MANUFACTURER.contains("Genymotion")
                || (Build.BRAND.startsWith("generic") && Build.DEVICE.startsWith("generic"))
                || "google_sdk".equals(Build.PRODUCT);
    }

    private LanguageCode getCurrentLanguageCode() {
        Utils.LOGD(TAG, "getCurrentLanguageCode: languageCode="+languageCode);
        if (!TextUtils.isEmpty(languageCode)) {
            if ("vi".equals(languageCode)) {
                return LanguageCode.LANGUAGE_VI;
            }
            else {
                return LanguageCode.LANGUAGE_EN;
            }
        }
        else {
            String deviceLanguageCode = Locale.getDefault().getLanguage();
            if ("vi".equals(deviceLanguageCode)) {
                return LanguageCode.LANGUAGE_VI;
            }
        }
        return LanguageCode.LANGUAGE_EN;
    }

    void showDialogWebView(String url, String title, String textClose) {
        FragmentManager fm = getFragmentManager();
        WebViewDialogFragment webViewDialogFragment = WebViewDialogFragment.newInstance(url, title, textClose);
        webViewDialogFragment.show(fm, "webViewDialogFragment");
    }
    private DataPay dataPay;

    void returnSuccessResultForFlutter(Object clearData) {
        if (routeChannel == ROUTE_APP) {
            returnResultSuccess(clearData);
        } else {
            returnResultSuccessCashier(clearData);
        }
    }

    void returnResultSuccess(Object data) {
        Utils.LOGD(TAG, "returnResultSuccess: " + data);
        try {
            String dataCompact = data != null ? (data.toString().length() > 50 ? data.toString().substring(0, 50) : data.toString()) : "";
            appendLogAct("-> success: " + dataCompact);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (resultChannel != null) {
            resultChannel.success(data);
            resultChannel = null;
            handlerTimeoutCallback.removeCallbacks(runnableTimeoutCallback);
        }
    }

    void returnResultSuccessCashier(Object data) {
//        isPayCashier = false;

        Utils.LOGD(TAG, "returnResultSuccessCashier: " + data);
        try {
            String dataCompact = data != null ? (data.toString().length() > 50 ? data.toString().substring(0, 50) : data.toString()) : "";
            appendLogAct("-> success: " + dataCompact);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (resultChannelCashier != null) {
            resultChannelCashier.success(data);
            resultChannelCashier = null;
            handlerTimeoutCallback.removeCallbacks(runnableTimeoutCallback);
        }
    }

    void returnResultError(DataError dataError, Object data) {
        returnResultError("" + dataError.getErrorCode(), dataError.getDetailError(), data);
    }
    void returnResultError(String errorCode, String errorMessage, Object data) {
        Utils.LOGD(TAG, "returnResultError() called with: errorCode = [" + errorCode + "], errorMessage = [" + errorMessage + "], data = [" + data + "]");
        try {
            appendLogAct("-> error: code=" + errorCode + " msg=" + errorMessage + " data=" + (data==null?"":data.toString()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (resultChannel != null) {
            resultChannel.error(errorCode, errorMessage, data);
            resultChannel = null;
            handlerTimeoutCallback.removeCallbacks(runnableTimeoutCallback);
        }
    }

    void returnResultErrorCashier(String errorCode, String errorMessage, Object data) {
        Utils.LOGD(TAG, "returnResultError() called with: errorCode = [" + errorCode + "], errorMessage = [" + errorMessage + "], data = [" + data + "]");
        try {
            appendLogAct("-> error: code=" + errorCode + " msg=" + errorMessage + " data=" + (data==null?"":data.toString()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (resultChannelCashier != null) {
            resultChannelCashier.error(errorCode, errorMessage, data);
            resultChannelCashier = null;
            handlerTimeoutCallback.removeCallbacks(runnableTimeoutCallback);
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        svl();
    }

    void appendLogAct(String msg) {
        if (saveLog != null) {
            saveLog.appendLogAction(msg);
        }
    }
    void appendLogReq(String msg) {
        if (saveLog != null) {
            saveLog.appendLogRequestApi(msg);
        }
    }
    void svl() {
        if (saveLog != null) {
            saveLog.saveLog();
        }
    }

    // HANDLER action in channel cashier
    void handleMethodReceiverCashier(MethodCall call) {
        initFlutterMposHandle();
        Utils.LOGD(TAG, "Module call native: " + call.method);

        switch (call.method) {
            case ACTION_PAY:
                processActStartPay(call.arguments.toString());
                break;
            case Action_Pay_Normal:
                processActStartPayNormal(call.arguments.toString());
                break;
            case Action_Pay_Deposit:
                processPayDeposit(call.arguments.toString(), true);
                break;
//            case ACTION_HISTORY:
//                processOpenHistory();
//                break;
            case ACTION_GET_CONFIG:
                processGetConfig();
                break;
            case _actionGetMerchantInfo:
                processGetMerchantInfo();
                break;
            case _actionArdGetMacqConfig:
                String data = createMacqInfo();
                returnResultSuccessCashier(data);
//                resultChannelCashier.success(data);
                break;
            case ACTION_PRINT:
                processPrintBase64Image(call.arguments.toString());
                break;
            case Action_Print_Offline:
                processPrintReceipt(call.arguments.toString());
                break;
            case ACTION_PAY_MOTO:
                processPayMoto(call.arguments.toString());
                break;
            case Action_Check_Is_P20L:
                processCheckIsP20l();
                break;
            case Action_Check_Is_SP02:
                processCheckIsSp02();
                break;
            case Action_Check_Is_SP02P8:
                processCheckIsSp02P8();
                break;
            case Action_Check_Is_AutoGoScreenCashier:
                processCheckIsAutoGoScreenCashier(true);
                break;
            case Action_Set_AutoGoScreenCashier:
                processSaveAutoGoScreenCashier(call.arguments);
                break;
            case Action_CALL_API_MACQ:
                String path = call.argument("path");
                String content = call.argument("content");
                flutterMposHandle.processSendApiMacq(path, content);
                break;
            case Action_Read_Card: {
                processReadCard();
                break;
            }
            case Action_Check_PermitDeposit:
                processCheckPermitDeposit();
                break;
            case Action_Get_Deposit_Config:
                processGetDepositConfig();
                break;
            case ActionVoidDeposit:
                processVoidDeposit(call.arguments.toString());
                break;
            case Action_Print_Summary_Receipt_Deposit:
                processPrintSummaryDeposit(call.arguments.toString());
                break;
            case ActionGetDetailAndPrintReceipt:
                processDownAndPrintReceipt(call.arguments.toString());
                break;
            case "Action_Check_Pan":
                String paymentType = call.argument("paymentType");
                String panMask = call.argument("pan");
                if ("INSTALLMENT".equalsIgnoreCase(paymentType)) {
                    PrefLibTV.getInstance(this).put(PrefLibTV.maskPanInstallmentNeedCheck, panMask);
                }
                returnResultSuccessCashier(true);
//                resultChannelCashier.success(true);
                break;
            case "Action_Set_Config_Pay":
//                resultChannelCashier.success(true);
                returnResultSuccessCashier(true);
                break;

            case Action_Cancel_Payment:
                Intent intentState = new Intent(Intents.nameFilterActionCancelOrder);
                intentState.putExtra(Intents.EXTRA_DATA_BC_CANCEL_ORDER, true);
                sendBroadcast(intentState);
                break;
            case Action_Get_SN:
                flutterMposHandle.processGetSN();
                break;
            case "ActionDisplayNavbar": {
                Utils.LOGD(TAG, "App Flutter call back cancel: ActionDisplayNavbar");
                boolean isDisplayNavbar = call.argument("isDisplayNavbar");
                handlerActionDisplayNavbar(isDisplayNavbar);
                returnResultSuccessCashier(true);
                break;
            }
            case "ActionDisableStatusBar": {
                Utils.LOGD(TAG, "App Flutter call back cancel: ActionDisableStatusBar");
                boolean isDisableStatusBar = call.argument("isDisableStatusBar");
                handlerActionDisableStatusBar(isDisableStatusBar);
                returnResultSuccessCashier(true);
                break;
            }
            case "setCacheDataFormKey": {
                Utils.LOGD(TAG, "App Flutter call: setCacheDataFormKey");
                String key = call.argument("key");
                Object value = call.argument("data");
                PrefLibTV.getInstance(getContext()).put(key, value);
                break;
            }
//            case "setCacheDataFormKey": {
////                Utils.LOGD(TAG, "App Flutter call back cancel: ActionDisableStatusBar");
////                String key = call.argument("key");
////                Object dataCache = call.argument("data");
////                PrefLibTV.getInstance(getContext()).put(key, dataCache);
//                break;
//            }
            case "getCacheDataFormKey": {
                Utils.LOGD(TAG, "App Flutter call back cancel: getCacheDataFormKey");
                String key = call.argument("key");
                int keyType = call.argument("type");
                getCacheDataFormKeyCashier(key, keyType);
                break;
            }
            case "continueTransactionUnsign": {
//                isPayCashier = true;

                String amount = call.argument("amount");
                String pan = call.argument("pan");
                String trxType = call.argument("trxType").toString();
                String itemDesc = call.argument("itemDesc");
                String cardholderName = call.argument("cardholderName");
                String transReqId = call.argument("transReqId");
                String transactionDate = call.argument("transactionDate").toString();
                String paymentIdentify = call.argument("paymentIdentify");
                String description_payment = call.argument("description_payment");
                String wfId = call.argument("wfId");
                gotoSignature(buildDataReversalLogin(amount, trxType, pan, itemDesc, cardholderName, transReqId, transactionDate, paymentIdentify, wfId));
                break;
            }
            case action_print_qr_receipt: {
                processPrintQRReceiptTrans(call.arguments.toString(), printerMpos);
                break;
            }
        }
    }

    private void processPrintQRReceiptTrans(String jsonObject, LibPrinterMpos libPrinterMpos) {
        try {
            JSONObject jsonConfig = new JSONObject(jsonObject);
            DataQrPrint dataQrPrint = new DataQrPrint();

            String businessName = JsonParser.getDataJson(jsonConfig, "printBusinessName","");
            if (businessName.isEmpty()){
                businessName = PrefLibTV.getInstance(getApplicationContext()).get(PrefLibTV.BUSINESS_NAME, String.class);
            }
            dataQrPrint.setMcName(businessName);

            String businessAddress = JsonParser.getDataJson(jsonConfig, "printBusinessAddress","");
            if (businessAddress.isEmpty()){
                businessAddress = PrefLibTV.getInstance(getApplicationContext()).get(PrefLibTV.BUSINESS_ADDRESS, String.class);
            }
            dataQrPrint.setMcAddress(businessAddress);
            String mid = JsonParser.getDataJson(jsonConfig, "printMid","");
            if (mid.isEmpty()){
                mid = PrefLibTV.getInstance(getApplicationContext()).getMId();
            }
            String tid = JsonParser.getDataJson(jsonConfig, "printTid","");
            if (tid.isEmpty()){
                tid = PrefLibTV.getInstance(getApplicationContext()).getTId();
            }

            dataQrPrint.setMid(mid);
            dataQrPrint.setTid(tid);
            dataQrPrint.setTxId(JsonParser.getDataJson(jsonConfig,"txid"));
            dataQrPrint.setRrn(JsonParser.getDataJson(jsonConfig, "rrn",""));
            dataQrPrint.setTimeTrans(JsonParser.getDataJson(jsonConfig,"createdDate"));
            dataQrPrint.setAuthCode(JsonParser.getDataJson(jsonConfig, "authCode",""));
            dataQrPrint.setIssuerName(JsonParser.getDataJson(jsonConfig, "issuerName",""));
            dataQrPrint.setIssuerName(JsonParser.getDataJson(jsonConfig, "remark",""));
            dataQrPrint.setAmount(JsonParser.getDataJson(jsonConfig, "amount"));

            initPrinter();
            libPrinterMpos.printReceiptQRCodeWithRemarkLayout(dataQrPrint);
        } catch (Exception e) {
            Utils.LOGD(TAG, "processPrintQRReceiptTrans: "+e.getMessage());
        }
    }

    void processPrintBase64Image(String base64Image) {
        initPrinter();
        int quantity = PrefLibTV.getInstance(getApplicationContext()).get("PRINT_MORE", Integer.class, 1);
        if (quantity == 0) {
            printerMpos.actionPrintImageBase64(base64Image);
            resultChannelCashier.success("");
            return;
        }

        for (int i = 1; i <= quantity; i++ ){
            new Handler().postDelayed(() -> {
                printerMpos.actionPrintImageBase64(base64Image);
            }, 500);
        }

//        actionPrintBase64(base64Image, quantity + 1);
    }

    private void processPrintReceipt(String toString) {
        WfDetailRes transItem = MyGson.parseJson(toString, WfDetailRes.class);
        DataPay dataPay = new DataPay(transItem);
        dataPay.setWfDetailRes(transItem);
        if (printerMpos == null) {
            printerMpos = new LibPrinterMpos(MainActivity.this, saveLog);
        }
        int numPrintReceipt = PrefLibTV.getInstance(getApplicationContext()).get("PRINT_MORE", Integer.class, 1);
        if (numPrintReceipt == 0) {
            printerMpos.printReceiptOfflineByLayout(dataPay);
            resultChannelCashier.success("");
            return;
        }
        for (int i = 1; i <= numPrintReceipt; i++ ){
            new Handler().postDelayed(() -> {
                printerMpos.printReceiptOfflineByLayout(dataPay);
            }, 500);
        }
        resultChannelCashier.success("");
    }

    private void processGetDepositConfig() {
        DepositConfig depositConfig = new DepositConfig();
        depositConfig.setpermitVoid(PrefLibTV.getInstance(getApplicationContext()).getPermitVoid());
        depositConfig.setpermitSettle(PrefLibTV.getInstance(getApplicationContext()).getPermitSettlement());
        depositConfig.setMaxDaySettleDeposit(PrefLibTV.getInstance(getApplicationContext()).get(PrefLibTV.maxDaySettleDeposit, Integer.class));
        String data =  MyGson.getGson().toJson(depositConfig);
        Utils.LOGD(TAG, "sendCbSetting: -->" + data);
        resultChannelCashier.success(data);
    }

    private void processPayDeposit(String arguments, boolean payCashier) {
//        isPayCashier = payCashier;
        Utils.LOGD(TAG, "processPayDeposit() called with: dataContent = " + arguments);
//        logUtil.appendLogAction("startPay: " + arguments);
        DataSaleSend payInfo = MyGson.parseJson(arguments, DataSaleSend.class);

        if (mposSdk == null) {
            Utils.LOGE(TAG, "mposSdk == null");
            if (payCashier) {
                returnResultErrorCashier("", getCurrentLanguageCode().equals(LanguageCode.LANGUAGE_EN) ? "Mpos service has not been initialized yet, please contact customer care." : "Dịch vụ Mpos chưa được khởi tạo, vui lòng thử lại sau hoặc liên hệ bộ phận CSKH để được hỗ trợ", null);
            } else {
                returnResultError("", getCurrentLanguageCode().equals(LanguageCode.LANGUAGE_EN) ? "Mpos service has not been initialized yet, please contact customer care." : "Dịch vụ Mpos chưa được khởi tạo, vui lòng thử lại sau hoặc liên hệ bộ phận CSKH để được hỗ trợ", null);
            }
            return;
        }
        try {
            String paymentId;
            appendLogAct("before chargeAmountDeposit: amount-" + payInfo.getAmount() + "-paymentID-" + payInfo.getUdid());
            paymentId = mposSdk.chargeAmountDeposit(this, payInfo.getDepositID(), Long.parseLong(payInfo.getAmount()), payInfo.getCustomerPhone(), null, payInfo.getUdid(), REQUEST_CODE_PAYMENT);
            Utils.LOGD(TAG, "paymentId: " + paymentId);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void processOpenHistory() {

    }

    private void processGetMerchantInfo() {
        String data = PrefLibTV.getInstance(getContext()).getDataLoginMerchant();
        Utils.LOGD(TAG, "processGetConfig: userId =" + PrefLibTV.getInstance(getContext()).getUserId());
        Utils.LOGD(TAG, "data: " + data);
        if (!TextUtils.isEmpty(data)) {
            try {
                JSONObject jsonConfig = new JSONObject(data);
                jsonConfig.put("muid", PrefLibTV.getInstance(getContext()).getUserId());
                if (!DevicesUtil.isSP02P12()) {
                    jsonConfig.put("password", PrefLibTV.getInstance(getContext()).getPW());
                }
                data = jsonConfig.toString();
            } catch (JSONException e) {
                e.printStackTrace();
                resultChannelCashier.success(data);
                return;
//                throw new RuntimeException(e);
            }
        }
        if (resultChannelCashier != null){
            resultChannelCashier.success(data);
        }
    }

    void processPayMoto(String data) {
        Utils.LOGD(TAG, "processPayMoto: " + data);
        StringEntity entity = ApiMultiAcquirerInterface.getInstance()
                .buildStringEntity(data);

        MposRestClient.getInstance(this).setPaymentMATimeout().post(this, ApiMultiAcquirerInterface.URL_NEW_PAYMENT, entity,
                ConstantsPay.CONTENT_TYPE, new MyTextHttpResponseHandler(getContext()) {

                    @Override
                    public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                        Utils.LOGD(TAG, "PayMoto onFailure() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");

                        String clearData = MacqUtil.getInstance().parseAndDecryptData(rawJsonData);
                        appendLogAct("onFail  sale PayMoto: " + clearData);
                        Utils.LOGD(TAG, "PayMoto onFailure: " + clearData);
                        callbackResultMoto(clearData);
//                handleFailApiMA(true, statusCode, clearData);
                    }

                    @Override
                    public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                        Utils.LOGD(TAG, "PayMoto onSuccess() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonResponse = [" + rawJsonResponse + "]");

                        String clearData = MacqUtil.getInstance().parseAndDecryptData(rawJsonResponse);
                        Utils.LOGD(TAG, "PayMoto onSuccess: " + clearData);
                        appendLogAct("PayMoto success=" + clearData);
                        callbackResultMoto(clearData);
                    }

                    private void callbackResultMoto(String data) {
                        sendCbResultMoto(data);
                    }
                });

    }

    private boolean runCheckAutoInject = false;
    private String readerInjected;
    private int deviceType;
    private boolean isRunMacq = false;
    private void resetCurrData() {
        runCheckAutoInject = false;
    }
    private void processReadCard() {
        if (ConstantsPay.MPOS_MULTI_ACQUIRER.equals(PrefLibTV.getInstance(this).getBankName())) {
            isRunMacq = true;
        }

        String serialNumber = PrefLibTV.getInstance(this).getSerialNumber();
        Utils.LOGD(TAG, "processReadCard: sn=" + serialNumber + " runCheckAutoInject=" + runCheckAutoInject);
        if (DevicesUtil.isSP02() || DevicesUtil.isP20L()) {
            if (!runCheckAutoInject) {
                runCheckAutoInject = true;
                deviceType = PrefLibTV.getInstance(this).getFlagDevices();
                if (checkAutoInjectKey(deviceType, serialNumber)){
                    return;
                }
            }
            LibReaderController libReader = DevicesUtil.isSP02() ? new LibKozenP5(this) : new LibP20L(this);
            startReadCard(libReader);
        }
    }

    private void startReadCard(LibReaderController libReader) {
        libReader.setTypeProcessCard(LibReaderController.TYPE_PROCESS_GET_CARD);
        libReader.setCbProcessCard(new ResultProcessCard() {
            @Override
            public void onSuccessReadCard(DataProcessCard dataProcessCard) {
                Utils.LOGD(TAG, "onSuccessReadCard: " + dataProcessCard.toString());
                flutterMposHandle.processCallbackCardData(null, dataProcessCard);
            }

            @Override
            public void onErrorReadCard(DataError dataError) {
                Utils.LOGD(TAG, "onErrorReadCard: " + dataError.toString());
                flutterMposHandle.processCallbackCardData(dataError, null);
            }
        });
        libReader.startPayment();
    }

    private boolean checkAutoInjectKey(int deviceType, String serialNumber) {
        readerInjected = PrefLibTV.getInstance(getContext()).get(PrefLibTV.readersInjected, String.class, "");
        libInjectKey = new LibInjectKey(getContext(), deviceType,
                ConstantsPay.SERVER_MPOS_ACQUIRER,
                serialNumber, null);
        if (libInjectKey.checkNeedAutoInjectKey(readerInjected, isRunMacq, serialNumber,
                PrefLibTV.getInstance(getContext()).getBankName())) {
            processInject();
            return true;
        }
        return false;
    }

    //  ======================================== INJECT KEY ========================================
    private void processInject() {
        libInjectKey.setCbLog((typeLog, s) -> appendLogAct(s));
        Utils.LOGD(TAG, "processInject -->" + (isRunMacq ? "macq" : "bank") + " ->" + deviceType);
        libInjectKey.setCallback((type, res, s) -> {
            Utils.LOGD(TAG, "processInjectBank: type="+type+" res="+res+" ->"+s);
            handlerFinishInject(type, res);

        });
//        libInjectKey.setCallbackShowLoading(this::showLoading);
        if (isRunMacq) {
            appendLogAct("start get Ipek MA");
            libInjectKey.getIpekFromMA();
        } else {
            appendLogAct("start get Ipek bank");
            libInjectKey.processInjectKey();
        }
    }

    private void handlerFinishInject(int type, int res) {
        if (type == LibInjectKey.TYPE_KEY_END || type == LibInjectKey.TYPE_KEY_NONE) {
            if (res == LibInjectKey.CODE_INJECT_SUCCESS) {
                libInjectKey.saveInjected(readerInjected, PrefLibTV.getInstance(this).getBankName());
            }
            if (DevicesUtil.isSP02() || DevicesUtil.isP20L()) {
                LibReaderController libReader = DevicesUtil.isSP02() ? new LibKozenP5(this) : new LibP20L(this);
                startReadCard(libReader);
            }
        }
    }

    private void sendCbResultMoto(String data) {
        appendLogAct("sendCbMoto");
//        methodChannel.invokeMethod(CALLBACK_MOTO_PAYMENT, data);
        returnSuccessResultForFlutter(data);
//        resultChannelCashier.success(data);
    }

    private void processActStartPay(String dataContent) {
//        isPayCashier = true;
        Utils.LOGD(TAG, "processActStartPay() called with: dataContent = " + dataContent);
        // {"amount":1500,"casherId":"anhnt","orderCode":"7710295","rethinkId":"b55f11ba-d16a-4bf2-afaa-55aa73995a19"}

//        logUtil.appendLogAction("startPay: " + dataContent);
        CashierPay payInfo = MyGson.parseJson(dataContent, CashierPay.class);

        if (mposSdk == null) {
            Utils.LOGE(TAG, "mposSdk == null");
            returnResultErrorCashier("", getCurrentLanguageCode().equals(LanguageCode.LANGUAGE_EN) ? "Mpos service has not been initialized yet, please contact customer care." : "Dịch vụ Mpos chưa được khởi tạo, vui lòng thử lại sau hoặc liên hệ bộ phận CSKH để được hỗ trợ", null);
            return;
        }
        try {
            String paymentId;
            appendLogAct("before chargeAmount: amount-" + payInfo.getAmount() + "-OrderID-" + payInfo.getOrderId());
            paymentId = mposSdk.chargeAmount(this, payInfo.getOrderId(), payInfo.getAmount(), payInfo.getDescription(), null, payInfo.getUdid(), REQUEST_CODE_PAYMENT);
            Utils.LOGD(TAG, "paymentId: " + paymentId);
        } catch (Exception e) {
            e.printStackTrace();
            appendLogAct("chargeAmount: Exception-" + e.toString());
        }
    }
    private void processActStartPayNormal(String dataContent) {
//        isPayCashier = true;
        Utils.LOGD(TAG, "processActStartPay() called with: dataContent = " + dataContent);
        // {"amount":1500,"casherId":"anhnt","orderCode":"7710295","rethinkId":"b55f11ba-d16a-4bf2-afaa-55aa73995a19"}
        PayNormalObj payInfo = MyGson.parseJson(dataContent, PayNormalObj.class);

        if (mposSdk == null) {
            Utils.LOGE(TAG, "mposSdk == null");
            returnResultErrorCashier("", getCurrentLanguageCode().equals(LanguageCode.LANGUAGE_EN) ? "Mpos service has not been initialized yet, please contact customer care." : "Dịch vụ Mpos chưa được khởi tạo, vui lòng thử lại sau hoặc liên hệ bộ phận CSKH để được hỗ trợ", null);
            return;
        }
        try {
            String paymentId;
            paymentId = mposSdk.chargeAmount(this, "", Long.parseLong(payInfo.getRnAmount()),
                    payInfo.getRnDescription(), payInfo.getRnEmail(), payInfo.getUdid(), REQUEST_CODE_PAYMENT);
            Utils.LOGD(TAG, "paymentId: " + paymentId);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void processSaveAutoGoScreenCashier(Object arguments) {
        PrefLibTV.getInstance(getContext()).put("isAutoGotoCashierActivity", (boolean) arguments);
    }

    private void processCheckIsAutoGoScreenCashier(boolean isCashierScreen) {
        if (isCashierScreen) {
            resultChannelCashier.success(PrefLibTV.getInstance(getContext()).get("isAutoGotoCashierActivity", Boolean.class, false));
        } else {
            returnResultSuccess(PrefLibTV.getInstance(getContext()).get("isAutoGotoCashierActivity", Boolean.class, false));
        }
    }

    private void processCheckPermitDeposit() {
        resultChannelCashier.success(PrefLibTV.getInstance(getContext()).get(PrefLibTV.allowDeposit, String.class, Constants.SVALUE_0));
    }

    private void processVoidDeposit(String data) {
        String transactionIDVoid = "";
        try {
            JSONObject object = new JSONObject(data);
            transactionIDVoid = object.getString("transactionID");
        } catch (JSONException e) {
            e.printStackTrace();
        }

        MposTransactions mposTransactions = new MposTransactions(getApplicationContext());
        mposTransactions.voidTransaction(transactionIDVoid, new MposTransactionsMacq.ItfHandlerActionTrans<Boolean>() {
            @Override
            public void onFailureActionTrans(@androidx.annotation.NonNull DataError dataError, MposTransactionsMacq.ActionTrans actionTrans) {
                Utils.LOGD(TAG, "void deposit failure: " + dataError.getMsg());
//                logUtil.appendLogAction("void deposit failure: " + dataError.getErrorCode());
                returnResultError(String.valueOf(dataError.getErrorCode()), dataError.getMsg(), "");
                MyDialogShow.showDialogError(getString(R.string.error_ma_default, dataError.getErrorCode()), dataError.getMsg(), MainActivity.this, true);
            }

            @Override
            public void onSuccessActionTrans(Boolean aBoolean, MposTransactionsMacq.ActionTrans actionTrans) {
                resultChannelCashier.success("success");
            }
        });
    }

    private void processPrintSummaryDeposit(String data) {
        Utils.LOGD(TAG, "processPrintSummaryDeposit: " + data);
        DataSummaryDeposit dataSummaryDeposit = MyGson.parseJson(data, DataSummaryDeposit.class);
        initPrinter();
        if (printerMpos != null) {
            printerMpos.printReceiptSummaryDepositOfflineByLayout(dataSummaryDeposit);
        }
        resultChannelCashier.success("print success");
    }
    private void processDownAndPrintReceipt(String wfId) {
        WorkFlow workFlow = new WorkFlow(wfId);
        StringEntity entity = ApiMultiAcquirerInterface.getInstance()
                .buildStringEntity(MyGson.getGson().toJson(workFlow));
        MposRestClient.getInstance(this).post(this, ApiMultiAcquirerInterface.URL_GET_TRANS_DETAIL, entity, new MyTextHttpResponseHandler(getContext()) {

            @Override
            public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
//                logUtil.appendLogAction("onFail get detail trans " + statusCode);
                Utils.LOGD(TAG, "TransDetails onFailure() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");
//                mPgdl.hideLoading();
                DataError dataError = new DataError();
                dataError.build(statusCode, rawJsonData, getString(R.string.error_ma_default, String.valueOf(statusCode)));
//                MyDialogShow.showDialogError(getString(R.string.dialog_error_title, dataError.getErrorCode()), dataError.getMsg(), FlutterTransferActivity.this, true);
                returnResultError(String.valueOf(dataError.getErrorCode()), dataError.getMsg(), "");
            }

            @Override
            public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
//                logUtil.appendLogAction("onSuccess get detail trans " + statusCode);
                Utils.LOGD(TAG, "TransDetails onSuccess() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonResponse = [" + rawJsonResponse + "]");
//                mPgdl.hideLoading();
                DataBaseObj data = MyGson.parseJson(rawJsonResponse, DataBaseObj.class);
                String clearData = CryptoInterface.getInstance().decryptData(data.getData());

                Utils.LOGD(TAG, "TransDetails onSuccess: " + clearData);

                WfDetailRes transItem = MyGson.parseJson(clearData, WfDetailRes.class);
                DataPay dataPay = new DataPay(transItem);
                dataPay.setWfDetailRes(transItem);
//                initPrinter();
                if (printerMpos == null) {
                    printerMpos = new LibPrinterMpos(MainActivity.this, saveLog);
                }
                printerMpos.printReceiptOfflineByLayout(dataPay);
//                libPrinter.printReceiptMaOfflineByLayout(dataPay);
                resultChannelCashier.success("");
            }
        });
    }


    private void processCheckIsSp02() {
        Utils.LOGD(TAG, "processCheckIsSp02: =>" + DevicesUtil.isSP02());
        resultChannelCashier.success(DevicesUtil.isSP02());
    }

    private void processCheckIsSp02P8() {
        Utils.LOGD(TAG, "processCheckIsSp02P8: =>" + DevicesUtil.isSP02P8());
        resultChannelCashier.success(DevicesUtil.isSP02P8());
    }

    private void processCheckIsP20l() {
        Utils.LOGD(TAG, "processCheckIsP20l: =>" + DevicesUtil.isP20L());
        resultChannelCashier.success(DevicesUtil.isP20L());
    }

    void processGetConfig() {
        String config = createConfig();
        Utils.LOGD(TAG, "processGetConfig: "+config);

        sendCbSetting();

        if (ROUTE_MOTO.equals(route)){
            sendCbMacqInfo();
        }
    }

    void sendCbMacqInfo() {
//        logUtil.appendLogAction("sendCbMacqInfo");
        String data = createMacqInfo();
        Utils.LOGD(TAG, "sendCbMacqInfo: " + data);
        methodChannelCashier.invokeMethod(CALLBACK_MACQ_INFO, data);
    }

    private String createMacqInfo() {
        MacqMerchantInfo macqMerchantInfo = new MacqMerchantInfo("",
                PrefLibTV.getInstance(getContext()).get(PrefLibTV.MPOS_MID, String.class, ""),
                PrefLibTV.getInstance(getContext()).get(PrefLibTV.MPOS_TID, String.class, ""),
                PrefLibTV.getInstance(getContext()).getUserId());
        macqMerchantInfo.setMotoBinRanges(PrefLibTV.getInstance(this).get(PrefLibTV.motoBinRanges, String.class, ""));
        return MyGson.getGson().toJson(macqMerchantInfo);
    }

//    private String createMacqInfo() {
//        MacqMerchantInfo macqMerchantInfo = new MacqMerchantInfo("",
//                PrefLibTV.getInstance(this).get(PrefLibTV.MPOS_MID, String.class, ""),
//                PrefLibTV.getInstance(this).get(PrefLibTV.MPOS_TID, String.class, ""),
//                PrefLibTV.getInstance(this).getUserId());
//        macqMerchantInfo.setMotoBinRanges(PrefLibTV.getInstance(this).get(PrefLibTV.motoBinRanges, String.class, ""));
//        return MyGson.getGson().toJson(macqMerchantInfo);
//    }

    private String createConfig() {
        RethinkConfig rethinkConfig = new RethinkConfig(
                PrefLibTV.getInstance(getContext()).get("rethinkDbName", String.class),
                PrefLibTV.getInstance(getContext()).get("rethinkHostName", String.class),
                Integer.parseInt(PrefLibTV.getInstance(getContext()).get("rethinkPort", String.class, "0")),
                PrefLibTV.getInstance(getContext()).get("rethinkUsername", String.class),
                PrefLibTV.getInstance(this).getUserId(),
                Integer.parseInt(PrefLibTV.getInstance(getContext()).getMerchantsId()),
                PrefLibTV.getInstance(getContext()).get("rethinkUserPassword", String.class),
                PrefLibTV.getInstance(getContext()).get("rethinkAutoPay", Boolean.class)
        );
//        rethinkConfig.setIsShowHistory(PrefLibTV.getInstance(getContext()).get(DataStoreApp.isShowTransactionHistory, Boolean.class));
//        if (PrefLibTV.getInstance(getContext()).get(DataStoreApp.isConfirmPassword, Boolean.class, Boolean.FALSE)) {
//            rethinkConfig.setMuPassword(PrefLibTV.getInstance(this).getPW());
//            if (DevicesUtil.isP20L()) {
//                disableButtonHomeRecent();
//            }
//        }
        rethinkConfig.setTypeDevice(PrefLibTV.getInstance(getApplicationContext()).getFlagDevices());

        return MyGson.getGson().toJson(rethinkConfig);
    }

    void sendCbSetting() {
//        logUtil.appendLogAction("sendCbSetting");
        String data = createConfig();
        Utils.LOGD(TAG, "sendCbSetting: -->" + data);
//        methodChannelCashier.invokeMethod(CALLBACK_SETTING, data);
        resultChannelCashier.success(data);

    }

    private void checkConfigRethink(String config) {
        JSONObject jObject = null;
        try {
            jObject = new JSONObject(config);

            boolean enableRethinkdb = false;
            if (jObject.has("enableRethinkdb")) {
                try {
                    enableRethinkdb = jObject.getBoolean("enableRethinkdb");
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }

//            DataStoreApp.getInstance().saveDataByKey(DataStoreApp.enableRethink, enableRethinkdb);
            PrefLibTV.getInstance(getContext()).put("enableRethink", enableRethinkdb);
            if (enableRethinkdb) {
                PrefLibTV.getInstance(getContext()).put("rethinkHostName", JsonParser.getDataJson(jObject, "rethinkHostName"));
                PrefLibTV.getInstance(getContext()).put("rethinkPort", JsonParser.getDataJson(jObject, "rethinkPort", "0"));
                PrefLibTV.getInstance(getContext()).put("rethinkDbName", JsonParser.getDataJson(jObject, "rethinkDbName"));
                PrefLibTV.getInstance(getContext()).put("rethinkUsername", JsonParser.getDataJson(jObject, "rethinkUsername"));
                PrefLibTV.getInstance(getContext()).put("rethinkUserPassword", JsonParser.getDataJson(jObject, "rethinkUserPassword"));
                PrefLibTV.getInstance(getContext()).put("rethinkAutoPay", Constants.SVALUE_1.equals(JsonParser.getDataJson(jObject, "isAutoPay", Constants.SVALUE_1)));
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void closeAndResetSocket() {
        if (mposSocketServer != null) {
            mposSocketServer.closeSocketAndService();
            mposSocketServer = null;
        }
    }

    private void registerReceiverTCP() {
        if (PrefLibTV.getInstance(getContext()).getPermitSocket()) {
            mposSdk.setHandlerActionPayment(nameFilterActionPayment);
        }
        if (mposSocketServer == null) {
            mposSocketServer = new MposSocketServer(getContext(), saveLog, new MposSocketServer.ItfReceiver() {
                @Override
                public void doReceiverFromClient(MPSocketManager.ServiceName serviceName, String msg) {
                    methodChannel.invokeMethod(serviceName.toString(), msg);
                }

                @Override
                public void doReceiverActionPayment(String action) {
                    Utils.LOGD(TAG, "actionPayment " + action);
                    methodChannel.invokeMethod(action_tcp_state_payment, action);
                }
            });
            mposSocketServer.initAndStartServiceSocket();
        }
    }

    FlutterMposHandle flutterMposHandle;
    ItfCallbackFlutter itfCallbackFlutter = new ItfCallbackFlutter() {
        @Override
        public void callbackResult(Object clearData) {
            Utils.LOGD(TAG, "callback data to Flutter: " + routeChannel + " --->"+clearData);
            returnSuccessResultForFlutter(clearData);
        }
    };
    private void initFlutterMposHandle() {
        if (flutterMposHandle == null) {
            if (saveLog == null) {
                saveLog = new SaveLogController(this);
            }
            flutterMposHandle = new FlutterMposHandle(this, saveLog, itfCallbackFlutter);
        }
    }

    public interface ItfCallbackFlutter{
        void callbackResult(Object clearData);
    }

    private Thread.UncaughtExceptionHandler androidDefaultUEH;
    private void createHandleUncaughtException() {

        androidDefaultUEH = Thread.getDefaultUncaughtExceptionHandler();
        Thread.setDefaultUncaughtExceptionHandler(handler);
    }

    private final Thread.UncaughtExceptionHandler handler = new Thread.UncaughtExceptionHandler() {
        public void uncaughtException(Thread thread, Throwable ex) {
            // log it & phone home.
            handleUncaughtException(ex);
            androidDefaultUEH.uncaughtException(thread, ex);
        }
    };

    public void handleUncaughtException(Throwable ex) {
        // not all Android versions will print the stack trace automatically
        Utils.LOGD("MyApplication", "handleUncaughtException");
        String errorString = getStackTraceString(ex);
        Utils.LOGD("MyApplication", "handleUncaughtException:" + errorString);
        SaveLogController logUtils = SaveLogController.getInstance(getApplicationContext());
        logUtils.saveLog(Utils.convertTimestamp(System.currentTimeMillis(), 3) + SaveLogController.LOG_CRASH + errorString);
        logUtils.pushLog();
    }
}
