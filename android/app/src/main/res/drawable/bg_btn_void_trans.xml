<?xml version="1.0" encoding="UTF-8" ?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <solid android:color="#acacac" />
            <stroke android:width="1dp" android:color="#acacac" />
            <corners
                android:radius="0px"
                />
        </shape>
    </item>
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="#acacac" />
            <stroke android:width="1dp" android:color="#acacac" />
            <corners
                android:radius="0px"
                />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#c2c2c2" />
            <stroke android:width="1dp" android:color="#c2c2c2" />
            <corners
                android:radius="0px"
                />
        </shape>
    </item>
</selector>
