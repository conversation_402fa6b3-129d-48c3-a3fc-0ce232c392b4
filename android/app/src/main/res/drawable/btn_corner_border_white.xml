<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <item android:state_pressed="true">
        <shape android:shape="rectangle">

            <solid android:color="@color/white_3" />
            <stroke android:width="1dip" android:color="@color/white_3" />
            <corners android:radius="@dimen/corner_item_3" />
        </shape>
    </item>

    <item android:state_enabled="true">
        <shape android:shape="rectangle">

            <solid android:color="@color/white" />
            <stroke android:width="1dip" android:color="@color/white" />
            <corners android:radius="@dimen/corner_item_3" />
        </shape>
    </item>

</selector>
