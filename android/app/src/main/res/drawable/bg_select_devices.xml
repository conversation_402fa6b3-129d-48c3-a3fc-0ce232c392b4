<?xml version="1.0" encoding="UTF-8" ?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/red_select_type_devices" />
            <stroke android:width="@dimen/stroke"
                android:color="@color/gray_2"/>
            <corners
                android:radius="0dp"
                android:topLeftRadius="@dimen/corner_item_3"
                android:topRightRadius="@dimen/corner_item_3"
                />
        </shape>
    </item>
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/red_select_type_devices" />
            <stroke android:width="@dimen/stroke"
                android:color="@color/gray_2"/>
            <corners
                android:radius="0dp"
                android:topLeftRadius="@dimen/corner_item_3"
                android:topRightRadius="@dimen/corner_item_3"
                />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/white" />

            <stroke android:width="@dimen/stroke"
                android:color="@color/gray_2"/>
            <corners android:radius="0dp"
                android:topLeftRadius="@dimen/corner_item_3"
                android:topRightRadius="@dimen/corner_item_3"
                />
        </shape>
    </item>
</selector>
