<?xml version="1.0" encoding="UTF-8" ?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <solid android:color="#FFFFFF" />
            <stroke android:width="1dp" android:color="#FFFFFF" />
            <corners
                android:radius="10px"
                />
        </shape>
    </item>
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="#FFFFFF" />
            <stroke android:width="1dp" android:color="#FFFFFF" />
            <corners
                android:radius="10px"
                />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#27ae61" />
            <stroke android:width="1dp" android:color="#27ae61" />
            <corners
                android:radius="10px"
                />
        </shape>
    </item>
</selector>
