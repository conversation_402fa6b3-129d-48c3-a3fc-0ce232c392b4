<resources>

    
     <style name="MyTheme" parent="Theme.AppCompat.Light.NoActionBar">
<!--      <style name="MyTheme" parent="@style/Theme.AppCompat.Light"> -->
        <!-- <item name="android:windowNoTitle">true</item> -->
        <!-- We will be using the toolbar so no need to show ActionBar -->
        <!-- <item name="android:windowActionBar">false</item> -->
        <!-- Set theme colors from http://www.google.com/design/spec/style/color.html#color-color-palette -->
        <!-- colorPrimary is used for the default action bar background -->
        <item name="android:colorPrimary">@color/red</item>
<!--         <item name="android:colorPrimary">#2196F3</item> -->
        <!-- colorPrimaryDark is used for the status bar -->
        <item name="android:colorPrimaryDark">@color/red</item>
<!--         <item name="android:colorPrimaryDark">#1976D2</item> -->
        <!--
         colorAccent is used as the default value for colorControlActivated
         which is used to tint widgets
        -->
        <item name="android:colorAccent">#FF4081</item>
        <!--
         You can also set colorControlNormal, colorControlActivated
         colorControlHighlight and colorSwitchThumbNormal.
        -->
        <!-- Toolbar + Overflow menu text color FFFFFF-->
      <!-- android:textColorPrimary is the  color of the title text
       in the Toolbar, in the Theme.AppCompat theme:  -->
	  <!-- <item name="android:textColorPrimary">@color/abc_primary_text_material_light</item> -->
	
	  <!-- android:textColorPrimaryInverse is the  color of the title
	       text in the Toolbar, in the Theme.AppCompat.Light theme:  -->
        <item name="android:textColorPrimaryInverse">#FFFFFF</item>
        
        <!-- Overflow menu button color -->
        <item name="android:textColorSecondary">#FFFFFF</item>
        <item name="android:textColor">#000000</item>
        <item name="android:typeface">serif</item>
        <!-- <item name="android:textAllCaps">true</item> -->

         <item name="android:fontFamily">@font/source_sans_pro</item>
         <item name="fontFamily">@font/source_sans_pro</item>
    </style>

</resources>
