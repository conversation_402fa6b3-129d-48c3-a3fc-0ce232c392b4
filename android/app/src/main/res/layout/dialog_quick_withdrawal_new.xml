<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_gravity="center"
    android:background="@android:color/transparent"
    android:minWidth="350dp"
    tools:ignore="ContentDescription">

    <TextView
        android:id="@+id/tv_title_dialog_exchange"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="#ac272e"
        android:gravity="center"
        android:maxLines="1"
        android:text="@string/tv_title_dialog_quick"
        android:textAllCaps="true"
        android:textColor="#FFFFFF"
        android:textSize="16sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tv_des_dialog_withdrawal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/tv_title_dialog_exchange"
        android:layout_marginStart="@dimen/padding_item"
        android:layout_marginTop="@dimen/padding_item"
        android:layout_marginEnd="@dimen/padding_item"
        android:layout_marginBottom="@dimen/padding_item"
        android:gravity="center_horizontal"
        android:background="@drawable/bg_dash_orange_border_round"
        android:padding="14dp"
        android:text="@string/dialog_des_quick_draw"
        android:textColor="#F48232"
        android:textSize="16sp"
        android:textStyle="normal" />

    <TextView
        android:id="@+id/tv_des_note"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/tv_des_dialog_withdrawal"
        android:paddingStart="@dimen/padding_hight"
        android:paddingEnd="@dimen/padding_hight"
        android:text="@string/dialog_note_new"
        android:textColor="@color/gray_8"
        android:textSize="16sp"
        android:gravity="center_horizontal"
        />


    <TextView
        android:id="@+id/tv_des_note_2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/tv_des_note"
        android:padding="@dimen/padding_item"
        android:text="@string/dialog_note_2_new"
        android:textColor="@color/gray_9"
        android:textSize="16sp"
        android:gravity="center_horizontal"
        />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/stroke"
        android:background="@color/gray_10"
        android:layout_below="@id/tv_des_note_2"
        android:layout_marginStart="@dimen/padding_item"
        android:layout_marginEnd="@dimen/padding_item"
        />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ll_des_cost_service"
        android:layout_below="@id/tv_des_note_2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginTop="@dimen/distance_10">

        <TextView
            android:id="@+id/tv_1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/title_note_quick_withdraw"
            android:layout_marginStart="@dimen/padding_item"
            android:textColor="@color/gray_9"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            />

        <TextView
            android:id="@+id/tv_2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingStart="@dimen/padding_item"
            android:paddingEnd="@dimen/padding_item"
            android:text="@string/content_note_quick_withdraw1_new"
            android:textColor="@color/gray_8"
            android:textSize="16sp"
            android:textStyle="normal"
            app:layout_constraintTop_toBottomOf="@id/tv_1"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginTop="@dimen/padding_half_item"
            />

        <TextView
            android:id="@+id/tv_5"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toEndOf="@id/tv_2"
            app:layout_constraintTop_toTopOf="@id/tv_2"
            app:layout_constraintEnd_toEndOf="parent"
            android:gravity="right"
            android:layout_marginEnd="@dimen/padding_item"
            android:text="33,000 VND"
            android:textColor="@color/gray_8"
            android:textSize="16sp"
            android:textStyle="bold"
            />

        <TextView
            android:id="@+id/tv_3"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:paddingStart="@dimen/padding_item"
            android:paddingEnd="@dimen/padding_item"
            android:text="@string/content_note_quick_withdraw2_new"
            android:textColor="#2c2c2c"
            android:textSize="16sp"
            android:textStyle="normal"
            app:layout_constraintTop_toBottomOf="@id/tv_2"
            android:layout_marginTop="@dimen/padding_half_item"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tv_6"
            />


        <TextView
            android:id="@+id/tv_6"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toEndOf="@id/tv_3"
            app:layout_constraintTop_toTopOf="@id/tv_3"
            app:layout_constraintEnd_toEndOf="parent"
            android:gravity="right"
            android:layout_marginEnd="@dimen/padding_item"
            android:text="@string/content_note_quick_withdraw3_new"
            android:textColor="@color/gray_8"
            android:textSize="16sp"
            android:textStyle="bold"
            />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/stroke"
        android:background="@color/gray_10"
        android:layout_marginTop="@dimen/padding_item"
        android:layout_below="@id/ll_des_cost_service"
        android:layout_marginStart="@dimen/padding_item"
        android:layout_marginEnd="@dimen/padding_item"
        />


    <LinearLayout
        android:id="@+id/ll_des_value_cost_service"
        android:layout_below="@id/ll_des_cost_service"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingStart="@dimen/padding_item"
        android:paddingEnd="@dimen/padding_item"
        android:paddingTop="@dimen/padding_item"
        android:layout_marginTop="@dimen/padding_item"
        >

        <TextView
            android:id="@+id/tv_des_cost_service"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/dialog_cost_for_service_qd"
            android:textColor="@color/gray_9"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_weight="1"
            />

        <TextView
            android:id="@+id/tv_des_value_cost_service"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingStart="14dp"
            tools:text="~94,000 VNĐ"
            android:gravity="right"
            android:textColor="@color/tv_bg_red_want_money"
            android:textSize="18sp"
            android:textStyle="bold" />

    </LinearLayout>
<!--    button-->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/ll_des_value_cost_service"
        android:orientation="horizontal"
        android:paddingBottom="14dp"
        android:paddingLeft="14dp"
        android:paddingRight="14dp"
        android:paddingTop="14dp"
        android:visibility="visible"
        android:weightSum="2">

        <Button
            android:id="@+id/btn_cancel_quick_money"
            style="?android:buttonBarButtonStyle"
            android:layout_width="fill_parent"
            android:layout_height="45dp"
            android:layout_marginEnd="8dp"
            android:layout_weight="1"
            android:background="@drawable/bg_quick_cancel"
            android:gravity="center"
            android:text="@string/tv_cancel"
            android:textAllCaps="true"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/btn_confirm_quick_money"
            style="?android:buttonBarButtonStyle"
            android:layout_width="fill_parent"
            android:layout_height="45dp"
            android:layout_weight="1"
            android:layout_marginLeft="6dp"
            android:layout_marginStart="6dp"
            android:background="@drawable/bg_quick_confirm"
            android:gravity="center"
            android:text="@string/tv_confirm"
            android:textAllCaps="true"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:textStyle="bold" />

    </LinearLayout>

</RelativeLayout>