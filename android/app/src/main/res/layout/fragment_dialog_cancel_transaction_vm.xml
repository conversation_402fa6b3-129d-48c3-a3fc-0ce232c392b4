<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="10dp"
        app:cardElevation="0dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/distance_15"
                android:orientation="vertical">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <ImageView
                        android:id="@+id/iv_close"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_close_gray"
                        android:layout_alignParentRight="true"
                        android:padding="@dimen/distance_5"/>

                </RelativeLayout>

                <TextView
                    android:id="@+id/tv_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textAlignment="center"
                    android:textSize="21sp"
                    android:textColor="#4A4A4A"
                    android:textStyle="bold"
                    android:text="@string/cancel_transaction"/>

                <TextView
                    android:id="@+id/tv_content"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textAlignment="center"
                    android:textSize="14sp"
                    android:textColor="#323232"
                    android:text="@string/cancel_transaction_content"
                    android:layout_marginTop="@dimen/distance_15"/>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginTop="@dimen/distance_15"
                    android:gravity="center">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="12sp"
                        android:textColor="#848484"
                        android:text="@string/cancel_transaction_content_label"/>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="12sp"
                        android:textColor="#D12D29"
                        android:text="*"/>

                </LinearLayout>

                <EditText
                    android:id="@+id/et_cancel"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@null"
                    android:textColor="#4A4A4A"
                    android:textSize="16sp"
                    android:layout_marginTop="@dimen/distance_5"
                    android:layout_marginBottom="@dimen/distance_5"
                    android:hint="@string/cancel_transaction_content_placeholder"
                    android:maxLength="150"/>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="2dp"
                    android:background="#D12D29"/>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/btn_skip"
                    android:layout_width="0dp"
                    android:layout_height="45dp"
                    android:layout_weight="1"
                    android:background="@drawable/bg_btn_void_trans_round_border"
                    android:gravity="center"
                    android:text="@string/skip"
                    android:textAllCaps="false"
                    android:textColor="@color/gray_4"
                    android:textSize="@dimen/text_size_normal"
                    android:layout_marginRight="@dimen/distance_5"
                    android:layout_marginLeft="@dimen/distance_15"
                    android:layout_marginBottom="@dimen/distance_15"/>

                <Button
                    android:id="@+id/btn_cancel"
                    android:layout_width="0dp"
                    android:layout_height="45dp"
                    android:layout_weight="2"
                    android:background="@drawable/bg_btn_void_trans_round_red"
                    android:gravity="center"
                    android:text="@string/cancel_transaction_confirm"
                    android:textAllCaps="false"
                    android:textColor="@color/white"
                    android:textSize="@dimen/text_size_normal"
                    android:layout_marginLeft="@dimen/distance_5"
                    android:layout_marginRight="@dimen/distance_15"
                    android:layout_marginBottom="@dimen/distance_15"/>

            </LinearLayout>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

</RelativeLayout>