<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@android:color/transparent">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/iv_cover"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:scaleType="fitCenter"
                    android:src="@drawable/ic_temp_dialog_affiliate"/>

                <TextView
                    android:id="@+id/tv_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textAlignment="center"
                    android:textSize="20sp"
                    android:textColor="#D12D29"
                    android:textStyle="bold"
                    tools:text="Kiếm tiền cực dễ"
                    android:layout_marginTop="@dimen/distance_5"/>

                <TextView
                    android:id="@+id/tv_content"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textAlignment="center"
                    android:textSize="14sp"
                    android:textColor="#323232"
                    tools:text="Nhận ngay 500.000đ khi bạn giới thiệu mPOS cho
cửa hàng mới.Người được bạn giới thiệu sẽ nhận
được thêm 300.000đ"
                    android:layout_marginTop="@dimen/distance_15"/>

            </LinearLayout>

            <ImageView
                android:id="@+id/iv_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_close_gray"
                android:layout_alignParentRight="true"
                android:padding="@dimen/distance_5"
                android:layout_marginRight="@dimen/distance_15"
                android:layout_marginTop="@dimen/distance_10"/>

        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_dialog"
                android:layout_width="0dp"
                android:layout_height="45dp"
                android:layout_weight="2"
                android:background="@drawable/bg_btn_void_trans_round_red"
                android:gravity="center"
                tools:text="Giới thiệu ngay"
                android:textAllCaps="true"
                android:textColor="@color/white"
                android:textSize="@dimen/text_size_normal"
                android:layout_marginLeft="@dimen/distance_15"
                android:layout_marginRight="@dimen/distance_15"
                android:layout_marginBottom="30dp"
                android:layout_marginTop="@dimen/distance_5"/>

        </LinearLayout>

    </LinearLayout>

</RelativeLayout>