<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.Toolbar xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/BaseTheme"
    android:layout_width="match_parent"
    android:layout_height="?attr/actionBarSize"
    android:background="@color/red_1"
    android:contentInsetEnd="0dp"
    android:contentInsetRight="0dp"
    app:contentInsetEnd="0dp"
    app:contentInsetLeft="0dp"
    app:contentInsetRight="0dp"
    app:contentInsetStart="0dp"
    tools:targetApi="lollipop">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <TextView
            android:id="@+id/toolbar_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:layout_marginStart="?attr/actionBarSize"
            android:layout_marginLeft="?attr/actionBarSize"
            android:gravity="center"
            android:paddingStart="0dp"
            android:paddingLeft="0dp"
            android:paddingEnd="?attr/actionBarSize"
            android:paddingRight="?attr/actionBarSize"
            android:text=""
            android:textAllCaps="false"
            android:textColor="@android:color/white"
            android:textSize="@dimen/font_large"
            tools:text="title" />

        <ImageView
            android:id="@+id/imv_search"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="@dimen/padding_item"
            android:padding="@dimen/padding_half_item"
            android:src="@drawable/ic_search"
            android:visibility="gone"
            tools:visibility="visible" />
    </RelativeLayout>

</androidx.appcompat.widget.Toolbar>