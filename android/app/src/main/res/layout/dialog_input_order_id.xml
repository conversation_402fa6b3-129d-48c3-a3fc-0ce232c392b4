<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/padding_hight"
    android:minWidth="@dimen/min_width_dialog_base"
    android:background="@drawable/border_radius"
    android:orientation="vertical"
    >

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/red"
        android:padding="@dimen/padding_half_item"
        >

        <ImageView
            android:id="@+id/imv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:padding="@dimen/padding_item"
            android:src="@drawable/ic_close"
            />

        <ImageView
            android:id="@+id/ic_fake"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:src="@drawable/ic_close"
            android:padding="@dimen/padding_item"
            android:visibility="invisible"
            />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_marginLeft="@dimen/padding_half_item"
            android:layout_marginRight="@dimen/padding_half_item"
            android:layout_toLeftOf="@id/imv_close"
            android:layout_toRightOf="@id/ic_fake"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:text="@string/pay_invoice"
            android:textAllCaps="true"
            android:textColor="@color/white"
            android:textSize="@dimen/font_normal"
            />

    </RelativeLayout>

    <LinearLayout style="@style/BgItemService">

        <TextView
            style="@style/TvItemService"
            android:text="@string/invoice_code_need_pay"
            />

        <EditText
            android:id="@+id/edt_order_code"
            style="@style/EdtItemService"
            android:drawableLeft="@drawable/ic_invoice"
            tools:text="12341234"/>
    </LinearLayout>


    <LinearLayout
        android:id="@+id/btn_invoice_pay"
        style="@style/MyButtonRedFullWidth"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/padding_item"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="@dimen/padding_item"
        >

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/pay_invoice"
            android:textColor="@color/white"
            android:textSize="@dimen/font_large"
            />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/with_invoice"
            android:textColor="@color/white"
            android:textSize="@dimen/font_small"
            />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/btn_normal_pay"
        style="@style/MyBtnGray"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/padding_item"
        android:layout_marginLeft="@dimen/padding_item"
        android:layout_marginRight="@dimen/padding_item"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="@dimen/padding_item"
        android:visibility="gone"
        >

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/pay_action_normal"
            android:textColor="@color/white"
            android:textSize="@dimen/font_large"
            />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/with_no_invoice"
            android:textColor="@color/white"
            android:textSize="@dimen/font_small"
            />
    </LinearLayout>

</LinearLayout>