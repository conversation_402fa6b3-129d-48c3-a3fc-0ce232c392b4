<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:orientation="vertical"
    android:id="@+id/layout_root">

    <RelativeLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <RelativeLayout
            android:id="@+id/layout_webview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical"
            android:layout_marginTop="?attr/actionBarSize">

            <WebView
                android:id="@+id/webView1"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="visible" />

            <LinearLayout
                android:id="@+id/reload"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical" >

                <TextView
                    android:id="@+id/textView1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/error_try_again" />

                <Button
                    android:id="@+id/button1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/ALERT_BTN_OK" />

            </LinearLayout>

        </RelativeLayout>

        <include
            android:id="@+id/ll_toolbar"
            layout="@layout/tool_bar"
            />

    </RelativeLayout>

    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_alignParentRight="true"
        android:layout_marginRight="@dimen/distance_15"
        android:layout_marginTop="@dimen/distance_15"
        android:visibility="gone"/>

    <ImageButton
        android:id="@+id/ib_home"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:src="@drawable/ic_home_web"
        android:background="@android:color/transparent"
        android:scaleType="fitCenter"
        android:layout_alignParentRight="true"
        android:layout_marginRight="@dimen/distance_15"
        android:layout_marginTop="@dimen/distance_15"
        android:adjustViewBounds="true"
        android:padding="@dimen/distance_5"
        android:visibility="visible"/>

</RelativeLayout>