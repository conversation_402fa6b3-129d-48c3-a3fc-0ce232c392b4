<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/SpecialDialog"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_horizontal"
    android:minWidth="@dimen/min_width_dialog_base"
    android:background="@drawable/border_radius"
    android:orientation="vertical">
<!--     style="@style/DialogRadius" -->

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center" 
        >
        <TextView
            android:id="@+id/txtTitleDialog"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="10dp"
            tools:text="Title"
            android:textAppearance="?android:attr/textAppearanceMedium"
            android:textColor="@color/black"
            android:textSize="20sp"
            android:textStyle="bold" />
    </LinearLayout>
	<LinearLayout 
	    android:layout_width="match_parent"
	    android:layout_height="1dp"
	    android:background="@color/gray_light"
	    android:layout_marginBottom="@dimen/padding_half_item"
	    />
    <TextView
        android:id="@+id/txtDesDialog"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/padding_item"
        android:gravity="center_horizontal"
        android:text=""
        android:textSize="@dimen/font_large"
        android:textAppearance="?android:attr/textAppearanceMedium"
        android:textColor="#FF2F2F2F" />

    <!-- <ImageView
        android:id="@+id/imvDesDialog"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:visibility="visible" /> -->

    <EditText
        android:id="@+id/edtxt_set_name"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/padding_item"
        android:layout_marginLeft="@dimen/padding_item"
        android:layout_marginTop="@dimen/padding_item"
        android:layout_marginRight="@dimen/padding_item"
        android:visibility="gone" />
    
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/padding_item"
        android:orientation="horizontal">
        <Button
            android:id="@+id/btnDialogCancel"
            android:layout_width="fill_parent"
            android:layout_height="50dp"
		    android:layout_weight="1" 
            android:background="@drawable/btn_grey_rounded"
            android:text="@string/ALERT_BTN_NO"
            android:singleLine="true"
            android:textColor="#FF000000"
            android:textSize="@dimen/font_large"
            android:gravity="center"
            />
		<LinearLayout 
		    android:id="@+id/ll_center"
		    android:layout_width="@dimen/padding_item"
		    android:layout_height="wrap_content"
		    android:orientation="horizontal"
		    />
        <Button
            android:id="@+id/btnDialogOk"
            android:layout_width="fill_parent"
            android:layout_height="50dp"
         	android:layout_weight="1" 
            android:background="@drawable/btn_orange_rounded"
            android:text="@string/ALERT_BTN_OK"
            android:singleLine="true"
            android:textColor="#FFffffff"
            android:textSize="@dimen/font_large"
            android:gravity="center"
            />
    </LinearLayout>

</LinearLayout>