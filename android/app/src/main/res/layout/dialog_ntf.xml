<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_marginStart="@dimen/padding_item"
    android:layout_marginEnd="@dimen/padding_item"
    android:padding="@dimen/padding_item"
    android:background="@drawable/border_radius"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:minWidth="@dimen/min_width_dialog_base"
        android:orientation="vertical"
        >

        <TextView
            android:id="@+id/title"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical|center_horizontal"
            android:gravity="center"
            android:text="@string/app_name"
            android:textSize="@dimen/text_size_large"
            android:textStyle="bold" />

        <WebView
            android:id="@+id/webView1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/padding_item"
            android:layout_marginBottom="@dimen/padding_item"
            />

        <com.mpos.customview.TextViewJustify
            android:id="@+id/tv_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_marginTop="@dimen/padding_item"
            android:layout_marginBottom="@dimen/padding_item"
            android:textSize="@dimen/font_normal"
            />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/linearLayout1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        >

        <Button
            android:id="@+id/btn_close"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            style="@style/MyBtnGray"
            android:text="@string/BTN_CLOSE"
            android:visibility="gone"
            tools:visibility="visible"
            />
        <TextView
            android:id="@+id/v_space"
            android:layout_width="@dimen/padding_item"
            android:layout_height="wrap_content"
            android:visibility="gone"
            tools:visibility="visible"
            />
        <Button
            android:id="@+id/ok"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            style="@style/MyButtonRedFullWidth"
            android:text="@string/ALERT_BTN_OK"
            />
    </LinearLayout>

</LinearLayout>