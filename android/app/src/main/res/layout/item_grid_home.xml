<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/v_root_item"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/distance_7"
    android:background="@drawable/btn_corner_border_white"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:elevation="@dimen/distance_5"
    android:paddingTop="20dp"
    android:paddingBottom="@dimen/distance_10">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/imv_icon"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:src="@drawable/ic_time"
        android:scaleType="fitCenter"/>

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/padding_item"
        android:gravity="center_horizontal"
        android:lines="2"
        android:textColor="@color/color_text"
        android:textSize="@dimen/font_small"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        style="@style/label_normal"
        tools:text="Thanh toán thường" />
</LinearLayout>
