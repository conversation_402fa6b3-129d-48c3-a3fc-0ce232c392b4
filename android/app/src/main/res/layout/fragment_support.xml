<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:clickable="true"
    android:focusable="true"
    android:orientation="vertical">

    <include
        android:id="@+id/ll_toolbar"
        layout="@layout/tool_bar" />
<!--
    <LinearLayout
        android:id="@+id/support_pay"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="@dimen/padding_item"
        android:visibility="gone"
        tools:visibility="visible">

        <TextView
            style="@style/normalFont"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:text="@string/SUPPORT_PAY"
            android:textColor="@color/black" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:src="@drawable/ic_arrow_go" />
    </LinearLayout>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/tablecell_bg_ontouch"
        android:visibility="gone"
        tools:visibility="visible" />

    <LinearLayout
        android:id="@+id/support_service"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="@dimen/padding_item"
        android:visibility="gone"
        tools:visibility="visible">

        <TextView
            style="@style/normalFont"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:text="@string/SUPPORT_SERVICE"
            android:textColor="@color/black" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:src="@drawable/ic_arrow_go" />
    </LinearLayout>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/tablecell_bg_ontouch"
        android:visibility="gone"
        tools:visibility="visible" />-->

    <LinearLayout
        android:id="@+id/support_other"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="@dimen/padding_item">

        <TextView
            style="@style/normalFont"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:text="@string/SUPPORT_OTHER"
            android:textColor="@color/black" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:src="@drawable/ic_arrow_go" />
    </LinearLayout>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/tablecell_bg_ontouch" />

</LinearLayout>