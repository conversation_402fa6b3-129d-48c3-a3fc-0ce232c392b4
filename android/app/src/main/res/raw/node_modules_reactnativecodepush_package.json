{"name": "react-native-code-push", "version": "6.4.1", "description": "React Native plugin for the CodePush service", "main": "CodePush.js", "typings": "typings/react-native-code-push.d.ts", "homepage": "https://microsoft.github.io/code-push", "keywords": ["react-native", "code", "push"], "author": "Microsoft Corporation", "license": "MIT", "scripts": {"clean": "shx rm -rf bin", "setup": "npm install --quiet --no-progress", "prebuild:tests": "npm run clean && npm run tslint", "build:tests": "tsc", "test": "npm run build:tests && npm run test:setup && npm run test:fast", "test:android": "npm run build:tests && npm run test:setup:android && npm run test:fast:android", "test:ios": "npm run build:tests && npm run test:setup:ios && npm run test:fast:ios", "test:setup": "mocha --recursive bin/test --android --ios --setup", "test:setup:android": "mocha --recursive bin/test --android --setup", "test:setup:ios": "mocha --recursive bin/test --ios --setup", "test:fast": "mocha --recursive bin/test --android --ios", "test:fast:android": "mocha --recursive bin/test --android", "test:fast:ios": "mocha --recursive bin/test --ios", "test:debugger:android": "mocha --recursive --inspect-brk=0.0.0.0 bin/test --android", "test:debugger:ios": "mocha --recursive --inspect-brk=0.0.0.0 bin/test --ios", "tslint": "tslint -c tslint.json test/**/*.ts"}, "repository": {"type": "git", "url": "https://github.com/microsoft/react-native-code-push"}, "dependencies": {"code-push": "^4.0.2", "glob": "^7.1.6", "hoist-non-react-statics": "^3.3.2", "inquirer": "^7.3.3", "plist": "3.0.1", "semver": "^7.3.2", "xcode": "3.0.1"}, "devDependencies": {"@types/assert": "^1.5.2", "@types/mkdirp": "^1.0.1", "@types/mocha": "^8.0.3", "@types/node": "^14.0.27", "@types/q": "^1.5.4", "archiver": "latest", "body-parser": "latest", "code-push-plugin-testing-framework": "file:./code-push-plugin-testing-framework", "del": "latest", "express": "latest", "mkdirp": "latest", "mocha": "^8.1.3", "q": "^1.5.1", "run-sequence": "latest", "shx": "^0.3.2", "slash": "^3.0.0", "tslint": "^6.1.3", "typescript": "^4.0.3"}, "rnpm": {"android": {"packageInstance": "new CodePush(getResources().getString(R.string.CodePushDeploymentKey), getApplicationContext(), BuildConfig.DEBUG)"}, "ios": {"sharedLibraries": ["libz"]}, "commands": {"postlink": "node node_modules/react-native-code-push/scripts/postlink/run", "postunlink": "node node_modules/react-native-code-push/scripts/postunlink/run"}}}