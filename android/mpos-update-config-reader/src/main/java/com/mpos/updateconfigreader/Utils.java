package com.mpos.updateconfigreader;

import android.content.Context;
import android.widget.Toast;

/**
 * Create by an<PERSON><PERSON>yen on 7/29/20
 */
public class Utils {

    // todo test
//    public final static boolean mDEBUG = true;
    public final static boolean mDEBUG = BuildConfig.DEBUG;

    private static final String TAG = "Utils";

    public static void LOGI(String tag, String msg) {
        if (mDEBUG)
            android.util.Log.i(tag, msg!=null?msg:"");
    }

    public static void LOGE(String tag, String msg) {
//        if (mDEBUG)
//            android.util.Log.e(tag, msg != null ? msg : "");
        LOGE(tag, msg, null);
    }

    public static void LOGE(String tag, String msg, Throwable tr) {
        if (mDEBUG)
            android.util.Log.e(tag, msg != null ? msg : "", tr);
    }

    public static void LOGD(String tag, String msg) {
        if (mDEBUG)
            android.util.Log.d(tag, msg != null ? msg : "");
    }

    public static void LOGW(String tag, String msg) {
        if (mDEBUG)
            android.util.Log.w(tag, msg != null ? msg : "");
    }

    public static void showToast(Context c, String msg) {
        Toast.makeText(c, msg, Toast.LENGTH_SHORT).show();
    }

    static final String HEXES = "0123456789ABCDEF";
    public static String byteArray2Hex(byte[] raw) {
        if (raw == null) {
            Utils.LOGD(TAG, "byteArray2Hex: raw==null");
            return null;
        }
        Utils.LOGD(TAG, "byteArray2Hex: size="+raw.length);
        final StringBuilder hex = new StringBuilder(2 * raw.length);
        for (final byte b : raw) {
            hex.append(HEXES.charAt((b & 0xF0) >> 4)).append(HEXES.charAt((b & 0x0F)));
        }
        return hex.toString();
    }
}
